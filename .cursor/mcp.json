{"mcpServers": {"task-master-ai": {"command": "npx", "args": ["task-master-ai"], "env": {"OPENAI_API_KEY": "sk-xxxxxxx"}}, "laravel-mcp": {"command": "php", "args": ["artisan", "mcp:serve"], "cwd": "/Users/<USER>/Development/Projects/TMS (Tenant_Management_System)/backend", "env": {"APP_ENV": "local", "APP_DEBUG": "true", "MCP_STDIO_ENABLED": "true", "MCP_AUTO_DISCOVER": "true", "MCP_CACHE_ENABLED": "true"}, "disabled": false}}}