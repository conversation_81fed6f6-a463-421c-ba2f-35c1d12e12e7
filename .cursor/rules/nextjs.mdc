---
description: Frontend Next.JS app
alwaysApply: true
globs:
  - frontend/src/**/*
  - frontend/pages/**/*
  - frontend/app/**/*
  - frontend/components/**/*
  - frontend/utils/**/*
  - frontend/services/**/*
  - frontend/hooks/**/*
  - frontend/styles/**/*
  - frontend/public/**/*
  - frontend/package.json
  - frontend/next.config.js
  - frontend/tailwind.config.js
  - frontend/postcss.config.js
  - frontend/eslint.config.js
  - frontend/tsconfig.json  
  - frontend/README.md
  - frontend/setup-project.sh
  - frontend/verify-taskmaster-setup.sh
  - frontend/.env.example
  - frontend/.env.local.example
  - frontend/.env.development.example
  - frontend/.env.test.example
  - frontend/.env.production.example
  - frontend/.prettierrc.js
  - frontend/.prettierignore
  - frontend/.gitignore
  - frontend/.taskmaster/**/*
  - frontend/.cursor/**/*
  - frontend/DEVELOPMENT_PLAN.md
  - frontend/DEVELOPMENT_CHECKLIST.md
  - frontend/PROJECT_LISTS.md
  - frontend/PROJECT_SUMMARY.md
  - frontend/TECHNICAL_IMPLEMENTATION_GUIDE.md
  - frontend/HOUSING_SOCIETY_TMS_PRD_ALIGNMENT.md
  - frontend/docs/api-specification.md
  - frontend/docs/nextjs.md
  - frontend/docs/tailwind.md
  - frontend/docs/eslint.md
  - frontend/docs/tsconfig.md
  - frontend/docs/prettier.md
  - frontend/docs/gitignore.md
  - frontend/docs/taskmaster.md
  - frontend/docs/cursor.md
  - frontend/docs/development_plan.md
  - frontend/docs/development_checklist.md
  - frontend/docs/project_lists.md
  - frontend/docs/project_summary.md
  - frontend/docs/technical_implementation_guide.md
  - frontend/docs/  