# RabbitMQ Real-time Sync Implementation

## Overview

This document describes the implementation of the RabbitMQ-based real-time synchronization system for the Housing Society TMS multi-tenant architecture. The system provides bidirectional data synchronization between the TMS backend and external OneSociety application while maintaining tenant isolation and data consistency.

## Architecture

### System Components

```mermaid
graph TB
    A[TMS Backend] --> B[Model Observers]
    B --> C[Sync Events]
    C --> D[RabbitMQ Queue]
    D --> E[Event Listeners]
    E --> F[Sync Processing]
    F --> G[Audit Logging]
    
    H[OneSociety App] --> D
    D --> I[TMS Listeners]
    I --> J[Conflict Resolution]
    J --> K[Entity Updates]
    
    L[Admin Dashboard] --> M[Sync Controller]
    M --> N[Sync Service]
    N --> O[Manual Operations]
```

### Data Flow

1. **Event Generation**: Model observers detect CRUD operations and dispatch sync events
2. **Queue Processing**: Events are queued in RabbitMQ with tenant isolation
3. **Event Consumption**: Listeners process events from external systems
4. **Conflict Resolution**: Timestamp-based or priority-based conflict resolution
5. **Audit Logging**: All sync operations are logged for monitoring and debugging

## Implementation Details

### Configuration

#### Environment Variables
```bash
# Queue Configuration
QUEUE_CONNECTION=rabbitmq

# RabbitMQ Configuration
RABBITMQ_HOST=127.0.0.1
RABBITMQ_PORT=5672
RABBITMQ_USER=guest
RABBITMQ_PASSWORD=guest
RABBITMQ_VHOST=/
RABBITMQ_QUEUE=tms_sync

# Sync Configuration
SYNC_ENABLED=true
SYNC_SOURCE_SYSTEM=tms
SYNC_RETRY_ATTEMPTS=3
```

#### Sync Configuration (`config/sync.php`)
```php
'entities' => [
    'Society' => [
        'model' => \App\Models\Society::class,
        'fields' => ['id', 'name', 'address', 'city', 'state', ...],
        'sync_on' => ['created', 'updated', 'deleted'],
    ],
    'Unit' => [
        'model' => \App\Models\Unit::class,
        'fields' => ['id', 'society_id', 'unit_number', ...],
        'sync_on' => ['created', 'updated', 'deleted'],
    ],
    // ... other entities
],
```

### Event System

#### Base Sync Event
```php
abstract class BaseSyncEvent implements ShouldQueue
{
    public array $entityData;
    public ?int $societyId;
    public string $operation;
    public string $sourceSystem;
    public string $correlationId;
    
    // Queue configuration
    public int $tries = 3;
    public array $backoff = [10, 30, 60];
    public int $timeout = 120;
}
```

#### Specific Events
- `SocietyUpdated`
- `UnitUpdated`
- `TenantUpdated`
- `AgreementUpdated`
- `PaymentUpdated`
- `EntityDeleted`

### Event Listeners

#### Base Sync Listener
```php
abstract class BaseSyncListener
{
    public function handle(BaseSyncEvent $event): void
    {
        // 1. Validate society access (tenant isolation)
        // 2. Check for conflicts
        // 3. Perform upsert operation
        // 4. Log audit trail
        // 5. Handle errors and retries
    }
}
```

### Model Observers

#### Sync Observer
```php
class SyncObserver
{
    public function created(Model $model): void
    {
        if ($this->shouldSync($model)) {
            $this->dispatchSyncEvent($model, 'created');
        }
    }
    
    public function updated(Model $model): void
    {
        if ($this->shouldSync($model)) {
            $this->dispatchSyncEvent($model, 'updated');
        }
    }
    
    public function deleted(Model $model): void
    {
        if ($this->shouldSync($model)) {
            $this->dispatchDeletionEvent($model);
        }
    }
}
```

### Audit Logging

#### Sync Audit Log Model
```php
class SyncAuditLog extends Model
{
    // Status constants
    const STATUS_PENDING = 'pending';
    const STATUS_PROCESSING = 'processing';
    const STATUS_SUCCESS = 'success';
    const STATUS_FAILED = 'failed';
    const STATUS_SKIPPED = 'skipped';
    const STATUS_CONFLICT = 'conflict';
    
    // Performance tracking
    protected $casts = [
        'processing_time_ms' => 'decimal:2',
        'memory_usage_bytes' => 'integer',
    ];
}
```

### Conflict Resolution

#### Strategies

1. **Timestamp-based** (default)
   - Compare `updated_at` timestamps
   - Newer version wins

2. **Source Priority**
   - Configurable system priority
   - Higher priority system wins

3. **Manual Resolution**
   - Flag conflicts for manual review
   - Admin dashboard for resolution

#### Implementation
```php
protected function resolveByTimestamp($event, $existingEntity): array
{
    $localTimestamp = $existingEntity->updated_at;
    $syncTimestamp = Carbon::parse($event->syncVersion);
    
    if ($localTimestamp->greaterThan($syncTimestamp)) {
        return [
            'success' => false,
            'reason' => 'Local version is newer',
        ];
    }
    
    return ['success' => true];
}
```

### Tenant Isolation

#### Society-based Access Control
```php
protected function canAccessSociety(?int $societyId): bool
{
    if (!config('sync.security.validate_society_access', true)) {
        return true;
    }
    
    if ($societyId === null) {
        return true; // Global entities
    }
    
    return Society::where('id', $societyId)->exists();
}
```

#### Society ID Extraction
```php
protected function extractSocietyId(Model $model): ?int
{
    // Direct society_id field
    if (isset($model->society_id)) {
        return $model->society_id;
    }
    
    // For Society model
    if ($model instanceof Society) {
        return $model->id;
    }
    
    // Through relationships
    if (method_exists($model, 'unit')) {
        return $model->unit?->society_id;
    }
    
    return null;
}
```

## API Endpoints

### Admin Dashboard APIs

```php
// Sync statistics and dashboard
GET /api/v1/admin/sync/dashboard?hours=24

// Sync audit logs with filtering
GET /api/v1/admin/sync/logs?entity_type=Unit&status=failed

// Retry failed syncs
POST /api/v1/admin/sync/retry-failed

// Retry specific sync log
POST /api/v1/admin/sync/logs/{id}/retry
```

### Response Format
```json
{
    "success": true,
    "data": {
        "total_events": 1250,
        "success_rate": 98.4,
        "failure_rate": 1.6,
        "average_processing_time": 45.2,
        "queue_health": {
            "status": "healthy",
            "pending_jobs": 12,
            "failed_jobs": 3
        }
    }
}
```

## Console Commands

### Sync Management Commands

```bash
# Check sync system status
php artisan sync:status --hours=24 --detailed

# Retry failed sync operations
php artisan sync:retry-failed --limit=100 --max-retries=3

# Manually trigger sync for specific entity
php artisan sync:entity unit 123 --operation=updated
```

### Queue Worker Commands

```bash
# Start queue worker for sync operations
php artisan queue:work rabbitmq --queue=tms_sync --tries=3 --backoff=10,30,60

# Monitor queue with Laravel Horizon
php artisan horizon
```

## Performance Considerations

### Queue Configuration

1. **Worker Processes**: Run multiple workers for high throughput
2. **Memory Management**: Set memory limits and restart workers periodically
3. **Retry Logic**: Exponential backoff for failed jobs
4. **Dead Letter Queue**: Handle permanently failed jobs

### Optimization Strategies

1. **Batch Processing**: Group related operations
2. **Field Filtering**: Only sync necessary fields
3. **Lazy Loading**: Load relationships on demand
4. **Compression**: Enable payload compression for large data

### Monitoring Metrics

- **Throughput**: Events processed per minute
- **Latency**: Average processing time
- **Error Rate**: Failed sync percentage
- **Queue Depth**: Pending jobs count
- **Memory Usage**: Worker memory consumption

## Security

### Data Protection

1. **Field Filtering**: Only sync predefined fields
2. **Tenant Isolation**: Society-based access control
3. **Payload Validation**: Schema validation for incoming data
4. **Audit Trail**: Complete operation logging

### Access Control

1. **Admin Only**: Sync management restricted to admin users
2. **Society Validation**: Verify society access for all operations
3. **Source Verification**: Validate event source system
4. **Rate Limiting**: Prevent abuse and flooding

## Testing

### Test Coverage

1. **Unit Tests**: Event generation and processing
2. **Integration Tests**: End-to-end sync flow
3. **Performance Tests**: Load testing with high volume
4. **Security Tests**: Tenant isolation validation

### Test Scenarios

```php
// Test sync event generation
public function test_unit_sync_event_triggered_on_update()
{
    Event::fake();
    $unit->update(['status' => 'rented']);
    Event::assertDispatched(UnitUpdated::class);
}

// Test tenant isolation
public function test_sync_respects_society_isolation()
{
    // Verify cross-society sync is blocked
}

// Test conflict resolution
public function test_timestamp_conflict_resolution()
{
    // Verify newer local data is preserved
}
```

## Deployment

### Infrastructure Requirements

1. **RabbitMQ Server**: Dedicated instance with clustering
2. **Queue Workers**: Supervised processes with auto-restart
3. **Monitoring**: Prometheus/Grafana for metrics
4. **Logging**: Centralized log aggregation

### Production Setup

```bash
# Install RabbitMQ
sudo apt-get install rabbitmq-server

# Configure virtual hosts
sudo rabbitmqctl add_vhost tms_production
sudo rabbitmqctl set_permissions -p tms_production tms_user ".*" ".*" ".*"

# Start queue workers with supervisor
sudo supervisorctl start tms-queue-worker:*
```

## Troubleshooting

### Common Issues

1. **Queue Backlog**: Increase worker processes
2. **Memory Leaks**: Restart workers periodically
3. **Connection Failures**: Check RabbitMQ connectivity
4. **Sync Loops**: Verify source system filtering

### Debugging Tools

1. **Sync Status Command**: Check system health
2. **Audit Logs**: Review failed operations
3. **Queue Monitoring**: Track job processing
4. **Performance Metrics**: Identify bottlenecks

### Log Analysis

```bash
# Check sync-related logs
tail -f storage/logs/laravel.log | grep "Sync"

# Monitor queue worker logs
tail -f storage/logs/worker.log

# Check RabbitMQ logs
sudo tail -f /var/log/rabbitmq/<EMAIL>
```

## Future Enhancements

### Planned Features

1. **Real-time Dashboard**: WebSocket-based live updates
2. **Advanced Conflict Resolution**: Machine learning-based decisions
3. **Data Transformation**: Field mapping and data conversion
4. **Webhook Integration**: HTTP-based sync alternatives
5. **Multi-region Support**: Cross-region data replication

### Scalability Improvements

1. **Horizontal Scaling**: Multiple queue worker instances
2. **Database Sharding**: Partition audit logs by date
3. **Caching Layer**: Redis for frequently accessed data
4. **Load Balancing**: Distribute queue processing load
