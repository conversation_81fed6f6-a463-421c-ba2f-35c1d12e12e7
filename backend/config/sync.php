<?php

return [
    /*
    |--------------------------------------------------------------------------
    | Sync Configuration
    |--------------------------------------------------------------------------
    |
    | This file contains configuration options for the real-time sync system
    | between Housing Society TMS and external OneSociety application.
    |
    */

    'enabled' => env('SYNC_ENABLED', true),

    'source_system' => env('SYNC_SOURCE_SYSTEM', 'tms'),

    'batch_size' => env('SYNC_BATCH_SIZE', 100),

    'retry_attempts' => env('SYNC_RETRY_ATTEMPTS', 3),

    'retry_delay' => env('SYNC_RETRY_DELAY', 10), // seconds

    'timeout' => env('SYNC_TIMEOUT', 30), // seconds

    /*
    |--------------------------------------------------------------------------
    | Queue Configuration
    |--------------------------------------------------------------------------
    */

    'queue' => [
        'connection' => env('QUEUE_CONNECTION', 'rabbitmq'),
        'name' => env('RABBITMQ_QUEUE', 'tms_sync'),
        'retry_after' => env('RABBITMQ_RETRY_AFTER', 90),
    ],

    /*
    |--------------------------------------------------------------------------
    | Syncable Entities
    |--------------------------------------------------------------------------
    |
    | Define which entities should be synchronized and their field mappings
    |
    */

    'entities' => [
        'Society' => [
            'model' => \App\Models\Society::class,
            'fields' => [
                'id', 'name', 'address', 'city', 'state', 'pincode',
                'contact_person', 'contact_phone', 'contact_email',
                'total_units', 'status', 'created_at', 'updated_at'
            ],
            'relationships' => ['units', 'users'],
            'sync_on' => ['created', 'updated', 'deleted'],
        ],

        'Unit' => [
            'model' => \App\Models\Unit::class,
            'fields' => [
                'id', 'society_id', 'unit_number', 'block', 'floor', 'wing',
                'type', 'bedrooms', 'bathrooms', 'area_sqft', 'status',
                'owner_id', 'current_tenant_id', 'is_sublet', 'is_owner_occupied',
                'market_rent', 'security_deposit', 'created_at', 'updated_at'
            ],
            'relationships' => ['society', 'owner', 'currentTenant'],
            'sync_on' => ['created', 'updated', 'deleted'],
        ],

        'Tenant' => [
            'model' => \App\Models\Tenant::class,
            'fields' => [
                'id', 'user_id', 'tenant_code', 'unit_id', 'owner_id',
                'occupation', 'company_name', 'monthly_income',
                'security_deposit', 'advance_amount', 'move_in_date',
                'move_out_date', 'agreement_start_date', 'agreement_end_date',
                'kyc_status', 'status', 'created_at', 'updated_at'
            ],
            'relationships' => ['user', 'unit', 'owner'],
            'sync_on' => ['created', 'updated', 'deleted'],
        ],

        'Agreement' => [
            'model' => \App\Models\Agreement::class,
            'fields' => [
                'id', 'tenant_id', 'unit_id', 'template_id', 'status',
                'start_date', 'end_date', 'signed_at', 'signed_by_owner',
                'signed_by_tenant', 'created_at', 'updated_at'
            ],
            'relationships' => ['tenant', 'unit', 'template'],
            'sync_on' => ['created', 'updated', 'deleted'],
        ],

        'RentPayment' => [
            'model' => \App\Models\RentPayment::class,
            'fields' => [
                'id', 'unit_id', 'tenant_id', 'amount', 'payment_date',
                'payment_method', 'external_reference', 'status',
                'reconciled_at', 'created_at', 'updated_at'
            ],
            'relationships' => ['unit', 'tenant'],
            'sync_on' => ['created', 'updated'],
        ],
    ],

    /*
    |--------------------------------------------------------------------------
    | Conflict Resolution
    |--------------------------------------------------------------------------
    |
    | Define how conflicts should be resolved when the same entity
    | is modified in both systems
    |
    */

    'conflict_resolution' => [
        'strategy' => 'timestamp', // 'timestamp', 'manual', 'source_priority'
        'timestamp_field' => 'updated_at',
        'source_priority' => ['tms', 'onesociety'], // Higher priority first
    ],

    /*
    |--------------------------------------------------------------------------
    | Security & Validation
    |--------------------------------------------------------------------------
    */

    'security' => [
        'validate_society_access' => true,
        'encrypt_payload' => false,
        'sign_payload' => false,
        'max_payload_size' => 1024 * 1024, // 1MB
    ],

    /*
    |--------------------------------------------------------------------------
    | Monitoring & Logging
    |--------------------------------------------------------------------------
    */

    'monitoring' => [
        'log_all_events' => true,
        'log_failed_events' => true,
        'log_skipped_events' => true,
        'performance_tracking' => true,
        'alert_on_failures' => true,
        'alert_threshold' => 10, // Number of consecutive failures
    ],

    /*
    |--------------------------------------------------------------------------
    | Performance Settings
    |--------------------------------------------------------------------------
    */

    'performance' => [
        'enable_batching' => true,
        'batch_timeout' => 5, // seconds
        'max_memory_usage' => 512 * 1024 * 1024, // 512MB
        'enable_compression' => true,
        'lazy_loading' => true,
    ],

    /*
    |--------------------------------------------------------------------------
    | Environment-specific Settings
    |--------------------------------------------------------------------------
    */

    'environments' => [
        'development' => [
            'queue_prefix' => 'dev_',
            'debug_mode' => true,
            'sync_delay' => 0,
        ],
        'staging' => [
            'queue_prefix' => 'staging_',
            'debug_mode' => true,
            'sync_delay' => 1,
        ],
        'production' => [
            'queue_prefix' => 'prod_',
            'debug_mode' => false,
            'sync_delay' => 0,
        ],
    ],
];
