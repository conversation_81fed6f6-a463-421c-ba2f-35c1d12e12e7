<?php

return [

    /*
    |--------------------------------------------------------------------------
    | MCP Server Information
    |--------------------------------------------------------------------------
    */
    'server' => [
        'name' => env('MCP_SERVER_NAME', 'TMS Laravel MCP Server'),
        'version' => env('MCP_SERVER_VERSION', '1.0.0'),
    ],

    /*
    |--------------------------------------------------------------------------
    | MCP Discovery Configuration
    |--------------------------------------------------------------------------
    */
    'discovery' => [
        'base_path' => base_path(),
        'directories' => [
            env('MCP_DISCOVERY_PATH', 'app/Mcp'),
        ],
    ],

    /*
    |--------------------------------------------------------------------------
    | MCP Cache Configuration
    |--------------------------------------------------------------------------
    */
    'cache' => [
        'store'  => env('MCP_CACHE_STORE', config('cache.default')),
        'prefix' => env('MCP_CACHE_PREFIX', 'mcp_'),
        'ttl'    => env('MCP_CACHE_TTL', 3600),
    ],

    /*
    |--------------------------------------------------------------------------
    | MCP Transport Configuration
    |--------------------------------------------------------------------------
    */
    'transports' => [
        'http' => [
            'enabled'    => true,
            'prefix'     => 'mcp',        // This will make endpoints like /mcp/tools
            'middleware' => ['web'],
            'domain'     => null,
        ],

        'stdio' => [
            'enabled' => true,
        ],
    ],

    /*
    |--------------------------------------------------------------------------
    | MCP Protocol & Capabilities
    |--------------------------------------------------------------------------
    */
    'pagination_limit' => env('MCP_PAGINATION_LIMIT', 50),

    'capabilities' => [
        'tools' => [
            'enabled'     => true,
            'listChanged' => true,
        ],
        'resources' => [
            'enabled'     => true,
            'subscribe'   => true,
            'listChanged' => true,
        ],
        'prompts' => [
            'enabled'     => true,
            'listChanged' => true,
        ],
        'logging' => [
            'enabled'   => true,
            'setLevel'  => false,
        ],
    ],

    /*
    |--------------------------------------------------------------------------
    | Logging Configuration
    |--------------------------------------------------------------------------
    */
    'logging' => [
        'channel' => env('MCP_LOG_CHANNEL', config('logging.default')),
        'level'   => env('MCP_LOG_LEVEL', 'info'),
    ],

];

