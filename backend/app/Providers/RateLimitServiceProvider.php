<?php

namespace App\Providers;

use Illuminate\Support\ServiceProvider;
use Illuminate\Support\Facades\RateLimiter;
use Illuminate\Http\Request;

class RateLimitServiceProvider extends ServiceProvider
{
    /**
     * Register services.
     */
    public function register(): void
    {
        //
    }

    /**
     * Bootstrap services.
     */
    public function boot(): void
    {
        // Auth endpoints rate limiting
        RateLimiter::for('auth', function (Request $request) {
            return $request->user()
                ? null // No limit for authenticated users
                : \Illuminate\Cache\RateLimiting\Limit::perMinute(5)->by($request->ip()); // 5 attempts per minute for guests
        });

        // Login specific rate limiting (more restrictive)
        RateLimiter::for('login', function (Request $request) {
            $email = $request->input('email');
            $key = $email ? $email . '|' . $request->ip() : $request->ip();
            return \Illuminate\Cache\RateLimiting\Limit::perMinute(5)->by($key);
        });

        // API rate limiting for authenticated users
        RateLimiter::for('api', function (Request $request) {
            return $request->user()
                ? \Illuminate\Cache\RateLimiting\Limit::perMinute(100)->by($request->user()->id) // 100 requests per minute for authenticated users
                : \Illuminate\Cache\RateLimiting\Limit::perMinute(20)->by($request->ip()); // 20 requests per minute for guests
        });

        // Admin API rate limiting (higher limits)
        RateLimiter::for('admin-api', function (Request $request) {
            return $request->user() && $request->user()->isAdmin()
                ? \Illuminate\Cache\RateLimiting\Limit::perMinute(200)->by($request->user()->id) // 200 requests per minute for admins
                : \Illuminate\Cache\RateLimiting\Limit::perMinute(50)->by($request->user()?->id ?? $request->ip()); // 50 for other authenticated users
        });

        // File upload rate limiting
        RateLimiter::for('uploads', function (Request $request) {
            return $request->user()
                ? \Illuminate\Cache\RateLimiting\Limit::perMinute(10)->by($request->user()->id) // 10 uploads per minute
                : \Illuminate\Cache\RateLimiting\Limit::perMinute(2)->by($request->ip()); // 2 uploads per minute for guests
        });

        // Password reset rate limiting
        RateLimiter::for('password-reset', function (Request $request) {
            return \Illuminate\Cache\RateLimiting\Limit::perMinute(3)->by($request->ip()); // 3 password reset attempts per minute
        });
    }
}
