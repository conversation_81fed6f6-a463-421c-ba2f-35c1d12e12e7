<?php

namespace App\Providers;

use App\Events\UnitStatusChanged;
use App\Events\PaymentCompletedEvent;
use App\Listeners\SendUnitStatusChangeNotification;
use App\Listeners\AutomaticReceiptGenerationListener;

// Sync Events and Listeners
use App\Events\Sync\SocietyUpdated;
use App\Events\Sync\UnitUpdated;
use App\Events\Sync\TenantUpdated;
use App\Events\Sync\AgreementUpdated;
use App\Events\Sync\PaymentUpdated;
use App\Events\Sync\EntityDeleted;
use App\Listeners\Sync\HandleSocietyUpdated;
use App\Listeners\Sync\HandleUnitUpdated;
use App\Listeners\Sync\HandleTenantUpdated;
use App\Listeners\Sync\HandleAgreementUpdated;
use App\Listeners\Sync\HandlePaymentUpdated;
use App\Listeners\Sync\HandleEntityDeleted;

use Illuminate\Foundation\Support\Providers\EventServiceProvider as ServiceProvider;

class EventServiceProvider extends ServiceProvider
{
    /**
     * The event to listener mappings for the application.
     *
     * @var array<class-string, array<int, class-string>>
     */
    protected $listen = [
        UnitStatusChanged::class => [
            SendUnitStatusChangeNotification::class,
        ],
        PaymentCompletedEvent::class => [
            AutomaticReceiptGenerationListener::class,
        ],

        // Sync Events
        SocietyUpdated::class => [
            HandleSocietyUpdated::class,
        ],
        UnitUpdated::class => [
            HandleUnitUpdated::class,
        ],
        TenantUpdated::class => [
            HandleTenantUpdated::class,
        ],
        AgreementUpdated::class => [
            HandleAgreementUpdated::class,
        ],
        PaymentUpdated::class => [
            HandlePaymentUpdated::class,
        ],
        EntityDeleted::class => [
            HandleEntityDeleted::class,
        ],
    ];

    /**
     * Register any events for your application.
     */
    public function boot(): void
    {
        // Register sync observers for models
        \App\Models\Society::observe(\App\Observers\SyncObserver::class);
        \App\Models\Unit::observe(\App\Observers\SyncObserver::class);
        \App\Models\Tenant::observe(\App\Observers\SyncObserver::class); // Re-enabled
        \App\Models\Agreement::observe(\App\Observers\SyncObserver::class);
        \App\Models\RentPayment::observe(\App\Observers\SyncObserver::class);
    }

    /**
     * Determine if events and listeners should be automatically discovered.
     */
    public function shouldDiscoverEvents(): bool
    {
        return false;
    }
}