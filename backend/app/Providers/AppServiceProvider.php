<?php

namespace App\Providers;

use Illuminate\Support\ServiceProvider;
use Illuminate\Support\Facades\Gate;
use App\Services\TmsService;
use App\Models\Agreement;
use App\Observers\AgreementObserver;
use App\Policies\AgreementPolicy;

class AppServiceProvider extends ServiceProvider
{
    /**
     * Register any application services.
     */
    public function register(): void
    {
        //
    }

    /**
     * Bootstrap any application services.
     */
    public function boot(): void
    {
        // Register TmsService for MCP auto-discovery
        $this->app->singleton(TmsService::class);
        Agreement::observe(AgreementObserver::class);
        Gate::policy(Agreement::class, AgreementPolicy::class);
    }
}
