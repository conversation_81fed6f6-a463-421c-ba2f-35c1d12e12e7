<?php

namespace App\Notifications;

use App\Events\UnitStatusChanged;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Notifications\Messages\MailMessage;
use Illuminate\Notifications\Notification;

class UnitStatusChangedNotification extends Notification
{
    use Queueable;

    protected UnitStatusChanged $event;

    /**
     * Create a new notification instance.
     */
    public function __construct(UnitStatusChanged $event)
    {
        $this->event = $event;
    }

    /**
     * Get the notification's delivery channels.
     *
     * @return array<int, string>
     */
    public function via(object $notifiable): array
    {
        return ['database'];
    }

    /**
     * Get the mail representation of the notification.
     */
    public function toMail(object $notifiable): MailMessage
    {
        $unit = $this->event->unit;
        $previousStatus = ucfirst(str_replace('-', ' ', $this->event->previousStatus));
        $newStatus = ucfirst(str_replace('-', ' ', $this->event->newStatus));
        $changedBy = $this->event->changedBy?->name ?? 'System';

        return (new MailMessage)
            ->subject("Unit Status Changed - {$unit->unit_number}")
            ->greeting("Hello {$notifiable->name},")
            ->line("The status of unit {$unit->unit_number} has been changed.")
            ->line("Previous Status: {$previousStatus}")
            ->line("New Status: {$newStatus}")
            ->line("Changed By: {$changedBy}")
            ->when($this->event->reason, function ($mail) {
                return $mail->line("Reason: {$this->event->reason}");
            })
            ->line('You can view more details about this unit in the system.')
            ->action('View Unit Details', url("/units/{$unit->id}"))
            ->line('Thank you for using our Tenant Management System!');
    }

    /**
     * Get the array representation of the notification.
     *
     * @return array<string, mixed>
     */
    public function toArray(object $notifiable): array
    {
        return [
            'unit_id' => $this->event->unit->id,
            'unit_number' => $this->event->unit->unit_number,
            'previous_status' => $this->event->previousStatus,
            'new_status' => $this->event->newStatus,
            'changed_by' => $this->event->changedBy?->name ?? 'System',
            'changed_by_id' => $this->event->changedBy?->id,
            'reason' => $this->event->reason,
            'timestamp' => now()->toISOString(),
            'type' => 'unit_status_changed',
            'title' => "Unit {$this->event->unit->unit_number} Status Changed",
            'message' => "Status changed from {$this->event->previousStatus} to {$this->event->newStatus}",
        ];
    }

    /**
     * Get the notification's database type.
     */
    public function databaseType(object $notifiable): string
    {
        return 'unit_status_changed';
    }
} 