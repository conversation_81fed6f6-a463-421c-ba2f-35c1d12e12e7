<?php

namespace App\Notifications;

use App\Models\RentPayment;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Notifications\Messages\MailMessage;
use Illuminate\Notifications\Notification;

class PaymentReceivedNotification extends Notification implements ShouldQueue
{
    use Queueable;

    protected RentPayment $payment;

    /**
     * Create a new notification instance.
     */
    public function __construct(RentPayment $payment)
    {
        $this->payment = $payment;
    }

    /**
     * Get the notification's delivery channels.
     *
     * @return array<int, string>
     */
    public function via(object $notifiable): array
    {
        return ['mail', 'database'];
    }

    /**
     * Get the mail representation of the notification.
     */
    public function toMail(object $notifiable): MailMessage
    {
        $amount = number_format($this->payment->amount, 2);
        $date = $this->payment->payment_date->format('M d, Y');

        return (new MailMessage)
            ->subject('Payment Received - Unit ' . $this->payment->unit->unit_number)
            ->greeting('Hello ' . $notifiable->name . ',')
            ->line('We have received your rent payment.')
            ->line('**Payment Details:**')
            ->line('Amount: $' . $amount)
            ->line('Date: ' . $date)
            ->line('Unit: ' . $this->payment->unit->unit_number)
            ->line('Status: ' . ucfirst($this->payment->status))
            ->action('View Payment Details', url('/payments/' . $this->payment->id))
            ->line('Thank you for your timely payment!');
    }

    /**
     * Get the array representation of the notification.
     *
     * @return array<string, mixed>
     */
    public function toArray(object $notifiable): array
    {
        return [
            'payment_id' => $this->payment->id,
            'amount' => $this->payment->amount,
            'payment_date' => $this->payment->payment_date->toDateString(),
            'unit_number' => $this->payment->unit->unit_number,
            'status' => $this->payment->status,
            'type' => 'payment_received',
        ];
    }
}
