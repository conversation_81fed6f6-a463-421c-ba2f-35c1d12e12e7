<?php

namespace App\Notifications;

use App\Models\NocApplication;
use Illuminate\Bus\Queueable;
use Illuminate\Notifications\Notification;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Notifications\Messages\MailMessage;

class NocApplicationStatusChangedNotification extends Notification
{
    use Queueable;

    protected $application;
    protected $previousStatus;
    protected $newStatus;
    protected $changedBy;
    protected $remarks;

    /**
     * Create a new notification instance.
     */
    public function __construct(NocApplication $application, $previousStatus, $newStatus, $changedBy, $remarks = null)
    {
        $this->application = $application;
        $this->previousStatus = $previousStatus;
        $this->newStatus = $newStatus;
        $this->changedBy = $changedBy;
        $this->remarks = $remarks;
    }

    /**
     * Get the notification's delivery channels.
     *
     * @param  mixed  $notifiable
     * @return array
     */
    public function via($notifiable)
    {
        return ['database'];
    }

    /**
     * Get the array representation of the notification.
     *
     * @param  mixed  $notifiable
     * @return array
     */
    public function toArray($notifiable)
    {
        return [
            'application_id' => $this->application->id,
            'noc_type' => $this->application->noc_type,
            'previous_status' => $this->previousStatus,
            'new_status' => $this->newStatus,
            'changed_by' => $this->changedBy?->name ?? 'System',
            'changed_by_id' => $this->changedBy?->id ?? null,
            'remarks' => $this->remarks,
            'timestamp' => now()->toISOString(),
            'type' => 'noc_application_status_changed',
            'title' => "NOC Application #{$this->application->id} Status Changed",
            'message' => "Status changed from {$this->previousStatus} to {$this->newStatus}" . ($this->remarks ? ". Remarks: {$this->remarks}" : ''),
        ];
    }
} 