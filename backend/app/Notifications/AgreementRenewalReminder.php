<?php

namespace App\Notifications;

use App\Models\Agreement;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Notifications\Messages\MailMessage;
use Illuminate\Notifications\Notification;

class AgreementRenewalReminder extends Notification implements ShouldQueue
{
    use Queueable;

    public $agreement;
    public $recipientType;
    public $daysUntilExpiry;

    /**
     * Create a new notification instance.
     */
    public function __construct(Agreement $agreement, string $recipientType)
    {
        $this->agreement = $agreement;
        $this->recipientType = $recipientType;
        $this->daysUntilExpiry = $agreement->end_date->diffInDays(now());
    }

    /**
     * Get the notification's delivery channels.
     *
     * @return array<int, string>
     */
    public function via(object $notifiable): array
    {
        return ['database', 'mail'];
    }

    /**
     * Get the mail representation of the notification.
     */
    public function toMail(object $notifiable): MailMessage
    {
        $subject = match($this->recipientType) {
            'tenant' => 'Agreement Renewal Reminder - Action Required',
            'owner' => 'Agreement Expiring Soon - Tenant Renewal Notice',
            'admin' => 'Agreement Renewal Alert - Administrative Notice',
            default => 'Agreement Renewal Reminder'
        };

        $message = (new MailMessage)
            ->subject($subject)
            ->greeting('Hello ' . $notifiable->name)
            ->line('This is a reminder about your rental agreement.');

        if ($this->recipientType === 'tenant') {
            $message->line('Your rental agreement for Unit ' . ($this->agreement->unit->unit_number ?? 'Unknown') . ' will expire on ' . $this->agreement->end_date->format('F j, Y') . '.')
                ->line('You have ' . $this->daysUntilExpiry . ' day(s) remaining on your current agreement.')
                ->action('Review Agreement', url('/agreements/' . $this->agreement->id . '/renew'))
                ->line('Please contact the property management office to discuss renewal options.');
        } else {
            $message->line('The rental agreement for Unit ' . ($this->agreement->unit->unit_number ?? 'Unknown') . ' will expire on ' . $this->agreement->end_date->format('F j, Y') . '.')
                ->line('The tenant has ' . $this->daysUntilExpiry . ' day(s) remaining on their current agreement.')
                ->action('View Agreement Details', url('/agreements/' . $this->agreement->id));
        }

        return $message;
    }

    /**
     * Get the array representation of the notification.
     *
     * @return array<string, mixed>
     */
    public function toArray(object $notifiable): array
    {
        return [
            'type' => 'agreement_renewal_reminder',
            'agreement_id' => $this->agreement->id,
            'recipient_type' => $this->recipientType,
            'days_until_expiry' => $this->daysUntilExpiry,
            'tenant_name' => $this->agreement->tenant->name ?? 'Unknown',
            'unit_number' => $this->agreement->unit->unit_number ?? 'Unknown',
            'end_date' => $this->agreement->end_date->format('Y-m-d'),
            'message' => $this->getNotificationMessage(),
            'action_url' => '/agreements/' . $this->agreement->id . '/renew',
        ];
    }

    /**
     * Get the notification message based on recipient type
     */
    private function getNotificationMessage(): string
    {
        return match($this->recipientType) {
            'tenant' => "Your rental agreement for Unit {$this->agreement->unit->unit_number} expires in {$this->daysUntilExpiry} day(s). Please review renewal options.",
            'owner' => "Tenant agreement for Unit {$this->agreement->unit->unit_number} expires in {$this->daysUntilExpiry} day(s).",
            'admin' => "Agreement #{$this->agreement->id} expires in {$this->daysUntilExpiry} day(s). Renewal reminders sent.",
            default => "Agreement renewal reminder for Unit {$this->agreement->unit->unit_number}."
        };
    }
} 