<?php

namespace App\Notifications;

use App\Models\Unit;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Notifications\Messages\MailMessage;
use Illuminate\Notifications\Notification;

class PaymentOverdueNotification extends Notification implements ShouldQueue
{
    use Queueable;

    protected Unit $unit;
    protected int $daysOverdue;
    protected float $amountDue;

    /**
     * Create a new notification instance.
     */
    public function __construct(Unit $unit, int $daysOverdue, float $amountDue)
    {
        $this->unit = $unit;
        $this->daysOverdue = $daysOverdue;
        $this->amountDue = $amountDue;
    }

    /**
     * Get the notification's delivery channels.
     *
     * @return array<int, string>
     */
    public function via(object $notifiable): array
    {
        return ['mail', 'database'];
    }

    /**
     * Get the mail representation of the notification.
     */
    public function toMail(object $notifiable): MailMessage
    {
        $amount = number_format($this->amountDue, 2);

        return (new MailMessage)
            ->subject('Payment Overdue - Unit ' . $this->unit->unit_number)
            ->greeting('Hello ' . $notifiable->name . ',')
            ->line('This is a reminder that your rent payment is overdue.')
            ->line('**Payment Details:**')
            ->line('Amount Due: $' . $amount)
            ->line('Unit: ' . $this->unit->unit_number)
            ->line('Days Overdue: ' . $this->daysOverdue)
            ->action('Make Payment', url('/payments/make-payment'))
            ->line('Please make your payment as soon as possible to avoid any late fees.');
    }

    /**
     * Get the array representation of the notification.
     *
     * @return array<string, mixed>
     */
    public function toArray(object $notifiable): array
    {
        return [
            'unit_id' => $this->unit->id,
            'unit_number' => $this->unit->unit_number,
            'days_overdue' => $this->daysOverdue,
            'amount_due' => $this->amountDue,
            'type' => 'payment_overdue',
        ];
    }
}
