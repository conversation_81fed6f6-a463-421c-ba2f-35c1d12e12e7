<?php

namespace App\Notifications;

use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Notifications\Messages\MailMessage;
use Illuminate\Notifications\Notification;

class PaymentReconciliationFailedNotification extends Notification implements ShouldQueue
{
    use Queueable;

    protected string $error;
    protected array $context;

    /**
     * Create a new notification instance.
     */
    public function __construct(string $error, array $context = [])
    {
        $this->error = $error;
        $this->context = $context;
    }

    /**
     * Get the notification's delivery channels.
     *
     * @return array<int, string>
     */
    public function via(object $notifiable): array
    {
        return ['mail', 'database'];
    }

    /**
     * Get the mail representation of the notification.
     */
    public function toMail(object $notifiable): MailMessage
    {
        return (new MailMessage)
            ->subject('Payment Reconciliation Failed')
            ->greeting('Hello ' . $notifiable->name . ',')
            ->line('The payment reconciliation process has encountered an error.')
            ->line('**Error Details:**')
            ->line('Error: ' . $this->error)
            ->line('Time: ' . now()->format('M d, Y H:i:s'))
            ->action('View System Logs', url('/admin/logs'))
            ->line('Please check the system logs for more details.');
    }

    /**
     * Get the array representation of the notification.
     *
     * @return array<string, mixed>
     */
    public function toArray(object $notifiable): array
    {
        return [
            'error' => $this->error,
            'context' => $this->context,
            'timestamp' => now()->toISOString(),
            'type' => 'payment_reconciliation_failed',
        ];
    }
}
