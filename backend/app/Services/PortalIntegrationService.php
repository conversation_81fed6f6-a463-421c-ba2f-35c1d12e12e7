<?php

namespace App\Services;

use App\Models\PropertyListing;
use App\Models\ListingPortal;
use App\Models\PropertyLead;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;
use Exception;

class PortalIntegrationService
{
    protected array $supportedPortals = [
        '99acres',
        'magicbricks', 
        'housing',
        'olx',
        'nobroker'
    ];

    /**
     * Sync property listing to specified portals.
     */
    public function syncToPortals(PropertyListing $listing, array $portals = null): array
    {
        $portals = $portals ?? $this->supportedPortals;
        $results = [];

        foreach ($portals as $portalName) {
            try {
                $result = $this->syncToPortal($listing, $portalName);
                $results[$portalName] = $result;
                
                // Update portal mapping
                $this->updatePortalMapping($listing, $portalName, $result);
                
            } catch (Exception $e) {
                Log::error("Portal sync failed for {$portalName}", [
                    'listing_id' => $listing->id,
                    'error' => $e->getMessage()
                ]);
                
                $results[$portalName] = [
                    'success' => false,
                    'error' => $e->getMessage(),
                    'portal_listing_id' => null
                ];
            }
        }

        // Update last sync timestamp
        $listing->update(['last_synced_at' => now()]);

        return $results;
    }

    /**
     * Sync to a specific portal.
     */
    protected function syncToPortal(PropertyListing $listing, string $portalName): array
    {
        $portal = ListingPortal::where('name', $portalName)
            ->where('is_active', true)
            ->first();

        if (!$portal) {
            throw new Exception("Portal {$portalName} is not configured or inactive");
        }

        // Get portal-specific data transformation
        $portalData = $this->transformDataForPortal($listing, $portal);
        
        // Check if listing already exists on portal
        $existingMapping = $listing->portal_mappings[$portalName] ?? null;
        
        if ($existingMapping && isset($existingMapping['listing_id'])) {
            // Update existing listing
            return $this->updatePortalListing($portal, $existingMapping['listing_id'], $portalData);
        } else {
            // Create new listing
            return $this->createPortalListing($portal, $portalData);
        }
    }

    /**
     * Transform listing data for specific portal.
     */
    protected function transformDataForPortal(PropertyListing $listing, ListingPortal $portal): array
    {
        $unit = $listing->unit;
        $fieldMapping = $portal->field_mapping ?? [];
        
        // Base data structure
        $data = [
            'title' => $listing->title,
            'description' => $listing->description,
            'rent' => $listing->rent_amount,
            'deposit' => $listing->deposit_amount,
            'property_type' => $unit->unit_type,
            'bedrooms' => $unit->bedrooms,
            'bathrooms' => $unit->bathrooms,
            'area' => $unit->area_sqft,
            'amenities' => $listing->amenities ?? [],
            'preferences' => $listing->preferences ?? [],
            'photos' => $listing->media_urls ?? [],
            'contact_name' => $unit->owner->name,
            'contact_phone' => $unit->owner->phone,
            'contact_email' => $unit->owner->email,
            'address' => $this->formatAddress($unit),
        ];

        // Apply portal-specific field mapping
        $transformedData = [];
        foreach ($fieldMapping as $portalField => $tmsField) {
            if (isset($data[$tmsField])) {
                $transformedData[$portalField] = $data[$tmsField];
            }
        }

        // Add portal-specific data if available
        $portalSpecificData = $listing->portal_specific_data[$portal->name] ?? [];
        $transformedData = array_merge($transformedData, $portalSpecificData);

        return $transformedData;
    }

    /**
     * Create new listing on portal.
     */
    protected function createPortalListing(ListingPortal $portal, array $data): array
    {
        $response = Http::withHeaders([
            'Authorization' => 'Bearer ' . $portal->api_key,
            'Content-Type' => 'application/json',
        ])->post($portal->base_url . '/listings', $data);

        if ($response->successful()) {
            $responseData = $response->json();
            return [
                'success' => true,
                'portal_listing_id' => $responseData['listing_id'] ?? $responseData['id'],
                'portal_url' => $responseData['listing_url'] ?? null,
                'message' => 'Listing created successfully',
                'response_data' => $responseData
            ];
        }

        throw new Exception("Failed to create listing: " . $response->body());
    }

    /**
     * Update existing listing on portal.
     */
    protected function updatePortalListing(ListingPortal $portal, string $portalListingId, array $data): array
    {
        $response = Http::withHeaders([
            'Authorization' => 'Bearer ' . $portal->api_key,
            'Content-Type' => 'application/json',
        ])->put($portal->base_url . "/listings/{$portalListingId}", $data);

        if ($response->successful()) {
            $responseData = $response->json();
            return [
                'success' => true,
                'portal_listing_id' => $portalListingId,
                'portal_url' => $responseData['listing_url'] ?? null,
                'message' => 'Listing updated successfully',
                'response_data' => $responseData
            ];
        }

        throw new Exception("Failed to update listing: " . $response->body());
    }

    /**
     * Update portal mapping in listing.
     */
    protected function updatePortalMapping(PropertyListing $listing, string $portalName, array $result): void
    {
        $portalMappings = $listing->portal_mappings ?? [];
        
        $portalMappings[$portalName] = [
            'listing_id' => $result['portal_listing_id'] ?? null,
            'portal_url' => $result['portal_url'] ?? null,
            'status' => $result['success'] ? 'synced' : 'failed',
            'last_sync' => now()->toISOString(),
            'error' => $result['error'] ?? null,
            'response_data' => $result['response_data'] ?? null,
        ];

        $listing->update(['portal_mappings' => $portalMappings]);
    }

    /**
     * Process webhook from portal.
     */
    public function processWebhook(string $portalName, array $webhookData): array
    {
        try {
            $eventType = $webhookData['event_type'] ?? 'unknown';
            
            switch ($eventType) {
                case 'lead_received':
                    return $this->processLeadWebhook($portalName, $webhookData);
                    
                case 'listing_updated':
                    return $this->processListingUpdateWebhook($portalName, $webhookData);
                    
                case 'listing_expired':
                    return $this->processListingExpiredWebhook($portalName, $webhookData);
                    
                default:
                    Log::warning("Unknown webhook event type: {$eventType}", [
                        'portal' => $portalName,
                        'data' => $webhookData
                    ]);
                    return ['success' => true, 'message' => 'Webhook received but not processed'];
            }
            
        } catch (Exception $e) {
            Log::error("Webhook processing failed", [
                'portal' => $portalName,
                'error' => $e->getMessage(),
                'data' => $webhookData
            ]);
            
            return ['success' => false, 'error' => $e->getMessage()];
        }
    }

    /**
     * Process lead webhook from portal.
     */
    protected function processLeadWebhook(string $portalName, array $webhookData): array
    {
        $leadData = $webhookData['lead_data'] ?? [];
        $portalListingId = $webhookData['listing_id'] ?? null;

        // Find the property listing
        $listing = PropertyListing::whereJsonContains(
            "portal_mappings->{$portalName}->listing_id", 
            $portalListingId
        )->first();

        if (!$listing) {
            throw new Exception("Property listing not found for portal listing ID: {$portalListingId}");
        }

        // Create property lead
        $lead = PropertyLead::create([
            'property_listing_id' => $listing->id,
            'portal_name' => $portalName,
            'portal_lead_id' => $leadData['lead_id'] ?? null,
            'enquirer_name' => $leadData['name'] ?? 'Unknown',
            'enquirer_email' => $leadData['email'] ?? null,
            'enquirer_phone' => $leadData['phone'] ?? null,
            'message' => $leadData['message'] ?? '',
            'status' => 'new',
            'priority' => $this->determinePriority($leadData),
            'response_data' => $leadData,
        ]);

        // Update listing inquiry count
        $listing->increment('inquiry_count');

        // Send notification to owner
        // TODO: Implement notification service

        return [
            'success' => true,
            'message' => 'Lead processed successfully',
            'lead_id' => $lead->id
        ];
    }

    /**
     * Determine lead priority based on data.
     */
    protected function determinePriority(array $leadData): string
    {
        // Simple priority logic - can be enhanced
        if (isset($leadData['phone']) && isset($leadData['email'])) {
            return 'high';
        } elseif (isset($leadData['phone']) || isset($leadData['email'])) {
            return 'medium';
        }
        
        return 'low';
    }

    /**
     * Format unit address for portal.
     */
    protected function formatAddress($unit): string
    {
        return "{$unit->unit_number}, Block {$unit->block}, Floor {$unit->floor}";
    }

    /**
     * Get sync status for all portals.
     */
    public function getSyncStatus(PropertyListing $listing): array
    {
        $status = [];
        
        foreach ($this->supportedPortals as $portal) {
            $mapping = $listing->portal_mappings[$portal] ?? null;
            $status[$portal] = [
                'synced' => !is_null($mapping),
                'portal_listing_id' => $mapping['listing_id'] ?? null,
                'last_sync' => $mapping['last_sync'] ?? null,
                'status' => $mapping['status'] ?? 'not_synced',
                'error' => $mapping['error'] ?? null,
            ];
        }
        
        return $status;
    }

    /**
     * Remove listing from portal.
     */
    public function removeFromPortal(PropertyListing $listing, string $portalName): array
    {
        $portal = ListingPortal::where('name', $portalName)->first();
        $mapping = $listing->portal_mappings[$portalName] ?? null;

        if (!$portal || !$mapping || !isset($mapping['listing_id'])) {
            return ['success' => false, 'error' => 'Listing not found on portal'];
        }

        try {
            $response = Http::withHeaders([
                'Authorization' => 'Bearer ' . $portal->api_key,
            ])->delete($portal->base_url . "/listings/{$mapping['listing_id']}");

            if ($response->successful()) {
                // Remove portal mapping
                $portalMappings = $listing->portal_mappings;
                unset($portalMappings[$portalName]);
                $listing->update(['portal_mappings' => $portalMappings]);

                return ['success' => true, 'message' => 'Listing removed from portal'];
            }

            throw new Exception("Failed to remove listing: " . $response->body());
            
        } catch (Exception $e) {
            Log::error("Failed to remove listing from portal", [
                'listing_id' => $listing->id,
                'portal' => $portalName,
                'error' => $e->getMessage()
            ]);
            
            return ['success' => false, 'error' => $e->getMessage()];
        }
    }
}
