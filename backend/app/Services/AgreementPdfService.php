<?php

namespace App\Services;

use App\Models\Agreement;
use Barryvdh\DomPDF\Facade\Pdf;

class AgreementPdfService
{
    /**
     * Generate a PDF for the given Agreement.
     *
     * @param  Agreement  $agreement
     * @return \Barryvdh\DomPDF\PDF
     */
    public function generate(Agreement $agreement): \Barryvdh\DomPDF\PDF
    {
        // Prepare data for the view
        $data = [
            'agreement' => $agreement,
        ];

        // Load the Blade view 'agreements.pdf' and render as PDF
        $pdf = Pdf::loadView('agreements.pdf', $data)
            ->setPaper('a4', 'portrait');

        return $pdf;
    }
} 