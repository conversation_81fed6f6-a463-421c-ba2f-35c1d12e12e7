<?php

namespace App\Services;

use App\Models\RentReceipt;
use App\Models\RentPayment;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Facades\View;
use Barryvdh\DomPDF\Facade\Pdf;
use SimpleSoftwareIO\QrCode\Facades\QrCode;

class ReceiptGenerationService
{
    /**
     * Generate PDF receipt for a payment.
     *
     * @param RentPayment $payment
     * @param string $recipientEmail
     * @return RentReceipt
     */
    public function generateReceipt(RentPayment $payment, string $recipientEmail): RentReceipt
    {
        // Create receipt record
        $receipt = RentReceipt::create([
            'payment_id' => $payment->id,
            'generated_at' => now(),
            'recipient_email' => $recipientEmail,
            'delivery_status' => 'pending',
        ]);

        // Generate QR code
        $qrCodePath = $this->generateQrCode($receipt);
        $receipt->update(['qr_code_path' => $qrCodePath]);

        // Generate PDF
        $pdfPath = $this->generatePdf($receipt);
        $receipt->update(['pdf_path' => $pdfPath]);

        return $receipt->fresh();
    }

    /**
     * Generate PDF from receipt template.
     *
     * @param RentReceipt $receipt
     * @return string
     */
    protected function generatePdf(RentReceipt $receipt): string
    {
        // Load the receipt with relationships
        $receipt->load(['payment.tenant', 'payment.unit.property']);

        // Generate PDF using DomPDF
        $pdf = Pdf::loadView('receipts.receipt', [
            'receipt' => $receipt
        ]);

        // Set PDF options
        $pdf->setPaper('A4', 'portrait');
        $pdf->setOptions([
            'isHtml5ParserEnabled' => true,
            'isRemoteEnabled' => true,
            'defaultFont' => 'DejaVu Sans',
        ]);

        // Generate filename
        $filename = 'receipts/' . $receipt->receipt_number . '.pdf';

        // Store PDF
        Storage::disk('public')->put($filename, $pdf->output());

        return $filename;
    }

    /**
     * Generate QR code for receipt verification.
     *
     * @param RentReceipt $receipt
     * @return string
     */
    protected function generateQrCode(RentReceipt $receipt): string
    {
        // Create verification URL
        $verificationUrl = route('receipts.verify', [
            'code' => $receipt->verification_code
        ]);

        // Generate QR code
        $qrCode = QrCode::format('png')
            ->size(200)
            ->margin(10)
            ->generate($verificationUrl);

        // Store QR code
        $filename = 'receipts/qr/' . $receipt->receipt_number . '.png';
        Storage::disk('public')->put($filename, $qrCode);

        return $filename;
    }

    /**
     * Generate receipt with custom template.
     *
     * @param RentPayment $payment
     * @param string $recipientEmail
     * @param string $template
     * @return RentReceipt
     */
    public function generateReceiptWithTemplate(RentPayment $payment, string $recipientEmail, string $template = 'receipts.receipt'): RentReceipt
    {
        // Create receipt record
        $receipt = RentReceipt::create([
            'payment_id' => $payment->id,
            'generated_at' => now(),
            'recipient_email' => $recipientEmail,
            'delivery_status' => 'pending',
        ]);

        // Generate QR code
        $qrCodePath = $this->generateQrCode($receipt);
        $receipt->update(['qr_code_path' => $qrCodePath]);

        // Generate PDF with custom template
        $pdfPath = $this->generatePdfWithTemplate($receipt, $template);
        $receipt->update(['pdf_path' => $pdfPath]);

        return $receipt->fresh();
    }

    /**
     * Generate PDF with custom template.
     *
     * @param RentReceipt $receipt
     * @param string $template
     * @return string
     */
    protected function generatePdfWithTemplate(RentReceipt $receipt, string $template): string
    {
        // Load the receipt with relationships
        $receipt->load(['payment.tenant', 'payment.unit.property']);

        // Generate PDF using custom template
        $pdf = Pdf::loadView($template, [
            'receipt' => $receipt
        ]);

        // Set PDF options
        $pdf->setPaper('A4', 'portrait');
        $pdf->setOptions([
            'isHtml5ParserEnabled' => true,
            'isRemoteEnabled' => true,
            'defaultFont' => 'DejaVu Sans',
        ]);

        // Generate filename
        $filename = 'receipts/' . $receipt->receipt_number . '.pdf';

        // Store PDF
        Storage::disk('public')->put($filename, $pdf->output());

        return $filename;
    }

    /**
     * Regenerate PDF for existing receipt.
     *
     * @param RentReceipt $receipt
     * @return bool
     */
    public function regeneratePdf(RentReceipt $receipt): bool
    {
        try {
            // Generate new PDF
            $pdfPath = $this->generatePdf($receipt);
            $receipt->update(['pdf_path' => $pdfPath]);

            return true;
        } catch (\Exception $e) {
            \Log::error('Failed to regenerate PDF for receipt: ' . $receipt->id, [
                'error' => $e->getMessage()
            ]);

            return false;
        }
    }

    /**
     * Add watermark to PDF.
     *
     * @param string $pdfContent
     * @param string $watermarkText
     * @return string
     */
    protected function addWatermark(string $pdfContent, string $watermarkText = 'PAID'): string
    {
        // This is a placeholder for watermark functionality
        // In a real implementation, you would use a PDF library like FPDF or TCPDF
        // to add watermarks to the PDF content
        
        return $pdfContent;
    }

    /**
     * Add digital signature to PDF.
     *
     * @param string $pdfContent
     * @param string $signaturePath
     * @return string
     */
    protected function addDigitalSignature(string $pdfContent, string $signaturePath): string
    {
        // This is a placeholder for digital signature functionality
        // In a real implementation, you would use a PDF library
        // to add digital signatures to the PDF content
        
        return $pdfContent;
    }

    /**
     * Get receipt PDF content.
     *
     * @param RentReceipt $receipt
     * @return string|null
     */
    public function getPdfContent(RentReceipt $receipt): ?string
    {
        if (!$receipt->pdf_path) {
            return null;
        }

        return Storage::disk('public')->get($receipt->pdf_path);
    }

    /**
     * Delete receipt files.
     *
     * @param RentReceipt $receipt
     * @return bool
     */
    public function deleteReceiptFiles(RentReceipt $receipt): bool
    {
        try {
            // Delete PDF file
            if ($receipt->pdf_path) {
                Storage::disk('public')->delete($receipt->pdf_path);
            }

            // Delete QR code file
            if ($receipt->qr_code_path) {
                Storage::disk('public')->delete($receipt->qr_code_path);
            }

            return true;
        } catch (\Exception $e) {
            \Log::error('Failed to delete receipt files: ' . $receipt->id, [
                'error' => $e->getMessage()
            ]);

            return false;
        }
    }
} 