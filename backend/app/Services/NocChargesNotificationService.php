<?php

namespace App\Services;

use App\Events\NocChargesNotificationEvent;
use App\Models\NocApplication;
use App\Models\Unit;
use App\Models\Tenant;
use Illuminate\Support\Facades\Log;

class NocChargesNotificationService
{
    /**
     * Send NOC charges notification to onesociety application
     */
    public function sendNocChargesNotification(NocApplication $nocApplication, string $action = 'apply_charges'): void
    {
        try {
            // Prepare the NOC data for onesociety
            $nocData = $this->prepareNocData($nocApplication);
            
            // Dispatch the event
            event(new NocChargesNotificationEvent($nocData, $action));
            
            Log::info('NOC charges notification sent to onesociety', [
                'noc_id' => $nocApplication->id,
                'action' => $action,
                'unit_id' => $nocApplication->unit_id,
                'tenant_id' => $nocApplication->applicant_id,
            ]);
            
        } catch (\Exception $e) {
            Log::error('Failed to send NOC charges notification to onesociety', [
                'noc_id' => $nocApplication->id,
                'error' => $e->getMessage(),
            ]);
            
            throw $e;
        }
    }

    /**
     * Send notification for NOC approval (trigger charges)
     */
    public function notifyNocApproved(NocApplication $nocApplication): void
    {
        $this->sendNocChargesNotification($nocApplication, 'apply_charges');
    }

    /**
     * Send notification for NOC rejection (cancel charges)
     */
    public function notifyNocRejected(NocApplication $nocApplication): void
    {
        $this->sendNocChargesNotification($nocApplication, 'cancel_charges');
    }

    /**
     * Send notification for NOC renewal
     */
    public function notifyNocRenewal(NocApplication $nocApplication): void
    {
        $this->sendNocChargesNotification($nocApplication, 'renew_charges');
    }

    /**
     * Prepare NOC data for onesociety application
     */
    private function prepareNocData(NocApplication $nocApplication): array
    {
        $unit = $nocApplication->unit;
        $tenant = $nocApplication->applicant;
        
        return [
            'noc_id' => $nocApplication->id,
            'noc_type' => $nocApplication->noc_type,
            'status' => $nocApplication->status,
            'unit' => [
                'id' => $unit->id,
                'unit_number' => $unit->unit_number,
                'block' => $unit->block,
                'floor' => $unit->floor,
                'type' => $unit->type,
                'status' => $unit->status,
            ],
            'tenant' => [
                'id' => $tenant->id,
                'name' => $tenant->full_name,
                'email' => $tenant->email,
                'phone' => $tenant->phone,
            ],
            'application_data' => [
                'purpose' => $nocApplication->purpose,
                'start_date' => $nocApplication->start_date,
                'end_date' => $nocApplication->end_date,
                'form_data' => $nocApplication->form_data,
                'created_at' => $nocApplication->created_at->toISOString(),
                'updated_at' => $nocApplication->updated_at->toISOString(),
            ],
            'billing_info' => [
                'charge_type' => 'noc_charges',
                'charge_category' => $this->getChargeCategory($nocApplication->noc_type),
                'billing_period' => $this->calculateBillingPeriod($nocApplication),
                'amount' => $this->calculateChargeAmount($nocApplication),
            ],
        ];
    }

    /**
     * Get charge category based on NOC type
     */
    private function getChargeCategory(string $nocType): string
    {
        return match ($nocType) {
            'rental' => 'rental_noc_charges',
            'residence' => 'residence_noc_charges',
            'vehicle' => 'vehicle_noc_charges',
            'renovation' => 'renovation_noc_charges',
            'transfer' => 'transfer_noc_charges',
            default => 'general_noc_charges',
        };
    }

    /**
     * Calculate billing period for NOC
     */
    private function calculateBillingPeriod(NocApplication $nocApplication): array
    {
        $startDate = $nocApplication->start_date;
        $endDate = $nocApplication->end_date;
        
        return [
            'start_date' => $startDate->toDateString(),
            'end_date' => $endDate->toDateString(),
            'duration_days' => $startDate->diffInDays($endDate),
            'duration_months' => $startDate->diffInMonths($endDate),
        ];
    }

    /**
     * Calculate charge amount based on NOC type and duration
     */
    private function calculateChargeAmount(NocApplication $nocApplication): float
    {
        // This is a placeholder calculation
        // The actual calculation should be based on onesociety's billing rules
        $durationMonths = $nocApplication->start_date->diffInMonths($nocApplication->end_date);
        
        return match ($nocApplication->noc_type) {
            'rental' => $durationMonths * 500, // Example: 500 per month for rental
            'residence' => $durationMonths * 300, // Example: 300 per month for residence
            'vehicle' => $durationMonths * 200, // Example: 200 per month for vehicle
            'renovation' => $durationMonths * 400, // Example: 400 per month for renovation
            'transfer' => $durationMonths * 600, // Example: 600 per month for transfer
            default => $durationMonths * 250, // Default charge
        };
    }
} 