<?php

namespace App\Services;

use App\Models\Notification;
use App\Models\NotificationPreference;
use App\Models\NotificationTemplate;
use App\Models\User;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Mail;
use Illuminate\Support\Facades\Notification as NotificationFacade;
use Illuminate\Support\Str;
use Illuminate\Support\Facades\Storage;

class NotificationService
{
    protected $config;

    public function __construct()
    {
        $this->config = config('notifications', [
            'default_channels' => ['database'],
            'enable_email' => true,
            'enable_sms' => false,
            'enable_push' => false,
            'enable_in_app' => true,
            'queue_notifications' => true,
            'retry_failed' => true,
            'max_retries' => 3,
        ]);
    }

    /**
     * Send notification to a user
     */
    public function sendNotification(
        User $user,
        string $type,
        string $title,
        string $message,
        array $data = [],
        array $channels = null,
        string $priority = 'normal',
        bool $isScheduled = false,
        \DateTime $scheduledAt = null
    ): Notification {
        // Get user's notification preferences
        $preferences = $this->getUserNotificationPreferences($user, $type);
        
        // Determine channels to use
        $channels = $channels ?? $this->getChannelsForUser($user, $type, $preferences);
        
        // Create notification record
        $notification = $this->createNotificationRecord(
            $user,
            $type,
            $title,
            $message,
            $data,
            $channels,
            $priority,
            $isScheduled,
            $scheduledAt
        );

        // Send through each channel
        foreach ($channels as $channel) {
            $this->sendThroughChannel($notification, $channel, $preferences);
        }

        return $notification;
    }

    /**
     * Send notification to multiple users
     */
    public function sendBulkNotification(
        array $users,
        string $type,
        string $title,
        string $message,
        array $data = [],
        array $channels = null,
        string $priority = 'normal'
    ): array {
        $results = [
            'total_users' => count($users),
            'successful' => 0,
            'failed' => 0,
            'errors' => []
        ];

        foreach ($users as $user) {
            try {
                $this->sendNotification($user, $type, $title, $message, $data, $channels, $priority);
                $results['successful']++;
            } catch (\Exception $e) {
                $results['failed']++;
                $results['errors'][] = [
                    'user_id' => $user->id,
                    'error' => $e->getMessage()
                ];
                Log::error('Failed to send notification to user', [
                    'user_id' => $user->id,
                    'error' => $e->getMessage()
                ]);
            }
        }

        return $results;
    }

    /**
     * Send notification using template
     */
    public function sendTemplateNotification(
        User $user,
        string $templateName,
        array $variables = [],
        array $channels = null,
        string $priority = 'normal'
    ): Notification {
        $template = NotificationTemplate::where('name', $templateName)
            ->where('is_active', true)
            ->first();

        if (!$template) {
            throw new \Exception("Notification template '{$templateName}' not found or inactive");
        }

        // Replace variables in template
        $title = $this->replaceTemplateVariables($template->title, $variables);
        $message = $this->replaceTemplateVariables($template->content, $variables);

        // Determine channels
        $channels = $channels ?? $template->channels ?? ['database'];

        return $this->sendNotification(
            $user,
            $template->type,
            $title,
            $message,
            $variables,
            $channels,
            $priority
        );
    }

    /**
     * Send scheduled notifications
     */
    public function sendScheduledNotifications(): array
    {
        $scheduledNotifications = Notification::where('is_scheduled', true)
            ->where('scheduled_at', '<=', now())
            ->where('status', 'pending')
            ->get();

        $results = [
            'total' => $scheduledNotifications->count(),
            'sent' => 0,
            'failed' => 0,
            'errors' => []
        ];

        foreach ($scheduledNotifications as $notification) {
            try {
                $this->processScheduledNotification($notification);
                $results['sent']++;
            } catch (\Exception $e) {
                $results['failed']++;
                $results['errors'][] = [
                    'notification_id' => $notification->id,
                    'error' => $e->getMessage()
                ];
                Log::error('Failed to send scheduled notification', [
                    'notification_id' => $notification->id,
                    'error' => $e->getMessage()
                ]);
            }
        }

        return $results;
    }

    /**
     * Retry failed notifications
     */
    public function retryFailedNotifications(): array
    {
        $failedNotifications = Notification::where('status', 'failed')
            ->where('delivery_log', 'like', '%retry_count%')
            ->get();

        $results = [
            'total' => $failedNotifications->count(),
            'retried' => 0,
            'still_failed' => 0,
            'errors' => []
        ];

        foreach ($failedNotifications as $notification) {
            try {
                $this->retryNotification($notification);
                $results['retried']++;
            } catch (\Exception $e) {
                $results['still_failed']++;
                $results['errors'][] = [
                    'notification_id' => $notification->id,
                    'error' => $e->getMessage()
                ];
            }
        }

        return $results;
    }

    /**
     * Send receipt generated notification
     */
    public function sendReceiptGeneratedNotification($receipt): void
    {
        try {
            $payment = $receipt->payment;
            $tenant = $payment->tenant;

            $title = 'Receipt Generated';
            $message = "Your rent receipt for payment of {$payment->amount} has been generated. Receipt number: {$receipt->receipt_number}";

            $this->sendNotification(
                $tenant,
                'receipt_generated',
                $title,
                $message,
                [
                    'receipt_id' => $receipt->id,
                    'receipt_number' => $receipt->receipt_number,
                    'payment_id' => $payment->id,
                    'amount' => $payment->amount,
                    'payment_date' => $payment->payment_date,
                    'download_url' => route('receipts.download', $receipt->id),
                ],
                ['email', 'database'],
                'normal'
            );

            Log::info('Receipt generated notification sent', [
                'receipt_id' => $receipt->id,
                'tenant_id' => $tenant->id,
            ]);

        } catch (\Exception $e) {
            Log::error('Failed to send receipt generated notification', [
                'receipt_id' => $receipt->id,
                'error' => $e->getMessage(),
            ]);
        }
    }

    /**
     * Send receipt notification with attachment
     */
    public function sendReceiptNotification($receipt, string $recipientEmail = null): void
    {
        try {
            $payment = $receipt->payment;
            $tenant = $payment->tenant;
            $email = $recipientEmail ?? $receipt->recipient_email ?? $tenant->email;

            $title = 'Rent Receipt - ' . $receipt->receipt_number;
            $message = "Please find attached your rent receipt for payment of {$payment->amount}.";

            // Get PDF content
            $pdfContent = Storage::disk('public')->get($receipt->pdf_path);
            
            if (!$pdfContent) {
                throw new \Exception('Receipt PDF not found');
            }

            // Send email with attachment
            Mail::send('emails.receipt', [
                'receipt' => $receipt,
                'payment' => $payment,
                'tenant' => $tenant,
            ], function ($message) use ($email, $title, $receipt, $pdfContent) {
                $message->to($email)
                    ->subject($title)
                    ->attachData($pdfContent, $receipt->receipt_number . '.pdf', [
                        'mime' => 'application/pdf',
                    ]);
            });

            // Update delivery status
            $receipt->update(['delivery_status' => 'delivered']);

            // Log delivery
            $receipt->auditLogs()->create([
                'action' => 'receipt_delivered',
                'description' => 'Receipt delivered via email',
                'user_id' => null,
                'metadata' => [
                    'recipient_email' => $email,
                    'delivery_method' => 'email',
                ],
            ]);

            Log::info('Receipt delivered via email', [
                'receipt_id' => $receipt->id,
                'recipient_email' => $email,
            ]);

        } catch (\Exception $e) {
            // Update delivery status to failed
            $receipt->update(['delivery_status' => 'failed']);

            Log::error('Failed to deliver receipt via email', [
                'receipt_id' => $receipt->id,
                'error' => $e->getMessage(),
            ]);

            throw $e;
        }
    }

    /**
     * Get user notification preferences
     */
    private function getUserNotificationPreferences(User $user, string $type): array
    {
        $preferences = NotificationPreference::where('user_id', $user->id)
            ->where('notification_type', $type)
            ->where('enabled', true)
            ->get()
            ->keyBy('channel')
            ->toArray();

        return $preferences;
    }

    /**
     * Get channels for user based on preferences
     */
    private function getChannelsForUser(User $user, string $type, array $preferences): array
    {
        $channels = [];

        // Check user preferences
        foreach ($preferences as $channel => $preference) {
            if ($preference['enabled'] && $this->isChannelEnabled($channel)) {
                $channels[] = $channel;
            }
        }

        // If no preferences found, use defaults
        if (empty($channels)) {
            $channels = $this->config['default_channels'];
        }

        return array_unique($channels);
    }

    /**
     * Check if channel is enabled
     */
    private function isChannelEnabled(string $channel): bool
    {
        return match($channel) {
            'email' => $this->config['enable_email'],
            'sms' => $this->config['enable_sms'],
            'push' => $this->config['enable_push'],
            'in_app' => $this->config['enable_in_app'],
            'database' => true,
            default => false
        };
    }

    /**
     * Create notification record
     */
    private function createNotificationRecord(
        User $user,
        string $type,
        string $title,
        string $message,
        array $data,
        array $channels,
        string $priority,
        bool $isScheduled,
        \DateTime $scheduledAt = null
    ): Notification {
        return Notification::create([
            'user_id' => $user->id,
            'type' => $type,
            'title' => $title,
            'message' => $message,
            'data' => $data,
            'channel' => implode(',', $channels),
            'status' => $isScheduled ? 'pending' : 'pending',
            'priority' => $priority,
            'is_scheduled' => $isScheduled,
            'scheduled_at' => $scheduledAt,
            'metadata' => [
                'channels' => $channels,
                'created_at' => now()->toISOString(),
            ]
        ]);
    }

    /**
     * Send notification through specific channel
     */
    private function sendThroughChannel(Notification $notification, string $channel, array $preferences): void
    {
        try {
            switch ($channel) {
                case 'email':
                    $this->sendEmailNotification($notification, $preferences);
                    break;
                case 'sms':
                    $this->sendSmsNotification($notification, $preferences);
                    break;
                case 'push':
                    $this->sendPushNotification($notification, $preferences);
                    break;
                case 'in_app':
                    $this->sendInAppNotification($notification, $preferences);
                    break;
                case 'database':
                    $this->sendDatabaseNotification($notification);
                    break;
                default:
                    Log::warning("Unknown notification channel: {$channel}");
            }

            $this->updateNotificationStatus($notification, 'sent');
            
        } catch (\Exception $e) {
            $this->handleNotificationError($notification, $channel, $e->getMessage());
        }
    }

    /**
     * Send email notification
     */
    private function sendEmailNotification(Notification $notification, array $preferences): void
    {
        $user = $notification->user;
        
        // Check if user has email
        if (!$user->email) {
            throw new \Exception('User has no email address');
        }

        // Check quiet hours
        $preference = $preferences['email'] ?? null;
        if ($preference && !$this->shouldSendInQuietHours($preference)) {
            throw new \Exception('Notification blocked by quiet hours');
        }

        // Send email
        Mail::to($user->email)->send(
            new \App\Mail\GenericNotification($notification)
        );

        Log::info('Email notification sent', [
            'notification_id' => $notification->id,
            'user_email' => $user->email
        ]);
    }

    /**
     * Send SMS notification
     */
    private function sendSmsNotification(Notification $notification, array $preferences): void
    {
        $user = $notification->user;
        
        // Check if user has phone
        if (!$user->phone) {
            throw new \Exception('User has no phone number');
        }

        // Check quiet hours
        $preference = $preferences['sms'] ?? null;
        if ($preference && !$this->shouldSendInQuietHours($preference)) {
            throw new \Exception('Notification blocked by quiet hours');
        }

        // In production, integrate with SMS service (Twilio, etc.)
        // For now, just log the SMS
        Log::info('SMS notification would be sent', [
            'notification_id' => $notification->id,
            'user_phone' => $user->phone,
            'message' => $notification->message
        ]);
    }

    /**
     * Send push notification
     */
    private function sendPushNotification(Notification $notification, array $preferences): void
    {
        // In production, integrate with push notification service
        // For now, just log the push notification
        Log::info('Push notification would be sent', [
            'notification_id' => $notification->id,
            'user_id' => $notification->user_id,
            'title' => $notification->title,
            'message' => $notification->message
        ]);
    }

    /**
     * Send in-app notification
     */
    private function sendInAppNotification(Notification $notification, array $preferences): void
    {
        // In-app notifications are stored in database and retrieved via API
        // The notification is already created, so just mark as sent
        Log::info('In-app notification sent', [
            'notification_id' => $notification->id,
            'user_id' => $notification->user_id
        ]);
    }

    /**
     * Send database notification
     */
    private function sendDatabaseNotification(Notification $notification): void
    {
        // Database notifications are already stored, just mark as sent
        Log::info('Database notification sent', [
            'notification_id' => $notification->id,
            'user_id' => $notification->user_id
        ]);
    }

    /**
     * Process scheduled notification
     */
    private function processScheduledNotification(Notification $notification): void
    {
        $channels = $notification->metadata['channels'] ?? ['database'];
        
        foreach ($channels as $channel) {
            $this->sendThroughChannel($notification, $channel, []);
        }

        $notification->update([
            'is_scheduled' => false,
            'scheduled_at' => null
        ]);
    }

    /**
     * Retry failed notification
     */
    private function retryNotification(Notification $notification): void
    {
        $deliveryLog = $notification->delivery_log ?? [];
        $retryCount = $deliveryLog['retry_count'] ?? 0;

        if ($retryCount >= $this->config['max_retries']) {
            throw new \Exception('Max retries exceeded');
        }

        // Increment retry count
        $deliveryLog['retry_count'] = $retryCount + 1;
        $deliveryLog['last_retry'] = now()->toISOString();

        $notification->update([
            'delivery_log' => $deliveryLog,
            'status' => 'pending'
        ]);

        // Retry sending
        $channels = $notification->metadata['channels'] ?? ['database'];
        foreach ($channels as $channel) {
            $this->sendThroughChannel($notification, $channel, []);
        }
    }

    /**
     * Update notification status
     */
    private function updateNotificationStatus(Notification $notification, string $status): void
    {
        $updateData = ['status' => $status];
        
        if ($status === 'sent') {
            $updateData['sent_at'] = now();
        } elseif ($status === 'delivered') {
            $updateData['delivered_at'] = now();
        }

        $notification->update($updateData);
    }

    /**
     * Handle notification error
     */
    private function handleNotificationError(Notification $notification, string $channel, string $error): void
    {
        $deliveryLog = $notification->delivery_log ?? [];
        $deliveryLog[] = [
            'channel' => $channel,
            'status' => 'failed',
            'error' => $error,
            'timestamp' => now()->toISOString()
        ];

        $notification->update([
            'status' => 'failed',
            'delivery_log' => $deliveryLog
        ]);

        Log::error('Notification failed', [
            'notification_id' => $notification->id,
            'channel' => $channel,
            'error' => $error
        ]);
    }

    /**
     * Check if should send in quiet hours
     */
    private function shouldSendInQuietHours(array $preference): bool
    {
        if (!isset($preference['quiet_hours_start']) || !isset($preference['quiet_hours_end'])) {
            return true;
        }

        $now = now();
        $start = $preference['quiet_hours_start'];
        $end = $preference['quiet_hours_end'];

        // Handle overnight quiet hours
        if ($start > $end) {
            return $now < $start && $now > $end;
        }

        return $now < $start || $now > $end;
    }

    /**
     * Replace template variables
     */
    private function replaceTemplateVariables(string $content, array $variables): string
    {
        foreach ($variables as $key => $value) {
            $content = str_replace("{{" . $key . "}}", $value, $content);
        }

        return $content;
    }

    /**
     * Get notification statistics
     */
    public function getStatistics(): array
    {
        return [
            'total_notifications' => Notification::count(),
            'pending_notifications' => Notification::where('status', 'pending')->count(),
            'sent_notifications' => Notification::where('status', 'sent')->count(),
            'failed_notifications' => Notification::where('status', 'failed')->count(),
            'delivered_notifications' => Notification::where('status', 'delivered')->count(),
            'scheduled_notifications' => Notification::where('is_scheduled', true)->count(),
            'recent_notifications' => Notification::where('created_at', '>=', now()->subDays(7))->count(),
        ];
    }
} 