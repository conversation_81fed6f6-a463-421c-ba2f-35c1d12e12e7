<?php

namespace App\Services;

use App\Models\Agreement;
use App\Models\User;
use App\Models\Tenant;
use App\Models\Unit;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Mail;
use Carbon\Carbon;
use Illuminate\Support\Facades\Notification;

class AgreementRenewalReminderService
{
    protected $config;

    public function __construct()
    {
        $this->config = config('agreements.renewal_reminders', [
            'advance_notice_days' => [30, 15, 7, 1],
            'enable_email_reminders' => true,
            'enable_sms_reminders' => false,
            'enable_in_app_notifications' => true,
        ]);
    }

    /**
     * Get agreements that need renewal reminders
     */
    public function getAgreementsNeedingReminders(int $daysAdvance = null): array
    {
        $daysAdvance = $daysAdvance ?? $this->config['advance_notice_days'][0];
        
        $agreements = Agreement::with(['tenant', 'unit', 'creator'])
            ->where('status', 'active')
            ->where('end_date', '<=', now()->addDays($daysAdvance))
            ->where('end_date', '>', now())
            ->where('renewal_reminder_sent', false)
            ->get();

        $reminders = [];
        foreach ($agreements as $agreement) {
            $reminders[] = [
                'agreement_id' => $agreement->id,
                'tenant_name' => $agreement->tenant->name ?? 'Unknown',
                'unit_number' => $agreement->unit->unit_number ?? 'Unknown',
                'end_date' => $agreement->end_date->format('Y-m-d'),
                'days_until_expiry' => $agreement->end_date->diffInDays(now()),
                'tenant_email' => $agreement->tenant->email ?? null,
                'owner_email' => $agreement->unit->owner->email ?? null,
                'creator_email' => $agreement->creator->email ?? null,
            ];
        }

        return $reminders;
    }

    /**
     * Send renewal reminders for agreements
     */
    public function sendRenewalReminders(int $daysAdvance = null): array
    {
        $reminders = $this->getAgreementsNeedingReminders($daysAdvance);
        $sentCount = 0;
        $errors = [];

        foreach ($reminders as $reminder) {
            try {
                $this->sendReminderForAgreement($reminder);
                $sentCount++;
                
                // Mark reminder as sent
                $this->markReminderAsSent($reminder['agreement_id']);
                
            } catch (\Exception $e) {
                $errors[] = [
                    'agreement_id' => $reminder['agreement_id'],
                    'error' => $e->getMessage()
                ];
                Log::error('Failed to send renewal reminder', [
                    'agreement_id' => $reminder['agreement_id'],
                    'error' => $e->getMessage()
                ]);
            }
        }

        return [
            'total_reminders' => count($reminders),
            'sent_count' => $sentCount,
            'errors' => $errors
        ];
    }

    /**
     * Send reminder for a specific agreement
     */
    public function sendReminderForAgreement(array $reminder): void
    {
        $agreement = Agreement::with(['tenant', 'unit', 'creator'])->find($reminder['agreement_id']);
        
        if (!$agreement) {
            throw new \Exception('Agreement not found');
        }

        // Send email reminders
        if ($this->config['enable_email_reminders']) {
            $this->sendEmailReminders($agreement, $reminder);
        }

        // Send SMS reminders (if configured)
        if ($this->config['enable_sms_reminders']) {
            $this->sendSmsReminders($agreement, $reminder);
        }

        // Send in-app notifications
        if ($this->config['enable_in_app_notifications']) {
            $this->sendInAppNotifications($agreement, $reminder);
        }

        // Log the reminder
        $this->logReminderSent($agreement, $reminder);
    }

    /**
     * Send email reminders
     */
    private function sendEmailReminders(Agreement $agreement, array $reminder): void
    {
        // Send to tenant
        if ($reminder['tenant_email']) {
            Mail::to($reminder['tenant_email'])->send(
                new \App\Mail\AgreementRenewalReminder($agreement, 'tenant')
            );
        }

        // Send to owner
        if ($reminder['owner_email']) {
            Mail::to($reminder['owner_email'])->send(
                new \App\Mail\AgreementRenewalReminder($agreement, 'owner')
            );
        }

        // Send to admin/creator
        if ($reminder['creator_email']) {
            Mail::to($reminder['creator_email'])->send(
                new \App\Mail\AgreementRenewalReminder($agreement, 'admin')
            );
        }
    }

    /**
     * Send SMS reminders
     */
    private function sendSmsReminders(Agreement $agreement, array $reminder): void
    {
        // In production, integrate with SMS service (Twilio, etc.)
        // For now, just log the SMS reminder
        Log::info('SMS renewal reminder would be sent', [
            'agreement_id' => $agreement->id,
            'tenant_phone' => $agreement->tenant->phone ?? null,
            'owner_phone' => $agreement->unit->owner->phone ?? null,
        ]);
    }

    /**
     * Send in-app notifications
     */
    private function sendInAppNotifications(Agreement $agreement, array $reminder): void
    {
        // Send to tenant
        if ($agreement->tenant && $agreement->tenant->user) {
            $agreement->tenant->user->notify(
                new \App\Notifications\AgreementRenewalReminder($agreement, 'tenant')
            );
        }

        // Send to owner
        if ($agreement->unit && $agreement->unit->owner && $agreement->unit->owner->user) {
            $agreement->unit->owner->user->notify(
                new \App\Notifications\AgreementRenewalReminder($agreement, 'owner')
            );
        }

        // Send to admin/creator
        if ($agreement->creator) {
            $agreement->creator->notify(
                new \App\Notifications\AgreementRenewalReminder($agreement, 'admin')
            );
        }
    }

    /**
     * Mark reminder as sent
     */
    private function markReminderAsSent(int $agreementId): void
    {
        $agreement = Agreement::find($agreementId);
        if ($agreement) {
            $agreement->update([
                'renewal_reminder_sent' => true,
                'renewal_reminder_sent_at' => now(),
            ]);
        }
    }

    /**
     * Log reminder sent
     */
    private function logReminderSent(Agreement $agreement, array $reminder): void
    {
        Log::info('Agreement renewal reminder sent', [
            'agreement_id' => $agreement->id,
            'tenant_name' => $reminder['tenant_name'],
            'unit_number' => $reminder['unit_number'],
            'end_date' => $reminder['end_date'],
            'days_until_expiry' => $reminder['days_until_expiry'],
            'sent_at' => now()->toISOString(),
        ]);
    }

    /**
     * Get renewal statistics
     */
    public function getRenewalStatistics(): array
    {
        $now = now();
        
        $statistics = [
            'total_active_agreements' => Agreement::where('status', 'active')->count(),
            'expiring_this_month' => Agreement::where('status', 'active')
                ->where('end_date', '<=', $now->copy()->addMonth())
                ->where('end_date', '>', $now)
                ->count(),
            'expiring_this_week' => Agreement::where('status', 'active')
                ->where('end_date', '<=', $now->copy()->addWeek())
                ->where('end_date', '>', $now)
                ->count(),
            'expiring_tomorrow' => Agreement::where('status', 'active')
                ->where('end_date', '<=', $now->copy()->addDay())
                ->where('end_date', '>', $now)
                ->count(),
            'reminders_sent_today' => Agreement::where('renewal_reminder_sent', true)
                ->where('renewal_reminder_sent_at', '>=', $now->startOfDay())
                ->count(),
            'pending_reminders' => Agreement::where('status', 'active')
                ->where('end_date', '<=', $now->copy()->addDays(30))
                ->where('end_date', '>', $now)
                ->where('renewal_reminder_sent', false)
                ->count(),
        ];

        return $statistics;
    }

    /**
     * Reset reminder flags for testing or manual resend
     */
    public function resetReminderFlags(int $agreementId = null): int
    {
        $query = Agreement::where('renewal_reminder_sent', true);
        
        if ($agreementId) {
            $query->where('id', $agreementId);
        }

        $updated = $query->update([
            'renewal_reminder_sent' => false,
            'renewal_reminder_sent_at' => null,
        ]);

        return $updated;
    }

    /**
     * Get agreements expiring soon with details
     */
    public function getExpiringAgreements(int $days = 30): array
    {
        $agreements = Agreement::with(['tenant', 'unit', 'creator'])
            ->where('status', 'active')
            ->where('end_date', '<=', now()->addDays($days))
            ->where('end_date', '>', now())
            ->orderBy('end_date')
            ->get();

        $expiringAgreements = [];
        foreach ($agreements as $agreement) {
            $expiringAgreements[] = [
                'id' => $agreement->id,
                'tenant_name' => $agreement->tenant->name ?? 'Unknown',
                'unit_number' => $agreement->unit->unit_number ?? 'Unknown',
                'end_date' => $agreement->end_date->format('Y-m-d'),
                'days_until_expiry' => $agreement->end_date->diffInDays(now()),
                'renewal_reminder_sent' => $agreement->renewal_reminder_sent,
                'renewal_reminder_sent_at' => $agreement->renewal_reminder_sent_at?->format('Y-m-d H:i:s'),
                'tenant_email' => $agreement->tenant->email ?? null,
                'owner_email' => $agreement->unit->owner->email ?? null,
            ];
        }

        return $expiringAgreements;
    }

    /**
     * Schedule renewal reminders (for use with Laravel's task scheduler)
     */
    public function scheduleRenewalReminders(): void
    {
        foreach ($this->config['advance_notice_days'] as $daysAdvance) {
            $this->sendRenewalReminders($daysAdvance);
        }
    }
} 