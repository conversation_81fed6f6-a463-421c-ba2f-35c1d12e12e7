<?php

namespace App\Services;

use App\Models\Notification;
use App\Models\NotificationTemplate;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Http;

class PushNotificationService
{
    protected $config;

    public function __construct()
    {
        $this->config = config('notifications.push', [
            'default_provider' => 'fcm',
            'providers' => [
                'fcm' => [
                    'server_key' => env('FCM_SERVER_KEY'),
                    'project_id' => env('FCM_PROJECT_ID'),
                    'enabled' => env('FCM_ENABLED', false),
                ],
                'web_push' => [
                    'vapid_public_key' => env('VAPID_PUBLIC_KEY'),
                    'vapid_private_key' => env('VAPID_PRIVATE_KEY'),
                    'enabled' => env('WEB_PUSH_ENABLED', false),
                ],
            ],
        ]);
    }

    /**
     * Send push notification
     */
    public function send(Notification $notification): bool
    {
        try {
            $user = $notification->user;
            if (!$user) {
                Log::error('Push notification failed: No user found', [
                    'notification_id' => $notification->id,
                ]);
                return false;
            }

            $deviceTokens = $this->getDeviceTokens($user);
            if (empty($deviceTokens)) {
                Log::warning('Push notification skipped: No device tokens found', [
                    'notification_id' => $notification->id,
                    'user_id' => $user->id,
                ]);
                return true; // Not a failure, just no devices to send to
            }

            $payload = $this->preparePayload($notification);
            $provider = $this->getActiveProvider();

            if (!$provider) {
                Log::error('Push notification failed: No active provider found');
                return false;
            }

            $successCount = 0;
            $failureCount = 0;

            foreach ($deviceTokens as $token) {
                $result = $this->sendViaProvider($provider, $token, $payload, $notification);
                if ($result) {
                    $successCount++;
                } else {
                    $failureCount++;
                }
            }

            // Update notification status based on results
            if ($successCount > 0) {
                $notification->markAsSent();
                $notification->update([
                    'delivery_log' => array_merge($notification->delivery_log ?? [], [
                        [
                            'provider' => $provider,
                            'status' => 'sent',
                            'success_count' => $successCount,
                            'failure_count' => $failureCount,
                            'timestamp' => now()->toISOString(),
                        ]
                    ])
                ]);
                return true;
            } else {
                $notification->markAsFailed('All device tokens failed');
                return false;
            }

        } catch (\Exception $e) {
            Log::error('Push notification failed', [
                'notification_id' => $notification->id,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
            ]);

            $notification->markAsFailed($e->getMessage());
            return false;
        }
    }

    /**
     * Send push notification via specific provider
     */
    protected function sendViaProvider(string $provider, string $deviceToken, array $payload, Notification $notification): bool
    {
        switch ($provider) {
            case 'fcm':
                return $this->sendViaFcm($deviceToken, $payload, $notification);
            case 'web_push':
                return $this->sendViaWebPush($deviceToken, $payload, $notification);
            default:
                Log::error("Unknown push notification provider: {$provider}");
                return false;
        }
    }

    /**
     * Send push notification via Firebase Cloud Messaging
     */
    protected function sendViaFcm(string $deviceToken, array $payload, Notification $notification): bool
    {
        try {
            $config = $this->config['providers']['fcm'];
            
            if (!$config['enabled']) {
                Log::warning('FCM push provider is disabled');
                return false;
            }

            $fcmPayload = [
                'to' => $deviceToken,
                'notification' => [
                    'title' => $payload['title'],
                    'body' => $payload['body'],
                    'icon' => $payload['icon'] ?? '/icon.png',
                    'click_action' => $payload['click_action'] ?? null,
                ],
                'data' => $payload['data'] ?? [],
                'priority' => $payload['priority'] ?? 'high',
            ];

            $response = Http::withHeaders([
                'Authorization' => 'key=' . $config['server_key'],
                'Content-Type' => 'application/json',
            ])->post('https://fcm.googleapis.com/fcm/send', $fcmPayload);

            if ($response->successful()) {
                $data = $response->json();
                if (isset($data['success']) && $data['success'] > 0) {
                    Log::info('Push notification sent via FCM', [
                        'notification_id' => $notification->id,
                        'message_id' => $data['results'][0]['message_id'] ?? null,
                    ]);
                    return true;
                }
            }

            Log::error('FCM push notification failed', [
                'notification_id' => $notification->id,
                'response' => $response->body(),
                'status' => $response->status(),
            ]);

            return false;

        } catch (\Exception $e) {
            Log::error('FCM push notification exception', [
                'notification_id' => $notification->id,
                'error' => $e->getMessage(),
            ]);
            return false;
        }
    }

    /**
     * Send push notification via Web Push
     */
    protected function sendViaWebPush(string $deviceToken, array $payload, Notification $notification): bool
    {
        try {
            $config = $this->config['providers']['web_push'];
            
            if (!$config['enabled']) {
                Log::warning('Web Push provider is disabled');
                return false;
            }

            // This would require a proper Web Push library
            // For now, we'll log the attempt
            Log::info('Push notification would be sent via Web Push', [
                'notification_id' => $notification->id,
                'device_token' => $deviceToken,
                'payload' => $payload,
            ]);

            return true; // Placeholder - implement actual Web Push integration

        } catch (\Exception $e) {
            Log::error('Web Push notification exception', [
                'notification_id' => $notification->id,
                'error' => $e->getMessage(),
            ]);
            return false;
        }
    }

    /**
     * Get device tokens for user
     */
    protected function getDeviceTokens($user): array
    {
        // This would typically come from a user_devices table
        // For now, we'll check if user has any device tokens stored
        $tokens = [];

        // Check if user has FCM tokens stored
        if ($user->fcm_tokens) {
            $tokens = array_merge($tokens, $user->fcm_tokens);
        }

        // Check if user has web push subscriptions
        if ($user->web_push_subscriptions) {
            $tokens = array_merge($tokens, $user->web_push_subscriptions);
        }

        return array_filter($tokens);
    }

    /**
     * Prepare notification payload
     */
    protected function preparePayload(Notification $notification): array
    {
        $data = $notification->data ?? [];
        
        // If template exists, render it
        if ($notification->template_id) {
            $template = NotificationTemplate::find($notification->template_id);
            if ($template && $template->type === 'push') {
                $title = $this->renderTemplate($template->title, $data);
                $body = $this->renderTemplate($template->content, $data);
            } else {
                $title = $notification->title;
                $body = $notification->message;
            }
        } else {
            $title = $notification->title;
            $body = $notification->message;
        }

        $payload = [
            'title' => $title,
            'body' => $body,
            'data' => array_merge($data, [
                'notification_id' => $notification->id,
                'type' => $notification->type,
                'timestamp' => now()->toISOString(),
            ]),
            'priority' => $this->getPriority($notification->priority),
        ];

        // Add click action if available
        if (isset($data['click_action'])) {
            $payload['click_action'] = $data['click_action'];
        }

        // Add icon if available
        if (isset($data['icon'])) {
            $payload['icon'] = $data['icon'];
        }

        return $payload;
    }

    /**
     * Render template with variables
     */
    protected function renderTemplate(string $template, array $variables): string
    {
        foreach ($variables as $key => $value) {
            $template = str_replace("{{" . $key . "}}", $value, $template);
        }
        
        return $template;
    }

    /**
     * Get priority for push notification
     */
    protected function getPriority(string $notificationPriority): string
    {
        return match($notificationPriority) {
            'urgent', 'high' => 'high',
            'normal' => 'normal',
            'low' => 'normal',
            default => 'normal',
        };
    }

    /**
     * Get active push notification provider
     */
    protected function getActiveProvider(): ?string
    {
        $defaultProvider = $this->config['default_provider'];
        
        if (isset($this->config['providers'][$defaultProvider]) && $this->config['providers'][$defaultProvider]['enabled']) {
            return $defaultProvider;
        }

        // Try to find any enabled provider
        foreach ($this->config['providers'] as $name => $config) {
            if ($config['enabled']) {
                return $name;
            }
        }

        return null;
    }

    /**
     * Check if push notifications are enabled
     */
    public function isEnabled(): bool
    {
        return $this->getActiveProvider() !== null;
    }

    /**
     * Get available providers
     */
    public function getProviders(): array
    {
        return array_keys($this->config['providers']);
    }

    /**
     * Get provider status
     */
    public function getProviderStatus(): array
    {
        $status = [];
        
        foreach ($this->config['providers'] as $name => $config) {
            $status[$name] = [
                'enabled' => $config['enabled'] ?? false,
                'configured' => $this->isProviderConfigured($name),
            ];
        }
        
        return $status;
    }

    /**
     * Check if provider is configured
     */
    protected function isProviderConfigured(string $provider): bool
    {
        $config = $this->config['providers'][$provider] ?? [];
        
        switch ($provider) {
            case 'fcm':
                return !empty($config['server_key']) && !empty($config['project_id']);
            case 'web_push':
                return !empty($config['vapid_public_key']) && !empty($config['vapid_private_key']);
            default:
                return false;
        }
    }

    /**
     * Register device token for user
     */
    public function registerDeviceToken($user, string $token, string $platform = 'web'): bool
    {
        try {
            $tokens = $user->fcm_tokens ?? [];
            
            if (!in_array($token, $tokens)) {
                $tokens[] = $token;
                $user->update(['fcm_tokens' => $tokens]);
            }

            Log::info('Device token registered', [
                'user_id' => $user->id,
                'token' => $token,
                'platform' => $platform,
            ]);

            return true;

        } catch (\Exception $e) {
            Log::error('Failed to register device token', [
                'user_id' => $user->id,
                'token' => $token,
                'error' => $e->getMessage(),
            ]);
            return false;
        }
    }

    /**
     * Unregister device token for user
     */
    public function unregisterDeviceToken($user, string $token): bool
    {
        try {
            $tokens = $user->fcm_tokens ?? [];
            $tokens = array_filter($tokens, fn($t) => $t !== $token);
            
            $user->update(['fcm_tokens' => $tokens]);

            Log::info('Device token unregistered', [
                'user_id' => $user->id,
                'token' => $token,
            ]);

            return true;

        } catch (\Exception $e) {
            Log::error('Failed to unregister device token', [
                'user_id' => $user->id,
                'token' => $token,
                'error' => $e->getMessage(),
            ]);
            return false;
        }
    }
} 