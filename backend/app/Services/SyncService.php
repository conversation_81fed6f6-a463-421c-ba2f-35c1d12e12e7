<?php

namespace App\Services;

use App\Models\SyncAuditLog;
use App\Events\Sync\SocietyUpdated;
use App\Events\Sync\UnitUpdated;
use App\Events\Sync\TenantUpdated;
use App\Events\Sync\AgreementUpdated;
use App\Events\Sync\PaymentUpdated;
use App\Events\Sync\EntityDeleted;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Carbon\Carbon;

class SyncService
{
    /**
     * Manually trigger sync for a specific entity.
     */
    public function syncEntity(Model $entity, string $operation = 'updated', ?string $correlationId = null): bool
    {
        try {
            $entityType = class_basename($entity);
            $eventClass = $this->getEventClass($entityType);
            
            if (!$eventClass) {
                Log::warning('No sync event class found for entity type', ['entity_type' => $entityType]);
                return false;
            }

            // Load relationships if needed
            $this->loadRequiredRelationships($entity, $entityType);
            
            // Generate correlation ID if not provided
            $correlationId = $correlationId ?? $this->generateCorrelationId($entity, $operation);
            
            // Dispatch the sync event
            if ($operation === 'deleted') {
                $societyId = $this->extractSocietyId($entity);
                event(new EntityDeleted($entityType, $entity->id, $societyId, $correlationId));
            } else {
                event(new $eventClass($entity->toArray(), $operation, $correlationId));
            }
            
            Log::info('Manual sync triggered', [
                'entity_type' => $entityType,
                'entity_id' => $entity->id,
                'operation' => $operation,
                'correlation_id' => $correlationId,
            ]);
            
            return true;
            
        } catch (\Exception $e) {
            Log::error('Failed to trigger manual sync', [
                'entity_type' => class_basename($entity),
                'entity_id' => $entity->id,
                'operation' => $operation,
                'error' => $e->getMessage(),
            ]);
            
            return false;
        }
    }

    /**
     * Bulk sync multiple entities.
     */
    public function bulkSync(array $entities, string $operation = 'updated'): array
    {
        $results = [];
        $batchId = 'bulk_' . uniqid() . '_' . time();
        
        // Set bulk operation flag to prevent individual observers from firing
        app()->instance('sync.bulk_operation', true);
        
        try {
            DB::beginTransaction();
            
            foreach ($entities as $entity) {
                $correlationId = $this->generateCorrelationId($entity, $operation, $batchId);
                $success = $this->syncEntity($entity, $operation, $correlationId);
                
                $results[] = [
                    'entity_type' => class_basename($entity),
                    'entity_id' => $entity->id,
                    'success' => $success,
                    'correlation_id' => $correlationId,
                ];
            }
            
            DB::commit();
            
            Log::info('Bulk sync completed', [
                'batch_id' => $batchId,
                'total_entities' => count($entities),
                'successful' => collect($results)->where('success', true)->count(),
                'failed' => collect($results)->where('success', false)->count(),
            ]);
            
        } catch (\Exception $e) {
            DB::rollBack();
            
            Log::error('Bulk sync failed', [
                'batch_id' => $batchId,
                'error' => $e->getMessage(),
            ]);
            
            throw $e;
        } finally {
            // Remove bulk operation flag
            app()->forgetInstance('sync.bulk_operation');
        }
        
        return $results;
    }

    /**
     * Retry failed sync operations.
     */
    public function retryFailedSyncs(int $maxRetries = null): array
    {
        $maxRetries = $maxRetries ?? config('sync.retry_attempts', 3);
        
        $failedLogs = SyncAuditLog::readyForRetry()
            ->orderBy('created_at')
            ->limit(100) // Process in batches
            ->get();
        
        $results = [];
        
        foreach ($failedLogs as $log) {
            try {
                // Recreate and dispatch the event
                $success = $this->retrySync($log);
                
                $results[] = [
                    'log_id' => $log->id,
                    'entity_type' => $log->entity_type,
                    'entity_id' => $log->entity_id,
                    'success' => $success,
                ];
                
            } catch (\Exception $e) {
                $log->markAsFailed($e->getMessage());
                
                $results[] = [
                    'log_id' => $log->id,
                    'entity_type' => $log->entity_type,
                    'entity_id' => $log->entity_id,
                    'success' => false,
                    'error' => $e->getMessage(),
                ];
            }
        }
        
        return $results;
    }

    /**
     * Get sync statistics.
     */
    public function getSyncStatistics(int $hours = 24): array
    {
        $stats = SyncAuditLog::getPerformanceMetrics($hours);
        
        // Add additional statistics
        $recentLogs = SyncAuditLog::recent($hours)->get();
        
        $stats['by_entity_type'] = $recentLogs->groupBy('entity_type')
            ->map(function ($logs, $entityType) {
                return [
                    'total' => $logs->count(),
                    'success' => $logs->where('status', SyncAuditLog::STATUS_SUCCESS)->count(),
                    'failed' => $logs->where('status', SyncAuditLog::STATUS_FAILED)->count(),
                    'skipped' => $logs->where('status', SyncAuditLog::STATUS_SKIPPED)->count(),
                ];
            });
        
        $stats['by_operation'] = $recentLogs->groupBy('operation')
            ->map(function ($logs) {
                return [
                    'total' => $logs->count(),
                    'success_rate' => $logs->where('status', SyncAuditLog::STATUS_SUCCESS)->count() / max($logs->count(), 1) * 100,
                ];
            });
        
        $stats['queue_health'] = $this->getQueueHealth();
        
        return $stats;
    }

    /**
     * Get queue health information.
     */
    public function getQueueHealth(): array
    {
        try {
            // Get pending jobs count (this would need to be implemented based on your queue driver)
            $pendingJobs = $this->getPendingJobsCount();
            $failedJobs = $this->getFailedJobsCount();
            
            return [
                'pending_jobs' => $pendingJobs,
                'failed_jobs' => $failedJobs,
                'status' => $this->determineQueueStatus($pendingJobs, $failedJobs),
                'last_processed' => $this->getLastProcessedJobTime(),
            ];
            
        } catch (\Exception $e) {
            return [
                'status' => 'unknown',
                'error' => $e->getMessage(),
            ];
        }
    }

    /**
     * Get the event class for an entity type.
     */
    protected function getEventClass(string $entityType): ?string
    {
        $eventMap = [
            'Society' => SocietyUpdated::class,
            'Unit' => UnitUpdated::class,
            'Tenant' => TenantUpdated::class,
            'Agreement' => AgreementUpdated::class,
            'RentPayment' => PaymentUpdated::class,
        ];
        
        return $eventMap[$entityType] ?? null;
    }

    /**
     * Load required relationships for an entity.
     */
    protected function loadRequiredRelationships(Model $entity, string $entityType): void
    {
        $entityConfig = config("sync.entities.{$entityType}", []);
        $relationships = $entityConfig['relationships'] ?? [];
        
        if (!empty($relationships)) {
            $entity->load($relationships);
        }
    }

    /**
     * Extract society ID from entity.
     */
    protected function extractSocietyId(Model $entity): ?int
    {
        // Use the same logic as in SyncObserver
        if (isset($entity->society_id)) {
            return $entity->society_id;
        }
        
        if ($entity instanceof \App\Models\Society) {
            return $entity->id;
        }
        
        if ($entity instanceof \App\Models\Unit) {
            return $entity->society_id;
        }
        
        if (isset($entity->unit_id) && method_exists($entity, 'unit')) {
            $unit = $entity->unit;
            return $unit?->society_id;
        }
        
        if (method_exists($entity, 'society')) {
            $society = $entity->society;
            return $society?->id;
        }
        
        return null;
    }

    /**
     * Generate correlation ID.
     */
    protected function generateCorrelationId(Model $entity, string $operation, ?string $batchId = null): string
    {
        $entityType = class_basename($entity);
        $base = "sync_{$entityType}_{$operation}_{$entity->id}_" . uniqid() . '_' . time();
        
        return $batchId ? "{$batchId}_{$base}" : $base;
    }

    /**
     * Retry a specific sync operation.
     */
    protected function retrySync(SyncAuditLog $log): bool
    {
        $eventClass = $this->getEventClass($log->entity_type);
        
        if (!$eventClass) {
            throw new \Exception("No event class found for entity type: {$log->entity_type}");
        }
        
        if ($log->operation === 'deleted') {
            event(new EntityDeleted(
                $log->entity_type,
                $log->entity_id,
                $log->payload['society_id'] ?? null,
                $log->correlation_id
            ));
        } else {
            event(new $eventClass(
                $log->payload,
                $log->operation,
                $log->correlation_id
            ));
        }
        
        return true;
    }

    /**
     * Get pending jobs count (implement based on your queue driver).
     */
    protected function getPendingJobsCount(): int
    {
        // This would need to be implemented based on your queue driver
        // For RabbitMQ, you'd need to query the queue management API
        return 0;
    }

    /**
     * Get failed jobs count.
     */
    protected function getFailedJobsCount(): int
    {
        return DB::table('failed_jobs')->count();
    }

    /**
     * Determine queue status based on metrics.
     */
    protected function determineQueueStatus(int $pendingJobs, int $failedJobs): string
    {
        if ($failedJobs > 10) {
            return 'critical';
        }
        
        if ($pendingJobs > 1000) {
            return 'warning';
        }
        
        return 'healthy';
    }

    /**
     * Get last processed job time.
     */
    protected function getLastProcessedJobTime(): ?Carbon
    {
        $lastLog = SyncAuditLog::whereNotNull('processed_at')
            ->orderBy('processed_at', 'desc')
            ->first();
        
        return $lastLog?->processed_at;
    }
}
