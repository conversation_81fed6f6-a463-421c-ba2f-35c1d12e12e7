<?php

namespace App\Services;

use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Arr;

class ExternalPaymentService
{
    protected ?string $baseUrl;
    protected ?string $apiKey;
    protected ?string $clientId;
    protected ?string $clientSecret;

    public function __construct()
    {
        $this->baseUrl = config('services.oneapp.base_url', '');
        $this->apiKey = config('services.oneapp.api_key', '');
        $this->clientId = config('services.oneapp.client_id', '');
        $this->clientSecret = config('services.oneapp.client_secret', '');
    }

    /**
     * Fetch rent payments from OneApp API.
     *
     * @param array $params
     * @return array
     */
    public function fetchPayments(array $params = []): array
    {
        // Return empty array if configuration is not set
        if (empty($this->baseUrl) || empty($this->apiKey)) {
            Log::warning('OneApp configuration not set, returning empty payment data');
            return [];
        }

        $endpoint = $this->baseUrl . '/payments';
        $page = 1;
        $results = [];
        do {
            $response = Http::withHeaders($this->getAuthHeaders())
                ->get($endpoint, array_merge($params, ['page' => $page]));

            if ($response->failed()) {
                Log::error('OneApp payment fetch failed', [
                    'endpoint' => $endpoint,
                    'params' => $params,
                    'status' => $response->status(),
                    'body' => $response->body(),
                ]);
                break;
            }

            $data = $response->json();
            $payments = Arr::get($data, 'data', []);
            $results = array_merge($results, $payments);
            $hasMore = Arr::get($data, 'meta.has_more', false);
            $page++;
        } while ($hasMore);

        return $results;
    }

    /**
     * Get authentication headers for OneApp API.
     *
     * @return array
     */
    protected function getAuthHeaders(): array
    {
        return [
            'Authorization' => 'Bearer ' . $this->apiKey,
            'X-Client-Id' => $this->clientId,
            'X-Client-Secret' => $this->clientSecret,
            'Accept' => 'application/json',
        ];
    }
} 