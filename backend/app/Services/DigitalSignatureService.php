<?php

namespace App\Services;

use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Str;
use setasign\Fpdi\Fpdi;

class DigitalSignatureService
{
    /**
     * Add digital signature to PDF
     */
    public function addSignatureToPdf(string $pdfPath, array $signatureData): string
    {
        // Create a new FPDI instance
        $pdf = new Fpdi();
        
        // Set document information
        $pdf->SetCreator('TMS System');
        $pdf->Set<PERSON>uthor('Housing Society TMS');
        $pdf->SetTitle('Signed Agreement');
        
        // Get the original PDF content from storage
        $originalPdfContent = Storage::get($pdfPath);
        
        if (!$originalPdfContent) {
            throw new \Exception("Original PDF file not found: {$pdfPath}");
        }
        
        // Create a temporary file for the original PDF
        $tempDir = storage_path('app/temp');
        if (!file_exists($tempDir)) {
            mkdir($tempDir, 0755, true);
        }
        
        $tempPdfPath = $tempDir . '/' . Str::uuid() . '.pdf';
        file_put_contents($tempPdfPath, $originalPdfContent);
        
        try {
            // Import the original PDF using FPDI
            $pageCount = $pdf->setSourceFile($tempPdfPath);
            
            for ($i = 1; $i <= $pageCount; $i++) {
                $pdf->AddPage();
                $tplIdx = $pdf->importPage($i);
                $pdf->useTemplate($tplIdx);
                
                // Add signature on the last page
                if ($i == $pageCount) {
                    $this->addSignatureLayer($pdf, $signatureData);
                }
            }
            
            // Generate signed PDF
            $signedPdfPath = 'agreements/signed/' . Str::uuid() . '.pdf';
            $signedPdfContent = $pdf->Output('', 'S');
            Storage::put($signedPdfPath, $signedPdfContent);
            
            return $signedPdfPath;
            
        } finally {
            // Clean up temporary file
            if (file_exists($tempPdfPath)) {
                unlink($tempPdfPath);
            }
        }
    }
    
    /**
     * Add signature layer to PDF
     */
    private function addSignatureLayer(Fpdi $pdf, array $signatureData): void
    {
        $pdf->SetFont('helvetica', '', 10);
        
        // Get current page dimensions
        $pageWidth = $pdf->GetPageWidth();
        $pageHeight = $pdf->GetPageHeight();
        
        // Add signature section at bottom of page
        $signatureY = $pageHeight - 80;
        
        // Add signature boxes
        $pdf->Rect(20, $signatureY, 80, 30);
        $pdf->Rect(120, $signatureY, 80, 30);
        
        // Add signature labels
        $pdf->SetXY(20, $signatureY + 35);
        $pdf->Cell(80, 10, 'Owner Signature', 0, 0, 'C');
        $pdf->SetXY(120, $signatureY + 35);
        $pdf->Cell(80, 10, 'Tenant Signature', 0, 0, 'C');
        
        // Add signature details
        $pdf->SetXY(20, $signatureY + 45);
        $pdf->Cell(80, 8, 'Name: ' . ($signatureData['owner_name'] ?? ''), 0, 0);
        $pdf->SetXY(120, $signatureY + 45);
        $pdf->Cell(80, 8, 'Name: ' . ($signatureData['tenant_name'] ?? ''), 0, 0);
        
        $pdf->SetXY(20, $signatureY + 53);
        $pdf->Cell(80, 8, 'Date: ' . ($signatureData['signature_date'] ?? date('Y-m-d')), 0, 0);
        $pdf->SetXY(120, $signatureY + 53);
        $pdf->Cell(80, 8, 'Date: ' . ($signatureData['signature_date'] ?? date('Y-m-d')), 0, 0);
        
        // Add digital signature verification
        $pdf->SetXY(20, $signatureY + 65);
        $pdf->SetFont('helvetica', '', 8);
        $pdf->Cell(180, 6, 'Digital Signature Verification: ' . $this->generateSignatureHash($signatureData), 0, 0);
    }
    
    /**
     * Generate signature hash for verification
     */
    private function generateSignatureHash(array $signatureData): string
    {
        $data = [
            'owner_name' => $signatureData['owner_name'] ?? '',
            'tenant_name' => $signatureData['tenant_name'] ?? '',
            'agreement_id' => $signatureData['agreement_id'] ?? '',
            'signature_date' => $signatureData['signature_date'] ?? date('Y-m-d'),
            'timestamp' => time()
        ];
        
        return hash('sha256', json_encode($data));
    }
    
    /**
     * Verify digital signature
     */
    public function verifySignature(string $signatureHash, array $signatureData): bool
    {
        $expectedHash = $this->generateSignatureHash($signatureData);
        return hash_equals($expectedHash, $signatureHash);
    }
    
    /**
     * Generate signature certificate
     */
    public function generateSignatureCertificate(array $signatureData): array
    {
        $signatureHash = $this->generateSignatureHash($signatureData);
        
        return [
            'signature_hash' => $signatureHash,
            'signature_date' => $signatureData['signature_date'] ?? date('Y-m-d'),
            'signature_timestamp' => time(),
            'owner_name' => $signatureData['owner_name'] ?? '',
            'tenant_name' => $signatureData['tenant_name'] ?? '',
            'agreement_id' => $signatureData['agreement_id'] ?? '',
            'verification_url' => config('app.url') . '/api/v1/agreements/verify-signature/' . $signatureHash
        ];
    }
    
    /**
     * Create a simple PDF for testing purposes
     */
    public function createTestPdf(string $content = 'Test Agreement Content'): string
    {
        $pdf = new Fpdi();
        
        $pdf->SetCreator('TMS System');
        $pdf->SetAuthor('Housing Society TMS');
        $pdf->SetTitle('Test Agreement');
        
        $pdf->AddPage();
        $pdf->SetFont('helvetica', '', 12);
        $pdf->Cell(0, 10, $content, 0, 1);
        
        $testPdfPath = 'agreements/test_' . Str::uuid() . '.pdf';
        $pdfContent = $pdf->Output('', 'S');
        Storage::put($testPdfPath, $pdfContent);
        
        return $testPdfPath;
    }
} 