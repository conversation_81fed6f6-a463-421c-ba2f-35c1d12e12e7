<?php

namespace App\Services;

use App\Models\Enquiry;
use App\Models\Unit;
use App\Models\User;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Storage;
use Exception;

class EnquiryService
{
    /**
     * Create a new enquiry with attachments and notifications.
     */
    public function createEnquiry(array $data, array $attachments = []): Enquiry
    {
        try {
            DB::beginTransaction();

            // Get unit and owner information
            $unit = Unit::with('owner')->findOrFail($data['unit_id']);
            $data['owner_id'] = $unit->owner_id;

            // Generate enquiry reference number
            $data['reference_number'] = $this->generateReferenceNumber();

            // Set initial status and timestamps
            $data['status'] = 'open';
            $data['created_at'] = now();

            // Create enquiry
            $enquiry = Enquiry::create($data);

            // Handle attachments
            if (!empty($attachments)) {
                $enquiry->attachments = $this->processAttachments($enquiry, $attachments);
                $enquiry->save();
            }

            // Auto-assign based on enquiry type and priority
            $this->autoAssignEnquiry($enquiry);

            // Send notifications
            $this->sendEnquiryNotifications($enquiry, 'created');

            DB::commit();

            return $enquiry->load(['unit', 'enquirer', 'owner', 'assignedUser']);

        } catch (Exception $e) {
            DB::rollBack();
            Log::error('Failed to create enquiry', [
                'error' => $e->getMessage(),
                'data' => $data
            ]);
            throw $e;
        }
    }

    /**
     * Update enquiry status with proper validation and notifications.
     */
    public function updateEnquiryStatus(Enquiry $enquiry, string $status, array $additionalData = []): Enquiry
    {
        try {
            DB::beginTransaction();

            $oldStatus = $enquiry->status;
            $enquiry->status = $status;

            // Set status-specific timestamps
            switch ($status) {
                case 'in_progress':
                    $enquiry->started_at = now();
                    break;
                case 'resolved':
                    $enquiry->resolved_at = now();
                    if (isset($additionalData['resolution_notes'])) {
                        $enquiry->resolution_notes = $additionalData['resolution_notes'];
                    }
                    break;
                case 'closed':
                    $enquiry->closed_at = now();
                    break;
            }

            // Update additional fields
            foreach ($additionalData as $key => $value) {
                if (in_array($key, $enquiry->getFillable())) {
                    $enquiry->$key = $value;
                }
            }

            $enquiry->save();

            // Add status change to communication history
            $this->addCommunicationEntry($enquiry, 'status_change', [
                'old_status' => $oldStatus,
                'new_status' => $status,
                'changed_by' => auth()->id(),
                'timestamp' => now(),
                'notes' => $additionalData['status_change_notes'] ?? null,
            ]);

            // Send notifications
            $this->sendEnquiryNotifications($enquiry, 'status_changed', [
                'old_status' => $oldStatus,
                'new_status' => $status,
            ]);

            DB::commit();

            return $enquiry->fresh(['unit', 'enquirer', 'owner', 'assignedUser']);

        } catch (Exception $e) {
            DB::rollBack();
            Log::error('Failed to update enquiry status', [
                'enquiry_id' => $enquiry->id,
                'status' => $status,
                'error' => $e->getMessage()
            ]);
            throw $e;
        }
    }

    /**
     * Assign enquiry to a user.
     */
    public function assignEnquiry(Enquiry $enquiry, int $userId): Enquiry
    {
        try {
            $assignee = User::findOrFail($userId);
            
            // Validate assignee permissions
            if (!$assignee->hasRole(['admin', 'manager', 'staff'])) {
                throw new Exception('Selected user cannot be assigned to enquiries.');
            }

            $oldAssignee = $enquiry->assigned_to;
            $enquiry->assigned_to = $userId;
            $enquiry->assigned_at = now();
            $enquiry->save();

            // Add assignment to communication history
            $this->addCommunicationEntry($enquiry, 'assignment', [
                'old_assignee' => $oldAssignee,
                'new_assignee' => $userId,
                'assigned_by' => auth()->id(),
                'timestamp' => now(),
            ]);

            // Send notifications
            $this->sendEnquiryNotifications($enquiry, 'assigned', [
                'assignee' => $assignee,
            ]);

            return $enquiry->fresh(['unit', 'enquirer', 'owner', 'assignedUser']);

        } catch (Exception $e) {
            Log::error('Failed to assign enquiry', [
                'enquiry_id' => $enquiry->id,
                'user_id' => $userId,
                'error' => $e->getMessage()
            ]);
            throw $e;
        }
    }

    /**
     * Add communication entry to enquiry.
     */
    public function addCommunicationEntry(Enquiry $enquiry, string $type, array $data): void
    {
        $communicationHistory = $enquiry->communication_history ?? [];
        
        $entry = [
            'id' => uniqid(),
            'type' => $type,
            'timestamp' => now()->toISOString(),
            'user_id' => auth()->id(),
            'user_name' => auth()->user()->name,
            'data' => $data,
        ];

        $communicationHistory[] = $entry;
        $enquiry->communication_history = $communicationHistory;
        $enquiry->save();
    }

    /**
     * Process and store attachments.
     */
    protected function processAttachments(Enquiry $enquiry, array $attachments): array
    {
        $processedAttachments = [];

        foreach ($attachments as $attachment) {
            $filename = time() . '_' . $attachment->getClientOriginalName();
            $path = $attachment->storeAs("enquiries/{$enquiry->id}/attachments", $filename, 'public');
            
            $processedAttachments[] = [
                'id' => uniqid(),
                'original_name' => $attachment->getClientOriginalName(),
                'filename' => $filename,
                'path' => $path,
                'size' => $attachment->getSize(),
                'mime_type' => $attachment->getMimeType(),
                'uploaded_at' => now()->toISOString(),
                'uploaded_by' => auth()->id(),
            ];
        }

        return $processedAttachments;
    }

    /**
     * Auto-assign enquiry based on type and priority.
     */
    protected function autoAssignEnquiry(Enquiry $enquiry): void
    {
        // Auto-assignment logic based on enquiry type
        $assignmentRules = [
            'maintenance_request' => ['role' => 'staff', 'department' => 'maintenance'],
            'complaint' => ['role' => 'manager'],
            'rental_inquiry' => ['role' => 'staff', 'department' => 'leasing'],
            'purchase_inquiry' => ['role' => 'manager'],
            'general_inquiry' => ['role' => 'staff'],
        ];

        $rule = $assignmentRules[$enquiry->type] ?? null;
        
        if ($rule) {
            $query = User::where('role', $rule['role'])->where('status', 'active');
            
            if (isset($rule['department'])) {
                $query->where('department', $rule['department']);
            }

            // Get user with least active assignments
            $assignee = $query->withCount(['assignedEnquiries' => function ($q) {
                $q->whereIn('status', ['open', 'in_progress']);
            }])->orderBy('assigned_enquiries_count')->first();

            if ($assignee) {
                $enquiry->assigned_to = $assignee->id;
                $enquiry->assigned_at = now();
                $enquiry->save();
            }
        }
    }

    /**
     * Send notifications for enquiry events.
     */
    protected function sendEnquiryNotifications(Enquiry $enquiry, string $event, array $context = []): void
    {
        // TODO: Implement notification service integration
        Log::info("Enquiry notification", [
            'enquiry_id' => $enquiry->id,
            'event' => $event,
            'context' => $context
        ]);
    }

    /**
     * Generate unique reference number for enquiry.
     */
    protected function generateReferenceNumber(): string
    {
        $prefix = 'ENQ';
        $date = now()->format('Ymd');
        $sequence = str_pad(Enquiry::whereDate('created_at', today())->count() + 1, 4, '0', STR_PAD_LEFT);
        
        return "{$prefix}{$date}{$sequence}";
    }

    /**
     * Get enquiry statistics.
     */
    public function getStatistics(User $user = null): array
    {
        $query = Enquiry::query();
        
        // Apply user-based filtering
        if ($user) {
            if ($user->hasRole('owner')) {
                $query->where('owner_id', $user->id);
            } elseif (!$user->hasRole(['admin', 'manager'])) {
                $query->where('enquirer_id', $user->id);
            }
        }

        return [
            'total' => $query->count(),
            'by_status' => $query->groupBy('status')->selectRaw('status, count(*) as count')->pluck('count', 'status'),
            'by_type' => $query->groupBy('type')->selectRaw('type, count(*) as count')->pluck('count', 'type'),
            'by_priority' => $query->groupBy('priority')->selectRaw('priority, count(*) as count')->pluck('count', 'priority'),
            'overdue' => $query->where('estimated_resolution_date', '<', now())
                ->whereNotIn('status', ['resolved', 'closed', 'cancelled'])->count(),
            'avg_resolution_time' => $query->whereNotNull('resolved_at')
                ->selectRaw('AVG(TIMESTAMPDIFF(HOUR, created_at, resolved_at)) as avg_hours')
                ->value('avg_hours'),
        ];
    }
}
