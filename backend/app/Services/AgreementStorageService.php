<?php

namespace App\Services;

use App\Models\Agreement;
use Illuminate\Http\UploadedFile;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Str;

class AgreementStorageService
{
    /**
     * Store a signed agreement file
     *
     * @param Agreement $agreement
     * @param UploadedFile $file
     * @return string
     */
    public function storeSignedAgreement(Agreement $agreement, UploadedFile $file): string
    {
        // Validate file type
        $this->validateFileType($file);
        
        // Generate unique filename
        $filename = $this->generateFilename($agreement, $file);
        
        // Store file
        $path = Storage::disk('agreements')->putFileAs(
            $this->getStoragePath($agreement),
            $file,
            $filename
        );
        
        // Update agreement with file path
        $agreement->update([
            'signed_file_path' => $path,
            'signed_at' => now(),
        ]);
        
        return $path;
    }
    
    /**
     * Get the signed agreement file URL
     *
     * @param Agreement $agreement
     * @return string|null
     */
    public function getSignedAgreementUrl(Agreement $agreement): ?string
    {
        if (!$agreement->signed_file_path) {
            return null;
        }
        
        return Storage::disk('agreements')->url($agreement->signed_file_path);
    }
    
    /**
     * Download the signed agreement file
     *
     * @param Agreement $agreement
     * @return \Symfony\Component\HttpFoundation\StreamedResponse|null
     */
    public function downloadSignedAgreement(Agreement $agreement)
    {
        if (!$agreement->signed_file_path || !Storage::disk('agreements')->exists($agreement->signed_file_path)) {
            return null;
        }
        
        return Storage::disk('agreements')->download(
            $agreement->signed_file_path,
            $this->getDownloadFilename($agreement)
        );
    }
    
    /**
     * Delete the signed agreement file
     *
     * @param Agreement $agreement
     * @return bool
     */
    public function deleteSignedAgreement(Agreement $agreement): bool
    {
        if (!$agreement->signed_file_path) {
            return true;
        }
        
        $deleted = Storage::disk('agreements')->delete($agreement->signed_file_path);
        
        if ($deleted) {
            $agreement->update([
                'signed_file_path' => null,
                'signed_at' => null,
            ]);
        }
        
        return $deleted;
    }
    
    /**
     * Validate file type
     *
     * @param UploadedFile $file
     * @throws \InvalidArgumentException
     */
    private function validateFileType(UploadedFile $file): void
    {
        $allowedTypes = ['application/pdf', 'image/jpeg', 'image/png'];
        
        if (!in_array($file->getMimeType(), $allowedTypes)) {
            throw new \InvalidArgumentException('Invalid file type. Only PDF, JPEG, and PNG files are allowed.');
        }
        
        if ($file->getSize() > 10 * 1024 * 1024) { // 10MB limit
            throw new \InvalidArgumentException('File size too large. Maximum size is 10MB.');
        }
    }
    
    /**
     * Generate unique filename for the signed agreement
     *
     * @param Agreement $agreement
     * @param UploadedFile $file
     * @return string
     */
    private function generateFilename(Agreement $agreement, UploadedFile $file): string
    {
        $extension = $file->getClientOriginalExtension();
        $timestamp = now()->format('Y-m-d_H-i-s');
        $uniqueId = Str::random(8);
        
        return "agreement_{$agreement->id}_signed_{$timestamp}_{$uniqueId}.{$extension}";
    }
    
    /**
     * Get storage path for the agreement
     *
     * @param Agreement $agreement
     * @return string
     */
    private function getStoragePath(Agreement $agreement): string
    {
        $year = $agreement->created_at->format('Y');
        $month = $agreement->created_at->format('m');
        
        return "agreements/{$year}/{$month}";
    }
    
    /**
     * Get download filename
     *
     * @param Agreement $agreement
     * @return string
     */
    private function getDownloadFilename(Agreement $agreement): string
    {
        $tenantName = Str::slug($agreement->tenant->name ?? 'tenant');
        $unitNumber = $agreement->unit->unit_number ?? 'unit';
        $date = $agreement->signed_at?->format('Y-m-d') ?? now()->format('Y-m-d');
        
        return "signed_agreement_{$tenantName}_{$unitNumber}_{$date}.pdf";
    }
} 