<?php

namespace App\Services;

use App\Models\Agreement;
use App\Models\User;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Str;
use Carbon\Carbon;

class DigitalSignatureWorkflowService
{
    protected $digitalSignatureService;
    protected $agreementStorageService;
    protected $config;

    public function __construct(
        DigitalSignatureService $digitalSignatureService,
        AgreementStorageService $agreementStorageService
    ) {
        $this->digitalSignatureService = $digitalSignatureService;
        $this->agreementStorageService = $agreementStorageService;
        $this->config = config('services.digital_signature', []);
    }

    /**
     * Initiate digital signature workflow
     */
    public function initiateWorkflow(Agreement $agreement, User $initiator): array
    {
        // Validate agreement can be signed
        $this->validateAgreementForSigning($agreement);

        // Create signature request
        $signatureRequest = $this->createSignatureRequest($agreement, $initiator);

        // Send signature invitations
        $invitations = $this->sendSignatureInvitations($agreement, $signatureRequest);

        // Update agreement status
        $agreement->update([
            'status' => 'pending_signature',
            'signature_request_id' => $signatureRequest['id'],
            'signature_workflow_started_at' => now(),
        ]);

        return [
            'signature_request' => $signatureRequest,
            'invitations' => $invitations,
            'workflow_status' => 'initiated'
        ];
    }

    /**
     * Process signature from external provider
     */
    public function processSignature(string $signatureRequestId, array $signatureData): array
    {
        $agreement = Agreement::where('signature_request_id', $signatureRequestId)->firstOrFail();
        
        // Verify signature with external provider
        $verificationResult = $this->verifyExternalSignature($signatureRequestId, $signatureData);
        
        if (!$verificationResult['verified']) {
            throw new \Exception('Signature verification failed: ' . $verificationResult['error']);
        }

        // Update agreement with signature
        $this->applySignature($agreement, $signatureData);

        // Check if workflow is complete
        $workflowComplete = $this->checkWorkflowCompletion($agreement);

        return [
            'agreement_id' => $agreement->id,
            'signature_applied' => true,
            'workflow_complete' => $workflowComplete,
            'next_signers' => $this->getNextSigners($agreement)
        ];
    }

    /**
     * Get signature workflow status
     */
    public function getWorkflowStatus(Agreement $agreement): array
    {
        $signatureRequest = $this->getSignatureRequest($agreement->signature_request_id);
        
        return [
            'agreement_id' => $agreement->id,
            'workflow_status' => $agreement->status,
            'signature_request' => $signatureRequest,
            'signature_progress' => [
                'owner_signed' => $agreement->signed_by_owner,
                'tenant_signed' => $agreement->signed_by_tenant,
                'total_signers' => 2,
                'signed_count' => ($agreement->signed_by_owner ? 1 : 0) + ($agreement->signed_by_tenant ? 1 : 0)
            ],
            'next_actions' => $this->getNextActions($agreement),
            'timeline' => $this->getWorkflowTimeline($agreement)
        ];
    }

    /**
     * Cancel signature workflow
     */
    public function cancelWorkflow(Agreement $agreement, User $cancelledBy, string $reason = null): array
    {
        // Cancel with external provider
        $this->cancelSignatureRequest($agreement->signature_request_id);

        // Update agreement
        $agreement->update([
            'status' => 'draft',
            'signature_request_id' => null,
            'signature_workflow_cancelled_at' => now(),
            'signature_workflow_cancelled_by' => $cancelledBy->id,
            'signature_workflow_cancellation_reason' => $reason
        ]);

        return [
            'agreement_id' => $agreement->id,
            'workflow_status' => 'cancelled',
            'cancelled_by' => $cancelledBy->name,
            'cancellation_reason' => $reason
        ];
    }

    /**
     * Resend signature invitations
     */
    public function resendInvitations(Agreement $agreement): array
    {
        $signatureRequest = $this->getSignatureRequest($agreement->signature_request_id);
        
        if (!$signatureRequest) {
            throw new \Exception('No active signature request found');
        }

        $invitations = $this->sendSignatureInvitations($agreement, $signatureRequest);

        return [
            'agreement_id' => $agreement->id,
            'invitations_resent' => true,
            'invitations' => $invitations
        ];
    }

    /**
     * Get signature reminders
     */
    public function getSignatureReminders(): array
    {
        $pendingAgreements = Agreement::where('status', 'pending_signature')
            ->where('signature_workflow_started_at', '<=', now()->subDays(3))
            ->with(['tenant', 'unit', 'creator'])
            ->get();

        $reminders = [];
        foreach ($pendingAgreements as $agreement) {
            $reminders[] = [
                'agreement_id' => $agreement->id,
                'tenant_name' => $agreement->tenant->name ?? 'Unknown',
                'unit_number' => $agreement->unit->unit_number ?? 'Unknown',
                'days_pending' => $agreement->signature_workflow_started_at->diffInDays(now()),
                'next_action' => $this->getNextActionForReminder($agreement)
            ];
        }

        return $reminders;
    }

    /**
     * Validate agreement can be signed
     */
    private function validateAgreementForSigning(Agreement $agreement): void
    {
        if ($agreement->status !== 'draft') {
            throw new \Exception('Agreement must be in draft status to initiate signature workflow');
        }

        if ($agreement->signature_request_id) {
            throw new \Exception('Signature workflow already initiated for this agreement');
        }

        if (!$agreement->tenant || !$agreement->unit) {
            throw new \Exception('Agreement must have valid tenant and unit');
        }
    }

    /**
     * Create signature request with external provider
     */
    private function createSignatureRequest(Agreement $agreement, User $initiator): array
    {
        // For now, simulate external provider integration
        // In production, this would integrate with DocuSign, HelloSign, etc.
        
        $signatureRequestId = 'sig_' . Str::uuid();
        
        $requestData = [
            'id' => $signatureRequestId,
            'agreement_id' => $agreement->id,
            'initiator_id' => $initiator->id,
            'created_at' => now()->toISOString(),
            'expires_at' => now()->addDays(30)->toISOString(),
            'signers' => [
                [
                    'id' => 'owner_signer',
                    'name' => $agreement->unit->owner->name ?? 'Property Owner',
                    'email' => $agreement->unit->owner->email ?? '<EMAIL>',
                    'role' => 'owner',
                    'order' => 1
                ],
                [
                    'id' => 'tenant_signer',
                    'name' => $agreement->tenant->name ?? 'Tenant',
                    'email' => $agreement->tenant->email ?? '<EMAIL>',
                    'role' => 'tenant',
                    'order' => 2
                ]
            ],
            'document_url' => $this->getAgreementDocumentUrl($agreement),
            'status' => 'pending'
        ];

        // Store signature request data
        Storage::put("signature_requests/{$signatureRequestId}.json", json_encode($requestData));

        return $requestData;
    }

    /**
     * Send signature invitations
     */
    private function sendSignatureInvitations(Agreement $agreement, array $signatureRequest): array
    {
        $invitations = [];

        foreach ($signatureRequest['signers'] as $signer) {
            $invitation = [
                'signer_id' => $signer['id'],
                'signer_name' => $signer['name'],
                'signer_email' => $signer['email'],
                'signature_url' => $this->generateSignatureUrl($signatureRequest['id'], $signer['id']),
                'expires_at' => $signatureRequest['expires_at'],
                'sent_at' => now()->toISOString()
            ];

            // In production, send actual emails/SMS
            $this->sendSignatureInvitation($invitation);

            $invitations[] = $invitation;
        }

        return $invitations;
    }

    /**
     * Verify external signature
     */
    private function verifyExternalSignature(string $signatureRequestId, array $signatureData): array
    {
        // In production, verify with external provider
        // For now, simulate verification
        
        $requiredFields = ['signer_id', 'signature_hash', 'signed_at'];
        foreach ($requiredFields as $field) {
            if (!isset($signatureData[$field])) {
                return ['verified' => false, 'error' => "Missing required field: {$field}"];
            }
        }

        // Verify signature hash
        $expectedHash = hash('sha256', $signatureRequestId . $signatureData['signer_id'] . $signatureData['signed_at']);
        
        if (!hash_equals($expectedHash, $signatureData['signature_hash'])) {
            return ['verified' => false, 'error' => 'Invalid signature hash'];
        }

        return ['verified' => true];
    }

    /**
     * Apply signature to agreement
     */
    private function applySignature(Agreement $agreement, array $signatureData): void
    {
        $signerRole = $signatureData['signer_role'] ?? 'unknown';
        
        if ($signerRole === 'owner') {
            $agreement->signed_by_owner = true;
        } elseif ($signerRole === 'tenant') {
            $agreement->signed_by_tenant = true;
        }

        // Update signature data
        $currentSignatureData = $agreement->signature_data ?? [];
        $currentSignatureData[$signerRole] = [
            'signed_at' => $signatureData['signed_at'],
            'signer_name' => $signatureData['signer_name'],
            'signature_hash' => $signatureData['signature_hash']
        ];

        $agreement->signature_data = $currentSignatureData;
        $agreement->signed_at = now();
        $agreement->save();

        // Log signature event
        $this->logSignatureEvent($agreement, $signatureData);
    }

    /**
     * Check if workflow is complete
     */
    private function checkWorkflowCompletion(Agreement $agreement): bool
    {
        if ($agreement->signed_by_owner && $agreement->signed_by_tenant) {
            $agreement->update([
                'status' => 'active',
                'signature_workflow_completed_at' => now()
            ]);
            return true;
        }
        return false;
    }

    /**
     * Get next signers
     */
    private function getNextSigners(Agreement $agreement): array
    {
        $nextSigners = [];

        if (!$agreement->signed_by_owner) {
            $nextSigners[] = [
                'role' => 'owner',
                'name' => $agreement->unit->owner->name ?? 'Property Owner',
                'email' => $agreement->unit->owner->email ?? '<EMAIL>'
            ];
        }

        if (!$agreement->signed_by_tenant) {
            $nextSigners[] = [
                'role' => 'tenant',
                'name' => $agreement->tenant->name ?? 'Tenant',
                'email' => $agreement->tenant->email ?? '<EMAIL>'
            ];
        }

        return $nextSigners;
    }

    /**
     * Get signature request
     */
    private function getSignatureRequest(string $requestId): ?array
    {
        $requestPath = "signature_requests/{$requestId}.json";
        
        if (!Storage::exists($requestPath)) {
            return null;
        }

        return json_decode(Storage::get($requestPath), true);
    }

    /**
     * Get next actions for agreement
     */
    private function getNextActions(Agreement $agreement): array
    {
        $actions = [];

        if ($agreement->status === 'pending_signature') {
            if (!$agreement->signed_by_owner) {
                $actions[] = 'waiting_for_owner_signature';
            }
            if (!$agreement->signed_by_tenant) {
                $actions[] = 'waiting_for_tenant_signature';
            }
        }

        if ($agreement->status === 'active') {
            $actions[] = 'agreement_fully_signed';
        }

        return $actions;
    }

    /**
     * Get workflow timeline
     */
    private function getWorkflowTimeline(Agreement $agreement): array
    {
        $timeline = [];

        if ($agreement->signature_workflow_started_at) {
            $timeline[] = [
                'event' => 'workflow_initiated',
                'timestamp' => $agreement->signature_workflow_started_at,
                'description' => 'Digital signature workflow started'
            ];
        }

        if ($agreement->signed_by_owner) {
            $timeline[] = [
                'event' => 'owner_signed',
                'timestamp' => $agreement->signature_data['owner']['signed_at'] ?? now(),
                'description' => 'Property owner signed the agreement'
            ];
        }

        if ($agreement->signed_by_tenant) {
            $timeline[] = [
                'event' => 'tenant_signed',
                'timestamp' => $agreement->signature_data['tenant']['signed_at'] ?? now(),
                'description' => 'Tenant signed the agreement'
            ];
        }

        if ($agreement->signature_workflow_completed_at) {
            $timeline[] = [
                'event' => 'workflow_completed',
                'timestamp' => $agreement->signature_workflow_completed_at,
                'description' => 'Digital signature workflow completed'
            ];
        }

        return $timeline;
    }

    /**
     * Cancel signature request
     */
    private function cancelSignatureRequest(string $requestId): void
    {
        // In production, cancel with external provider
        $requestPath = "signature_requests/{$requestId}.json";
        
        if (Storage::exists($requestPath)) {
            Storage::delete($requestPath);
        }
    }

    /**
     * Get next action for reminder
     */
    private function getNextActionForReminder(Agreement $agreement): string
    {
        if (!$agreement->signed_by_owner) {
            return 'Send reminder to property owner';
        }
        if (!$agreement->signed_by_tenant) {
            return 'Send reminder to tenant';
        }
        return 'No action needed';
    }

    /**
     * Get agreement document URL
     */
    private function getAgreementDocumentUrl(Agreement $agreement): string
    {
        return config('app.url') . "/api/v1/agreements/{$agreement->id}/download";
    }

    /**
     * Generate signature URL
     */
    private function generateSignatureUrl(string $requestId, string $signerId): string
    {
        return config('app.url') . "/api/v1/agreements/sign/{$requestId}/{$signerId}";
    }

    /**
     * Send signature invitation
     */
    private function sendSignatureInvitation(array $invitation): void
    {
        // In production, send actual email/SMS
        Log::info('Signature invitation sent', $invitation);
    }

    /**
     * Log signature event
     */
    private function logSignatureEvent(Agreement $agreement, array $signatureData): void
    {
        Log::info('Agreement signed', [
            'agreement_id' => $agreement->id,
            'signer_role' => $signatureData['signer_role'] ?? 'unknown',
            'signer_name' => $signatureData['signer_name'] ?? 'unknown',
            'signed_at' => $signatureData['signed_at'] ?? now()
        ]);
    }
} 