<?php

namespace App\Services;

use GuzzleHttp\Client;
use GuzzleHttp\Exception\RequestException;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Cache;
use App\Models\ThirdPartyApiConfig;
use App\Models\ThirdPartyApiLog;
use Carbon\Carbon;

class ThirdPartyApiService
{
    protected $client;
    protected $defaultTimeout = 30;
    protected $defaultRetries = 3;

    public function __construct()
    {
        $this->client = new Client([
            'timeout' => $this->defaultTimeout,
            'verify' => config('app.env') === 'production',
        ]);
    }

    /**
     * Make a request to a third-party API
     */
    public function makeRequest(ThirdPartyApiConfig $config, string $endpoint, string $method = 'GET', array $data = [], array $headers = []): array
    {
        try {
            // Build the complete URL
            $url = rtrim($config->base_url, '/') . '/' . ltrim($endpoint, '/');

            // Prepare headers with authentication
            $requestHeaders = $this->buildHeaders($config, $headers);

            // Prepare request options
            $options = [
                'headers' => $requestHeaders,
                'timeout' => $config->timeout ?? $this->defaultTimeout,
            ];

            // Add body data based on method
            if (in_array(strtoupper($method), ['POST', 'PUT', 'PATCH'])) {
                if ($config->content_type === 'application/json') {
                    $options['json'] = $data;
                } else {
                    $options['form_params'] = $data;
                }
            } elseif (strtoupper($method) === 'GET' && !empty($data)) {
                $options['query'] = $data;
            }

            // Make the request with retry logic
            $response = $this->makeRequestWithRetry($method, $url, $options, $config->retries ?? $this->defaultRetries);

            return [
                'success' => true,
                'status_code' => $response->getStatusCode(),
                'headers' => $response->getHeaders(),
                'body' => json_decode($response->getBody()->getContents(), true),
                'raw_body' => $response->getBody()->getContents()
            ];

        } catch (RequestException $e) {
            Log::error('Third-party API request failed', [
                'config_id' => $config->id,
                'endpoint' => $endpoint,
                'method' => $method,
                'error' => $e->getMessage(),
                'response' => $e->hasResponse() ? $e->getResponse()->getBody()->getContents() : null
            ]);

            return [
                'success' => false,
                'error' => $e->getMessage(),
                'status_code' => $e->hasResponse() ? $e->getResponse()->getStatusCode() : 0,
                'body' => $e->hasResponse() ? $e->getResponse()->getBody()->getContents() : null
            ];
        }
    }

    /**
     * Build headers with authentication
     */
    protected function buildHeaders(ThirdPartyApiConfig $config, array $additionalHeaders = []): array
    {
        $headers = array_merge([
            'Content-Type' => $config->content_type ?? 'application/json',
            'Accept' => 'application/json',
            'User-Agent' => 'TMS-ThirdPartyAPI/1.0',
        ], $additionalHeaders);

        // Add authentication headers based on type
        switch ($config->auth_type) {
            case 'bearer':
                $headers['Authorization'] = 'Bearer ' . $config->auth_token;
                break;

            case 'api_key':
                $headers[$config->auth_header ?? 'X-API-Key'] = $config->auth_token;
                break;

            case 'basic':
                $headers['Authorization'] = 'Basic ' . base64_encode($config->auth_username . ':' . $config->auth_password);
                break;

            case 'custom':
                if ($config->custom_headers) {
                    $customHeaders = json_decode($config->custom_headers, true);
                    $headers = array_merge($headers, $customHeaders);
                }
                break;
        }

        return $headers;
    }

    /**
     * Make request with retry logic
     */
    protected function makeRequestWithRetry(string $method, string $url, array $options, int $retries)
    {
        $attempt = 0;
        $lastException = null;

        while ($attempt < $retries) {
            try {
                return $this->client->request($method, $url, $options);
            } catch (RequestException $e) {
                $lastException = $e;
                $attempt++;

                if ($attempt < $retries) {
                    // Exponential backoff
                    $delay = pow(2, $attempt - 1);
                    sleep($delay);
                }
            }
        }

        throw $lastException;
    }

    /**
     * Test API connection
     */
    public function testConnection(ThirdPartyApiConfig $config): array
    {
        try {
            $testEndpoint = $config->test_endpoint ?? '/health';
            $result = $this->makeRequest($config, $testEndpoint, 'GET');

            return [
                'success' => $result['success'],
                'message' => $result['success'] ? 'Connection successful' : 'Connection failed',
                'details' => $result
            ];

        } catch (\Exception $e) {
            return [
                'success' => false,
                'message' => 'Connection test failed',
                'error' => $e->getMessage()
            ];
        }
    }

    /**
     * Get cached response if available
     */
    public function getCachedResponse(ThirdPartyApiConfig $config, string $cacheKey)
    {
        if (!$config->cache_enabled) {
            return null;
        }

        return Cache::get($cacheKey);
    }

    /**
     * Cache response
     */
    public function cacheResponse(ThirdPartyApiConfig $config, string $cacheKey, $data): void
    {
        if ($config->cache_enabled && $config->cache_duration > 0) {
            Cache::put($cacheKey, $data, $config->cache_duration);
        }
    }

    /**
     * Generate cache key
     */
    public function generateCacheKey(ThirdPartyApiConfig $config, string $endpoint, string $method, array $data = []): string
    {
        return "third_party_api:{$config->identifier}:" . md5($endpoint . $method . serialize($data));
    }

    /**
     * Transform request data based on mapping rules
     */
    public function transformRequestData(ThirdPartyApiConfig $config, array $data): array
    {
        if (!$config->request_mapping) {
            return $data;
        }

        $mapping = json_decode($config->request_mapping, true);
        $transformedData = [];

        foreach ($mapping as $internalField => $externalField) {
            if (isset($data[$internalField])) {
                $transformedData[$externalField] = $data[$internalField];
            }
        }

        return $transformedData;
    }

    /**
     * Transform response data based on mapping rules
     */
    public function transformResponseData(ThirdPartyApiConfig $config, array $data): array
    {
        if (!$config->response_mapping) {
            return $data;
        }

        $mapping = json_decode($config->response_mapping, true);
        $transformedData = [];

        foreach ($mapping as $externalField => $internalField) {
            if (isset($data[$externalField])) {
                $transformedData[$internalField] = $data[$externalField];
            }
        }

        return $transformedData;
    }

    /**
     * Handle webhooks from third-party APIs
     */
    public function handleWebhook(ThirdPartyApiConfig $config, array $payload): array
    {
        try {
            // Verify webhook signature if configured
            if ($config->webhook_secret && !$this->verifyWebhookSignature($config, $payload)) {
                return [
                    'success' => false,
                    'error' => 'Invalid webhook signature'
                ];
            }

            // Log webhook
            ThirdPartyApiLog::create([
                'log_id' => uniqid('webhook_'),
                'api_config_id' => $config->id,
                'request_method' => 'WEBHOOK',
                'request_body' => $payload,
                'ip_address' => request()->ip(),
                'created_at' => Carbon::now(),
                'status' => 'success'
            ]);

            // Process webhook based on configuration
            $this->processWebhook($config, $payload);

            return ['success' => true];

        } catch (\Exception $e) {
            Log::error('Webhook processing failed', [
                'config_id' => $config->id,
                'error' => $e->getMessage(),
                'payload' => $payload
            ]);

            return [
                'success' => false,
                'error' => $e->getMessage()
            ];
        }
    }

    /**
     * Verify webhook signature
     */
    protected function verifyWebhookSignature(ThirdPartyApiConfig $config, array $payload): bool
    {
        // Implementation depends on the third-party API's signature method
        // This is a basic example
        $signature = request()->header('X-Signature');
        $expectedSignature = hash_hmac('sha256', json_encode($payload), $config->webhook_secret);
        
        return hash_equals($signature, $expectedSignature);
    }

    /**
     * Process webhook
     */
    protected function processWebhook(ThirdPartyApiConfig $config, array $payload): void
    {
        // Fire events based on webhook type
        $webhookType = $payload['type'] ?? 'unknown';
        
        event(new \App\Events\ThirdPartyWebhookReceived($config, $webhookType, $payload));
    }
} 