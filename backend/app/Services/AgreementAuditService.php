<?php

namespace App\Services;

use App\Models\Agreement;
use App\Models\AgreementAuditLog;
use App\Models\User;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Log;

class AgreementAuditService
{
    /**
     * Log an agreement action
     */
    public function logAction(
        Agreement $agreement,
        string $action,
        array $oldValues = null,
        array $newValues = null,
        array $metadata = null,
        User $user = null,
        Request $request = null
    ): AgreementAuditLog {
        $user = $user ?? Auth::user();
        $request = $request ?? request();

        $auditLog = AgreementAuditLog::create([
            'agreement_id' => $agreement->id,
            'user_id' => $user?->id,
            'action' => $action,
            'entity_type' => 'agreement',
            'entity_id' => $agreement->id,
            'old_values' => $oldValues,
            'new_values' => $newValues,
            'metadata' => array_merge([
                'agreement_status' => $agreement->status,
                'tenant_id' => $agreement->tenant_id,
                'unit_id' => $agreement->unit_id,
            ], $metadata ?? []),
            'ip_address' => $request?->ip(),
            'user_agent' => $request?->userAgent(),
            'performed_at' => now(),
        ]);

        // Log to application log as well
        Log::info('Agreement audit log created', [
            'audit_log_id' => $auditLog->id,
            'agreement_id' => $agreement->id,
            'action' => $action,
            'user_id' => $user?->id,
            'performed_at' => $auditLog->performed_at,
        ]);

        return $auditLog;
    }

    /**
     * Log agreement creation
     */
    public function logCreation(Agreement $agreement, User $user = null, Request $request = null): AgreementAuditLog
    {
        return $this->logAction(
            $agreement,
            'created',
            null,
            $agreement->toArray(),
            [
                'template_id' => $agreement->template_id,
                'template_name' => $agreement->template->name ?? 'Unknown',
            ],
            $user,
            $request
        );
    }

    /**
     * Log agreement update
     */
    public function logUpdate(Agreement $agreement, array $oldValues, array $newValues, User $user = null, Request $request = null): AgreementAuditLog
    {
        return $this->logAction(
            $agreement,
            'updated',
            $oldValues,
            $newValues,
            [
                'fields_changed' => array_keys($newValues),
            ],
            $user,
            $request
        );
    }

    /**
     * Log agreement signing
     */
    public function logSigning(Agreement $agreement, string $signerType, User $user = null, Request $request = null): AgreementAuditLog
    {
        $action = match($signerType) {
            'tenant' => 'signed_by_tenant',
            'owner' => 'signed_by_owner',
            default => 'signed'
        };

        return $this->logAction(
            $agreement,
            $action,
            null,
            [
                'signer_type' => $signerType,
                'signed_at' => now(),
                'is_fully_signed' => $agreement->isFullySigned(),
            ],
            [
                'signature_status' => $agreement->getSignatureStatus(),
                'signed_by_owner' => $agreement->signed_by_owner,
                'signed_by_tenant' => $agreement->signed_by_tenant,
            ],
            $user,
            $request
        );
    }

    /**
     * Log full signing completion
     */
    public function logFullySigned(Agreement $agreement, User $user = null, Request $request = null): AgreementAuditLog
    {
        return $this->logAction(
            $agreement,
            'fully_signed',
            null,
            [
                'fully_signed_at' => now(),
                'signed_by_owner' => $agreement->signed_by_owner,
                'signed_by_tenant' => $agreement->signed_by_tenant,
            ],
            [
                'signature_status' => 'fully_signed',
                'workflow_completed' => true,
            ],
            $user,
            $request
        );
    }

    /**
     * Log status change
     */
    public function logStatusChange(Agreement $agreement, string $oldStatus, string $newStatus, User $user = null, Request $request = null): AgreementAuditLog
    {
        return $this->logAction(
            $agreement,
            'status_changed',
            ['status' => $oldStatus],
            ['status' => $newStatus],
            [
                'status_transition' => "{$oldStatus} -> {$newStatus}",
            ],
            $user,
            $request
        );
    }

    /**
     * Log renewal reminder sent
     */
    public function logRenewalReminder(Agreement $agreement, User $user = null, Request $request = null): AgreementAuditLog
    {
        return $this->logAction(
            $agreement,
            'reminder_sent',
            null,
            [
                'reminder_sent_at' => now(),
                'days_until_expiry' => $agreement->getDaysUntilExpiry(),
            ],
            [
                'reminder_type' => 'renewal',
                'end_date' => $agreement->end_date,
            ],
            $user,
            $request
        );
    }

    /**
     * Log signature workflow events
     */
    public function logSignatureWorkflow(Agreement $agreement, string $workflowAction, array $metadata = null, User $user = null, Request $request = null): AgreementAuditLog
    {
        $action = "signature_workflow_{$workflowAction}";
        
        return $this->logAction(
            $agreement,
            $action,
            null,
            [
                'workflow_action' => $workflowAction,
                'workflow_timestamp' => now(),
            ],
            array_merge([
                'signature_request_id' => $agreement->signature_request_id,
                'workflow_status' => $agreement->getSignatureStatus(),
            ], $metadata ?? []),
            $user,
            $request
        );
    }

    /**
     * Log document operations
     */
    public function logDocumentOperation(Agreement $agreement, string $operation, string $documentType, array $metadata = null, User $user = null, Request $request = null): AgreementAuditLog
    {
        $action = "document_{$operation}";
        
        return $this->logAction(
            $agreement,
            $action,
            null,
            [
                'document_operation' => $operation,
                'document_type' => $documentType,
                'timestamp' => now(),
            ],
            array_merge([
                'document_category' => $documentType,
            ], $metadata ?? []),
            $user,
            $request
        );
    }

    /**
     * Get audit logs for an agreement
     */
    public function getAgreementAuditLogs(int $agreementId, array $filters = []): \Illuminate\Database\Eloquent\Collection
    {
        $query = AgreementAuditLog::with(['user'])
            ->where('agreement_id', $agreementId)
            ->orderBy('performed_at', 'desc');

        // Apply filters
        if (isset($filters['action'])) {
            $query->byAction($filters['action']);
        }

        if (isset($filters['user_id'])) {
            $query->byUser($filters['user_id']);
        }

        if (isset($filters['start_date']) && isset($filters['end_date'])) {
            $query->byDateRange($filters['start_date'], $filters['end_date']);
        }

        if (isset($filters['recent_days'])) {
            $query->recent($filters['recent_days']);
        }

        return $query->get();
    }

    /**
     * Get audit statistics
     */
    public function getAuditStatistics(int $agreementId = null): array
    {
        $query = AgreementAuditLog::query();
        
        if ($agreementId) {
            $query->where('agreement_id', $agreementId);
        }

        $totalLogs = $query->count();
        $recentLogs = $query->recent(7)->count();
        
        $actionCounts = $query->selectRaw('action, COUNT(*) as count')
            ->groupBy('action')
            ->pluck('count', 'action')
            ->toArray();

        $userCounts = $query->selectRaw('user_id, COUNT(*) as count')
            ->whereNotNull('user_id')
            ->groupBy('user_id')
            ->with('user')
            ->pluck('count', 'user_id')
            ->toArray();

        return [
            'total_logs' => $totalLogs,
            'recent_logs' => $recentLogs,
            'action_counts' => $actionCounts,
            'user_counts' => $userCounts,
            'most_active_actions' => array_slice(arsort($actionCounts) ? $actionCounts : [], 0, 5, true),
        ];
    }
} 