<?php

namespace App\Services;

use App\Models\TenantHistory;
use App\Models\Tenant;
use App\Models\User;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;

class TenantActivityLogger
{
    /**
     * Log tenant activity with context
     */
    public static function log(
        int $tenantId,
        string $activityType,
        string $description,
        ?array $metadata = null,
        ?array $oldValues = null,
        ?array $newValues = null,
        string $severity = 'medium',
        ?User $user = null,
        ?Request $request = null
    ): TenantHistory {
        // Get current user if not provided
        if (!$user && Auth::check()) {
            $user = Auth::user();
        }

        // Extract request information if provided
        $ipAddress = null;
        $userAgent = null;
        
        if ($request) {
            $ipAddress = $request->ip();
            $userAgent = $request->userAgent();
        }

        return TenantHistory::logActivity(
            tenantId: $tenantId,
            activityType: $activityType,
            description: $description,
            userId: $user?->id,
            metadata: $metadata,
            oldValues: $oldValues,
            newValues: $newValues,
            severity: $severity,
            ipAddress: $ipAddress,
            userAgent: $userAgent
        );
    }

    /**
     * Log tenant creation
     */
    public static function logTenantCreated(Tenant $tenant, ?User $user = null, ?Request $request = null): TenantHistory
    {
        return self::log(
            tenantId: $tenant->id,
            activityType: 'tenant_created',
            description: "Tenant account created with code {$tenant->tenant_code}",
            metadata: [
                'tenant_code' => $tenant->tenant_code,
                'user_id' => $tenant->user_id,
            ],
            severity: 'high',
            user: $user,
            request: $request
        );
    }

    /**
     * Log onboarding started
     */
    public static function logOnboardingStarted(Tenant $tenant, ?User $user = null, ?Request $request = null): TenantHistory
    {
        return self::log(
            tenantId: $tenant->id,
            activityType: 'onboarding_started',
            description: "Tenant onboarding process started",
            severity: 'medium',
            user: $user,
            request: $request
        );
    }

    /**
     * Log personal details update
     */
    public static function logPersonalDetailsUpdated(Tenant $tenant, array $oldValues, array $newValues, ?User $user = null, ?Request $request = null): TenantHistory
    {
        return self::log(
            tenantId: $tenant->id,
            activityType: 'personal_details_updated',
            description: "Personal details updated",
            oldValues: $oldValues,
            newValues: $newValues,
            severity: 'medium',
            user: $user,
            request: $request
        );
    }

    /**
     * Log family details update
     */
    public static function logFamilyDetailsUpdated(Tenant $tenant, array $oldValues, array $newValues, ?User $user = null, ?Request $request = null): TenantHistory
    {
        return self::log(
            tenantId: $tenant->id,
            activityType: 'family_details_updated',
            description: "Family details updated",
            oldValues: $oldValues,
            newValues: $newValues,
            severity: 'medium',
            user: $user,
            request: $request
        );
    }

    /**
     * Log emergency contact added
     */
    public static function logEmergencyContactAdded(Tenant $tenant, array $contactData, ?User $user = null, ?Request $request = null): TenantHistory
    {
        return self::log(
            tenantId: $tenant->id,
            activityType: 'emergency_contact_added',
            description: "Emergency contact added: {$contactData['name']} ({$contactData['relationship']})",
            metadata: $contactData,
            severity: 'medium',
            user: $user,
            request: $request
        );
    }

    /**
     * Log emergency contact updated
     */
    public static function logEmergencyContactUpdated(Tenant $tenant, array $oldValues, array $newValues, ?User $user = null, ?Request $request = null): TenantHistory
    {
        return self::log(
            tenantId: $tenant->id,
            activityType: 'emergency_contact_updated',
            description: "Emergency contact updated: {$newValues['name']} ({$newValues['relationship']})",
            oldValues: $oldValues,
            newValues: $newValues,
            severity: 'medium',
            user: $user,
            request: $request
        );
    }

    /**
     * Log emergency contact removed
     */
    public static function logEmergencyContactRemoved(Tenant $tenant, array $contactData, ?User $user = null, ?Request $request = null): TenantHistory
    {
        return self::log(
            tenantId: $tenant->id,
            activityType: 'emergency_contact_removed',
            description: "Emergency contact removed: {$contactData['name']} ({$contactData['relationship']})",
            metadata: $contactData,
            severity: 'medium',
            user: $user,
            request: $request
        );
    }

    /**
     * Log document upload
     */
    public static function logDocumentUploaded(Tenant $tenant, string $documentType, string $fileName, ?User $user = null, ?Request $request = null): TenantHistory
    {
        return self::log(
            tenantId: $tenant->id,
            activityType: 'document_uploaded',
            description: "Document uploaded: {$documentType} - {$fileName}",
            metadata: [
                'document_type' => $documentType,
                'file_name' => $fileName,
            ],
            severity: 'medium',
            user: $user,
            request: $request
        );
    }

    /**
     * Log document verification
     */
    public static function logDocumentVerified(Tenant $tenant, string $documentType, ?User $user = null, ?Request $request = null): TenantHistory
    {
        return self::log(
            tenantId: $tenant->id,
            activityType: 'document_verified',
            description: "Document verified: {$documentType}",
            metadata: [
                'document_type' => $documentType,
                'verified_by' => $user?->name,
            ],
            severity: 'high',
            user: $user,
            request: $request
        );
    }

    /**
     * Log document rejection
     */
    public static function logDocumentRejected(Tenant $tenant, string $documentType, string $reason, ?User $user = null, ?Request $request = null): TenantHistory
    {
        return self::log(
            tenantId: $tenant->id,
            activityType: 'document_rejected',
            description: "Document rejected: {$documentType} - {$reason}",
            metadata: [
                'document_type' => $documentType,
                'rejection_reason' => $reason,
                'rejected_by' => $user?->name,
            ],
            severity: 'high',
            user: $user,
            request: $request
        );
    }

    /**
     * Log KYC status change
     */
    public static function logKycStatusChanged(Tenant $tenant, string $oldStatus, string $newStatus, ?string $reason = null, ?User $user = null, ?Request $request = null): TenantHistory
    {
        return self::log(
            tenantId: $tenant->id,
            activityType: 'kyc_status_changed',
            description: "KYC status changed from {$oldStatus} to {$newStatus}" . ($reason ? " - {$reason}" : ""),
            oldValues: ['kyc_status' => $oldStatus],
            newValues: ['kyc_status' => $newStatus],
            metadata: $reason ? ['reason' => $reason] : null,
            severity: 'high',
            user: $user,
            request: $request
        );
    }

    /**
     * Log profile update
     */
    public static function logProfileUpdated(Tenant $tenant, array $oldValues, array $newValues, ?User $user = null, ?Request $request = null): TenantHistory
    {
        return self::log(
            tenantId: $tenant->id,
            activityType: 'profile_updated',
            description: "Tenant profile updated",
            oldValues: $oldValues,
            newValues: $newValues,
            severity: 'medium',
            user: $user,
            request: $request
        );
    }

    /**
     * Log unit assignment
     */
    public static function logUnitAssigned(Tenant $tenant, int $unitId, ?User $user = null, ?Request $request = null): TenantHistory
    {
        return self::log(
            tenantId: $tenant->id,
            activityType: 'unit_assigned',
            description: "Unit assigned: Unit #{$unitId}",
            metadata: [
                'unit_id' => $unitId,
                'assigned_by' => $user?->name,
            ],
            severity: 'high',
            user: $user,
            request: $request
        );
    }

    /**
     * Log unit unassignment
     */
    public static function logUnitUnassigned(Tenant $tenant, int $unitId, ?string $reason = null, ?User $user = null, ?Request $request = null): TenantHistory
    {
        return self::log(
            tenantId: $tenant->id,
            activityType: 'unit_unassigned',
            description: "Unit unassigned: Unit #{$unitId}" . ($reason ? " - {$reason}" : ""),
            metadata: [
                'unit_id' => $unitId,
                'reason' => $reason,
                'unassigned_by' => $user?->name,
            ],
            severity: 'high',
            user: $user,
            request: $request
        );
    }

    /**
     * Log NOC application submission
     */
    public static function logNocApplicationSubmitted(Tenant $tenant, string $nocType, int $applicationId, ?User $user = null, ?Request $request = null): TenantHistory
    {
        return self::log(
            tenantId: $tenant->id,
            activityType: 'noc_application_submitted',
            description: "NOC application submitted: {$nocType}",
            metadata: [
                'noc_type' => $nocType,
                'application_id' => $applicationId,
            ],
            severity: 'medium',
            user: $user,
            request: $request
        );
    }

    /**
     * Log NOC status change
     */
    public static function logNocStatusChanged(Tenant $tenant, string $nocType, string $oldStatus, string $newStatus, ?User $user = null, ?Request $request = null): TenantHistory
    {
        return self::log(
            tenantId: $tenant->id,
            activityType: 'noc_status_changed',
            description: "NOC status changed: {$nocType} from {$oldStatus} to {$newStatus}",
            oldValues: ['status' => $oldStatus],
            newValues: ['status' => $newStatus],
            metadata: [
                'noc_type' => $nocType,
            ],
            severity: 'medium',
            user: $user,
            request: $request
        );
    }

    /**
     * Log status change
     */
    public static function logStatusChanged(Tenant $tenant, string $oldStatus, string $newStatus, ?string $reason = null, ?User $user = null, ?Request $request = null): TenantHistory
    {
        return self::log(
            tenantId: $tenant->id,
            activityType: 'status_changed',
            description: "Tenant status changed from {$oldStatus} to {$newStatus}" . ($reason ? " - {$reason}" : ""),
            oldValues: ['status' => $oldStatus],
            newValues: ['status' => $newStatus],
            metadata: $reason ? ['reason' => $reason] : null,
            severity: 'high',
            user: $user,
            request: $request
        );
    }

    /**
     * Log admin action
     */
    public static function logAdminAction(Tenant $tenant, string $action, ?array $metadata = null, ?User $user = null, ?Request $request = null): TenantHistory
    {
        return self::log(
            tenantId: $tenant->id,
            activityType: 'admin_action',
            description: "Admin action performed: {$action}",
            metadata: $metadata,
            severity: 'high',
            user: $user,
            request: $request
        );
    }

    /**
     * Log login activity
     */
    public static function logLoginActivity(Tenant $tenant, ?User $user = null, ?Request $request = null): TenantHistory
    {
        return self::log(
            tenantId: $tenant->id,
            activityType: 'login_activity',
            description: "Tenant logged in",
            severity: 'low',
            user: $user,
            request: $request
        );
    }

    /**
     * Log logout activity
     */
    public static function logLogoutActivity(Tenant $tenant, ?User $user = null, ?Request $request = null): TenantHistory
    {
        return self::log(
            tenantId: $tenant->id,
            activityType: 'logout_activity',
            description: "Tenant logged out",
            severity: 'low',
            user: $user,
            request: $request
        );
    }
} 