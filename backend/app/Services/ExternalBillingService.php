<?php

namespace App\Services;

use App\Models\Unit;
use App\Models\User;
use App\Models\NocApplication;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Cache;
use Illuminate\Http\Client\RequestException;

class ExternalBillingService
{
    protected $config;
    protected $baseUrl;
    protected $apiKey;
    protected $timeout;

    public function __construct()
    {
        $this->config = config('billing.external', [
            'base_url' => env('EXTERNAL_BILLING_BASE_URL'),
            'api_key' => env('EXTERNAL_BILLING_API_KEY'),
            'timeout' => env('EXTERNAL_BILLING_TIMEOUT', 30),
            'cache_duration' => env('EXTERNAL_BILLING_CACHE_DURATION', 3600), // 1 hour
            'retry_attempts' => env('EXTERNAL_BILLING_RETRY_ATTEMPTS', 3),
        ]);

        $this->baseUrl = $this->config['base_url'];
        $this->apiKey = $this->config['api_key'];
        $this->timeout = $this->config['timeout'];
    }

    /**
     * Fetch billing rules from external service
     */
    public function fetchBillingRules(string $chargeType, array $context = []): array
    {
        $cacheKey = "billing_rules_{$chargeType}_" . md5(serialize($context));
        
        return Cache::remember($cacheKey, $this->config['cache_duration'], function () use ($chargeType, $context) {
            return $this->makeApiRequest('GET', '/api/v1/billing-rules', [
                'charge_type' => $chargeType,
                'context' => $context,
            ]);
        });
    }

    /**
     * Calculate charges using external service
     */
    public function calculateCharges(string $chargeType, array $data, array $context = []): array
    {
        try {
            $response = $this->makeApiRequest('POST', '/api/v1/calculate-charges', [
                'charge_type' => $chargeType,
                'data' => $data,
                'context' => $context,
            ]);

            Log::info('External billing calculation completed', [
                'charge_type' => $chargeType,
                'amount' => $response['amount'] ?? 0,
                'breakdown' => $response['breakdown'] ?? [],
            ]);

            return $response;

        } catch (\Exception $e) {
            Log::error('External billing calculation failed', [
                'charge_type' => $chargeType,
                'error' => $e->getMessage(),
                'data' => $data,
            ]);

            // Return fallback calculation
            return $this->getFallbackCalculation($chargeType, $data);
        }
    }

    /**
     * Get non-occupancy charges for a unit
     */
    public function getNonOccupancyCharges(Unit $unit, array $period = []): array
    {
        $data = [
            'unit_id' => $unit->id,
            'unit_number' => $unit->unit_number,
            'block' => $unit->block,
            'floor' => $unit->floor,
            'type' => $unit->type,
            'area_sqft' => $unit->area_sqft,
            'owner_id' => $unit->owner_id,
            'status' => $unit->status,
            'is_owner_occupied' => $unit->is_owner_occupied,
            'period' => $period,
        ];

        $context = [
            'society_id' => config('app.society_id'),
            'calculation_date' => now()->toISOString(),
        ];

        return $this->calculateCharges('non_occupancy', $data, $context);
    }

    /**
     * Get NOC charges for an application
     */
    public function getNocCharges(NocApplication $nocApplication): array
    {
        $data = [
            'noc_id' => $nocApplication->id,
            'noc_type' => $nocApplication->noc_type,
            'unit_id' => $nocApplication->unit_id,
            'applicant_id' => $nocApplication->applicant_id,
            'start_date' => $nocApplication->start_date?->toDateString(),
            'end_date' => $nocApplication->end_date?->toDateString(),
            'purpose' => $nocApplication->purpose,
            'form_data' => $nocApplication->form_data,
        ];

        $context = [
            'society_id' => config('app.society_id'),
            'calculation_date' => now()->toISOString(),
        ];

        return $this->calculateCharges('noc_charges', $data, $context);
    }

    /**
     * Get maintenance charges for a unit
     */
    public function getMaintenanceCharges(Unit $unit, array $period = []): array
    {
        $data = [
            'unit_id' => $unit->id,
            'unit_number' => $unit->unit_number,
            'block' => $unit->block,
            'floor' => $unit->floor,
            'type' => $unit->type,
            'area_sqft' => $unit->area_sqft,
            'owner_id' => $unit->owner_id,
            'current_tenant_id' => $unit->current_tenant_id,
            'period' => $period,
        ];

        $context = [
            'society_id' => config('app.society_id'),
            'calculation_date' => now()->toISOString(),
        ];

        return $this->calculateCharges('maintenance', $data, $context);
    }

    /**
     * Get utility charges for a unit
     */
    public function getUtilityCharges(Unit $unit, array $period = []): array
    {
        $data = [
            'unit_id' => $unit->id,
            'unit_number' => $unit->unit_number,
            'block' => $unit->block,
            'floor' => $unit->floor,
            'type' => $unit->type,
            'area_sqft' => $unit->area_sqft,
            'current_tenant_id' => $unit->current_tenant_id,
            'period' => $period,
        ];

        $context = [
            'society_id' => config('app.society_id'),
            'calculation_date' => now()->toISOString(),
        ];

        return $this->calculateCharges('utilities', $data, $context);
    }

    /**
     * Get parking charges for a unit
     */
    public function getParkingCharges(Unit $unit, array $period = []): array
    {
        $data = [
            'unit_id' => $unit->id,
            'unit_number' => $unit->unit_number,
            'block' => $unit->block,
            'floor' => $unit->floor,
            'type' => $unit->type,
            'owner_id' => $unit->owner_id,
            'current_tenant_id' => $unit->current_tenant_id,
            'period' => $period,
        ];

        $context = [
            'society_id' => config('app.society_id'),
            'calculation_date' => now()->toISOString(),
        ];

        return $this->calculateCharges('parking', $data, $context);
    }

    /**
     * Get all charges for a unit in a period
     */
    public function getAllCharges(Unit $unit, array $period = []): array
    {
        $charges = [];

        // Get non-occupancy charges if applicable
        if ($unit->isEligibleForNonOccupancyCharge()) {
            $charges['non_occupancy'] = $this->getNonOccupancyCharges($unit, $period);
        }

        // Get maintenance charges
        $charges['maintenance'] = $this->getMaintenanceCharges($unit, $period);

        // Get utility charges
        $charges['utilities'] = $this->getUtilityCharges($unit, $period);

        // Get parking charges
        $charges['parking'] = $this->getParkingCharges($unit, $period);

        // Calculate total
        $total = 0;
        foreach ($charges as $type => $charge) {
            $total += $charge['amount'] ?? 0;
        }

        return [
            'charges' => $charges,
            'total_amount' => $total,
            'currency' => 'INR',
            'calculation_date' => now()->toISOString(),
            'period' => $period,
        ];
    }

    /**
     * Create billing record in external service
     */
    public function createBillingRecord(array $data): array
    {
        return $this->makeApiRequest('POST', '/api/v1/billing-records', $data);
    }

    /**
     * Update billing record in external service
     */
    public function updateBillingRecord(string $recordId, array $data): array
    {
        return $this->makeApiRequest('PUT', "/api/v1/billing-records/{$recordId}", $data);
    }

    /**
     * Get billing record from external service
     */
    public function getBillingRecord(string $recordId): array
    {
        return $this->makeApiRequest('GET', "/api/v1/billing-records/{$recordId}");
    }

    /**
     * Get billing history for a unit
     */
    public function getBillingHistory(Unit $unit, array $filters = []): array
    {
        $params = array_merge([
            'unit_id' => $unit->id,
        ], $filters);

        return $this->makeApiRequest('GET', '/api/v1/billing-history', $params);
    }

    /**
     * Make API request to external billing service
     */
    protected function makeApiRequest(string $method, string $endpoint, array $data = []): array
    {
        $url = rtrim($this->baseUrl, '/') . $endpoint;
        
        $headers = [
            'Authorization' => 'Bearer ' . $this->apiKey,
            'Content-Type' => 'application/json',
            'Accept' => 'application/json',
            'X-Request-ID' => uniqid(),
            'X-Source' => 'tms',
        ];

        $attempts = 0;
        $maxAttempts = $this->config['retry_attempts'];

        while ($attempts < $maxAttempts) {
            try {
                $response = Http::timeout($this->timeout)
                    ->withHeaders($headers)
                    ->$method($url, $data);

                if ($response->successful()) {
                    return $response->json();
                }

                Log::warning('External billing API request failed', [
                    'method' => $method,
                    'endpoint' => $endpoint,
                    'status' => $response->status(),
                    'response' => $response->body(),
                    'attempt' => $attempts + 1,
                ]);

                if ($response->status() >= 400 && $response->status() < 500) {
                    // Client error, don't retry
                    throw new \Exception('External billing API client error: ' . $response->status());
                }

            } catch (RequestException $e) {
                Log::error('External billing API request exception', [
                    'method' => $method,
                    'endpoint' => $endpoint,
                    'error' => $e->getMessage(),
                    'attempt' => $attempts + 1,
                ]);

                if ($attempts === $maxAttempts - 1) {
                    throw $e;
                }
            }

            $attempts++;
            
            if ($attempts < $maxAttempts) {
                sleep(pow(2, $attempts)); // Exponential backoff
            }
        }

        throw new \Exception('External billing API request failed after ' . $maxAttempts . ' attempts');
    }

    /**
     * Get fallback calculation when external service is unavailable
     */
    protected function getFallbackCalculation(string $chargeType, array $data): array
    {
        Log::warning('Using fallback calculation for external billing service', [
            'charge_type' => $chargeType,
            'data' => $data,
        ]);

        return match ($chargeType) {
            'non_occupancy' => $this->getFallbackNonOccupancyCalculation($data),
            'noc_charges' => $this->getFallbackNocCalculation($data),
            'maintenance' => $this->getFallbackMaintenanceCalculation($data),
            'utilities' => $this->getFallbackUtilitiesCalculation($data),
            'parking' => $this->getFallbackParkingCalculation($data),
            default => [
                'amount' => 0,
                'breakdown' => [],
                'currency' => 'INR',
                'calculation_method' => 'fallback',
            ],
        };
    }

    /**
     * Fallback calculation for non-occupancy charges
     */
    protected function getFallbackNonOccupancyCalculation(array $data): array
    {
        $areaSqft = $data['area_sqft'] ?? 1000;
        $baseRate = 2; // Rs. 2 per sq ft per month
        
        $amount = $areaSqft * $baseRate;

        return [
            'amount' => $amount,
            'breakdown' => [
                'base_rate' => $baseRate,
                'area_sqft' => $areaSqft,
                'calculation' => "{$areaSqft} sq ft × Rs. {$baseRate}",
            ],
            'currency' => 'INR',
            'calculation_method' => 'fallback',
        ];
    }

    /**
     * Fallback calculation for NOC charges
     */
    protected function getFallbackNocCalculation(array $data): array
    {
        $nocType = $data['noc_type'] ?? 'rental';
        $startDate = $data['start_date'] ?? now();
        $endDate = $data['end_date'] ?? now()->addMonth();
        
        $durationMonths = \Carbon\Carbon::parse($startDate)->diffInMonths(\Carbon\Carbon::parse($endDate));
        
        $baseRates = [
            'rental' => 500,
            'residence' => 300,
            'vehicle' => 200,
            'renovation' => 400,
            'transfer' => 600,
        ];
        
        $baseRate = $baseRates[$nocType] ?? 250;
        $amount = $durationMonths * $baseRate;

        return [
            'amount' => $amount,
            'breakdown' => [
                'noc_type' => $nocType,
                'duration_months' => $durationMonths,
                'base_rate' => $baseRate,
                'calculation' => "{$durationMonths} months × Rs. {$baseRate}",
            ],
            'currency' => 'INR',
            'calculation_method' => 'fallback',
        ];
    }

    /**
     * Fallback calculation for maintenance charges
     */
    protected function getFallbackMaintenanceCalculation(array $data): array
    {
        $areaSqft = $data['area_sqft'] ?? 1000;
        $baseRate = 1.5; // Rs. 1.5 per sq ft per month
        
        $amount = $areaSqft * $baseRate;

        return [
            'amount' => $amount,
            'breakdown' => [
                'base_rate' => $baseRate,
                'area_sqft' => $areaSqft,
                'calculation' => "{$areaSqft} sq ft × Rs. {$baseRate}",
            ],
            'currency' => 'INR',
            'calculation_method' => 'fallback',
        ];
    }

    /**
     * Fallback calculation for utility charges
     */
    protected function getFallbackUtilitiesCalculation(array $data): array
    {
        $areaSqft = $data['area_sqft'] ?? 1000;
        $baseRate = 0.5; // Rs. 0.5 per sq ft per month
        
        $amount = $areaSqft * $baseRate;

        return [
            'amount' => $amount,
            'breakdown' => [
                'base_rate' => $baseRate,
                'area_sqft' => $areaSqft,
                'calculation' => "{$areaSqft} sq ft × Rs. {$baseRate}",
            ],
            'currency' => 'INR',
            'calculation_method' => 'fallback',
        ];
    }

    /**
     * Fallback calculation for parking charges
     */
    protected function getFallbackParkingCalculation(array $data): array
    {
        $baseRate = 200; // Rs. 200 per month for parking
        
        return [
            'amount' => $baseRate,
            'breakdown' => [
                'base_rate' => $baseRate,
                'calculation' => "Parking charge: Rs. {$baseRate}",
            ],
            'currency' => 'INR',
            'calculation_method' => 'fallback',
        ];
    }

    /**
     * Check if external billing service is available
     */
    public function isServiceAvailable(): bool
    {
        try {
            $response = $this->makeApiRequest('GET', '/api/v1/health');
            return isset($response['status']) && $response['status'] === 'healthy';
        } catch (\Exception $e) {
            Log::warning('External billing service health check failed', [
                'error' => $e->getMessage(),
            ]);
            return false;
        }
    }

    /**
     * Get service status and configuration
     */
    public function getServiceStatus(): array
    {
        return [
            'available' => $this->isServiceAvailable(),
            'base_url' => $this->baseUrl,
            'timeout' => $this->timeout,
            'cache_duration' => $this->config['cache_duration'],
            'retry_attempts' => $this->config['retry_attempts'],
        ];
    }
} 