<?php

namespace App\Services;

use App\Models\RentPayment;
use App\Models\User;
use App\Notifications\PaymentReceivedNotification;
use App\Notifications\PaymentReconciliationFailedNotification;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Notification;

class RentPaymentService
{
    protected ExternalPaymentService $externalPaymentService;

    public function __construct(ExternalPaymentService $externalPaymentService)
    {
        $this->externalPaymentService = $externalPaymentService;
    }

    /**
     * Reconcile external payments with local records.
     *
     * @param array $params
     * @return array
     */
    public function reconcilePayments(array $params = []): array
    {
        try {
            $externalPayments = $this->externalPaymentService->fetchPayments($params);
            $reconciled = [];
            $discrepancies = [];

            foreach ($externalPayments as $externalPayment) {
                $result = $this->processPayment($externalPayment);
                if ($result['success']) {
                    $reconciled[] = $result['payment'];
                } else {
                    $discrepancies[] = $result['error'];
                }
            }

            Log::info('Payment reconciliation completed', [
                'reconciled_count' => count($reconciled),
                'discrepancy_count' => count($discrepancies),
            ]);

            return [
                'reconciled' => $reconciled,
                'discrepancies' => $discrepancies,
            ];
        } catch (\Exception $e) {
            Log::error('Payment reconciliation failed', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
            ]);

            // Notify admins about reconciliation failure
            $this->notifyReconciliationFailure($e->getMessage());

            return [
                'reconciled' => [],
                'discrepancies' => ['Reconciliation failed: ' . $e->getMessage()],
            ];
        }
    }

    /**
     * Process a single external payment.
     *
     * @param array $externalPayment
     * @return array
     */
    protected function processPayment(array $externalPayment): array
    {
        $externalReference = $externalPayment['external_reference'] ?? null;

        if (!$externalReference) {
            return [
                'success' => false,
                'error' => 'Missing external reference',
            ];
        }

        // Check if payment already exists
        $existingPayment = RentPayment::where('external_reference', $externalReference)->first();

        if ($existingPayment) {
            return $this->updateExistingPayment($existingPayment, $externalPayment);
        }

        return $this->createNewPayment($externalPayment);
    }

    /**
     * Update an existing payment record.
     *
     * @param RentPayment $payment
     * @param array $externalPayment
     * @return array
     */
    protected function updateExistingPayment(RentPayment $payment, array $externalPayment): array
    {
        $payment->update([
            'status' => $externalPayment['status'] ?? $payment->status,
            'metadata' => array_merge($payment->metadata ?? [], $externalPayment),
            'reconciled_at' => now(),
        ]);

        return [
            'success' => true,
            'payment' => $payment,
        ];
    }

    /**
     * Create a new payment record.
     *
     * @param array $externalPayment
     * @return array
     */
    protected function createNewPayment(array $externalPayment): array
    {
        try {
            $payment = RentPayment::create([
                'unit_id' => $externalPayment['unit_id'],
                'tenant_id' => $externalPayment['tenant_id'],
                'amount' => $externalPayment['amount'],
                'payment_date' => $externalPayment['payment_date'],
                'payment_method' => $externalPayment['payment_method'] ?? null,
                'external_reference' => $externalPayment['external_reference'],
                'status' => $externalPayment['status'] ?? 'pending',
                'metadata' => $externalPayment,
                'reconciled_at' => now(),
            ]);

            // Notify about new payment
            $this->notifyPaymentReceived($payment);

            return [
                'success' => true,
                'payment' => $payment,
            ];
        } catch (\Exception $e) {
            Log::error('Failed to create payment record', [
                'external_payment' => $externalPayment,
                'error' => $e->getMessage(),
            ]);

            return [
                'success' => false,
                'error' => 'Failed to create payment record: ' . $e->getMessage(),
            ];
        }
    }

    /**
     * Notify about payment received.
     *
     * @param RentPayment $payment
     * @return void
     */
    protected function notifyPaymentReceived(RentPayment $payment): void
    {
        try {
            // Notify tenant
            $tenant = $payment->tenant;
            if ($tenant) {
                $tenant->notify(new PaymentReceivedNotification($payment));
            }

            // Notify unit owner (if different from tenant)
            $unit = $payment->unit;
            if ($unit && $unit->owner && $unit->owner->id !== $tenant->id) {
                $unit->owner->notify(new PaymentReceivedNotification($payment));
            }
        } catch (\Exception $e) {
            Log::error('Failed to send payment received notification', [
                'payment_id' => $payment->id,
                'error' => $e->getMessage(),
            ]);
        }
    }

    /**
     * Notify about reconciliation failure.
     *
     * @param string $error
     * @return void
     */
    protected function notifyReconciliationFailure(string $error): void
    {
        try {
            // Notify admins
            $admins = User::where('role', 'admin')->get();
            Notification::send($admins, new PaymentReconciliationFailedNotification($error));
        } catch (\Exception $e) {
            Log::error('Failed to send reconciliation failure notification', [
                'error' => $e->getMessage(),
            ]);
        }
    }
} 