<?php

namespace App\Services;

use App\Models\Notification;
use App\Models\NotificationTemplate;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Http;

class SmsNotificationService
{
    protected $config;
    protected $providers;

    public function __construct()
    {
        $this->config = config('notifications.sms', [
            'default_provider' => 'twilio',
            'providers' => [
                'twilio' => [
                    'account_sid' => env('TWILIO_ACCOUNT_SID'),
                    'auth_token' => env('TWILIO_AUTH_TOKEN'),
                    'from_number' => env('TWILIO_FROM_NUMBER'),
                    'enabled' => env('TWILIO_ENABLED', false),
                ],
                'aws_sns' => [
                    'access_key_id' => env('AWS_ACCESS_KEY_ID'),
                    'secret_access_key' => env('AWS_SECRET_ACCESS_KEY'),
                    'region' => env('AWS_DEFAULT_REGION', 'us-east-1'),
                    'enabled' => env('AWS_SNS_ENABLED', false),
                ],
                'vonage' => [
                    'api_key' => env('VONAGE_API_KEY'),
                    'api_secret' => env('VONAGE_API_SECRET'),
                    'from_number' => env('VONAGE_FROM_NUMBER'),
                    'enabled' => env('VONAGE_ENABLED', false),
                ],
            ],
        ]);

        $this->providers = $this->config['providers'];
    }

    /**
     * Send SMS notification
     */
    public function send(Notification $notification): bool
    {
        try {
            $phoneNumber = $this->getPhoneNumber($notification);
            if (!$phoneNumber) {
                Log::error('SMS notification failed: No phone number found', [
                    'notification_id' => $notification->id,
                    'user_id' => $notification->user_id,
                ]);
                return false;
            }

            $message = $this->prepareMessage($notification);
            $provider = $this->getActiveProvider();

            if (!$provider) {
                Log::error('SMS notification failed: No active provider found');
                return false;
            }

            $result = $this->sendViaProvider($provider, $phoneNumber, $message, $notification);

            if ($result) {
                $notification->markAsSent();
                $notification->update([
                    'delivery_log' => array_merge($notification->delivery_log ?? [], [
                        [
                            'provider' => $provider,
                            'status' => 'sent',
                            'timestamp' => now()->toISOString(),
                        ]
                    ])
                ]);
                return true;
            }

            return false;

        } catch (\Exception $e) {
            Log::error('SMS notification failed', [
                'notification_id' => $notification->id,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
            ]);

            $notification->markAsFailed($e->getMessage());
            return false;
        }
    }

    /**
     * Send SMS via specific provider
     */
    protected function sendViaProvider(string $provider, string $phoneNumber, string $message, Notification $notification): bool
    {
        switch ($provider) {
            case 'twilio':
                return $this->sendViaTwilio($phoneNumber, $message, $notification);
            case 'aws_sns':
                return $this->sendViaAwsSns($phoneNumber, $message, $notification);
            case 'vonage':
                return $this->sendViaVonage($phoneNumber, $message, $notification);
            default:
                Log::error("Unknown SMS provider: {$provider}");
                return false;
        }
    }

    /**
     * Send SMS via Twilio
     */
    protected function sendViaTwilio(string $phoneNumber, string $message, Notification $notification): bool
    {
        try {
            $config = $this->providers['twilio'];
            
            if (!$config['enabled']) {
                Log::warning('Twilio SMS provider is disabled');
                return false;
            }

            $response = Http::withBasicAuth($config['account_sid'], $config['auth_token'])
                ->post("https://api.twilio.com/2010-04-01/Accounts/{$config['account_sid']}/Messages.json", [
                    'From' => $config['from_number'],
                    'To' => $phoneNumber,
                    'Body' => $message,
                ]);

            if ($response->successful()) {
                $data = $response->json();
                Log::info('SMS sent via Twilio', [
                    'notification_id' => $notification->id,
                    'message_sid' => $data['sid'] ?? null,
                    'status' => $data['status'] ?? null,
                ]);
                return true;
            }

            Log::error('Twilio SMS failed', [
                'notification_id' => $notification->id,
                'response' => $response->body(),
                'status' => $response->status(),
            ]);

            return false;

        } catch (\Exception $e) {
            Log::error('Twilio SMS exception', [
                'notification_id' => $notification->id,
                'error' => $e->getMessage(),
            ]);
            return false;
        }
    }

    /**
     * Send SMS via AWS SNS
     */
    protected function sendViaAwsSns(string $phoneNumber, string $message, Notification $notification): bool
    {
        try {
            $config = $this->providers['aws_sns'];
            
            if (!$config['enabled']) {
                Log::warning('AWS SNS SMS provider is disabled');
                return false;
            }

            // This would require AWS SDK to be properly configured
            // For now, we'll log the attempt
            Log::info('SMS would be sent via AWS SNS', [
                'notification_id' => $notification->id,
                'phone_number' => $phoneNumber,
                'message' => $message,
            ]);

            return true; // Placeholder - implement actual AWS SNS integration

        } catch (\Exception $e) {
            Log::error('AWS SNS SMS exception', [
                'notification_id' => $notification->id,
                'error' => $e->getMessage(),
            ]);
            return false;
        }
    }

    /**
     * Send SMS via Vonage
     */
    protected function sendViaVonage(string $phoneNumber, string $message, Notification $notification): bool
    {
        try {
            $config = $this->providers['vonage'];
            
            if (!$config['enabled']) {
                Log::warning('Vonage SMS provider is disabled');
                return false;
            }

            $response = Http::post('https://rest.nexmo.com/sms/json', [
                'api_key' => $config['api_key'],
                'api_secret' => $config['api_secret'],
                'from' => $config['from_number'],
                'to' => $phoneNumber,
                'text' => $message,
            ]);

            if ($response->successful()) {
                $data = $response->json();
                if (isset($data['messages'][0]['status']) && $data['messages'][0]['status'] === '0') {
                    Log::info('SMS sent via Vonage', [
                        'notification_id' => $notification->id,
                        'message_id' => $data['messages'][0]['message-id'] ?? null,
                    ]);
                    return true;
                }
            }

            Log::error('Vonage SMS failed', [
                'notification_id' => $notification->id,
                'response' => $response->body(),
            ]);

            return false;

        } catch (\Exception $e) {
            Log::error('Vonage SMS exception', [
                'notification_id' => $notification->id,
                'error' => $e->getMessage(),
            ]);
            return false;
        }
    }

    /**
     * Get phone number from notification
     */
    protected function getPhoneNumber(Notification $notification): ?string
    {
        $user = $notification->user;
        
        if (!$user) {
            return null;
        }

        // Try to get phone number from user model
        if ($user->phone) {
            return $this->formatPhoneNumber($user->phone);
        }

        // Try to get from notification data
        $data = $notification->data;
        if (isset($data['phone_number'])) {
            return $this->formatPhoneNumber($data['phone_number']);
        }

        return null;
    }

    /**
     * Format phone number for SMS
     */
    protected function formatPhoneNumber(string $phoneNumber): string
    {
        // Remove all non-digit characters
        $cleaned = preg_replace('/[^0-9]/', '', $phoneNumber);
        
        // Add country code if not present (assuming +91 for India)
        if (strlen($cleaned) === 10) {
            $cleaned = '91' . $cleaned;
        }
        
        return '+' . $cleaned;
    }

    /**
     * Prepare message content
     */
    protected function prepareMessage(Notification $notification): string
    {
        $data = $notification->data ?? [];
        
        // If template exists, render it
        if ($notification->template_id) {
            $template = NotificationTemplate::find($notification->template_id);
            if ($template && $template->type === 'sms') {
                return $this->renderTemplate($template->content, $data);
            }
        }

        // Fallback to notification message
        $message = $notification->message;
        
        // Replace variables in message
        foreach ($data as $key => $value) {
            $message = str_replace("{{" . $key . "}}", $value, $message);
        }

        // Truncate if too long (SMS limit)
        if (strlen($message) > 160) {
            $message = substr($message, 0, 157) . '...';
        }

        return $message;
    }

    /**
     * Render template with variables
     */
    protected function renderTemplate(string $template, array $variables): string
    {
        foreach ($variables as $key => $value) {
            $template = str_replace("{{" . $key . "}}", $value, $template);
        }
        
        return $template;
    }

    /**
     * Get active SMS provider
     */
    protected function getActiveProvider(): ?string
    {
        $defaultProvider = $this->config['default_provider'];
        
        if (isset($this->providers[$defaultProvider]) && $this->providers[$defaultProvider]['enabled']) {
            return $defaultProvider;
        }

        // Try to find any enabled provider
        foreach ($this->providers as $name => $config) {
            if ($config['enabled']) {
                return $name;
            }
        }

        return null;
    }

    /**
     * Check if SMS notifications are enabled
     */
    public function isEnabled(): bool
    {
        return $this->getActiveProvider() !== null;
    }

    /**
     * Get available providers
     */
    public function getProviders(): array
    {
        return array_keys($this->providers);
    }

    /**
     * Get provider status
     */
    public function getProviderStatus(): array
    {
        $status = [];
        
        foreach ($this->providers as $name => $config) {
            $status[$name] = [
                'enabled' => $config['enabled'] ?? false,
                'configured' => $this->isProviderConfigured($name),
            ];
        }
        
        return $status;
    }

    /**
     * Check if provider is configured
     */
    protected function isProviderConfigured(string $provider): bool
    {
        $config = $this->providers[$provider] ?? [];
        
        switch ($provider) {
            case 'twilio':
                return !empty($config['account_sid']) && !empty($config['auth_token']) && !empty($config['from_number']);
            case 'aws_sns':
                return !empty($config['access_key_id']) && !empty($config['secret_access_key']) && !empty($config['region']);
            case 'vonage':
                return !empty($config['api_key']) && !empty($config['api_secret']) && !empty($config['from_number']);
            default:
                return false;
        }
    }
} 