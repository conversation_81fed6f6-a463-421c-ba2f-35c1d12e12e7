<?php

namespace App\Events\Sync;

class AgreementUpdated extends BaseSyncEvent
{
    /**
     * Get the entity type for this event.
     */
    protected function getEntityType(): string
    {
        return 'Agreement';
    }

    /**
     * Get the syncable fields for this entity.
     */
    protected function getSyncableFields(): array
    {
        return config('sync.entities.Agreement.fields', [
            'id', 'tenant_id', 'unit_id', 'template_id', 'status',
            'start_date', 'end_date', 'signed_at', 'signed_by_owner',
            'signed_by_tenant', 'created_at', 'updated_at'
        ]);
    }

    /**
     * Extract society ID from entity data.
     */
    protected function extractSocietyId(array $entityData): ?int
    {
        // For agreements, we need to get society_id from the unit relationship
        if (isset($entityData['unit']['society_id'])) {
            return $entityData['unit']['society_id'];
        }

        // If unit data is not included, we'll need to fetch it
        if (isset($entityData['unit_id'])) {
            $unit = \App\Models\Unit::find($entityData['unit_id']);
            return $unit?->society_id;
        }

        return null;
    }
}
