<?php

namespace App\Events\Sync;

class SocietyUpdated extends BaseSyncEvent
{
    /**
     * Get the entity type for this event.
     */
    protected function getEntityType(): string
    {
        return 'Society';
    }

    /**
     * Get the syncable fields for this entity.
     */
    protected function getSyncableFields(): array
    {
        return config('sync.entities.Society.fields', [
            'id', 'name', 'address', 'city', 'state', 'pincode',
            'contact_person', 'contact_phone', 'contact_email',
            'total_units', 'status', 'created_at', 'updated_at'
        ]);
    }

    /**
     * Extract society ID from entity data.
     */
    protected function extractSocietyId(array $entityData): ?int
    {
        return $entityData['id'] ?? null;
    }
}
