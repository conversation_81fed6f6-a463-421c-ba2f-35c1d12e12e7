<?php

namespace App\Events\Sync;

class TenantUpdated extends BaseSyncEvent
{
    /**
     * Get the entity type for this event.
     */
    protected function getEntityType(): string
    {
        return 'Tenant';
    }

    /**
     * Get the syncable fields for this entity.
     */
    protected function getSyncableFields(): array
    {
        return config('sync.entities.Tenant.fields', [
            'id', 'user_id', 'tenant_code', 'unit_id', 'owner_id',
            'occupation', 'company_name', 'monthly_income',
            'security_deposit', 'advance_amount', 'move_in_date',
            'move_out_date', 'agreement_start_date', 'agreement_end_date',
            'kyc_status', 'status', 'created_at', 'updated_at'
        ]);
    }

    /**
     * Extract society ID from entity data.
     */
    protected function extractSocietyId(array $entityData): ?int
    {
        // For tenants, we need to get society_id from the unit relationship
        if (isset($entityData['unit']['society_id'])) {
            return $entityData['unit']['society_id'];
        }

        // If unit data is not included, we'll need to fetch it
        if (isset($entityData['unit_id'])) {
            $unit = \App\Models\Unit::find($entityData['unit_id']);
            return $unit?->society_id;
        }

        return null;
    }
}
