<?php

namespace App\Events\Sync;

class PaymentUpdated extends BaseSyncEvent
{
    /**
     * Get the entity type for this event.
     */
    protected function getEntityType(): string
    {
        return 'RentPayment';
    }

    /**
     * Get the syncable fields for this entity.
     */
    protected function getSyncableFields(): array
    {
        return config('sync.entities.RentPayment.fields', [
            'id', 'unit_id', 'tenant_id', 'amount', 'payment_date',
            'payment_method', 'external_reference', 'status',
            'reconciled_at', 'created_at', 'updated_at'
        ]);
    }

    /**
     * Extract society ID from entity data.
     */
    protected function extractSocietyId(array $entityData): ?int
    {
        // For payments, we need to get society_id from the unit relationship
        if (isset($entityData['unit']['society_id'])) {
            return $entityData['unit']['society_id'];
        }

        // If unit data is not included, we'll need to fetch it
        if (isset($entityData['unit_id'])) {
            $unit = \App\Models\Unit::find($entityData['unit_id']);
            return $unit?->society_id;
        }

        return null;
    }
}
