<?php

namespace App\Events\Sync;

class EntityDeleted extends BaseSyncEvent
{
    public string $entityType;

    /**
     * Create a new event instance.
     */
    public function __construct(
        string $entityType,
        int $entityId,
        ?int $societyId = null,
        ?string $correlationId = null,
        ?array $metadata = null
    ) {
        $this->entityType = $entityType;
        
        // Create minimal entity data for deletion
        $entityData = [
            'id' => $entityId,
            'deleted_at' => now()->toISOString(),
        ];
        
        if ($societyId) {
            $entityData['society_id'] = $societyId;
        }

        parent::__construct($entityData, 'deleted', $correlationId, $metadata);
        
        // Override society ID if provided
        if ($societyId !== null) {
            $this->societyId = $societyId;
        }
    }

    /**
     * Get the entity type for this event.
     */
    protected function getEntityType(): string
    {
        return $this->entityType;
    }

    /**
     * Get the syncable fields for this entity.
     */
    protected function getSyncableFields(): array
    {
        // For deletions, we only need the ID and deletion timestamp
        return ['id', 'deleted_at', 'society_id'];
    }

    /**
     * Extract society ID from entity data.
     */
    protected function extractSocietyId(array $entityData): ?int
    {
        // Use the provided society ID or try to extract from entity data
        return $entityData['society_id'] ?? $this->societyId;
    }

    /**
     * Determine if the entity should be synchronized.
     */
    public function shouldSync(): bool
    {
        // Always sync deletions if sync is enabled
        if (!config('sync.enabled', true)) {
            return false;
        }

        // Check if deletion operation is configured for sync
        $entityConfig = config("sync.entities.{$this->entityType}", []);
        $syncOperations = $entityConfig['sync_on'] ?? [];
        
        return in_array('deleted', $syncOperations);
    }
}
