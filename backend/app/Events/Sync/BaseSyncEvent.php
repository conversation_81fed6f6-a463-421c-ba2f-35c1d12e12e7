<?php

namespace App\Events\Sync;

use Illuminate\Broadcasting\InteractsWithSockets;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Events\Dispatchable;
use Illuminate\Queue\SerializesModels;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Support\Arr;
use Carbon\Carbon;

abstract class BaseSyncEvent implements ShouldQueue
{
    use Dispatchable, InteractsWithSockets, SerializesModels, InteractsWithQueue;

    public array $entityData;
    public ?int $societyId;
    public string $operation;
    public string $timestamp;
    public string $sourceSystem;
    public string $syncVersion;
    public string $correlationId;
    public ?array $metadata;

    /**
     * The number of times the job may be attempted.
     */
    public int $tries = 3;

    /**
     * The number of seconds to wait before retrying the job.
     */
    public array $backoff = [10, 30, 60];

    /**
     * The maximum number of seconds the job can run.
     */
    public int $timeout = 120;

    /**
     * Create a new event instance.
     */
    public function __construct(
        array $entityData,
        string $operation = 'updated',
        ?string $correlationId = null,
        ?array $metadata = null
    ) {
        $this->entityData = $this->filterSyncableFields($entityData);
        $this->societyId = $this->extractSocietyId($entityData);
        $this->operation = $operation;
        $this->timestamp = now()->toISOString();
        $this->sourceSystem = config('sync.source_system', 'tms');
        $this->syncVersion = $entityData['updated_at'] ?? now()->toISOString();
        $this->correlationId = $correlationId ?? $this->generateCorrelationId();
        $this->metadata = $metadata;

        // Removed onQueue and onConnection calls (handled by listeners/jobs, not events)
    }

    /**
     * Get the entity type for this event.
     */
    abstract protected function getEntityType(): string;

    /**
     * Get the syncable fields for this entity.
     */
    abstract protected function getSyncableFields(): array;

    /**
     * Filter entity data to only include syncable fields.
     */
    protected function filterSyncableFields(array $entityData): array
    {
        $syncableFields = $this->getSyncableFields();
        $filtered = Arr::only($entityData, $syncableFields);
        
        // Ensure required fields are present
        $requiredFields = ['id', 'created_at', 'updated_at'];
        foreach ($requiredFields as $field) {
            if (!isset($filtered[$field]) && isset($entityData[$field])) {
                $filtered[$field] = $entityData[$field];
            }
        }
        
        return $filtered;
    }

    /**
     * Extract society ID from entity data.
     */
    protected function extractSocietyId(array $entityData): ?int
    {
        // Try different possible society ID fields
        $societyFields = ['society_id', 'unit.society_id', 'tenant.unit.society_id'];
        
        foreach ($societyFields as $field) {
            $value = Arr::get($entityData, $field);
            if ($value !== null) {
                return (int) $value;
            }
        }
        
        return null;
    }

    /**
     * Generate a correlation ID for tracking related events.
     */
    protected function generateCorrelationId(): string
    {
        return 'sync_' . $this->getEntityType() . '_' . uniqid() . '_' . time();
    }

    /**
     * Get the queue name for this event.
     */
    protected function getQueueName(): string
    {
        $environment = app()->environment();
        $prefix = config("sync.environments.{$environment}.queue_prefix", '');
        $baseQueue = config('sync.queue.name', 'tms_sync');
        
        return $prefix . $baseQueue . '_' . strtolower($this->getEntityType());
    }

    /**
     * Determine if the entity should be synchronized.
     */
    public function shouldSync(): bool
    {
        // Check if sync is enabled
        if (!config('sync.enabled', true)) {
            return false;
        }

        // Check if this operation is configured for sync
        $entityConfig = config("sync.entities.{$this->getEntityType()}", []);
        $syncOperations = $entityConfig['sync_on'] ?? [];
        
        if (!in_array($this->operation, $syncOperations)) {
            return false;
        }

        // Check if we have a valid society ID for tenant isolation
        if (config('sync.security.validate_society_access', true) && $this->societyId === null) {
            return false;
        }

        return true;
    }

    /**
     * Get the event payload for logging.
     */
    public function getPayload(): array
    {
        return [
            'entity_type' => $this->getEntityType(),
            'entity_data' => $this->entityData,
            'society_id' => $this->societyId,
            'operation' => $this->operation,
            'timestamp' => $this->timestamp,
            'source_system' => $this->sourceSystem,
            'sync_version' => $this->syncVersion,
            'correlation_id' => $this->correlationId,
            'metadata' => $this->metadata,
        ];
    }

    /**
     * Handle a job failure.
     */
    public function failed(\Throwable $exception): void
    {
        \Log::error('Sync event failed', [
            'event' => static::class,
            'entity_type' => $this->getEntityType(),
            'entity_id' => $this->entityData['id'] ?? null,
            'operation' => $this->operation,
            'correlation_id' => $this->correlationId,
            'error' => $exception->getMessage(),
            'trace' => $exception->getTraceAsString(),
        ]);

        // Create audit log entry for the failure
        \App\Models\SyncAuditLog::createSyncLog(
            $this->getEntityType(),
            $this->entityData['id'] ?? null,
            $this->operation,
            $this->sourceSystem,
            $this->entityData,
            \App\Models\SyncAuditLog::STATUS_FAILED,
            $this->correlationId,
            [
                'error_message' => $exception->getMessage(),
                'error_trace' => $exception->getTraceAsString(),
                'failed_at' => now()->toISOString(),
            ]
        );
    }

    /**
     * Get the tags that should be assigned to the job.
     */
    public function tags(): array
    {
        return [
            'sync',
            'entity:' . strtolower($this->getEntityType()),
            'operation:' . $this->operation,
            'source:' . $this->sourceSystem,
            'society:' . ($this->societyId ?? 'unknown'),
        ];
    }

    /**
     * Calculate the number of seconds to wait before retrying the job.
     */
    public function backoff(): array
    {
        return $this->backoff;
    }

    /**
     * Determine the time at which the job should timeout.
     */
    public function retryUntil(): \DateTime
    {
        return now()->addMinutes(10);
    }
}
