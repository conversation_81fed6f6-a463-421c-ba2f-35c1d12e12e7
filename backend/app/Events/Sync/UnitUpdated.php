<?php

namespace App\Events\Sync;

class UnitUpdated extends BaseSyncEvent
{
    /**
     * Get the entity type for this event.
     */
    protected function getEntityType(): string
    {
        return 'Unit';
    }

    /**
     * Get the syncable fields for this entity.
     */
    protected function getSyncableFields(): array
    {
        return config('sync.entities.Unit.fields', [
            'id', 'society_id', 'unit_number', 'block', 'floor', 'wing',
            'type', 'bedrooms', 'bathrooms', 'area_sqft', 'status',
            'owner_id', 'current_tenant_id', 'is_sublet', 'is_owner_occupied',
            'market_rent', 'security_deposit', 'created_at', 'updated_at'
        ]);
    }
}
