<?php

namespace App\Events;

use App\Models\Unit;
use App\Models\User;
use Illuminate\Broadcasting\Channel;
use Illuminate\Broadcasting\InteractsWithSockets;
use Illuminate\Broadcasting\PresenceChannel;
use Illuminate\Broadcasting\PrivateChannel;
use Illuminate\Contracts\Broadcasting\ShouldBroadcast;
use Illuminate\Foundation\Events\Dispatchable;
use Illuminate\Queue\SerializesModels;

class UnitStatusChanged
{
    use Dispatchable, InteractsWithSockets, SerializesModels;

    public Unit $unit;
    public string $previousStatus;
    public string $newStatus;
    public ?User $changedBy;
    public ?string $reason;

    /**
     * Create a new event instance.
     */
    public function __construct(Unit $unit, string $previousStatus, string $newStatus, ?User $changedBy = null, ?string $reason = null)
    {
        $this->unit = $unit;
        $this->previousStatus = $previousStatus;
        $this->newStatus = $newStatus;
        $this->changedBy = $changedBy;
        $this->reason = $reason;
    }

    /**
     * Get the channels the event should broadcast on.
     *
     * @return array<int, \Illuminate\Broadcasting\Channel>
     */
    public function broadcastOn(): array
    {
        return [
            new PrivateChannel('unit-status.' . $this->unit->id),
            new Channel('unit-status-public'),
        ];
    }

    /**
     * Get the data to broadcast.
     *
     * @return array
     */
    public function broadcastWith(): array
    {
        return [
            'unit_id' => $this->unit->id,
            'unit_number' => $this->unit->unit_number,
            'previous_status' => $this->previousStatus,
            'new_status' => $this->newStatus,
            'changed_by' => $this->changedBy ? $this->changedBy->name : 'System',
            'reason' => $this->reason,
            'timestamp' => now()->toISOString(),
        ];
    }
} 