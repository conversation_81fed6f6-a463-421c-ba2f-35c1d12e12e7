<?php

namespace App\Events;

use Illuminate\Broadcasting\Channel;
use Illuminate\Broadcasting\InteractsWithSockets;
use Illuminate\Broadcasting\PresenceChannel;
use Illuminate\Broadcasting\PrivateChannel;
use Illuminate\Contracts\Broadcasting\ShouldBroadcast;
use Illuminate\Foundation\Events\Dispatchable;
use Illuminate\Queue\SerializesModels;

class NocChargesNotificationEvent implements ShouldBroadcast
{
    use Dispatchable, InteractsWithSockets, SerializesModels;

    public $nocData;
    public $action;
    public $timestamp;

    /**
     * Create a new event instance.
     */
    public function __construct(array $nocData, string $action = 'apply_charges')
    {
        $this->nocData = $nocData;
        $this->action = $action;
        $this->timestamp = now()->toISOString();
    }

    /**
     * Get the channels the event should broadcast on.
     *
     * @return array<int, \Illuminate\Broadcasting\Channel>
     */
    public function broadcastOn(): array
    {
        return [
            new Channel('onesociety-noc-charges'),
        ];
    }

    /**
     * Get the data to broadcast.
     */
    public function broadcastWith(): array
    {
        return [
            'event_type' => 'noc_charges_notification',
            'action' => $this->action,
            'data' => $this->nocData,
            'timestamp' => $this->timestamp,
            'source' => 'tms_system',
        ];
    }
} 