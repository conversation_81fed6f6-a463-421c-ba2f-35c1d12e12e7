<?php

namespace App\Listeners;

use App\Events\UnitStatusChanged;
use App\Models\User;
use App\Notifications\UnitStatusChangedNotification;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Notification;

class SendUnitStatusChangeNotification implements ShouldQueue
{
    use InteractsWithQueue;

    /**
     * Create the event listener.
     */
    public function __construct()
    {
        //
    }

    /**
     * Handle the event.
     */
    public function handle(UnitStatusChanged $event): void
    {
        try {
            Log::info('UnitStatusChanged event received', [
                'unit_id' => $event->unit->id,
                'unit_number' => $event->unit->unit_number,
                'previous_status' => $event->previousStatus,
                'new_status' => $event->newStatus,
            ]);
            
            // Get the unit and load relationships
            $unit = $event->unit->load(['owner', 'currentTenant']);
            
            // Collect recipients
            $recipients = collect();
            
            // Notify the unit owner if exists
            if ($unit->owner) {
                $recipients->push($unit->owner);
            }
            
            // Notify current tenant if exists and status change affects them
            if ($unit->currentTenant && $this->shouldNotifyTenant($event->newStatus)) {
                $recipients->push($unit->currentTenant);
            }
            
            // Notify all administrators
            $admins = User::where('role', 'admin')->get();
            $recipients = $recipients->merge($admins);
            
            // Remove duplicates
            $recipients = $recipients->unique('id');
            
            // Send notifications
            if ($recipients->isNotEmpty()) {
                Notification::send($recipients, new UnitStatusChangedNotification($event));
            }
            
            // Log the notification
            Log::info('Unit status change notification sent', [
                'unit_id' => $unit->id,
                'unit_number' => $unit->unit_number,
                'previous_status' => $event->previousStatus,
                'new_status' => $event->newStatus,
                'recipients_count' => $recipients->count(),
                'changed_by' => $event->changedBy?->name ?? 'System',
            ]);
            
        } catch (\Exception $e) {
            Log::error('Failed to send unit status change notification', [
                'unit_id' => $event->unit->id,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
            ]);
        }
    }

    /**
     * Determine if tenant should be notified based on status change
     */
    private function shouldNotifyTenant(string $newStatus): bool
    {
        // Notify tenant for status changes that affect them
        return in_array($newStatus, ['vacant', 'to-let', 'rented']);
    }
} 