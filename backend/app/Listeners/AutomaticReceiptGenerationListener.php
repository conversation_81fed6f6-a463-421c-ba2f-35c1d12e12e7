<?php

namespace App\Listeners;

use App\Events\PaymentCompletedEvent;
use App\Models\RentReceipt;
use App\Services\ReceiptGenerationService;
use App\Services\NotificationService;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Support\Facades\Log;

class AutomaticReceiptGenerationListener implements ShouldQueue
{
    use InteractsWithQueue;

    protected $receiptGenerationService;
    protected $notificationService;

    /**
     * Create the event listener.
     *
     * @param ReceiptGenerationService $receiptGenerationService
     * @param NotificationService $notificationService
     * @return void
     */
    public function __construct(
        ReceiptGenerationService $receiptGenerationService,
        NotificationService $notificationService
    ) {
        $this->receiptGenerationService = $receiptGenerationService;
        $this->notificationService = $notificationService;
    }

    /**
     * Handle the event.
     *
     * @param PaymentCompletedEvent $event
     * @return void
     */
    public function handle(PaymentCompletedEvent $event): void
    {
        $payment = $event->payment;

        try {
            // Check if receipt already exists
            $existingReceipt = RentReceipt::where('payment_id', $payment->id)->first();
            if ($existingReceipt) {
                Log::info('Receipt already exists for payment', [
                    'payment_id' => $payment->id,
                    'receipt_id' => $existingReceipt->id,
                ]);
                return;
            }

            // Load payment with relationships
            $payment->load(['tenant', 'unit.property']);

            // Generate receipt automatically
            $receipt = $this->receiptGenerationService->generateReceipt(
                $payment,
                $payment->tenant->email
            );

            // Log the automatic generation
            $receipt->auditLogs()->create([
                'action' => 'receipt_generated_automatically',
                'description' => 'Receipt generated automatically on payment completion',
                'user_id' => null, // System generated
                'metadata' => [
                    'payment_id' => $payment->id,
                    'tenant_email' => $payment->tenant->email,
                    'amount' => $payment->amount,
                ],
            ]);

            Log::info('Receipt generated automatically for payment', [
                'payment_id' => $payment->id,
                'receipt_id' => $receipt->id,
                'receipt_number' => $receipt->receipt_number,
            ]);

            // Send notification to tenant about receipt generation
            $this->notificationService->sendReceiptGeneratedNotification($receipt);

        } catch (\Exception $e) {
            Log::error('Failed to generate receipt automatically for payment', [
                'payment_id' => $payment->id,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
            ]);

            // Don't throw the exception to prevent payment processing from failing
            // The receipt can be generated manually later
        }
    }

    /**
     * Handle a job failure.
     *
     * @param PaymentCompletedEvent $event
     * @param \Throwable $exception
     * @return void
     */
    public function failed(PaymentCompletedEvent $event, \Throwable $exception): void
    {
        Log::error('Automatic receipt generation failed', [
            'payment_id' => $event->payment->id,
            'error' => $exception->getMessage(),
        ]);
    }
} 