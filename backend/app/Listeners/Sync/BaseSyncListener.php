<?php

namespace App\Listeners\Sync;

use App\Events\Sync\BaseSyncEvent;
use App\Models\SyncAuditLog;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\DB;
use Carbon\Carbon;

abstract class BaseSyncListener
{
    /**
     * Handle the event.
     */
    public function handle(BaseSyncEvent $event): void
    {
        // Skip if sync is disabled
        if (!config('sync.enabled', true)) {
            return;
        }

        // Skip if this is from our own system to prevent loops
        if ($event->sourceSystem === config('sync.source_system', 'tms')) {
            return;
        }

        // Skip if the event shouldn't be synced
        if (!$event->shouldSync()) {
            return;
        }

        $startTime = microtime(true);
        $startMemory = memory_get_usage(true);
        
        // Create audit log entry
        $auditLog = SyncAuditLog::createSyncLog(
            $event->getEntityType(),
            $event->entityData['id'] ?? null,
            $event->operation,
            $event->sourceSystem,
            $event->entityData,
            SyncAuditLog::STATUS_PENDING,
            $event->correlationId,
            $event->metadata
        );

        try {
            // Mark as processing
            $auditLog->markAsProcessing();

            // Validate society access for tenant isolation
            if (!$this->canAccessSociety($event->societyId)) {
                $auditLog->markAsSkipped(
                    'Access denied: No permission for society',
                    ['society_id' => $event->societyId]
                );
                
                Log::warning('Sync blocked: No access to society', [
                    'society_id' => $event->societyId,
                    'entity_type' => $event->getEntityType(),
                    'entity_id' => $event->entityData['id'] ?? null,
                    'correlation_id' => $event->correlationId,
                ]);
                return;
            }

            // Handle the specific sync operation
            $result = $this->processSync($event, $auditLog);

            // Calculate performance metrics
            $processingTime = (microtime(true) - $startTime) * 1000; // Convert to milliseconds
            $memoryUsage = memory_get_usage(true) - $startMemory;

            if ($result['success']) {
                $auditLog->markAsSuccess($processingTime, $memoryUsage);
                
                Log::info('Sync completed successfully', [
                    'entity_type' => $event->getEntityType(),
                    'entity_id' => $event->entityData['id'] ?? null,
                    'operation' => $event->operation,
                    'correlation_id' => $event->correlationId,
                    'processing_time_ms' => $processingTime,
                    'memory_usage_bytes' => $memoryUsage,
                ]);
            } else {
                $auditLog->markAsSkipped($result['reason'], $result['details'] ?? null);
                
                Log::info('Sync skipped', [
                    'entity_type' => $event->getEntityType(),
                    'entity_id' => $event->entityData['id'] ?? null,
                    'operation' => $event->operation,
                    'reason' => $result['reason'],
                    'correlation_id' => $event->correlationId,
                ]);
            }

        } catch (\Exception $e) {
            $processingTime = (microtime(true) - $startTime) * 1000;
            $auditLog->markAsFailed($e->getMessage(), [
                'exception_class' => get_class($e),
                'file' => $e->getFile(),
                'line' => $e->getLine(),
                'trace' => $e->getTraceAsString(),
                'processing_time_ms' => $processingTime,
            ]);

            Log::error('Sync failed', [
                'entity_type' => $event->getEntityType(),
                'entity_id' => $event->entityData['id'] ?? null,
                'operation' => $event->operation,
                'correlation_id' => $event->correlationId,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
            ]);

            throw $e; // Re-throw for queue retry mechanism
        }
    }

    /**
     * Process the sync operation for the specific entity.
     */
    protected function processSync(BaseSyncEvent $event, SyncAuditLog $auditLog): array
    {
        return $this->performUpsert($event);
    }

    /**
     * Get the model class for this listener.
     */
    abstract protected function getModelClass(): string;

    /**
     * Validate society access for tenant isolation.
     */
    protected function canAccessSociety(?int $societyId): bool
    {
        // Skip validation if disabled
        if (!config('sync.security.validate_society_access', true)) {
            return true;
        }

        // Allow if no society ID (for global entities)
        if ($societyId === null) {
            return true;
        }

        // Check if society exists and is accessible
        return \App\Models\Society::where('id', $societyId)->exists();
    }

    /**
     * Resolve conflicts based on configuration.
     */
    protected function resolveConflict(BaseSyncEvent $event, $existingEntity, SyncAuditLog $auditLog): array
    {
        $strategy = config('sync.conflict_resolution.strategy', 'timestamp');
        
        switch ($strategy) {
            case 'timestamp':
                return $this->resolveByTimestamp($event, $existingEntity, $auditLog);
                
            case 'source_priority':
                return $this->resolveBySourcePriority($event, $existingEntity, $auditLog);
                
            case 'manual':
                return $this->flagForManualResolution($event, $existingEntity, $auditLog);
                
            default:
                return $this->resolveByTimestamp($event, $existingEntity, $auditLog);
        }
    }

    /**
     * Resolve conflict by timestamp comparison.
     */
    protected function resolveByTimestamp(BaseSyncEvent $event, $existingEntity, SyncAuditLog $auditLog): array
    {
        $timestampField = config('sync.conflict_resolution.timestamp_field', 'updated_at');
        $localTimestamp = $existingEntity->{$timestampField};
        $syncTimestamp = Carbon::parse($event->syncVersion);

        if ($localTimestamp->greaterThan($syncTimestamp)) {
            return [
                'success' => false,
                'reason' => 'Local version is newer',
                'details' => [
                    'local_timestamp' => $localTimestamp->toISOString(),
                    'sync_timestamp' => $syncTimestamp->toISOString(),
                    'resolution_strategy' => 'timestamp',
                ],
            ];
        }

        return ['success' => true];
    }

    /**
     * Resolve conflict by source system priority.
     */
    protected function resolveBySourcePriority(BaseSyncEvent $event, $existingEntity, SyncAuditLog $auditLog): array
    {
        $priorities = config('sync.conflict_resolution.source_priority', ['tms', 'onesociety']);
        $currentSystemPriority = array_search(config('sync.source_system'), $priorities);
        $syncSystemPriority = array_search($event->sourceSystem, $priorities);

        if ($currentSystemPriority !== false && $syncSystemPriority !== false) {
            if ($currentSystemPriority < $syncSystemPriority) {
                return [
                    'success' => false,
                    'reason' => 'Local system has higher priority',
                    'details' => [
                        'local_system' => config('sync.source_system'),
                        'sync_system' => $event->sourceSystem,
                        'resolution_strategy' => 'source_priority',
                    ],
                ];
            }
        }

        return ['success' => true];
    }

    /**
     * Flag conflict for manual resolution.
     */
    protected function flagForManualResolution(BaseSyncEvent $event, $existingEntity, SyncAuditLog $auditLog): array
    {
        $auditLog->markAsConflict(
            'Manual resolution required',
            [
                'local_data' => $existingEntity->toArray(),
                'sync_data' => $event->entityData,
                'resolution_strategy' => 'manual',
            ]
        );

        return [
            'success' => false,
            'reason' => 'Conflict flagged for manual resolution',
            'details' => ['resolution_strategy' => 'manual'],
        ];
    }

    /**
     * Perform idempotent upsert operation.
     */
    protected function performUpsert(BaseSyncEvent $event, array $additionalData = []): array
    {
        $modelClass = $this->getModelClass();
        $entityId = $event->entityData['id'];

        if ($event->operation === 'deleted') {
            // Handle deletion
            $entity = $modelClass::find($entityId);
            if ($entity) {
                $entity->delete();
                return [
                    'success' => true,
                    'action' => 'deleted',
                    'entity' => $entity,
                ];
            } else {
                return [
                    'success' => false,
                    'reason' => 'Entity not found for deletion',
                    'details' => ['entity_id' => $entityId],
                ];
            }
        }

        // Handle create/update
        $existing = $modelClass::find($entityId);
        
        if ($existing) {
            // Check for conflicts
            $conflictResult = $this->resolveConflict($event, $existing, null);
            if (!$conflictResult['success']) {
                return $conflictResult;
            }
        }

        // Prepare data for upsert
        $upsertData = array_merge($event->entityData, $additionalData, [
            'synced_at' => now(),
            'sync_source' => $event->sourceSystem,
        ]);

        // Perform upsert
        $entity = $modelClass::updateOrCreate(
            ['id' => $entityId],
            $upsertData
        );

        return [
            'success' => true,
            'action' => $existing ? 'updated' : 'created',
            'entity' => $entity,
        ];
    }

    /**
     * Handle the job failure.
     */
    public function failed(BaseSyncEvent $event, \Throwable $exception): void
    {
        Log::error('Sync listener failed', [
            'listener' => static::class,
            'entity_type' => $event->getEntityType(),
            'entity_id' => $event->entityData['id'] ?? null,
            'operation' => $event->operation,
            'correlation_id' => $event->correlationId,
            'error' => $exception->getMessage(),
            'trace' => $exception->getTraceAsString(),
        ]);
    }
}
