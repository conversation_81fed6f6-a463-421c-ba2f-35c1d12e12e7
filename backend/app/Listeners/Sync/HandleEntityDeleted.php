<?php

namespace App\Listeners\Sync;

use App\Events\Sync\EntityDeleted;
use App\Models\SyncAuditLog;

class HandleEntityDeleted extends BaseSyncListener
{
    /**
     * Handle the entity deletion event.
     */
    public function handle(\App\Events\Sync\BaseSyncEvent $event): void
    {
        // Use the parent handle method but with specific logic for deletions
        parent::handle($event);
    }

    /**
     * Get the model class for this listener.
     */
    protected function getModelClass(): string
    {
        // Return the model class based on the entity type
        $entityType = $this->event->entityType ?? 'Unknown';
        
        $modelMap = [
            'Society' => \App\Models\Society::class,
            'Unit' => \App\Models\Unit::class,
            'Tenant' => \App\Models\Tenant::class,
            'Agreement' => \App\Models\Agreement::class,
            'RentPayment' => \App\Models\RentPayment::class,
        ];

        return $modelMap[$entityType] ?? \App\Models\Model::class;
    }

    /**
     * Process the sync operation for entity deletion.
     */
    protected function processSync(\App\Events\Sync\BaseSyncEvent $event, SyncAuditLog $auditLog): array
    {
        if (!($event instanceof EntityDeleted)) {
            return [
                'success' => false,
                'reason' => 'Invalid event type for deletion handler',
            ];
        }

        $modelClass = $this->getModelClass();
        $entityId = $event->entityData['id'];

        // Find and delete the entity
        $entity = $modelClass::find($entityId);
        
        if ($entity) {
            $entity->delete();
            return [
                'success' => true,
                'action' => 'deleted',
                'entity' => $entity,
            ];
        } else {
            return [
                'success' => false,
                'reason' => 'Entity not found for deletion',
                'details' => [
                    'entity_type' => $event->entityType,
                    'entity_id' => $entityId,
                ],
            ];
        }
    }
}
