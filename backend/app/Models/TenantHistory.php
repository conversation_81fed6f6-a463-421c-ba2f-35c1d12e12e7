<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Carbon\Carbon;

class TenantHistory extends Model
{
    use HasFactory;

    protected $table = 'tenant_history';

    protected $fillable = [
        'tenant_id',
        'user_id',
        'activity_type',
        'activity_description',
        'metadata',
        'old_values',
        'new_values',
        'ip_address',
        'user_agent',
        'severity',
        'performed_at',
    ];

    protected $casts = [
        'metadata' => 'array',
        'old_values' => 'array',
        'new_values' => 'array',
        'performed_at' => 'datetime',
    ];

    protected $dates = [
        'performed_at',
        'created_at',
        'updated_at',
    ];

    /**
     * Get the tenant that this history belongs to
     */
    public function tenant(): BelongsTo
    {
        return $this->belongsTo(Tenant::class);
    }

    /**
     * Get the user who performed this action
     */
    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    /**
     * Scope to filter by activity type
     */
    public function scopeOfType($query, string $activityType)
    {
        return $query->where('activity_type', $activityType);
    }

    /**
     * Scope to filter by date range
     */
    public function scopeDateRange($query, $startDate, $endDate)
    {
        return $query->whereBetween('performed_at', [$startDate, $endDate]);
    }

    /**
     * Scope to filter by severity
     */
    public function scopeBySeverity($query, string $severity)
    {
        return $query->where('severity', $severity);
    }

    /**
     * Scope for recent activities
     */
    public function scopeRecent($query, int $days = 30)
    {
        return $query->where('performed_at', '>=', Carbon::now()->subDays($days));
    }

    /**
     * Static method to log tenant activity
     */
    public static function logActivity(
        int $tenantId,
        string $activityType,
        string $description,
        ?int $userId = null,
        ?array $metadata = null,
        ?array $oldValues = null,
        ?array $newValues = null,
        string $severity = 'medium',
        ?string $ipAddress = null,
        ?string $userAgent = null
    ): self {
        return self::create([
            'tenant_id' => $tenantId,
            'user_id' => $userId,
            'activity_type' => $activityType,
            'activity_description' => $description,
            'metadata' => $metadata,
            'old_values' => $oldValues,
            'new_values' => $newValues,
            'severity' => $severity,
            'ip_address' => $ipAddress,
            'user_agent' => $userAgent,
            'performed_at' => Carbon::now(),
        ]);
    }

    /**
     * Get formatted activity description with context
     */
    public function getFormattedDescriptionAttribute(): string
    {
        $description = $this->activity_description;
        
        if ($this->user) {
            $description .= " (by {$this->user->name})";
        }
        
        return $description;
    }

    /**
     * Get human readable time difference
     */
    public function getTimeAgoAttribute(): string
    {
        return $this->performed_at->diffForHumans();
    }

    /**
     * Check if this is a critical activity
     */
    public function getIsCriticalAttribute(): bool
    {
        return $this->severity === 'high';
    }

    /**
     * Activity type constants
     */
    const ACTIVITY_TYPES = [
        'tenant_created' => 'Tenant Created',
        'onboarding_started' => 'Onboarding Started',
        'personal_details_updated' => 'Personal Details Updated',
        'family_details_updated' => 'Family Details Updated',
        'emergency_contact_added' => 'Emergency Contact Added',
        'emergency_contact_updated' => 'Emergency Contact Updated',
        'emergency_contact_removed' => 'Emergency Contact Removed',
        'document_uploaded' => 'Document Uploaded',
        'document_verified' => 'Document Verified',
        'document_rejected' => 'Document Rejected',
        'kyc_status_changed' => 'KYC Status Changed',
        'profile_updated' => 'Profile Updated',
        'unit_assigned' => 'Unit Assigned',
        'unit_unassigned' => 'Unit Unassigned',
        'agreement_generated' => 'Agreement Generated',
        'agreement_signed' => 'Agreement Signed',
        'noc_application_submitted' => 'NOC Application Submitted',
        'noc_status_changed' => 'NOC Status Changed',
        'payment_made' => 'Payment Made',
        'status_changed' => 'Status Changed',
        'admin_action' => 'Admin Action',
        'login_activity' => 'Login Activity',
        'logout_activity' => 'Logout Activity',
    ];

    /**
     * Get activity type display name
     */
    public function getActivityTypeDisplayAttribute(): string
    {
        return self::ACTIVITY_TYPES[$this->activity_type] ?? ucwords(str_replace('_', ' ', $this->activity_type));
    }
}
