<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\SoftDeletes;

class BillingRule extends Model
{
    use HasFactory, SoftDeletes;

    protected $fillable = [
        'name',
        'type',
        'calculation_method',
        'amount',
        'description',
        'is_active',
        'applies_to',
        'frequency',
        'due_day',
        'late_fee_amount',
        'late_fee_days',
        'created_by',
        'metadata',
    ];

    protected $casts = [
        'amount' => 'decimal:2',
        'late_fee_amount' => 'decimal:2',
        'is_active' => 'boolean',
        'due_day' => 'integer',
        'late_fee_days' => 'integer',
        'metadata' => 'array',
    ];

    /**
     * Get the user who created this billing rule.
     */
    public function creator(): BelongsTo
    {
        return $this->belongsTo(User::class, 'created_by');
    }

    /**
     * Scope to get only active rules.
     */
    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }

    /**
     * Scope to filter by type.
     */
    public function scopeOfType($query, string $type)
    {
        return $query->where('type', $type);
    }

    /**
     * Scope to filter by applies_to.
     */
    public function scopeAppliesTo($query, string $appliesTo)
    {
        return $query->where('applies_to', $appliesTo);
    }

    /**
     * Check if this rule applies to a specific unit.
     */
    public function appliesToUnit(Unit $unit): bool
    {
        switch ($this->applies_to) {
            case 'all_units':
                return true;
            case 'specific_units':
                // Check if unit is in the metadata specific_unit_ids array
                return in_array($unit->id, $this->metadata['specific_unit_ids'] ?? []);
            case 'tenant_type':
                // Check if unit's tenant type matches
                $tenantType = $this->metadata['tenant_type'] ?? null;
                return $unit->current_tenant && $unit->current_tenant->user_type === $tenantType;
            default:
                return false;
        }
    }

    /**
     * Calculate amount for a specific unit.
     */
    public function calculateAmount(Unit $unit): float
    {
        switch ($this->calculation_method) {
            case 'per_sqft':
                return $this->amount * ($unit->area ?? 0);
            case 'fixed':
                return $this->amount;
            case 'percentage':
                $baseAmount = $unit->market_rent ?? 0;
                return $baseAmount * ($this->amount / 100);
            case 'per_unit':
                return $this->amount;
            default:
                return 0;
        }
    }
}
