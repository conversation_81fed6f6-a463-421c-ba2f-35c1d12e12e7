<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Database\Eloquent\Relations\HasMany;

class ListingPortal extends Model
{
    use HasFactory, SoftDeletes;

    /**
     * The attributes that are mass assignable.
     */
    protected $fillable = [
        'name',
        'display_name',
        'slug',
        'description',
        'base_url',
        'api_key',
        'api_secret',
        'username',
        'password',
        'api_config',
        'field_mapping',
        'media_requirements',
        'is_active',
        'requires_approval',
        'rate_limit_per_hour',
        'rate_limit_per_day',
        'last_sync_at',
        'sync_status',
        'metadata',
    ];

    /**
     * The attributes that should be cast.
     */
    protected $casts = [
        'api_config' => 'array',
        'field_mapping' => 'array',
        'media_requirements' => 'array',
        'sync_status' => 'array',
        'metadata' => 'array',
        'is_active' => 'boolean',
        'requires_approval' => 'boolean',
        'rate_limit_per_hour' => 'integer',
        'rate_limit_per_day' => 'integer',
        'last_sync_at' => 'datetime',
    ];

    /**
     * Get all leads for this portal
     */
    public function leads(): HasMany
    {
        return $this->hasMany(PropertyLead::class, 'portal_id');
    }

    /**
     * Scope to get active portals
     */
    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }

    /**
     * Scope to get portals by slug
     */
    public function scopeBySlug($query, $slug)
    {
        return $query->where('slug', $slug);
    }

    /**
     * Check if portal is active
     */
    public function isActive(): bool
    {
        return $this->is_active;
    }

    /**
     * Check if portal requires approval
     */
    public function requiresApproval(): bool
    {
        return $this->requires_approval;
    }

    /**
     * Get API configuration
     */
    public function getApiConfig(): array
    {
        return $this->api_config ?? [];
    }

    /**
     * Get field mapping
     */
    public function getFieldMapping(): array
    {
        return $this->field_mapping ?? [];
    }

    /**
     * Get media requirements
     */
    public function getMediaRequirements(): array
    {
        return $this->media_requirements ?? [];
    }

    /**
     * Get sync status
     */
    public function getSyncStatus(): array
    {
        return $this->sync_status ?? [];
    }

    /**
     * Update last sync time
     */
    public function updateLastSync(): bool
    {
        return $this->update(['last_sync_at' => now()]);
    }

    /**
     * Update sync status
     */
    public function updateSyncStatus(array $status): bool
    {
        return $this->update(['sync_status' => $status]);
    }

    /**
     * Get portal display name
     */
    public function getDisplayNameAttribute(): string
    {
        return $this->display_name ?: $this->name;
    }

    /**
     * Get status color for UI
     */
    public function getStatusColorAttribute(): string
    {
        return $this->is_active ? 'success' : 'danger';
    }

    /**
     * Get status label for UI
     */
    public function getStatusLabelAttribute(): string
    {
        return $this->is_active ? 'Active' : 'Inactive';
    }

    /**
     * Check if portal has API credentials
     */
    public function hasApiCredentials(): bool
    {
        return !empty($this->api_key) || (!empty($this->username) && !empty($this->password));
    }

    /**
     * Get API credentials
     */
    public function getApiCredentials(): array
    {
        return [
            'api_key' => $this->api_key,
            'api_secret' => $this->api_secret,
            'username' => $this->username,
            'password' => $this->password,
        ];
    }

    /**
     * Check if portal supports specific field
     */
    public function supportsField(string $field): bool
    {
        $fieldMapping = $this->getFieldMapping();
        return isset($fieldMapping[$field]);
    }

    /**
     * Get mapped field name for portal
     */
    public function getMappedField(string $field): ?string
    {
        $fieldMapping = $this->getFieldMapping();
        return $fieldMapping[$field] ?? null;
    }

    /**
     * Check if portal supports media type
     */
    public function supportsMediaType(string $type): bool
    {
        $mediaRequirements = $this->getMediaRequirements();
        return isset($mediaRequirements[$type]);
    }

    /**
     * Get media requirements for type
     */
    public function getMediaRequirementsForType(string $type): array
    {
        $mediaRequirements = $this->getMediaRequirements();
        return $mediaRequirements[$type] ?? [];
    }

    /**
     * Check if portal is within rate limits
     */
    public function isWithinRateLimits(): bool
    {
        // This would need to be implemented with actual rate limiting logic
        // For now, return true
        return true;
    }

    /**
     * Get portal URL
     */
    public function getPortalUrl(): string
    {
        return $this->base_url;
    }

    /**
     * Get portal logo URL (if available in metadata)
     */
    public function getLogoUrl(): ?string
    {
        $metadata = $this->metadata ?? [];
        return $metadata['logo_url'] ?? null;
    }

    /**
     * Get portal description
     */
    public function getDescription(): string
    {
        return $this->description ?: "Property listing portal: {$this->display_name}";
    }
}
