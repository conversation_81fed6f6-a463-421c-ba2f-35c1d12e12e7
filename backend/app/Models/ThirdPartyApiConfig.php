<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Support\Str;

class ThirdPartyApiConfig extends Model
{
    use HasFactory, SoftDeletes;

    protected $fillable = [
        'identifier',
        'name',
        'description',
        'base_url',
        'auth_type',
        'auth_token',
        'auth_header',
        'auth_username',
        'auth_password',
        'custom_headers',
        'content_type',
        'timeout',
        'retries',
        'rate_limit_enabled',
        'rate_limit_requests',
        'rate_limit_duration',
        'cache_enabled',
        'cache_duration',
        'request_mapping',
        'response_mapping',
        'webhook_secret',
        'webhook_url',
        'test_endpoint',
        'is_active',
        'environment',
        'version',
        'documentation_url',
        'settings'
    ];

    protected $casts = [
        'is_active' => 'boolean',
        'rate_limit_enabled' => 'boolean',
        'cache_enabled' => 'boolean',
        'custom_headers' => 'array',
        'request_mapping' => 'array',
        'response_mapping' => 'array',
        'settings' => 'array',
        'timeout' => 'integer',
        'retries' => 'integer',
        'rate_limit_requests' => 'integer',
        'rate_limit_duration' => 'integer',
        'cache_duration' => 'integer',
    ];

    protected $hidden = [
        'auth_token',
        'auth_password',
        'webhook_secret',
    ];

    /**
     * Authentication types
     */
    const AUTH_TYPES = [
        'none' => 'No Authentication',
        'bearer' => 'Bearer Token',
        'api_key' => 'API Key',
        'basic' => 'Basic Authentication',
        'oauth2' => 'OAuth 2.0',
        'custom' => 'Custom Headers',
    ];

    /**
     * Content types
     */
    const CONTENT_TYPES = [
        'application/json' => 'JSON',
        'application/x-www-form-urlencoded' => 'Form Data',
        'multipart/form-data' => 'Multipart Form',
        'text/xml' => 'XML',
        'text/plain' => 'Plain Text',
    ];

    /**
     * Environments
     */
    const ENVIRONMENTS = [
        'production' => 'Production',
        'staging' => 'Staging',
        'development' => 'Development',
        'testing' => 'Testing',
    ];

    /**
     * Boot method
     */
    protected static function boot()
    {
        parent::boot();

        static::creating(function ($model) {
            if (!$model->identifier) {
                $model->identifier = Str::slug($model->name);
            }
        });
    }

    /**
     * Get API logs
     */
    public function logs()
    {
        return $this->hasMany(ThirdPartyApiLog::class, 'api_config_id');
    }

    /**
     * Get recent logs
     */
    public function recentLogs($limit = 10)
    {
        return $this->logs()
            ->latest()
            ->limit($limit)
            ->get();
    }

    /**
     * Get success rate
     */
    public function getSuccessRateAttribute()
    {
        $totalRequests = $this->logs()->count();
        
        if ($totalRequests === 0) {
            return 0;
        }

        $successfulRequests = $this->logs()
            ->where('status', 'success')
            ->count();

        return round(($successfulRequests / $totalRequests) * 100, 2);
    }

    /**
     * Get average response time
     */
    public function getAverageResponseTimeAttribute()
    {
        return $this->logs()
            ->whereNotNull('response_time')
            ->avg('response_time') ?? 0;
    }

    /**
     * Check if API is healthy
     */
    public function isHealthy()
    {
        // Check recent logs for health status
        $recentLogs = $this->logs()
            ->where('created_at', '>=', now()->subHours(1))
            ->get();

        if ($recentLogs->isEmpty()) {
            return true; // No recent activity, assume healthy
        }

        $errorRate = $recentLogs->where('status', 'error')->count() / $recentLogs->count();
        
        return $errorRate < 0.1; // Less than 10% error rate
    }

    /**
     * Get configuration for display
     */
    public function getDisplayConfig()
    {
        return [
            'identifier' => $this->identifier,
            'name' => $this->name,
            'description' => $this->description,
            'base_url' => $this->base_url,
            'auth_type' => $this->auth_type,
            'content_type' => $this->content_type,
            'timeout' => $this->timeout,
            'retries' => $this->retries,
            'rate_limit_enabled' => $this->rate_limit_enabled,
            'rate_limit_requests' => $this->rate_limit_requests,
            'rate_limit_duration' => $this->rate_limit_duration,
            'cache_enabled' => $this->cache_enabled,
            'cache_duration' => $this->cache_duration,
            'is_active' => $this->is_active,
            'environment' => $this->environment,
            'version' => $this->version,
            'documentation_url' => $this->documentation_url,
            'success_rate' => $this->success_rate,
            'average_response_time' => $this->average_response_time,
            'is_healthy' => $this->isHealthy(),
            'created_at' => $this->created_at,
            'updated_at' => $this->updated_at,
        ];
    }

    /**
     * Scope for active APIs
     */
    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }

    /**
     * Scope for environment
     */
    public function scopeEnvironment($query, $environment)
    {
        return $query->where('environment', $environment);
    }

    /**
     * Validate configuration
     */
    public function validateConfiguration()
    {
        $errors = [];

        // Validate base URL
        if (!filter_var($this->base_url, FILTER_VALIDATE_URL)) {
            $errors[] = 'Invalid base URL format';
        }

        // Validate authentication
        switch ($this->auth_type) {
            case 'bearer':
            case 'api_key':
                if (empty($this->auth_token)) {
                    $errors[] = 'Auth token is required for ' . $this->auth_type . ' authentication';
                }
                break;

            case 'basic':
                if (empty($this->auth_username) || empty($this->auth_password)) {
                    $errors[] = 'Username and password are required for basic authentication';
                }
                break;

            case 'custom':
                if (empty($this->custom_headers)) {
                    $errors[] = 'Custom headers are required for custom authentication';
                }
                break;
        }

        // Validate rate limiting
        if ($this->rate_limit_enabled) {
            if ($this->rate_limit_requests <= 0) {
                $errors[] = 'Rate limit requests must be greater than 0';
            }
            if ($this->rate_limit_duration <= 0) {
                $errors[] = 'Rate limit duration must be greater than 0';
            }
        }

        // Validate caching
        if ($this->cache_enabled && $this->cache_duration <= 0) {
            $errors[] = 'Cache duration must be greater than 0 when caching is enabled';
        }

        return $errors;
    }

    /**
     * Get authentication display name
     */
    public function getAuthTypeDisplayAttribute()
    {
        return self::AUTH_TYPES[$this->auth_type] ?? 'Unknown';
    }

    /**
     * Get content type display name
     */
    public function getContentTypeDisplayAttribute()
    {
        return self::CONTENT_TYPES[$this->content_type] ?? 'Unknown';
    }
} 