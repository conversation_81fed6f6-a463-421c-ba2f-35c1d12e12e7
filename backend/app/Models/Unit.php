<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Support\Facades\Auth;

class Unit extends Model
{
    use HasFactory, SoftDeletes;

    /**
     * The attributes that are mass assignable.
     */
    protected $fillable = [
        'unit_number',
        'block',
        'floor',
        'wing',
        'type',
        'bedrooms',
        'bathrooms',
        'area_sqft',
        'carpet_area',
        'built_up_area',
        'status',
        'owner_id',
        'current_tenant_id',
        'is_sublet',
        'is_owner_occupied',
        'market_rent',
        'security_deposit',
        'maintenance_charge',
        'last_status_change',
        'status_changed_by',
        'status_change_reason',
        'available_from',
        'description',
        'amenities',
        'preferences',
        'photos',
        'documents',
        'is_active',
        'notes',
        'metadata',
    ];

    /**
     * The attributes that should be cast.
     */
    protected $casts = [
        'amenities' => 'array',
        'preferences' => 'array',
        'photos' => 'array',
        'documents' => 'array',
        'metadata' => 'array',
        'is_sublet' => 'boolean',
        'is_owner_occupied' => 'boolean',
        'is_active' => 'boolean',
        'last_status_change' => 'datetime',
        'available_from' => 'date',
        'market_rent' => 'decimal:2',
        'security_deposit' => 'decimal:2',
        'maintenance_charge' => 'decimal:2',
        'area_sqft' => 'decimal:2',
        'carpet_area' => 'decimal:2',
        'built_up_area' => 'decimal:2',
    ];

    /**
     * Unit status constants
     */
    const STATUS_OCCUPIED = 'occupied';
    const STATUS_VACANT = 'vacant';
    const STATUS_TO_LET = 'to-let';
    const STATUS_RENTED = 'rented';

    /**
     * Unit type constants
     */
    const TYPE_RESIDENTIAL = 'residential';
    const TYPE_COMMERCIAL = 'commercial';
    const TYPE_PARKING = 'parking';

    /**
     * Get all possible unit statuses
     */
    public static function getStatuses(): array
    {
        return [
            self::STATUS_OCCUPIED,
            self::STATUS_VACANT,
            self::STATUS_TO_LET,
            self::STATUS_RENTED,
        ];
    }

    /**
     * Get all possible unit types
     */
    public static function getTypes(): array
    {
        return [
            self::TYPE_RESIDENTIAL,
            self::TYPE_COMMERCIAL,
            self::TYPE_PARKING,
        ];
    }

    /**
     * Get the owner of the unit
     */
    public function owner(): BelongsTo
    {
        return $this->belongsTo(User::class, 'owner_id');
    }

    /**
     * Get the current tenant of the unit
     */
    public function currentTenant(): BelongsTo
    {
        return $this->belongsTo(User::class, 'current_tenant_id');
    }

    /**
     * Get the user who last changed the status
     */
    public function statusChangedBy(): BelongsTo
    {
        return $this->belongsTo(User::class, 'status_changed_by');
    }

    /**
     * Get all tenants who have lived in this unit
     */
    public function tenants(): HasMany
    {
        return $this->hasMany(Tenant::class);
    }

    /**
     * Get all agreements for this unit
     */
    public function agreements(): HasMany
    {
        return $this->hasMany(Agreement::class);
    }

    /**
     * Get all NOC requests for this unit
     */
    public function nocRequests(): HasMany
    {
        return $this->hasMany(NocRequest::class);
    }

    /**
     * Get all enquiries for this unit
     */
    public function enquiries(): HasMany
    {
        return $this->hasMany(Enquiry::class);
    }

    /**
     * Get all charges for this unit
     */
    public function charges(): HasMany
    {
        return $this->hasMany(Charge::class);
    }

    /**
     * Get status history for this unit
     */
    public function statusHistory(): HasMany
    {
        return $this->hasMany(UnitStatusHistory::class);
    }

    /**
     * Scope to get units by status
     */
    public function scopeByStatus($query, $status)
    {
        return $query->where('status', $status);
    }

    /**
     * Scope to get active units
     */
    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }

    /**
     * Scope to get units by owner
     */
    public function scopeByOwner($query, $ownerId)
    {
        return $query->where('owner_id', $ownerId);
    }

    /**
     * Scope to get available units (vacant or to-let)
     */
    public function scopeAvailable($query)
    {
        return $query->whereIn('status', [self::STATUS_VACANT, self::STATUS_TO_LET]);
    }

    /**
     * Scope to get rented units
     */
    public function scopeRented($query)
    {
        return $query->where('status', self::STATUS_RENTED);
    }

    /**
     * Check if unit is available for rent
     */
    public function isAvailable(): bool
    {
        return in_array($this->status, [self::STATUS_VACANT, self::STATUS_TO_LET]);
    }

    /**
     * Check if unit is occupied
     */
    public function isOccupied(): bool
    {
        return $this->status === self::STATUS_OCCUPIED;
    }

    /**
     * Check if unit is rented
     */
    public function isRented(): bool
    {
        return $this->status === self::STATUS_RENTED;
    }

    /**
     * Check if unit is vacant
     */
    public function isVacant(): bool
    {
        return $this->status === self::STATUS_VACANT;
    }

    /**
     * Update unit status with tracking
     */
    public function updateStatus(string $newStatus, string $reason = null, $userId = null): bool
    {
        $oldStatus = $this->status;
        
        if ($oldStatus === $newStatus) {
            return true; // No change needed
        }

        // Validate status transition
        if (!$this->isValidStatusTransition($oldStatus, $newStatus)) {
            return false;
        }

        // Update status
        $this->update([
            'status' => $newStatus,
            'last_status_change' => now(),
            'status_changed_by' => $userId ?? Auth::id(),
            'status_change_reason' => $reason,
        ]);

        // Log status change in history
        $this->statusHistory()->create([
            'previous_status' => $oldStatus,
            'new_status' => $newStatus,
            'changed_by' => $userId ?? Auth::id(),
            'reason' => $reason,
            'changed_at' => now(),
        ]);

        // Fire the unit status changed event
        $changedBy = $userId ? User::find($userId) : Auth::user();
        event(new \App\Events\UnitStatusChanged($this, $oldStatus, $newStatus, $changedBy, $reason));

        return true;
    }

    /**
     * Validate status transition
     */
    private function isValidStatusTransition(string $from, string $to): bool
    {
        // Define valid transitions
        $validTransitions = [
            self::STATUS_VACANT => [self::STATUS_OCCUPIED, self::STATUS_TO_LET],
            self::STATUS_OCCUPIED => [self::STATUS_VACANT, self::STATUS_TO_LET],
            self::STATUS_TO_LET => [self::STATUS_RENTED, self::STATUS_VACANT, self::STATUS_OCCUPIED],
            self::STATUS_RENTED => [self::STATUS_VACANT, self::STATUS_TO_LET],
        ];

        return isset($validTransitions[$from]) && in_array($to, $validTransitions[$from]);
    }

    /**
     * Get unit display name
     */
    public function getDisplayNameAttribute(): string
    {
        $parts = array_filter([$this->block, $this->floor, $this->unit_number]);
        return implode('-', $parts);
    }

    /**
     * Get status color for UI
     */
    public function getStatusColorAttribute(): string
    {
        return match ($this->status) {
            self::STATUS_OCCUPIED => 'success',
            self::STATUS_VACANT => 'default',
            self::STATUS_TO_LET => 'warning',
            self::STATUS_RENTED => 'info',
            default => 'default',
        };
    }

    /**
     * Get status label for UI
     */
    public function getStatusLabelAttribute(): string
    {
        return match ($this->status) {
            self::STATUS_OCCUPIED => 'Occupied',
            self::STATUS_VACANT => 'Vacant',
            self::STATUS_TO_LET => 'To-Let',
            self::STATUS_RENTED => 'Rented',
            default => 'Unknown',
        };
    }

    /**
     * Calculate non-occupancy charge eligibility
     */
    public function isEligibleForNonOccupancyCharge(): bool
    {
        return in_array($this->status, [self::STATUS_VACANT, self::STATUS_RENTED]) && !$this->is_owner_occupied;
    }

    /**
     * Get full address
     */
    public function getFullAddressAttribute(): string
    {
        $parts = array_filter([
            $this->unit_number,
            $this->block ? "Block {$this->block}" : null,
            $this->floor ? "Floor {$this->floor}" : null,
            $this->wing ? "Wing {$this->wing}" : null,
        ]);

        return implode(', ', $parts);
    }

    // Add society relationship
    public function society(): BelongsTo
    {
        return $this->belongsTo(Society::class);
    }

    // Add scope for society filtering
    public function scopeBySociety($query, $societyId)
    {
        return $query->where('society_id', $societyId);
    }
}
