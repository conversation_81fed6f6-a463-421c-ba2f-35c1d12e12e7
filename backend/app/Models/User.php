<?php

namespace App\Models;

// use Illuminate\Contracts\Auth\MustVerifyEmail;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Foundation\Auth\User as Authenticatable;
use Illuminate\Notifications\Notifiable;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\HasOne;
use Illuminate\Database\Eloquent\Relations\MorphMany;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Laravel\Sanctum\HasApiTokens;

class User extends Authenticatable
{
    use HasFactory, Notifiable, SoftDeletes, HasApiTokens;

    // Role constants
    const ROLE_ADMIN = 'admin';
    const ROLE_OWNER = 'owner';
    const ROLE_TENANT = 'tenant';
    const ROLE_STAFF = 'staff';

    // User type constants
    const TYPE_SOCIETY_ADMIN = 'society_admin';
    const TYPE_OWNER = 'owner';
    const TYPE_TENANT = 'tenant';
    const TYPE_STAFF = 'staff';

    // Status constants
    const STATUS_ACTIVE = 'active';
    const STATUS_INACTIVE = 'inactive';
    const STATUS_SUSPENDED = 'suspended';
    const STATUS_PENDING_VERIFICATION = 'pending_verification';

    /**
     * The attributes that are mass assignable.
     *
     * @var list<string>
     */
    protected $fillable = [
        'name',
        'email',
        'password',
        'role',
        'user_type',
        'first_name',
        'last_name',
        'phone',
        'alternate_phone',
        'date_of_birth',
        'gender',
        'address',
        'city',
        'state',
        'country',
        'pincode',
        'emergency_contact',
        'profile_photo',
        'preferences',
        'status',
        'is_verified',
        'verified_at',
        'verified_by',
        'permissions',
        'last_login_at',
        'last_login_ip',
    ];

    /**
     * The attributes that should be hidden for serialization.
     *
     * @var list<string>
     */
    protected $hidden = [
        'password',
        'remember_token',
    ];

    /**
     * Get the attributes that should be cast.
     *
     * @return array<string, string>
     */
    protected function casts(): array
    {
        return [
            'email_verified_at' => 'datetime',
            'password' => 'hashed',
            'date_of_birth' => 'date',
            'emergency_contact' => 'array',
            'preferences' => 'array',
            'permissions' => 'array',
            'is_verified' => 'boolean',
            'verified_at' => 'datetime',
            'last_login_at' => 'datetime',
        ];
    }

    /**
     * Get the user's full name.
     */
    public function getFullNameAttribute(): string
    {
        return trim($this->first_name . ' ' . $this->last_name) ?: $this->name;
    }

    /**
     * Get the user's display name.
     */
    public function getDisplayNameAttribute(): string
    {
        return $this->full_name ?: $this->email;
    }

    /**
     * Check if user has a specific role.
     */
    public function hasRole(string|array $role): bool
    {
        if (is_array($role)) {
            return in_array($this->role, $role);
        }
        return $this->role === $role;
    }

    /**
     * Check if user is an admin.
     */
    public function isAdmin(): bool
    {
        return $this->hasRole(self::ROLE_ADMIN);
    }

    /**
     * Check if user is an owner.
     */
    public function isOwner(): bool
    {
        return $this->hasRole(self::ROLE_OWNER);
    }

    /**
     * Check if user is a tenant.
     */
    public function isTenant(): bool
    {
        return $this->hasRole(self::ROLE_TENANT);
    }

    /**
     * Check if user is staff.
     */
    public function isStaff(): bool
    {
        return $this->hasRole(self::ROLE_STAFF);
    }

    /**
     * Check if user is active.
     */
    public function isActive(): bool
    {
        return $this->status === self::STATUS_ACTIVE;
    }

    /**
     * Check if user is verified.
     */
    public function isVerified(): bool
    {
        return (bool) $this->is_verified;
    }

    /**
     * Get user's permissions.
     */
    public function getPermissions(): array
    {
        return $this->permissions ?? [];
    }

    /**
     * Check if user has a specific permission.
     */
    public function hasPermission(string $permission): bool
    {
        return in_array($permission, $this->getPermissions());
    }

    /**
     * Get the units owned by this user.
     */
    public function ownedUnits(): HasMany
    {
        return $this->hasMany(Unit::class, 'owner_id');
    }

    /**
     * Get the unit currently rented by this user.
     */
    public function currentUnit(): HasOne
    {
        return $this->hasOne(Unit::class, 'current_tenant_id');
    }

    /**
     * Get the tenant record for this user.
     */
    public function tenant(): HasOne
    {
        return $this->hasOne(Tenant::class, 'user_id');
    }

    /**
     * Get tenants for units owned by this user.
     */
    public function tenants(): HasMany
    {
        return $this->hasMany(Tenant::class, 'owner_id');
    }

    /**
     * Get documents associated with this user.
     */
    public function documents(): MorphMany
    {
        return $this->morphMany(Document::class, 'documentable');
    }

    /**
     * Get the user who verified this user.
     */
    public function verifiedBy(): BelongsTo
    {
        return $this->belongsTo(User::class, 'verified_by');
    }

    /**
     * Get users verified by this user.
     */
    public function verifiedUsers(): HasMany
    {
        return $this->hasMany(User::class, 'verified_by');
    }

    /**
     * Scope to get users by role.
     */
    public function scopeByRole($query, string $role)
    {
        return $query->where('role', $role);
    }

    /**
     * Scope to get users by status.
     */
    public function scopeByStatus($query, string $status)
    {
        return $query->where('status', $status);
    }

    /**
     * Scope to get active users.
     */
    public function scopeActive($query)
    {
        return $query->where('status', self::STATUS_ACTIVE);
    }

    /**
     * Scope to get verified users.
     */
    public function scopeVerified($query)
    {
        return $query->where('is_verified', true);
    }

    /**
     * Scope to get users by type.
     */
    public function scopeByType($query, string $type)
    {
        return $query->where('user_type', $type);
    }

    /**
     * Get role display name.
     */
    public function getRoleDisplayName(): string
    {
        return match($this->role) {
            self::ROLE_ADMIN => 'Administrator',
            self::ROLE_OWNER => 'Owner',
            self::ROLE_TENANT => 'Tenant',
            self::ROLE_STAFF => 'Staff',
            default => 'Unknown',
        };
    }

    /**
     * Get status display name.
     */
    public function getStatusDisplayName(): string
    {
        return match($this->status) {
            self::STATUS_ACTIVE => 'Active',
            self::STATUS_INACTIVE => 'Inactive',
            self::STATUS_SUSPENDED => 'Suspended',
            self::STATUS_PENDING_VERIFICATION => 'Pending Verification',
            default => 'Unknown',
        };
    }

    /**
     * Get status color for UI.
     */
    public function getStatusColor(): string
    {
        return match($this->status) {
            self::STATUS_ACTIVE => 'success',
            self::STATUS_INACTIVE => 'secondary',
            self::STATUS_SUSPENDED => 'danger',
            self::STATUS_PENDING_VERIFICATION => 'warning',
            default => 'secondary',
        };
    }

    /**
     * Update last login information.
     */
    public function updateLastLogin(string $ipAddress = null): void
    {
        $this->update([
            'last_login_at' => now(),
            'last_login_ip' => $ipAddress,
        ]);
    }

    /**
     * Mark user as verified.
     */
    public function markAsVerified(User $verifiedBy = null): void
    {
        $this->update([
            'is_verified' => true,
            'verified_at' => now(),
            'verified_by' => $verifiedBy?->id,
            'status' => self::STATUS_ACTIVE,
        ]);
    }

    /**
     * Get all available roles.
     */
    public static function getAllRoles(): array
    {
        return [
            self::ROLE_ADMIN,
            self::ROLE_OWNER,
            self::ROLE_TENANT,
            self::ROLE_STAFF,
        ];
    }

    /**
     * Get all available statuses.
     */
    public static function getAllStatuses(): array
    {
        return [
            self::STATUS_ACTIVE,
            self::STATUS_INACTIVE,
            self::STATUS_SUSPENDED,
            self::STATUS_PENDING_VERIFICATION,
        ];
    }

    /**
     * Get all available roles (alias for getAllRoles).
     */
    public static function getRoles(): array
    {
        return self::getAllRoles();
    }

    /**
     * Get all available user types.
     */
    public static function getUserTypes(): array
    {
        return [
            self::TYPE_SOCIETY_ADMIN,
            self::TYPE_OWNER,
            self::TYPE_TENANT,
            self::TYPE_STAFF,
        ];
    }

    /**
     * Get all available statuses (alias for getAllStatuses).
     */
    public static function getStatuses(): array
    {
        return self::getAllStatuses();
    }
}
