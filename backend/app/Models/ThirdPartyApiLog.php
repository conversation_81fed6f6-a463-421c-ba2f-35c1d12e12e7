<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Carbon\Carbon;

class ThirdPartyApiLog extends Model
{
    use HasFactory;

    protected $fillable = [
        'log_id',
        'api_config_id',
        'request_method',
        'request_url',
        'request_headers',
        'request_body',
        'response_status',
        'response_headers',
        'response_body',
        'response_time',
        'ip_address',
        'user_agent',
        'status',
        'error_message',
        'metadata'
    ];

    protected $casts = [
        'request_headers' => 'array',
        'request_body' => 'array',
        'response_headers' => 'array',
        'response_body' => 'array',
        'metadata' => 'array',
        'response_time' => 'float',
        'response_status' => 'integer',
    ];

    /**
     * Status constants
     */
    const STATUS_PENDING = 'pending';
    const STATUS_SUCCESS = 'success';
    const STATUS_ERROR = 'error';
    const STATUS_TIMEOUT = 'timeout';
    const STATUS_CANCELLED = 'cancelled';

    /**
     * Get the API configuration
     */
    public function apiConfig()
    {
        return $this->belongsTo(ThirdPartyApiConfig::class, 'api_config_id');
    }

    /**
     * Scope for successful requests
     */
    public function scopeSuccessful($query)
    {
        return $query->where('status', self::STATUS_SUCCESS);
    }

    /**
     * Scope for failed requests
     */
    public function scopeFailed($query)
    {
        return $query->where('status', self::STATUS_ERROR);
    }

    /**
     * Scope for recent logs
     */
    public function scopeRecent($query, $hours = 24)
    {
        return $query->where('created_at', '>=', now()->subHours($hours));
    }

    /**
     * Scope for specific API
     */
    public function scopeForApi($query, $apiIdentifier)
    {
        return $query->whereHas('apiConfig', function ($q) use ($apiIdentifier) {
            $q->where('identifier', $apiIdentifier);
        });
    }

    /**
     * Get formatted response time
     */
    public function getFormattedResponseTimeAttribute()
    {
        if (!$this->response_time) {
            return 'N/A';
        }

        if ($this->response_time < 1) {
            return round($this->response_time * 1000, 2) . 'ms';
        }

        return round($this->response_time, 2) . 's';
    }

    /**
     * Get status color for UI
     */
    public function getStatusColorAttribute()
    {
        switch ($this->status) {
            case self::STATUS_SUCCESS:
                return 'green';
            case self::STATUS_ERROR:
                return 'red';
            case self::STATUS_TIMEOUT:
                return 'orange';
            case self::STATUS_PENDING:
                return 'blue';
            case self::STATUS_CANCELLED:
                return 'gray';
            default:
                return 'gray';
        }
    }

    /**
     * Get request size in bytes
     */
    public function getRequestSizeAttribute()
    {
        return strlen(json_encode($this->request_body));
    }

    /**
     * Get response size in bytes
     */
    public function getResponseSizeAttribute()
    {
        return strlen(json_encode($this->response_body));
    }

    /**
     * Check if request was successful
     */
    public function isSuccessful()
    {
        return $this->status === self::STATUS_SUCCESS && 
               $this->response_status >= 200 && 
               $this->response_status < 300;
    }

    /**
     * Check if request failed
     */
    public function isFailed()
    {
        return $this->status === self::STATUS_ERROR || 
               $this->response_status >= 400;
    }

    /**
     * Get summary for dashboard
     */
    public function getSummary()
    {
        return [
            'log_id' => $this->log_id,
            'api_name' => $this->apiConfig->name ?? 'Unknown',
            'method' => $this->request_method,
            'url' => $this->request_url,
            'status' => $this->status,
            'response_status' => $this->response_status,
            'response_time' => $this->formatted_response_time,
            'created_at' => $this->created_at->format('Y-m-d H:i:s'),
            'is_successful' => $this->isSuccessful(),
            'request_size' => $this->request_size,
            'response_size' => $this->response_size,
        ];
    }

    /**
     * Get detailed log information
     */
    public function getDetailedLog()
    {
        return [
            'log_id' => $this->log_id,
            'api_config' => $this->apiConfig->getDisplayConfig(),
            'request' => [
                'method' => $this->request_method,
                'url' => $this->request_url,
                'headers' => $this->request_headers,
                'body' => $this->request_body,
            ],
            'response' => [
                'status' => $this->response_status,
                'headers' => $this->response_headers,
                'body' => $this->response_body,
                'time' => $this->response_time,
            ],
            'metadata' => [
                'ip_address' => $this->ip_address,
                'user_agent' => $this->user_agent,
                'status' => $this->status,
                'error_message' => $this->error_message,
                'created_at' => $this->created_at,
                'updated_at' => $this->updated_at,
            ]
        ];
    }

    /**
     * Archive old logs
     */
    public static function archiveOldLogs($days = 30)
    {
        return static::where('created_at', '<', now()->subDays($days))
            ->delete();
    }

    /**
     * Get statistics for a specific API
     */
    public static function getApiStatistics($apiConfigId, $days = 7)
    {
        $logs = static::where('api_config_id', $apiConfigId)
            ->where('created_at', '>=', now()->subDays($days))
            ->get();

        $total = $logs->count();
        $successful = $logs->where('status', self::STATUS_SUCCESS)->count();
        $failed = $logs->where('status', self::STATUS_ERROR)->count();
        $avgResponseTime = $logs->whereNotNull('response_time')->avg('response_time');

        return [
            'total_requests' => $total,
            'successful_requests' => $successful,
            'failed_requests' => $failed,
            'success_rate' => $total > 0 ? round(($successful / $total) * 100, 2) : 0,
            'failure_rate' => $total > 0 ? round(($failed / $total) * 100, 2) : 0,
            'average_response_time' => $avgResponseTime ? round($avgResponseTime, 3) : 0,
        ];
    }

    /**
     * Get system-wide statistics
     */
    public static function getSystemStatistics($days = 7)
    {
        $logs = static::where('created_at', '>=', now()->subDays($days))
            ->get();

        $total = $logs->count();
        $successful = $logs->where('status', self::STATUS_SUCCESS)->count();
        $failed = $logs->where('status', self::STATUS_ERROR)->count();
        $avgResponseTime = $logs->whereNotNull('response_time')->avg('response_time');

        // Group by API
        $apiStats = $logs->groupBy('api_config_id')
            ->map(function ($apiLogs) {
                $apiTotal = $apiLogs->count();
                $apiSuccessful = $apiLogs->where('status', self::STATUS_SUCCESS)->count();
                
                return [
                    'api_name' => $apiLogs->first()->apiConfig->name ?? 'Unknown',
                    'total_requests' => $apiTotal,
                    'success_rate' => $apiTotal > 0 ? round(($apiSuccessful / $apiTotal) * 100, 2) : 0,
                ];
            });

        return [
            'overview' => [
                'total_requests' => $total,
                'successful_requests' => $successful,
                'failed_requests' => $failed,
                'success_rate' => $total > 0 ? round(($successful / $total) * 100, 2) : 0,
                'failure_rate' => $total > 0 ? round(($failed / $total) * 100, 2) : 0,
                'average_response_time' => $avgResponseTime ? round($avgResponseTime, 3) : 0,
            ],
            'api_breakdown' => $apiStats,
        ];
    }
} 