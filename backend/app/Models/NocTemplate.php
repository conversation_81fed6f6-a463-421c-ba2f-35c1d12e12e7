<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Factories\HasFactory;

class NocTemplate extends Model
{
    use HasFactory;
    
    // NOC type constants
    const TYPE_RENTAL = 'rental';
    const TYPE_RESIDENCE = 'residence';
    const TYPE_VEHICLE = 'vehicle';
    const TYPE_RENOVATION = 'renovation';
    const TYPE_TRANSFER = 'transfer';

    protected $fillable = [
        'name',
        'description',
        'noc_type',
        'template_content',
        'required_fields',
        'document_requirements',
        'placeholders',
        'is_active',
        'created_by',
    ];

    protected $casts = [
        'required_fields' => 'array',
        'document_requirements' => 'array',
        'placeholders' => 'array',
        'is_active' => 'boolean',
    ];

    /**
     * Get the user who created this template
     */
    public function creator(): BelongsTo
    {
        return $this->belongsTo(User::class, 'created_by');
    }

    /**
     * Get NOC applications using this template
     */
    public function applications(): HasMany
    {
        return $this->hasMany(NocApplication::class, 'template_id');
    }

    /**
     * Get all available NOC types
     */
    public static function getAllTypes(): array
    {
        return [
            self::TYPE_RENTAL,
            self::TYPE_RESIDENCE,
            self::TYPE_VEHICLE,
            self::TYPE_RENOVATION,
            self::TYPE_TRANSFER,
        ];
    }

    /**
     * Get type display name
     */
    public function getTypeDisplayName(): string
    {
        return match($this->noc_type) {
            self::TYPE_RENTAL => 'Rental NOC',
            self::TYPE_RESIDENCE => 'Residence NOC',
            self::TYPE_VEHICLE => 'Vehicle Parking NOC',
            self::TYPE_RENOVATION => 'Renovation NOC',
            self::TYPE_TRANSFER => 'Transfer NOC',
            default => 'Unknown NOC Type',
        };
    }

    /**
     * Scope for active templates
     */
    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }

    /**
     * Scope for specific NOC type
     */
    public function scopeByType($query, string $type)
    {
        return $query->where('noc_type', $type);
    }

    /**
     * Get default document requirements for NOC type
     */
    public static function getDefaultDocumentRequirements(string $type): array
    {
        return match($type) {
            self::TYPE_RENTAL => [
                'rental_agreement' => [
                    'label' => 'Rental Agreement',
                    'description' => 'Valid rental agreement with landlord',
                    'required' => true,
                    'max_files' => 1,
                    'allowed_types' => ['pdf'],
                ],
                'id_proof' => [
                    'label' => 'ID Proof',
                    'description' => 'Government issued photo ID',
                    'required' => true,
                    'max_files' => 1,
                    'allowed_types' => ['pdf', 'jpg', 'jpeg', 'png'],
                ],
                'police_verification' => [
                    'label' => 'Police Verification',
                    'description' => 'Police verification certificate',
                    'required' => true,
                    'max_files' => 1,
                    'allowed_types' => ['pdf'],
                ],
            ],
            self::TYPE_RESIDENCE => [
                'ownership_proof' => [
                    'label' => 'Ownership Proof',
                    'description' => 'Property ownership documents',
                    'required' => true,
                    'max_files' => 2,
                    'allowed_types' => ['pdf'],
                ],
                'id_proof' => [
                    'label' => 'ID Proof',
                    'description' => 'Government issued photo ID',
                    'required' => true,
                    'max_files' => 1,
                    'allowed_types' => ['pdf', 'jpg', 'jpeg', 'png'],
                ],
            ],
            self::TYPE_VEHICLE => [
                'vehicle_registration' => [
                    'label' => 'Vehicle Registration',
                    'description' => 'Vehicle registration certificate',
                    'required' => true,
                    'max_files' => 1,
                    'allowed_types' => ['pdf', 'jpg', 'jpeg', 'png'],
                ],
                'driving_license' => [
                    'label' => 'Driving License',
                    'description' => 'Valid driving license',
                    'required' => true,
                    'max_files' => 1,
                    'allowed_types' => ['pdf', 'jpg', 'jpeg', 'png'],
                ],
                'insurance' => [
                    'label' => 'Vehicle Insurance',
                    'description' => 'Valid vehicle insurance certificate',
                    'required' => true,
                    'max_files' => 1,
                    'allowed_types' => ['pdf'],
                ],
            ],
            self::TYPE_RENOVATION => [
                'renovation_plan' => [
                    'label' => 'Renovation Plan',
                    'description' => 'Detailed renovation plan and drawings',
                    'required' => true,
                    'max_files' => 3,
                    'allowed_types' => ['pdf', 'jpg', 'jpeg', 'png'],
                ],
                'contractor_details' => [
                    'label' => 'Contractor Details',
                    'description' => 'Licensed contractor information',
                    'required' => true,
                    'max_files' => 2,
                    'allowed_types' => ['pdf'],
                ],
                'timeline' => [
                    'label' => 'Project Timeline',
                    'description' => 'Expected renovation timeline',
                    'required' => true,
                    'max_files' => 1,
                    'allowed_types' => ['pdf'],
                ],
            ],
            self::TYPE_TRANSFER => [
                'transfer_deed' => [
                    'label' => 'Transfer Deed',
                    'description' => 'Property transfer documents',
                    'required' => true,
                    'max_files' => 2,
                    'allowed_types' => ['pdf'],
                ],
                'new_owner_id' => [
                    'label' => 'New Owner ID',
                    'description' => 'New owner identification documents',
                    'required' => true,
                    'max_files' => 1,
                    'allowed_types' => ['pdf', 'jpg', 'jpeg', 'png'],
                ],
                'legal_clearance' => [
                    'label' => 'Legal Clearance',
                    'description' => 'Legal clearance certificates',
                    'required' => false,
                    'max_files' => 2,
                    'allowed_types' => ['pdf'],
                ],
            ],
            default => [],
        };
    }

    /**
     * Get default required fields for NOC type
     */
    public static function getDefaultRequiredFields(string $type): array
    {
        $commonFields = [
            'applicant_name' => [
                'label' => 'Applicant Name',
                'type' => 'text',
                'required' => true,
                'validation' => 'required|string|max:255',
            ],
            'unit_number' => [
                'label' => 'Unit Number',
                'type' => 'text',
                'required' => true,
                'validation' => 'required|string|max:50',
            ],
            'contact_number' => [
                'label' => 'Contact Number',
                'type' => 'tel',
                'required' => true,
                'validation' => 'required|string|max:15',
            ],
            'email' => [
                'label' => 'Email Address',
                'type' => 'email',
                'required' => true,
                'validation' => 'required|email|max:255',
            ],
        ];

        $typeSpecificFields = match($type) {
            self::TYPE_RENTAL => [
                'tenant_name' => [
                    'label' => 'Tenant Name',
                    'type' => 'text',
                    'required' => true,
                    'validation' => 'required|string|max:255',
                ],
                'rental_amount' => [
                    'label' => 'Monthly Rental Amount',
                    'type' => 'number',
                    'required' => true,
                    'validation' => 'required|numeric|min:0',
                ],
                'lease_duration' => [
                    'label' => 'Lease Duration (months)',
                    'type' => 'number',
                    'required' => true,
                    'validation' => 'required|integer|min:1|max:60',
                ],
                'move_in_date' => [
                    'label' => 'Expected Move-in Date',
                    'type' => 'date',
                    'required' => true,
                    'validation' => 'required|date|after:today',
                ],
            ],
            self::TYPE_VEHICLE => [
                'vehicle_type' => [
                    'label' => 'Vehicle Type',
                    'type' => 'select',
                    'required' => true,
                    'options' => ['car', 'motorcycle', 'bicycle', 'other'],
                    'validation' => 'required|in:car,motorcycle,bicycle,other',
                ],
                'vehicle_number' => [
                    'label' => 'Vehicle Registration Number',
                    'type' => 'text',
                    'required' => true,
                    'validation' => 'required|string|max:20',
                ],
                'parking_slot' => [
                    'label' => 'Preferred Parking Slot',
                    'type' => 'text',
                    'required' => false,
                    'validation' => 'nullable|string|max:20',
                ],
            ],
            self::TYPE_RENOVATION => [
                'renovation_type' => [
                    'label' => 'Type of Renovation',
                    'type' => 'select',
                    'required' => true,
                    'options' => ['interior', 'exterior', 'structural', 'electrical', 'plumbing'],
                    'validation' => 'required|in:interior,exterior,structural,electrical,plumbing',
                ],
                'start_date' => [
                    'label' => 'Expected Start Date',
                    'type' => 'date',
                    'required' => true,
                    'validation' => 'required|date|after:today',
                ],
                'end_date' => [
                    'label' => 'Expected Completion Date',
                    'type' => 'date',
                    'required' => true,
                    'validation' => 'required|date|after:start_date',
                ],
                'contractor_name' => [
                    'label' => 'Contractor Name',
                    'type' => 'text',
                    'required' => true,
                    'validation' => 'required|string|max:255',
                ],
            ],
            default => [],
        };

        return array_merge($commonFields, $typeSpecificFields);
    }
} 