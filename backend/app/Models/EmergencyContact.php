<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Builder;

class EmergencyContact extends Model
{
    use HasFactory;

    // Relationship constants
    const RELATIONSHIP_PARENT = 'parent';
    const RELATIONSHIP_SPOUSE = 'spouse';
    const RELATIONSHIP_SIBLING = 'sibling';
    const RELATIONSHIP_CHILD = 'child';
    const RELATIONSHIP_RELATIVE = 'relative';
    const RELATIONSHIP_FRIEND = 'friend';
    const RELATIONSHIP_COLLEAGUE = 'colleague';
    const RELATIONSHIP_NEIGHBOR = 'neighbor';
    const RELATIONSHIP_OTHER = 'other';

    /**
     * The attributes that are mass assignable.
     */
    protected $fillable = [
        'tenant_id',
        'name',
        'relationship',
        'primary_phone',
        'secondary_phone',
        'email',
        'address',
        'city',
        'state',
        'country',
        'pincode',
        'priority',
        'is_local',
        'is_active',
        'notes',
        'metadata',
        'created_by',
        'updated_by',
    ];

    /**
     * The attributes that should be cast.
     */
    protected $casts = [
        'is_local' => 'boolean',
        'is_active' => 'boolean',
        'metadata' => 'array',
        'priority' => 'integer',
    ];

    /**
     * Get the tenant that owns this emergency contact.
     */
    public function tenant(): BelongsTo
    {
        return $this->belongsTo(Tenant::class);
    }

    /**
     * Get the user who created this emergency contact.
     */
    public function createdBy(): BelongsTo
    {
        return $this->belongsTo(User::class, 'created_by');
    }

    /**
     * Get the user who last updated this emergency contact.
     */
    public function updatedBy(): BelongsTo
    {
        return $this->belongsTo(User::class, 'updated_by');
    }

    /**
     * Scope to get active emergency contacts.
     */
    public function scopeActive(Builder $query): Builder
    {
        return $query->where('is_active', true);
    }

    /**
     * Scope to get emergency contacts by priority.
     */
    public function scopeByPriority(Builder $query, int $priority): Builder
    {
        return $query->where('priority', $priority);
    }

    /**
     * Scope to get primary emergency contacts.
     */
    public function scopePrimary(Builder $query): Builder
    {
        return $query->where('priority', 1);
    }

    /**
     * Scope to get local emergency contacts.
     */
    public function scopeLocal(Builder $query): Builder
    {
        return $query->where('is_local', true);
    }

    /**
     * Scope to get emergency contacts by relationship.
     */
    public function scopeByRelationship(Builder $query, string $relationship): Builder
    {
        return $query->where('relationship', $relationship);
    }

    /**
     * Scope to get emergency contacts ordered by priority.
     */
    public function scopeOrderedByPriority(Builder $query): Builder
    {
        return $query->orderBy('priority')->orderBy('created_at');
    }

    /**
     * Check if this is a primary emergency contact.
     */
    public function isPrimary(): bool
    {
        return $this->priority === 1;
    }

    /**
     * Check if this emergency contact is local.
     */
    public function isLocal(): bool
    {
        return $this->is_local;
    }

    /**
     * Check if this emergency contact is active.
     */
    public function isActive(): bool
    {
        return $this->is_active;
    }

    /**
     * Get the full address.
     */
    public function getFullAddressAttribute(): string
    {
        $parts = array_filter([
            $this->address,
            $this->city,
            $this->state,
            $this->country,
            $this->pincode ? "PIN: {$this->pincode}" : null,
        ]);

        return implode(', ', $parts);
    }

    /**
     * Get the display name with relationship.
     */
    public function getDisplayNameAttribute(): string
    {
        return "{$this->name} ({$this->getRelationshipDisplayName()})";
    }

    /**
     * Get the relationship display name.
     */
    public function getRelationshipDisplayName(): string
    {
        return match($this->relationship) {
            self::RELATIONSHIP_PARENT => 'Parent',
            self::RELATIONSHIP_SPOUSE => 'Spouse',
            self::RELATIONSHIP_SIBLING => 'Sibling',
            self::RELATIONSHIP_CHILD => 'Child',
            self::RELATIONSHIP_RELATIVE => 'Relative',
            self::RELATIONSHIP_FRIEND => 'Friend',
            self::RELATIONSHIP_COLLEAGUE => 'Colleague',
            self::RELATIONSHIP_NEIGHBOR => 'Neighbor',
            self::RELATIONSHIP_OTHER => 'Other',
            default => ucfirst($this->relationship),
        };
    }

    /**
     * Get the priority display name.
     */
    public function getPriorityDisplayName(): string
    {
        return match($this->priority) {
            1 => 'Primary',
            2 => 'Secondary',
            3 => 'Tertiary',
            default => "Priority {$this->priority}",
        };
    }

    /**
     * Get the priority color for UI.
     */
    public function getPriorityColor(): string
    {
        return match($this->priority) {
            1 => 'success',
            2 => 'warning',
            3 => 'info',
            default => 'secondary',
        };
    }

    /**
     * Get all available relationships.
     */
    public static function getAllRelationships(): array
    {
        return [
            self::RELATIONSHIP_PARENT,
            self::RELATIONSHIP_SPOUSE,
            self::RELATIONSHIP_SIBLING,
            self::RELATIONSHIP_CHILD,
            self::RELATIONSHIP_RELATIVE,
            self::RELATIONSHIP_FRIEND,
            self::RELATIONSHIP_COLLEAGUE,
            self::RELATIONSHIP_NEIGHBOR,
            self::RELATIONSHIP_OTHER,
        ];
    }

    /**
     * Get relationship options for forms.
     */
    public static function getRelationshipOptions(): array
    {
        return [
            self::RELATIONSHIP_PARENT => 'Parent',
            self::RELATIONSHIP_SPOUSE => 'Spouse',
            self::RELATIONSHIP_SIBLING => 'Sibling',
            self::RELATIONSHIP_CHILD => 'Child',
            self::RELATIONSHIP_RELATIVE => 'Relative',
            self::RELATIONSHIP_FRIEND => 'Friend',
            self::RELATIONSHIP_COLLEAGUE => 'Colleague',
            self::RELATIONSHIP_NEIGHBOR => 'Neighbor',
            self::RELATIONSHIP_OTHER => 'Other',
        ];
    }

    /**
     * Validate phone number format.
     */
    public function isValidPhoneNumber(string $phone): bool
    {
        // Basic phone validation - can be enhanced based on requirements
        return preg_match('/^[+]?[0-9\s\-\(\)]{10,15}$/', $phone);
    }

    /**
     * Format phone number for display.
     */
    public function formatPhoneNumber(string $phone): string
    {
        // Remove non-numeric characters except +
        $cleaned = preg_replace('/[^0-9+]/', '', $phone);
        
        // Basic formatting for Indian numbers
        if (strlen($cleaned) === 10) {
            return preg_replace('/(\d{5})(\d{5})/', '$1-$2', $cleaned);
        } elseif (strlen($cleaned) === 13 && str_starts_with($cleaned, '+91')) {
            return preg_replace('/(\+91)(\d{5})(\d{5})/', '$1 $2-$3', $cleaned);
        }
        
        return $phone;
    }

    /**
     * Get formatted primary phone.
     */
    public function getFormattedPrimaryPhoneAttribute(): string
    {
        return $this->formatPhoneNumber($this->primary_phone);
    }

    /**
     * Get formatted secondary phone.
     */
    public function getFormattedSecondaryPhoneAttribute(): string
    {
        return $this->secondary_phone ? $this->formatPhoneNumber($this->secondary_phone) : '';
    }

    /**
     * Get contact summary for notifications.
     */
    public function getContactSummary(): array
    {
        return [
            'id' => $this->id,
            'name' => $this->name,
            'relationship' => $this->getRelationshipDisplayName(),
            'priority' => $this->getPriorityDisplayName(),
            'primary_phone' => $this->formatted_primary_phone,
            'secondary_phone' => $this->formatted_secondary_phone,
            'email' => $this->email,
            'is_local' => $this->is_local,
            'is_active' => $this->is_active,
        ];
    }
} 