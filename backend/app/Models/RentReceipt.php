<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Support\Str;

class RentReceipt extends Model
{
    protected $fillable = [
        'payment_id',
        'receipt_number',
        'generated_at',
        'recipient_email',
        'delivery_status',
        'pdf_path',
        'metadata',
        'delivery_notes',
        'delivered_at',
        'verification_code',
        'qr_code_path',
    ];

    protected $casts = [
        'generated_at' => 'datetime',
        'delivered_at' => 'datetime',
        'metadata' => 'array',
    ];

    protected static function boot()
    {
        parent::boot();

        static::creating(function ($receipt) {
            if (empty($receipt->receipt_number)) {
                $receipt->receipt_number = static::generateReceiptNumber();
            }
            
            if (empty($receipt->verification_code)) {
                $receipt->verification_code = static::generateVerificationCode();
            }
        });
    }

    /**
     * Get the payment that owns the receipt.
     */
    public function payment(): BelongsTo
    {
        return $this->belongsTo(RentPayment::class, 'payment_id');
    }

    /**
     * Get the unit through the payment.
     */
    public function unit(): BelongsTo
    {
        return $this->belongsTo(Unit::class, 'unit_id', 'id');
    }

    /**
     * Get the tenant through the payment.
     */
    public function tenant(): BelongsTo
    {
        return $this->belongsTo(User::class, 'tenant_id', 'id');
    }

    /**
     * Generate a unique receipt number.
     */
    public static function generateReceiptNumber(): string
    {
        $year = now()->year;
        $prefix = "REC-{$year}-";
        
        $lastReceipt = static::where('receipt_number', 'like', $prefix . '%')
            ->orderBy('receipt_number', 'desc')
            ->first();

        if ($lastReceipt) {
            $lastNumber = (int) str_replace($prefix, '', $lastReceipt->receipt_number);
            $nextNumber = $lastNumber + 1;
        } else {
            $nextNumber = 1;
        }

        return $prefix . str_pad($nextNumber, 6, '0', STR_PAD_LEFT);
    }

    /**
     * Generate a unique verification code.
     */
    public static function generateVerificationCode(): string
    {
        do {
            $code = strtoupper(Str::random(8));
        } while (static::where('verification_code', $code)->exists());

        return $code;
    }

    /**
     * Check if receipt is delivered.
     */
    public function isDelivered(): bool
    {
        return $this->delivery_status === 'delivered';
    }

    /**
     * Check if receipt delivery failed.
     */
    public function isDeliveryFailed(): bool
    {
        return $this->delivery_status === 'failed';
    }

    /**
     * Check if receipt is pending delivery.
     */
    public function isPendingDelivery(): bool
    {
        return $this->delivery_status === 'pending';
    }

    /**
     * Mark receipt as delivered.
     */
    public function markAsDelivered(): bool
    {
        return $this->update([
            'delivery_status' => 'delivered',
            'delivered_at' => now(),
        ]);
    }

    /**
     * Mark receipt as failed.
     */
    public function markAsFailed(string $notes = null): bool
    {
        return $this->update([
            'delivery_status' => 'failed',
            'delivery_notes' => $notes,
        ]);
    }

    /**
     * Get the full URL to the PDF file.
     */
    public function getPdfUrlAttribute(): ?string
    {
        if (!$this->pdf_path) {
            return null;
        }

        return asset('storage/' . $this->pdf_path);
    }

    /**
     * Get the full URL to the QR code image.
     */
    public function getQrCodeUrlAttribute(): ?string
    {
        if (!$this->qr_code_path) {
            return null;
        }

        return asset('storage/' . $this->qr_code_path);
    }
}
