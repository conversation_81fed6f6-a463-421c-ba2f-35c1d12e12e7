<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\MorphMany;
use Illuminate\Support\Facades\Auth;

class PropertyListing extends Model
{
    use HasFactory, SoftDeletes;

    // Listing status constants
    const STATUS_DRAFT = 'draft';
    const STATUS_PENDING_APPROVAL = 'pending_approval';
    const STATUS_APPROVED = 'approved';
    const STATUS_PUBLISHED = 'published';
    const STATUS_EXPIRED = 'expired';
    const STATUS_INACTIVE = 'inactive';

    /**
     * The attributes that are mass assignable.
     */
    protected $fillable = [
        'unit_id',
        'listing_title',
        'description',
        'rent_amount',
        'deposit_amount',
        'available_from',
        'listing_status',
        'portal_mappings',
        'media_urls',
        'amenities',
        'preferences',
        'created_by',
        'approved_by',
        'approved_at',
        'rejection_reason',
        'published_at',
        'expires_at',
        'is_featured',
        'is_urgent',
        'view_count',
        'inquiry_count',
        'metadata',
    ];

    /**
     * The attributes that should be cast.
     */
    protected $casts = [
        'portal_mappings' => 'array',
        'media_urls' => 'array',
        'amenities' => 'array',
        'preferences' => 'array',
        'metadata' => 'array',
        'rent_amount' => 'decimal:2',
        'deposit_amount' => 'decimal:2',
        'available_from' => 'date',
        'approved_at' => 'datetime',
        'published_at' => 'datetime',
        'expires_at' => 'datetime',
        'is_featured' => 'boolean',
        'is_urgent' => 'boolean',
        'view_count' => 'integer',
        'inquiry_count' => 'integer',
    ];

    /**
     * Get all possible listing statuses
     */
    public static function getStatuses(): array
    {
        return [
            self::STATUS_DRAFT,
            self::STATUS_PENDING_APPROVAL,
            self::STATUS_APPROVED,
            self::STATUS_PUBLISHED,
            self::STATUS_EXPIRED,
            self::STATUS_INACTIVE,
        ];
    }

    /**
     * Get the unit this listing belongs to
     */
    public function unit(): BelongsTo
    {
        return $this->belongsTo(Unit::class);
    }

    /**
     * Get the user who created this listing
     */
    public function creator(): BelongsTo
    {
        return $this->belongsTo(User::class, 'created_by');
    }

    /**
     * Get the user who approved this listing
     */
    public function approver(): BelongsTo
    {
        return $this->belongsTo(User::class, 'approved_by');
    }

    /**
     * Get all leads for this listing
     */
    public function leads(): HasMany
    {
        return $this->hasMany(PropertyLead::class, 'listing_id');
    }

    /**
     * Get documents associated with this listing
     */
    public function documents(): MorphMany
    {
        return $this->morphMany(Document::class, 'documentable');
    }

    /**
     * Scope for filtering by rent range.
     */
    public function scopeRentRange($query, $value)
    {
        if (is_string($value) && str_contains($value, '-')) {
            [$min, $max] = explode('-', $value);
            return $query->whereBetween('rent_amount', [(float)$min, (float)$max]);
        }
        return $query;
    }

    /**
     * Scope for filtering by number of bedrooms.
     */
    public function scopeBedrooms($query, $value)
    {
        return $query->whereHas('unit', function ($q) use ($value) {
            $q->where('bedrooms', $value);
        });
    }

    /**
     * Scope for filtering by unit type.
     */
    public function scopeUnitType($query, $value)
    {
        return $query->whereHas('unit', function ($q) use ($value) {
            $q->where('unit_type', $value);
        });
    }

    /**
     * Scope for filtering by owner.
     */
    public function scopeByOwner($query, $ownerId)
    {
        return $query->whereHas('unit', function ($q) use ($ownerId) {
            $q->where('owner_id', $ownerId);
        });
    }

    /**
     * Scope for active listings.
     */
    public function scopeActive($query)
    {
        return $query->whereIn('listing_status', [
            self::STATUS_APPROVED,
            self::STATUS_PUBLISHED
        ]);
    }

    /**
     * Scope for published listings.
     */
    public function scopePublished($query)
    {
        return $query->where('listing_status', self::STATUS_PUBLISHED);
    }

    /**
     * Scope for expired listings.
     */
    public function scopeExpired($query)
    {
        return $query->where('expires_at', '<', now())
            ->orWhere('listing_status', self::STATUS_EXPIRED);
    }

    /**
     * Scope for featured listings.
     */
    public function scopeFeatured($query)
    {
        return $query->where('is_featured', true);
    }

    /**
     * Scope for urgent listings.
     */
    public function scopeUrgent($query)
    {
        return $query->where('is_urgent', true);
    }

    /**
     * Get the owner of the unit
     */
    public function owner(): BelongsTo
    {
        return $this->belongsTo(User::class, 'owner_id');
    }

    /**
     * Scope to get listings by status
     */
    public function scopeByStatus($query, $status)
    {
        return $query->where('listing_status', $status);
    }





    /**
     * Scope to get pending approval listings
     */
    public function scopePendingApproval($query)
    {
        return $query->where('listing_status', self::STATUS_PENDING_APPROVAL);
    }

    /**
     * Scope to get listings by creator
     */
    public function scopeByCreator($query, $creatorId)
    {
        return $query->where('created_by', $creatorId);
    }

    /**
     * Scope to get listings by unit
     */
    public function scopeByUnit($query, $unitId)
    {
        return $query->where('unit_id', $unitId);
    }



    /**
     * Check if listing is draft
     */
    public function isDraft(): bool
    {
        return $this->listing_status === self::STATUS_DRAFT;
    }

    /**
     * Check if listing is pending approval
     */
    public function isPendingApproval(): bool
    {
        return $this->listing_status === self::STATUS_PENDING_APPROVAL;
    }

    /**
     * Check if listing is approved
     */
    public function isApproved(): bool
    {
        return $this->listing_status === self::STATUS_APPROVED;
    }

    /**
     * Check if listing is published
     */
    public function isPublished(): bool
    {
        return $this->listing_status === self::STATUS_PUBLISHED;
    }

    /**
     * Check if listing is expired
     */
    public function isExpired(): bool
    {
        return $this->listing_status === self::STATUS_EXPIRED;
    }

    /**
     * Check if listing is inactive
     */
    public function isInactive(): bool
    {
        return $this->listing_status === self::STATUS_INACTIVE;
    }

    /**
     * Check if listing is featured
     */
    public function isFeatured(): bool
    {
        return $this->is_featured;
    }

    /**
     * Check if listing is urgent
     */
    public function isUrgent(): bool
    {
        return $this->is_urgent;
    }

    /**
     * Approve the listing
     */
    public function approve(User $approver): bool
    {
        return $this->update([
            'listing_status' => self::STATUS_APPROVED,
            'approved_by' => $approver->id,
            'approved_at' => now(),
            'rejection_reason' => null,
        ]);
    }

    /**
     * Reject the listing
     */
    public function reject(string $reason, User $rejector): bool
    {
        return $this->update([
            'listing_status' => self::STATUS_DRAFT,
            'rejection_reason' => $reason,
            'approved_by' => $rejector->id,
            'approved_at' => now(),
        ]);
    }

    /**
     * Publish the listing
     */
    public function publish(): bool
    {
        return $this->update([
            'listing_status' => self::STATUS_PUBLISHED,
            'published_at' => now(),
        ]);
    }

    /**
     * Expire the listing
     */
    public function expire(): bool
    {
        return $this->update([
            'listing_status' => self::STATUS_EXPIRED,
            'expires_at' => now(),
        ]);
    }

    /**
     * Deactivate the listing
     */
    public function deactivate(): bool
    {
        return $this->update([
            'listing_status' => self::STATUS_INACTIVE,
        ]);
    }

    /**
     * Increment view count
     */
    public function incrementViewCount(): bool
    {
        return $this->increment('view_count');
    }

    /**
     * Increment inquiry count
     */
    public function incrementInquiryCount(): bool
    {
        return $this->increment('inquiry_count');
    }

    /**
     * Get listing display name
     */
    public function getDisplayNameAttribute(): string
    {
        return $this->listing_title ?: "Listing for {$this->unit->display_name}";
    }

    /**
     * Get status color for UI
     */
    public function getStatusColorAttribute(): string
    {
        return match ($this->listing_status) {
            self::STATUS_DRAFT => 'secondary',
            self::STATUS_PENDING_APPROVAL => 'warning',
            self::STATUS_APPROVED => 'info',
            self::STATUS_PUBLISHED => 'success',
            self::STATUS_EXPIRED => 'danger',
            self::STATUS_INACTIVE => 'dark',
            default => 'secondary',
        };
    }

    /**
     * Get status label for UI
     */
    public function getStatusLabelAttribute(): string
    {
        return match ($this->listing_status) {
            self::STATUS_DRAFT => 'Draft',
            self::STATUS_PENDING_APPROVAL => 'Pending Approval',
            self::STATUS_APPROVED => 'Approved',
            self::STATUS_PUBLISHED => 'Published',
            self::STATUS_EXPIRED => 'Expired',
            self::STATUS_INACTIVE => 'Inactive',
            default => 'Unknown',
        };
    }

    /**
     * Get portal mappings as array
     */
    public function getPortalMappings(): array
    {
        return $this->portal_mappings ?? [];
    }

    /**
     * Get media URLs as array
     */
    public function getMediaUrls(): array
    {
        return $this->media_urls ?? [];
    }

    /**
     * Get amenities as array
     */
    public function getAmenities(): array
    {
        return $this->amenities ?? [];
    }

    /**
     * Get preferences as array
     */
    public function getPreferences(): array
    {
        return $this->preferences ?? [];
    }

    /**
     * Get full address from unit
     */
    public function getFullAddressAttribute(): string
    {
        return $this->unit->full_address ?? 'Address not available';
    }

    /**
     * Get formatted rent amount
     */
    public function getFormattedRentAmountAttribute(): string
    {
        return '₹' . number_format($this->rent_amount, 2);
    }

    /**
     * Get formatted deposit amount
     */
    public function getFormattedDepositAmountAttribute(): string
    {
        return $this->deposit_amount ? '₹' . number_format($this->deposit_amount, 2) : 'Not specified';
    }

    /**
     * Get the title attribute (maps to listing_title).
     */
    public function getTitleAttribute(): ?string
    {
        return $this->listing_title;
    }

    /**
     * Get the status attribute (maps to listing_status).
     */
    public function getStatusAttribute(): ?string
    {
        return $this->listing_status;
    }

    /**
     * Get the portal sync status for the listing.
     */
    public function getPortalSyncStatusAttribute(): array
    {
        $portals = ['99acres', 'magicbricks', 'housing', 'olx', 'nobroker'];
        $status = [];

        foreach ($portals as $portal) {
            $mapping = $this->portal_mappings[$portal] ?? null;
            $status[$portal] = [
                'synced' => !empty($mapping['portal_listing_id']),
                'portal_listing_id' => $mapping['portal_listing_id'] ?? null,
                'last_sync' => $mapping['last_sync'] ?? null,
                'sync_status' => $mapping['sync_status'] ?? 'not_synced',
                'error_message' => $mapping['error_message'] ?? null,
            ];
        }

        return $status;
    }
}
