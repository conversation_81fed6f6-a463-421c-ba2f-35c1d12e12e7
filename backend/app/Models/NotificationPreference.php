<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Factories\HasFactory;

class NotificationPreference extends Model
{
    use HasFactory;

    /**
     * The attributes that are mass assignable.
     */
    protected $fillable = [
        'user_id',
        'notification_type',
        'channel',
        'enabled',
        'settings',
        'frequency',
        'quiet_hours_start',
        'quiet_hours_end',
        'metadata',
    ];

    /**
     * The attributes that should be cast.
     */
    protected $casts = [
        'enabled' => 'boolean',
        'settings' => 'array',
        'metadata' => 'array',
        'quiet_hours_start' => 'datetime',
        'quiet_hours_end' => 'datetime',
    ];

    /**
     * Notification types
     */
    const TYPE_SYSTEM = 'system';
    const TYPE_TENANT = 'tenant';
    const TYPE_UNIT = 'unit';
    const TYPE_ENQUIRY = 'enquiry';
    const TYPE_NOC = 'noc';
    const TYPE_AGREEMENT = 'agreement';
    const TYPE_BILLING = 'billing';
    const TYPE_MAINTENANCE = 'maintenance';
    const TYPE_PAYMENT = 'payment';
    const TYPE_PAYMENT_OVERDUE = 'payment_overdue';
    const TYPE_PAYMENT_RECONCILIATION = 'payment_reconciliation';

    /**
     * Notification channels
     */
    const CHANNEL_EMAIL = 'email';
    const CHANNEL_SMS = 'sms';
    const CHANNEL_PUSH = 'push';
    const CHANNEL_IN_APP = 'in_app';

    /**
     * Frequency options
     */
    const FREQUENCY_IMMEDIATE = 'immediate';
    const FREQUENCY_DAILY = 'daily';
    const FREQUENCY_WEEKLY = 'weekly';

    /**
     * Get the user that owns the preference.
     */
    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    /**
     * Scope to get enabled preferences
     */
    public function scopeEnabled($query)
    {
        return $query->where('enabled', true);
    }

    /**
     * Scope to get disabled preferences
     */
    public function scopeDisabled($query)
    {
        return $query->where('enabled', false);
    }

    /**
     * Scope to get preferences by type
     */
    public function scopeByType($query, $type)
    {
        return $query->where('notification_type', $type);
    }

    /**
     * Scope to get preferences by channel
     */
    public function scopeByChannel($query, $channel)
    {
        return $query->where('channel', $channel);
    }

    /**
     * Scope to get preferences for a specific user
     */
    public function scopeForUser($query, $userId)
    {
        return $query->where('user_id', $userId);
    }

    /**
     * Check if preference is enabled
     */
    public function isEnabled(): bool
    {
        return $this->enabled;
    }

    /**
     * Check if preference is disabled
     */
    public function isDisabled(): bool
    {
        return !$this->enabled;
    }

    /**
     * Check if notification should be sent based on quiet hours
     */
    public function shouldSendInQuietHours(): bool
    {
        if (!$this->quiet_hours_start || !$this->quiet_hours_end) {
            return true;
        }

        $now = now();
        $start = $this->quiet_hours_start;
        $end = $this->quiet_hours_end;

        // Handle overnight quiet hours
        if ($start > $end) {
            return $now < $start && $now > $end;
        }

        return $now < $start || $now > $end;
    }

    /**
     * Get setting value
     */
    public function getSetting(string $key, $default = null)
    {
        return $this->settings[$key] ?? $default;
    }

    /**
     * Set setting value
     */
    public function setSetting(string $key, $value): bool
    {
        $settings = $this->settings ?? [];
        $settings[$key] = $value;
        
        return $this->update(['settings' => $settings]);
    }

    /**
     * Get all possible notification types
     */
    public static function getTypes(): array
    {
        return [
            self::TYPE_SYSTEM,
            self::TYPE_TENANT,
            self::TYPE_UNIT,
            self::TYPE_ENQUIRY,
            self::TYPE_NOC,
            self::TYPE_AGREEMENT,
            self::TYPE_BILLING,
            self::TYPE_MAINTENANCE,
            self::TYPE_PAYMENT,
            self::TYPE_PAYMENT_OVERDUE,
            self::TYPE_PAYMENT_RECONCILIATION,
        ];
    }

    /**
     * Get all possible notification channels
     */
    public static function getChannels(): array
    {
        return [
            self::CHANNEL_EMAIL,
            self::CHANNEL_SMS,
            self::CHANNEL_PUSH,
            self::CHANNEL_IN_APP,
        ];
    }

    /**
     * Get all possible frequency options
     */
    public static function getFrequencies(): array
    {
        return [
            self::FREQUENCY_IMMEDIATE,
            self::FREQUENCY_DAILY,
            self::FREQUENCY_WEEKLY,
        ];
    }
}
