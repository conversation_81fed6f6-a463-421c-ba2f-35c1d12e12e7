<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class AgreementAuditLog extends Model
{
    use HasFactory;

    protected $fillable = [
        'agreement_id',
        'user_id',
        'action',
        'entity_type',
        'entity_id',
        'old_values',
        'new_values',
        'metadata',
        'ip_address',
        'user_agent',
        'performed_at',
    ];

    protected $casts = [
        'old_values' => 'array',
        'new_values' => 'array',
        'metadata' => 'array',
        'performed_at' => 'datetime',
    ];

    /**
     * Get the agreement that this audit log belongs to
     */
    public function agreement(): BelongsTo
    {
        return $this->belongsTo(Agreement::class);
    }

    /**
     * Get the user who performed the action
     */
    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    /**
     * Scope to filter by action type
     */
    public function scopeByAction($query, string $action)
    {
        return $query->where('action', $action);
    }

    /**
     * Scope to filter by date range
     */
    public function scopeByDateRange($query, $startDate, $endDate)
    {
        return $query->whereBetween('performed_at', [$startDate, $endDate]);
    }

    /**
     * Scope to filter by user
     */
    public function scopeByUser($query, int $userId)
    {
        return $query->where('user_id', $userId);
    }

    /**
     * Scope to get recent logs
     */
    public function scopeRecent($query, int $days = 30)
    {
        return $query->where('performed_at', '>=', now()->subDays($days));
    }

    /**
     * Get formatted action description
     */
    public function getActionDescriptionAttribute(): string
    {
        return match($this->action) {
            'created' => 'Agreement created',
            'updated' => 'Agreement updated',
            'signed_by_tenant' => 'Signed by tenant',
            'signed_by_owner' => 'Signed by owner',
            'fully_signed' => 'Fully signed by all parties',
            'renewed' => 'Agreement renewed',
            'terminated' => 'Agreement terminated',
            'expired' => 'Agreement expired',
            'status_changed' => 'Status changed',
            'reminder_sent' => 'Renewal reminder sent',
            'signature_workflow_started' => 'Digital signature workflow started',
            'signature_workflow_completed' => 'Digital signature workflow completed',
            'signature_workflow_cancelled' => 'Digital signature workflow cancelled',
            'document_uploaded' => 'Document uploaded',
            'document_deleted' => 'Document deleted',
            default => ucfirst(str_replace('_', ' ', $this->action))
        };
    }

    /**
     * Get the changes made in a readable format
     */
    public function getChangesAttribute(): array
    {
        $changes = [];
        
        if ($this->old_values && $this->new_values) {
            foreach ($this->new_values as $key => $newValue) {
                $oldValue = $this->old_values[$key] ?? null;
                if ($oldValue !== $newValue) {
                    $changes[$key] = [
                        'old' => $oldValue,
                        'new' => $newValue
                    ];
                }
            }
        }
        
        return $changes;
    }
} 