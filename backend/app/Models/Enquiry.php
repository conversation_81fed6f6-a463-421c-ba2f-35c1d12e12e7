<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Carbon\Carbon;

class Enquiry extends Model
{
    use HasFactory;

    protected $fillable = [
        'unit_id',
        'enquirer_id',
        'owner_id',
        'enquiry_type',
        'status',
        'priority',
        'message',
        'enquirer_details',
        'communication_history',
        'response_data',
        'contacted_at',
        'responded_at',
        'closed_at',
        'notes',
        'metadata',
    ];

    protected $casts = [
        'enquirer_details' => 'array',
        'communication_history' => 'array',
        'response_data' => 'array',
        'metadata' => 'array',
        'contacted_at' => 'datetime',
        'responded_at' => 'datetime',
        'closed_at' => 'datetime',
    ];

    // Enquiry types
    const TYPE_RENTAL = 'rental';
    const TYPE_PURCHASE = 'purchase';
    const TYPE_VISIT = 'visit';
    const TYPE_OTHER = 'other';

    // Status constants
    const STATUS_NEW = 'new';
    const STATUS_CONTACTED = 'contacted';
    const STATUS_INTERESTED = 'interested';
    const STATUS_NOT_INTERESTED = 'not_interested';
    const STATUS_CLOSED = 'closed';

    // Priority constants
    const PRIORITY_LOW = 'low';
    const PRIORITY_MEDIUM = 'medium';
    const PRIORITY_HIGH = 'high';
    const PRIORITY_URGENT = 'urgent';

    // Relationships
    public function unit(): BelongsTo
    {
        return $this->belongsTo(Unit::class);
    }

    public function enquirer(): BelongsTo
    {
        return $this->belongsTo(User::class, 'enquirer_id');
    }

    public function owner(): BelongsTo
    {
        return $this->belongsTo(User::class, 'owner_id');
    }

    // Scopes
    public function scopeByStatus($query, $status)
    {
        return $query->where('status', $status);
    }

    public function scopeByType($query, $type)
    {
        return $query->where('enquiry_type', $type);
    }

    public function scopeByPriority($query, $priority)
    {
        return $query->where('priority', $priority);
    }

    public function scopeForOwner($query, $ownerId)
    {
        return $query->where('owner_id', $ownerId);
    }

    public function scopeForUnit($query, $unitId)
    {
        return $query->where('unit_id', $unitId);
    }

    public function scopeRecent($query, $days = 30)
    {
        return $query->where('created_at', '>=', Carbon::now()->subDays($days));
    }

    // Helper methods
    public function isNew(): bool
    {
        return $this->status === self::STATUS_NEW;
    }

    public function isContacted(): bool
    {
        return $this->status === self::STATUS_CONTACTED;
    }

    public function isInterested(): bool
    {
        return $this->status === self::STATUS_INTERESTED;
    }

    public function isNotInterested(): bool
    {
        return $this->status === self::STATUS_NOT_INTERESTED;
    }

    public function isClosed(): bool
    {
        return $this->status === self::STATUS_CLOSED;
    }

    public function isUrgent(): bool
    {
        return $this->priority === self::PRIORITY_URGENT;
    }

    public function isHighPriority(): bool
    {
        return in_array($this->priority, [self::PRIORITY_HIGH, self::PRIORITY_URGENT]);
    }

    // Status update methods
    public function markAsContacted(): void
    {
        $this->update([
            'status' => self::STATUS_CONTACTED,
            'contacted_at' => now(),
        ]);
    }

    public function markAsInterested(): void
    {
        $this->update([
            'status' => self::STATUS_INTERESTED,
            'responded_at' => now(),
        ]);
    }

    public function markAsNotInterested(): void
    {
        $this->update([
            'status' => self::STATUS_NOT_INTERESTED,
            'responded_at' => now(),
        ]);
    }

    public function close(): void
    {
        $this->update([
            'status' => self::STATUS_CLOSED,
            'closed_at' => now(),
        ]);
    }

    // Communication history methods
    public function addCommunicationEntry($type, $message, $userId = null): void
    {
        $history = $this->communication_history ?? [];
        $history[] = [
            'type' => $type, // email, sms, call, note
            'message' => $message,
            'user_id' => $userId,
            'timestamp' => now()->toISOString(),
        ];
        
        $this->update(['communication_history' => $history]);
    }

    public function getCommunicationHistory(): array
    {
        return $this->communication_history ?? [];
    }

    // Response data methods
    public function setResponseData($data): void
    {
        $this->update([
            'response_data' => $data,
            'responded_at' => now(),
        ]);
    }

    public function getResponseData(): array
    {
        return $this->response_data ?? [];
    }

    // Enquirer details methods
    public function setEnquirerDetails($details): void
    {
        $this->update(['enquirer_details' => $details]);
    }

    public function getEnquirerDetails(): array
    {
        return $this->enquirer_details ?? [];
    }

    // Metadata methods
    public function setMetadata($key, $value): void
    {
        $metadata = $this->metadata ?? [];
        $metadata[$key] = $value;
        $this->update(['metadata' => $metadata]);
    }

    public function getMetadata($key = null)
    {
        $metadata = $this->metadata ?? [];
        return $key ? ($metadata[$key] ?? null) : $metadata;
    }
}
