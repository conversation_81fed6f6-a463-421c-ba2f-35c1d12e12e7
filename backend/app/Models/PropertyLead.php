<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class PropertyLead extends Model
{
    use HasFactory, SoftDeletes;

    // Lead status constants
    const STATUS_NEW = 'new';
    const STATUS_CONTACTED = 'contacted';
    const STATUS_INTERESTED = 'interested';
    const STATUS_NOT_INTERESTED = 'not_interested';
    const STATUS_CONVERTED = 'converted';
    const STATUS_LOST = 'lost';

    // Priority constants
    const PRIORITY_LOW = 'low';
    const PRIORITY_MEDIUM = 'medium';
    const PRIORITY_HIGH = 'high';
    const PRIORITY_URGENT = 'urgent';

    /**
     * The attributes that are mass assignable.
     */
    protected $fillable = [
        'listing_id',
        'portal_id',
        'portal_lead_id',
        'enquirer_name',
        'enquirer_email',
        'enquirer_phone',
        'message',
        'lead_status',
        'priority',
        'enquirer_details',
        'communication_history',
        'response_data',
        'contacted_at',
        'responded_at',
        'converted_at',
        'assigned_to',
        'notes',
        'metadata',
    ];

    /**
     * The attributes that should be cast.
     */
    protected $casts = [
        'enquirer_details' => 'array',
        'communication_history' => 'array',
        'response_data' => 'array',
        'metadata' => 'array',
        'contacted_at' => 'datetime',
        'responded_at' => 'datetime',
        'converted_at' => 'datetime',
    ];

    /**
     * Get all possible lead statuses
     */
    public static function getStatuses(): array
    {
        return [
            self::STATUS_NEW,
            self::STATUS_CONTACTED,
            self::STATUS_INTERESTED,
            self::STATUS_NOT_INTERESTED,
            self::STATUS_CONVERTED,
            self::STATUS_LOST,
        ];
    }

    /**
     * Get all possible priorities
     */
    public static function getPriorities(): array
    {
        return [
            self::PRIORITY_LOW,
            self::PRIORITY_MEDIUM,
            self::PRIORITY_HIGH,
            self::PRIORITY_URGENT,
        ];
    }

    /**
     * Get the listing this lead belongs to
     */
    public function listing(): BelongsTo
    {
        return $this->belongsTo(PropertyListing::class, 'listing_id');
    }

    /**
     * Get the portal this lead came from
     */
    public function portal(): BelongsTo
    {
        return $this->belongsTo(ListingPortal::class, 'portal_id');
    }

    /**
     * Get the user assigned to this lead
     */
    public function assignedTo(): BelongsTo
    {
        return $this->belongsTo(User::class, 'assigned_to');
    }

    /**
     * Get the unit through the listing
     */
    public function unit(): BelongsTo
    {
        return $this->belongsTo(Unit::class, 'unit_id', 'id');
    }

    /**
     * Get the owner through the listing
     */
    public function owner(): BelongsTo
    {
        return $this->belongsTo(User::class, 'owner_id', 'id');
    }

    /**
     * Scope to get leads by status
     */
    public function scopeByStatus($query, $status)
    {
        return $query->where('lead_status', $status);
    }

    /**
     * Scope to get leads by priority
     */
    public function scopeByPriority($query, $priority)
    {
        return $query->where('priority', $priority);
    }

    /**
     * Scope to get leads by portal
     */
    public function scopeByPortal($query, $portalId)
    {
        return $query->where('portal_id', $portalId);
    }

    /**
     * Scope to get leads by listing
     */
    public function scopeByListing($query, $listingId)
    {
        return $query->where('listing_id', $listingId);
    }

    /**
     * Scope to get leads by assigned user
     */
    public function scopeByAssignedTo($query, $userId)
    {
        return $query->where('assigned_to', $userId);
    }

    /**
     * Scope to get new leads
     */
    public function scopeNew($query)
    {
        return $query->where('lead_status', self::STATUS_NEW);
    }

    /**
     * Scope to get urgent leads
     */
    public function scopeUrgent($query)
    {
        return $query->where('priority', self::PRIORITY_URGENT);
    }

    /**
     * Scope to get high priority leads
     */
    public function scopeHighPriority($query)
    {
        return $query->whereIn('priority', [self::PRIORITY_HIGH, self::PRIORITY_URGENT]);
    }

    /**
     * Check if lead is new
     */
    public function isNew(): bool
    {
        return $this->lead_status === self::STATUS_NEW;
    }

    /**
     * Check if lead is contacted
     */
    public function isContacted(): bool
    {
        return $this->lead_status === self::STATUS_CONTACTED;
    }

    /**
     * Check if lead is interested
     */
    public function isInterested(): bool
    {
        return $this->lead_status === self::STATUS_INTERESTED;
    }

    /**
     * Check if lead is not interested
     */
    public function isNotInterested(): bool
    {
        return $this->lead_status === self::STATUS_NOT_INTERESTED;
    }

    /**
     * Check if lead is converted
     */
    public function isConverted(): bool
    {
        return $this->lead_status === self::STATUS_CONVERTED;
    }

    /**
     * Check if lead is lost
     */
    public function isLost(): bool
    {
        return $this->lead_status === self::STATUS_LOST;
    }

    /**
     * Check if lead is urgent
     */
    public function isUrgent(): bool
    {
        return $this->priority === self::PRIORITY_URGENT;
    }

    /**
     * Check if lead is high priority
     */
    public function isHighPriority(): bool
    {
        return in_array($this->priority, [self::PRIORITY_HIGH, self::PRIORITY_URGENT]);
    }

    /**
     * Mark lead as contacted
     */
    public function markAsContacted(): bool
    {
        return $this->update([
            'lead_status' => self::STATUS_CONTACTED,
            'contacted_at' => now(),
        ]);
    }

    /**
     * Mark lead as interested
     */
    public function markAsInterested(): bool
    {
        return $this->update([
            'lead_status' => self::STATUS_INTERESTED,
            'responded_at' => now(),
        ]);
    }

    /**
     * Mark lead as not interested
     */
    public function markAsNotInterested(): bool
    {
        return $this->update([
            'lead_status' => self::STATUS_NOT_INTERESTED,
            'responded_at' => now(),
        ]);
    }

    /**
     * Mark lead as converted
     */
    public function markAsConverted(): bool
    {
        return $this->update([
            'lead_status' => self::STATUS_CONVERTED,
            'converted_at' => now(),
        ]);
    }

    /**
     * Mark lead as lost
     */
    public function markAsLost(): bool
    {
        return $this->update([
            'lead_status' => self::STATUS_LOST,
        ]);
    }

    /**
     * Assign lead to user
     */
    public function assignTo(User $user): bool
    {
        return $this->update(['assigned_to' => $user->id]);
    }

    /**
     * Add communication history entry
     */
    public function addCommunicationEntry(string $type, string $message, ?User $user = null): bool
    {
        $history = $this->communication_history ?? [];
        $history[] = [
            'type' => $type,
            'message' => $message,
            'user_id' => $user?->id,
            'user_name' => $user?->name,
            'timestamp' => now()->toISOString(),
        ];

        return $this->update(['communication_history' => $history]);
    }

    /**
     * Get enquirer details
     */
    public function getEnquirerDetails(): array
    {
        return $this->enquirer_details ?? [];
    }

    /**
     * Get communication history
     */
    public function getCommunicationHistory(): array
    {
        return $this->communication_history ?? [];
    }

    /**
     * Get response data
     */
    public function getResponseData(): array
    {
        return $this->response_data ?? [];
    }

    /**
     * Get lead display name
     */
    public function getDisplayNameAttribute(): string
    {
        return $this->enquirer_name ?: "Lead from {$this->portal->display_name}";
    }

    /**
     * Get status color for UI
     */
    public function getStatusColorAttribute(): string
    {
        return match ($this->lead_status) {
            self::STATUS_NEW => 'primary',
            self::STATUS_CONTACTED => 'info',
            self::STATUS_INTERESTED => 'success',
            self::STATUS_NOT_INTERESTED => 'warning',
            self::STATUS_CONVERTED => 'success',
            self::STATUS_LOST => 'danger',
            default => 'secondary',
        };
    }

    /**
     * Get status label for UI
     */
    public function getStatusLabelAttribute(): string
    {
        return match ($this->lead_status) {
            self::STATUS_NEW => 'New',
            self::STATUS_CONTACTED => 'Contacted',
            self::STATUS_INTERESTED => 'Interested',
            self::STATUS_NOT_INTERESTED => 'Not Interested',
            self::STATUS_CONVERTED => 'Converted',
            self::STATUS_LOST => 'Lost',
            default => 'Unknown',
        };
    }

    /**
     * Get priority color for UI
     */
    public function getPriorityColorAttribute(): string
    {
        return match ($this->priority) {
            self::PRIORITY_LOW => 'secondary',
            self::PRIORITY_MEDIUM => 'info',
            self::PRIORITY_HIGH => 'warning',
            self::PRIORITY_URGENT => 'danger',
            default => 'secondary',
        };
    }

    /**
     * Get priority label for UI
     */
    public function getPriorityLabelAttribute(): string
    {
        return match ($this->priority) {
            self::PRIORITY_LOW => 'Low',
            self::PRIORITY_MEDIUM => 'Medium',
            self::PRIORITY_HIGH => 'High',
            self::PRIORITY_URGENT => 'Urgent',
            default => 'Unknown',
        };
    }

    /**
     * Get formatted phone number
     */
    public function getFormattedPhoneAttribute(): string
    {
        return $this->enquirer_phone ?: 'Not provided';
    }

    /**
     * Get lead age in days
     */
    public function getAgeInDaysAttribute(): int
    {
        return $this->created_at->diffInDays(now());
    }
}
