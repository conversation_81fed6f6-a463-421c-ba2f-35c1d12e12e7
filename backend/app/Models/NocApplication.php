<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Factories\HasFactory;

class NocApplication extends Model
{
    use HasFactory;
    
    protected $fillable = [
        'applicant_id',
        'unit_id',
        'noc_type',
        'template_id',
        'purpose',
        'form_data',
        'status',
        'documents',
        'remarks',
        'history',
    ];

    protected $casts = [
        'form_data' => 'array',
        'documents' => 'array',
        'history' => 'array',
        'start_date' => 'date',
        'end_date' => 'date',
    ];

    public function applicant()
    {
        return $this->belongsTo(User::class, 'applicant_id');
    }

    public function unit()
    {
        return $this->belongsTo(Unit::class);
    }

    public function template()
    {
        return $this->belongsTo(NocTemplate::class, 'template_id');
    }
}
