<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Factories\HasFactory;

class UnitStatusHistory extends Model
{
    use HasFactory;

    /**
     * The attributes that are mass assignable.
     */
    protected $fillable = [
        'unit_id',
        'previous_status',
        'new_status',
        'changed_by',
        'reason',
        'changed_at',
        'change_type',
        'metadata',
    ];

    /**
     * The attributes that should be cast.
     */
    protected $casts = [
        'metadata' => 'array',
        'changed_at' => 'datetime',
    ];

    /**
     * Change type constants
     */
    const CHANGE_TYPE_MANUAL = 'manual';
    const CHANGE_TYPE_AUTOMATIC = 'automatic';
    const CHANGE_TYPE_SYSTEM = 'system';

    /**
     * Get the unit this history belongs to
     */
    public function unit(): BelongsTo
    {
        return $this->belongsTo(Unit::class);
    }

    /**
     * Get the user who made the change
     */
    public function changedBy(): BelongsTo
    {
        return $this->belongsTo(User::class, 'changed_by');
    }

    /**
     * Scope to get history for a specific unit
     */
    public function scopeForUnit($query, $unitId)
    {
        return $query->where('unit_id', $unitId);
    }

    /**
     * Scope to get recent changes
     */
    public function scopeRecent($query, $days = 30)
    {
        return $query->where('changed_at', '>=', now()->subDays($days));
    }

    /**
     * Scope to get changes by user
     */
    public function scopeByUser($query, $userId)
    {
        return $query->where('changed_by', $userId);
    }

    /**
     * Get formatted change description
     */
    public function getChangeDescriptionAttribute(): string
    {
        $from = $this->previous_status ? ucfirst(str_replace('-', ' ', $this->previous_status)) : 'Unknown';
        $to = ucfirst(str_replace('-', ' ', $this->new_status));
        
        return "Changed from {$from} to {$to}";
    }

    /**
     * Get change type label
     */
    public function getChangeTypeLabelAttribute(): string
    {
        return match ($this->change_type) {
            self::CHANGE_TYPE_MANUAL => 'Manual',
            self::CHANGE_TYPE_AUTOMATIC => 'Automatic',
            self::CHANGE_TYPE_SYSTEM => 'System',
            default => 'Unknown',
        };
    }
}
