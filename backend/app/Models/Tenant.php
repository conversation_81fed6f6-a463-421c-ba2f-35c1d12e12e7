<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\MorphMany;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Support\Str;

class Tenant extends Model
{
    use HasFactory, SoftDeletes;

    // KYC Status constants
    const KYC_PENDING = 'pending';
    const KYC_SUBMITTED = 'submitted';
    const KYC_VERIFIED = 'verified';
    const KYC_REJECTED = 'rejected';

    // Status constants
    const STATUS_ACTIVE = 'active';
    const STATUS_INACTIVE = 'inactive';
    const STATUS_TERMINATED = 'terminated';
    const STATUS_NOTICE_PERIOD = 'notice_period';

    // Background check status constants
    const BACKGROUND_CHECK_PENDING = 'pending';
    const BACKGROUND_CHECK_PASSED = 'passed';
    const BACKGROUND_CHECK_FAILED = 'failed';

    // Police verification status constants
    const POLICE_VERIFICATION_PENDING = 'pending';
    const POLICE_VERIFICATION_SUBMITTED = 'submitted';
    const POLICE_VERIFICATION_VERIFIED = 'verified';
    const POLICE_VERIFICATION_REJECTED = 'rejected';

    // Behavior rating constants
    const BEHAVIOR_EXCELLENT = 'excellent';
    const BEHAVIOR_GOOD = 'good';
    const BEHAVIOR_AVERAGE = 'average';
    const BEHAVIOR_POOR = 'poor';

    /**
     * The attributes that are mass assignable.
     */
    protected $fillable = [
        'user_id',
        'tenant_code',
        'unit_id',
        'owner_id',
        'occupation',
        'company_name',
        'company_address',
        'family_members',
        'family_details',
        'monthly_income',
        'security_deposit',
        'advance_amount',
        'move_in_date',
        'move_out_date',
        'agreement_start_date',
        'agreement_end_date',
        'agreement_type',
        'kyc_status',
        'kyc_documents',
        'kyc_submitted_at',
        'kyc_verified_at',
        'kyc_verified_by',
        'kyc_rejection_reason',
        'background_check_done',
        'background_check_status',
        'background_check_date',
        'background_check_notes',
        'police_verification_required',
        'police_verification_status',
        'police_verification_date',
        'police_verification_notes',
        'references',
        'status',
        'behavior_rating',
        'behavior_notes',
        'notice_given_date',
        'notice_period_days',
        'termination_reason',
        'terminated_at',
        'terminated_by',
        'preferences',
        'special_requirements',
        'admin_notes',
        'last_reminder_sent_at',
        'reminder_count',
    ];

    /**
     * The attributes that should be cast.
     */
    protected $casts = [
        'family_details' => 'array',
        'monthly_income' => 'decimal:2',
        'security_deposit' => 'decimal:2',
        'advance_amount' => 'decimal:2',
        'move_in_date' => 'date',
        'move_out_date' => 'date',
        'agreement_start_date' => 'date',
        'agreement_end_date' => 'date',
        'kyc_documents' => 'array',
        'kyc_submitted_at' => 'datetime',
        'kyc_verified_at' => 'datetime',
        'background_check_done' => 'boolean',
        'background_check_date' => 'datetime',
        'police_verification_required' => 'boolean',
        'police_verification_date' => 'datetime',
        'references' => 'array',
        'notice_given_date' => 'date',
        'terminated_at' => 'datetime',
        'preferences' => 'array',
        'last_reminder_sent_at' => 'datetime',
        'reminder_count' => 'integer',
    ];

    /**
     * Boot the model.
     */
    protected static function boot()
    {
        parent::boot();

        static::creating(function ($tenant) {
            if (empty($tenant->tenant_code)) {
                $tenant->tenant_code = static::generateTenantCode();
            }
        });
    }

    /**
     * Generate unique tenant code.
     */
    public static function generateTenantCode(): string
    {
        do {
            $code = 'TN' . str_pad(mt_rand(1, 99999), 5, '0', STR_PAD_LEFT);
        } while (static::where('tenant_code', $code)->exists());

        return $code;
    }

    /**
     * Get the user associated with this tenant.
     */
    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    /**
     * Get the unit associated with this tenant.
     */
    public function unit(): BelongsTo
    {
        return $this->belongsTo(Unit::class);
    }

    /**
     * Get the owner of the unit.
     */
    public function owner(): BelongsTo
    {
        return $this->belongsTo(User::class, 'owner_id');
    }

    /**
     * Get the user who verified KYC.
     */
    public function kycVerifiedBy(): BelongsTo
    {
        return $this->belongsTo(User::class, 'kyc_verified_by');
    }

    /**
     * Get the user who terminated the tenancy.
     */
    public function terminatedBy(): BelongsTo
    {
        return $this->belongsTo(User::class, 'terminated_by');
    }

    /**
     * Get documents associated with this tenant.
     */
    public function documents(): MorphMany
    {
        return $this->morphMany(Document::class, 'documentable');
    }

    /**
     * Get emergency contacts for this tenant.
     */
    public function emergencyContacts(): HasMany
    {
        return $this->hasMany(EmergencyContact::class)->orderedByPriority();
    }

    /**
     * Get history records for this tenant.
     */
    public function history(): HasMany
    {
        return $this->hasMany(TenantHistory::class)->orderBy('performed_at', 'desc');
    }

    /**
     * Get active emergency contacts for this tenant.
     */
    public function activeEmergencyContacts(): HasMany
    {
        return $this->hasMany(EmergencyContact::class)->active()->orderedByPriority();
    }

    /**
     * Get primary emergency contact for this tenant.
     */
    public function primaryEmergencyContact()
    {
        return $this->emergencyContacts()->primary()->first();
    }

    /**
     * Get all emergency contact summaries for notifications.
     */
    public function getEmergencyContactSummaries(): array
    {
        return $this->activeEmergencyContacts->map(function ($contact) {
            return $contact->getContactSummary();
        })->toArray();
    }

    /**
     * Check if tenant is active.
     */
    public function isActive(): bool
    {
        return $this->status === self::STATUS_ACTIVE;
    }

    /**
     * Check if tenant is terminated.
     */
    public function isTerminated(): bool
    {
        return $this->status === self::STATUS_TERMINATED;
    }

    /**
     * Check if tenant is in notice period.
     */
    public function isInNoticePeriod(): bool
    {
        return $this->status === self::STATUS_NOTICE_PERIOD;
    }

    /**
     * Check if KYC is completed.
     */
    public function isKycCompleted(): bool
    {
        return $this->kyc_status === self::KYC_VERIFIED;
    }

    /**
     * Check if KYC is pending.
     */
    public function isKycPending(): bool
    {
        return in_array($this->kyc_status, [self::KYC_PENDING, self::KYC_SUBMITTED]);
    }

    /**
     * Check if background check is completed.
     */
    public function isBackgroundCheckCompleted(): bool
    {
        return $this->background_check_done && 
               $this->background_check_status === self::BACKGROUND_CHECK_PASSED;
    }

    /**
     * Check if police verification is required.
     */
    public function isPoliceVerificationRequired(): bool
    {
        return $this->police_verification_required;
    }

    /**
     * Check if police verification is completed.
     */
    public function isPoliceVerificationCompleted(): bool
    {
        return $this->police_verification_status === self::POLICE_VERIFICATION_VERIFIED;
    }

    /**
     * Check if agreement is active.
     */
    public function isAgreementActive(): bool
    {
        if (!$this->agreement_start_date || !$this->agreement_end_date) {
            return false;
        }

        $now = now()->toDateString();
        return $now >= $this->agreement_start_date->toDateString() && 
               $now <= $this->agreement_end_date->toDateString();
    }

    /**
     * Check if agreement is expiring soon.
     */
    public function isAgreementExpiringSoon(int $days = 30): bool
    {
        if (!$this->agreement_end_date) {
            return false;
        }

        return $this->agreement_end_date->diffInDays(now()) <= $days;
    }

    /**
     * Get tenant's full name.
     */
    public function getFullNameAttribute(): string
    {
        return $this->user->full_name;
    }

    /**
     * Get tenant's display name.
     */
    public function getDisplayNameAttribute(): string
    {
        return $this->user->display_name;
    }

    /**
     * Get KYC status display name.
     */
    public function getKycStatusDisplayName(): string
    {
        return match($this->kyc_status) {
            self::KYC_PENDING => 'Pending',
            self::KYC_SUBMITTED => 'Submitted',
            self::KYC_VERIFIED => 'Verified',
            self::KYC_REJECTED => 'Rejected',
            default => 'Unknown',
        };
    }

    /**
     * Get status display name.
     */
    public function getStatusDisplayName(): string
    {
        return match($this->status) {
            self::STATUS_ACTIVE => 'Active',
            self::STATUS_INACTIVE => 'Inactive',
            self::STATUS_TERMINATED => 'Terminated',
            self::STATUS_NOTICE_PERIOD => 'Notice Period',
            default => 'Unknown',
        };
    }

    /**
     * Get status color for UI.
     */
    public function getStatusColor(): string
    {
        return match($this->status) {
            self::STATUS_ACTIVE => 'success',
            self::STATUS_INACTIVE => 'secondary',
            self::STATUS_TERMINATED => 'danger',
            self::STATUS_NOTICE_PERIOD => 'warning',
            default => 'secondary',
        };
    }

    /**
     * Get KYC status color for UI.
     */
    public function getKycStatusColor(): string
    {
        return match($this->kyc_status) {
            self::KYC_PENDING => 'warning',
            self::KYC_SUBMITTED => 'info',
            self::KYC_VERIFIED => 'success',
            self::KYC_REJECTED => 'danger',
            default => 'secondary',
        };
    }

    /**
     * Submit KYC documents.
     */
    public function submitKyc(array $documents): void
    {
        $this->update([
            'kyc_status' => self::KYC_SUBMITTED,
            'kyc_documents' => $documents,
            'kyc_submitted_at' => now(),
        ]);
    }

    /**
     * Verify KYC documents.
     */
    public function verifyKyc(User $verifiedBy): void
    {
        $this->update([
            'kyc_status' => self::KYC_VERIFIED,
            'kyc_verified_at' => now(),
            'kyc_verified_by' => $verifiedBy->id,
        ]);
    }

    /**
     * Reject KYC documents.
     */
    public function rejectKyc(string $reason, User $rejectedBy): void
    {
        $this->update([
            'kyc_status' => self::KYC_REJECTED,
            'kyc_rejection_reason' => $reason,
            'kyc_verified_by' => $rejectedBy->id,
            'kyc_verified_at' => now(),
        ]);
    }

    /**
     * Terminate tenancy.
     */
    public function terminate(string $reason, User $terminatedBy): void
    {
        $this->update([
            'status' => self::STATUS_TERMINATED,
            'termination_reason' => $reason,
            'terminated_at' => now(),
            'terminated_by' => $terminatedBy->id,
        ]);
    }

    /**
     * Start notice period.
     */
    public function startNoticePeriod(int $noticePeriodDays): void
    {
        $this->update([
            'status' => self::STATUS_NOTICE_PERIOD,
            'notice_given_date' => now()->toDateString(),
            'notice_period_days' => $noticePeriodDays,
        ]);
    }

    /**
     * Scope to get active tenants.
     */
    public function scopeActive($query)
    {
        return $query->where('status', self::STATUS_ACTIVE);
    }

    /**
     * Scope to get tenants by status.
     */
    public function scopeByStatus($query, string $status)
    {
        return $query->where('status', $status);
    }

    /**
     * Scope to get tenants by KYC status.
     */
    public function scopeByKycStatus($query, string $status)
    {
        return $query->where('kyc_status', $status);
    }

    /**
     * Scope to get tenants with expiring agreements.
     */
    public function scopeExpiringAgreements($query, int $days = 30)
    {
        return $query->whereNotNull('agreement_end_date')
                    ->where('agreement_end_date', '<=', now()->addDays($days));
    }

    /**
     * Scope to get tenants by unit.
     */
    public function scopeByUnit($query, int $unitId)
    {
        return $query->where('unit_id', $unitId);
    }

    /**
     * Scope to get tenants by owner.
     */
    public function scopeByOwner($query, int $ownerId)
    {
        return $query->where('owner_id', $ownerId);
    }

    /**
     * Get all available statuses.
     */
    public static function getAllStatuses(): array
    {
        return [
            self::STATUS_ACTIVE,
            self::STATUS_INACTIVE,
            self::STATUS_TERMINATED,
            self::STATUS_NOTICE_PERIOD,
        ];
    }

    /**
     * Get all available KYC statuses.
     */
    public static function getAllKycStatuses(): array
    {
        return [
            self::KYC_PENDING,
            self::KYC_SUBMITTED,
            self::KYC_VERIFIED,
            self::KYC_REJECTED,
        ];
    }

    /**
     * Get all available behavior ratings.
     */
    public static function getAllBehaviorRatings(): array
    {
        return [
            self::BEHAVIOR_EXCELLENT,
            self::BEHAVIOR_GOOD,
            self::BEHAVIOR_AVERAGE,
            self::BEHAVIOR_POOR,
        ];
    }
}
