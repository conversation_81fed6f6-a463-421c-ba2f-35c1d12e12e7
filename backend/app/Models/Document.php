<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\MorphTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Str;

class Document extends Model
{
    use HasFactory, SoftDeletes;

    // Document type constants
    const TYPE_ID_PROOF = 'id_proof';
    const TYPE_ADDRESS_PROOF = 'address_proof';
    const TYPE_INCOME_PROOF = 'income_proof';
    const TYPE_PHOTO = 'photo';
    const TYPE_AGREEMENT = 'agreement';
    const TYPE_NOC = 'noc';
    const TYPE_POLICE_VERIFICATION = 'police_verification';
    const TYPE_BACKGROUND_CHECK = 'background_check';
    const TYPE_REFERENCE_LETTER = 'reference_letter';
    const TYPE_BANK_STATEMENT = 'bank_statement';
    const TYPE_SALARY_SLIP = 'salary_slip';
    const TYPE_OTHER = 'other';

    // Status constants
    const STATUS_PENDING = 'pending';
    const STATUS_VERIFIED = 'verified';
    const STATUS_REJECTED = 'rejected';
    const STATUS_EXPIRED = 'expired';

    // Visibility constants
    const VISIBILITY_PUBLIC = 'public';
    const VISIBILITY_PRIVATE = 'private';
    const VISIBILITY_ADMIN_ONLY = 'admin_only';

    /**
     * The attributes that are mass assignable.
     */
    protected $fillable = [
        'document_number',
        'title',
        'description',
        'type',
        'category',
        'documentable_id',
        'documentable_type',
        'file_name',
        'file_path',
        'file_type',
        'file_size',
        'mime_type',
        'file_hash',
        'metadata',
        'document_date',
        'expiry_date',
        'issuing_authority',
        'document_id_number',
        'status',
        'uploaded_by',
        'verified_by',
        'verified_at',
        'verification_notes',
        'rejection_reason',
        'visibility',
        'is_required',
        'is_sensitive',
        'parent_document_id',
        'version',
        'is_current_version',
    ];

    /**
     * The attributes that should be cast.
     */
    protected $casts = [
        'metadata' => 'array',
        'document_date' => 'date',
        'expiry_date' => 'date',
        'verified_at' => 'datetime',
        'is_required' => 'boolean',
        'is_sensitive' => 'boolean',
        'is_current_version' => 'boolean',
    ];

    /**
     * Boot the model.
     */
    protected static function boot()
    {
        parent::boot();

        static::creating(function ($document) {
            if (empty($document->document_number)) {
                $document->document_number = static::generateDocumentNumber();
            }
        });

        static::deleting(function ($document) {
            // Delete the physical file when document is deleted
            if ($document->file_path && Storage::exists($document->file_path)) {
                Storage::delete($document->file_path);
            }
        });
    }

    /**
     * Generate unique document number.
     */
    public static function generateDocumentNumber(): string
    {
        do {
            $number = 'DOC' . date('Y') . str_pad(mt_rand(1, 99999), 5, '0', STR_PAD_LEFT);
        } while (static::where('document_number', $number)->exists());

        return $number;
    }

    /**
     * Get the owning documentable model.
     */
    public function documentable(): MorphTo
    {
        return $this->morphTo();
    }

    /**
     * Get the user who uploaded this document.
     */
    public function uploadedBy(): BelongsTo
    {
        return $this->belongsTo(User::class, 'uploaded_by');
    }

    /**
     * Get the user who verified this document.
     */
    public function verifiedBy(): BelongsTo
    {
        return $this->belongsTo(User::class, 'verified_by');
    }

    /**
     * Get the parent document (for versions).
     */
    public function parentDocument(): BelongsTo
    {
        return $this->belongsTo(Document::class, 'parent_document_id');
    }

    /**
     * Get document versions.
     */
    public function versions(): HasMany
    {
        return $this->hasMany(Document::class, 'parent_document_id');
    }

    /**
     * Check if document is verified.
     */
    public function isVerified(): bool
    {
        return $this->status === self::STATUS_VERIFIED;
    }

    /**
     * Check if document is rejected.
     */
    public function isRejected(): bool
    {
        return $this->status === self::STATUS_REJECTED;
    }

    /**
     * Check if document is pending.
     */
    public function isPending(): bool
    {
        return $this->status === self::STATUS_PENDING;
    }

    /**
     * Check if document is expired.
     */
    public function isExpired(): bool
    {
        return $this->status === self::STATUS_EXPIRED || 
               ($this->expiry_date && $this->expiry_date->isPast());
    }

    /**
     * Check if document is expiring soon.
     */
    public function isExpiringSoon(int $days = 30): bool
    {
        if (!$this->expiry_date) {
            return false;
        }

        return $this->expiry_date->diffInDays(now()) <= $days;
    }

    /**
     * Check if document is required.
     */
    public function isRequired(): bool
    {
        return $this->is_required;
    }

    /**
     * Check if document is sensitive.
     */
    public function isSensitive(): bool
    {
        return $this->is_sensitive;
    }

    /**
     * Check if document is current version.
     */
    public function isCurrentVersion(): bool
    {
        return $this->is_current_version;
    }

    /**
     * Get document file URL.
     */
    public function getFileUrlAttribute(): string
    {
        if (!$this->file_path) {
            return '';
        }

        return Storage::url($this->file_path);
    }

    /**
     * Get document file size in human readable format.
     */
    public function getFileSizeHumanAttribute(): string
    {
        $bytes = $this->file_size;
        $units = ['B', 'KB', 'MB', 'GB'];
        
        for ($i = 0; $bytes > 1024 && $i < count($units) - 1; $i++) {
            $bytes /= 1024;
        }
        
        return round($bytes, 2) . ' ' . $units[$i];
    }

    /**
     * Get type display name.
     */
    public function getTypeDisplayName(): string
    {
        return match($this->type) {
            self::TYPE_ID_PROOF => 'ID Proof',
            self::TYPE_ADDRESS_PROOF => 'Address Proof',
            self::TYPE_INCOME_PROOF => 'Income Proof',
            self::TYPE_PHOTO => 'Photo',
            self::TYPE_AGREEMENT => 'Agreement',
            self::TYPE_NOC => 'NOC',
            self::TYPE_POLICE_VERIFICATION => 'Police Verification',
            self::TYPE_BACKGROUND_CHECK => 'Background Check',
            self::TYPE_REFERENCE_LETTER => 'Reference Letter',
            self::TYPE_BANK_STATEMENT => 'Bank Statement',
            self::TYPE_SALARY_SLIP => 'Salary Slip',
            self::TYPE_OTHER => 'Other',
            default => 'Unknown',
        };
    }

    /**
     * Get status display name.
     */
    public function getStatusDisplayName(): string
    {
        return match($this->status) {
            self::STATUS_PENDING => 'Pending',
            self::STATUS_VERIFIED => 'Verified',
            self::STATUS_REJECTED => 'Rejected',
            self::STATUS_EXPIRED => 'Expired',
            default => 'Unknown',
        };
    }

    /**
     * Get status color for UI.
     */
    public function getStatusColor(): string
    {
        return match($this->status) {
            self::STATUS_PENDING => 'warning',
            self::STATUS_VERIFIED => 'success',
            self::STATUS_REJECTED => 'danger',
            self::STATUS_EXPIRED => 'secondary',
            default => 'secondary',
        };
    }

    /**
     * Verify document.
     */
    public function verify(User $verifiedBy, string $notes = null): void
    {
        $this->update([
            'status' => self::STATUS_VERIFIED,
            'verified_by' => $verifiedBy->id,
            'verified_at' => now(),
            'verification_notes' => $notes,
        ]);
    }

    /**
     * Reject document.
     */
    public function reject(User $rejectedBy, string $reason): void
    {
        $this->update([
            'status' => self::STATUS_REJECTED,
            'verified_by' => $rejectedBy->id,
            'verified_at' => now(),
            'rejection_reason' => $reason,
        ]);
    }

    /**
     * Mark document as expired.
     */
    public function markAsExpired(): void
    {
        $this->update([
            'status' => self::STATUS_EXPIRED,
        ]);
    }

    /**
     * Create new version of document.
     */
    public function createNewVersion(array $data): Document
    {
        // Mark current version as not current
        $this->update(['is_current_version' => false]);

        // Create new version
        $newVersion = static::create(array_merge($data, [
            'parent_document_id' => $this->id,
            'version' => $this->version + 1,
            'is_current_version' => true,
        ]));

        return $newVersion;
    }

    /**
     * Scope to get documents by type.
     */
    public function scopeByType($query, string $type)
    {
        return $query->where('type', $type);
    }

    /**
     * Scope to get documents by status.
     */
    public function scopeByStatus($query, string $status)
    {
        return $query->where('status', $status);
    }

    /**
     * Scope to get verified documents.
     */
    public function scopeVerified($query)
    {
        return $query->where('status', self::STATUS_VERIFIED);
    }

    /**
     * Scope to get pending documents.
     */
    public function scopePending($query)
    {
        return $query->where('status', self::STATUS_PENDING);
    }

    /**
     * Scope to get expired documents.
     */
    public function scopeExpired($query)
    {
        return $query->where('status', self::STATUS_EXPIRED)
                    ->orWhere('expiry_date', '<', now());
    }

    /**
     * Scope to get expiring documents.
     */
    public function scopeExpiring($query, int $days = 30)
    {
        return $query->whereNotNull('expiry_date')
                    ->where('expiry_date', '<=', now()->addDays($days))
                    ->where('expiry_date', '>', now());
    }

    /**
     * Scope to get current versions only.
     */
    public function scopeCurrentVersions($query)
    {
        return $query->where('is_current_version', true);
    }

    /**
     * Scope to get required documents.
     */
    public function scopeRequired($query)
    {
        return $query->where('is_required', true);
    }

    /**
     * Scope to get sensitive documents.
     */
    public function scopeSensitive($query)
    {
        return $query->where('is_sensitive', true);
    }

    /**
     * Scope to get documents by visibility.
     */
    public function scopeByVisibility($query, string $visibility)
    {
        return $query->where('visibility', $visibility);
    }

    /**
     * Get all available types.
     */
    public static function getAllTypes(): array
    {
        return [
            self::TYPE_ID_PROOF,
            self::TYPE_ADDRESS_PROOF,
            self::TYPE_INCOME_PROOF,
            self::TYPE_PHOTO,
            self::TYPE_AGREEMENT,
            self::TYPE_NOC,
            self::TYPE_POLICE_VERIFICATION,
            self::TYPE_BACKGROUND_CHECK,
            self::TYPE_REFERENCE_LETTER,
            self::TYPE_BANK_STATEMENT,
            self::TYPE_SALARY_SLIP,
            self::TYPE_OTHER,
        ];
    }

    /**
     * Get all available statuses.
     */
    public static function getAllStatuses(): array
    {
        return [
            self::STATUS_PENDING,
            self::STATUS_VERIFIED,
            self::STATUS_REJECTED,
            self::STATUS_EXPIRED,
        ];
    }

    /**
     * Get all available visibility options.
     */
    public static function getAllVisibilityOptions(): array
    {
        return [
            self::VISIBILITY_PUBLIC,
            self::VISIBILITY_PRIVATE,
            self::VISIBILITY_ADMIN_ONLY,
        ];
    }
}
