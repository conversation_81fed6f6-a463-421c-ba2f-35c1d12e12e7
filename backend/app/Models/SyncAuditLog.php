<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Builder;
use Carbon\Carbon;

class SyncAuditLog extends Model
{
    use HasFactory;

    protected $fillable = [
        'entity_type',
        'entity_id',
        'operation',
        'source_system',
        'target_system',
        'payload',
        'original_payload',
        'status',
        'error_message',
        'error_details',
        'sync_version',
        'correlation_id',
        'batch_id',
        'retry_count',
        'last_retry_at',
        'processed_at',
        'scheduled_at',
        'processing_time_ms',
        'memory_usage_bytes',
        'metadata',
    ];

    protected $casts = [
        'payload' => 'array',
        'original_payload' => 'array',
        'error_details' => 'array',
        'metadata' => 'array',
        'last_retry_at' => 'datetime',
        'processed_at' => 'datetime',
        'scheduled_at' => 'datetime',
        'processing_time_ms' => 'decimal:2',
        'memory_usage_bytes' => 'integer',
        'retry_count' => 'integer',
    ];

    // Status constants
    const STATUS_PENDING = 'pending';
    const STATUS_PROCESSING = 'processing';
    const STATUS_SUCCESS = 'success';
    const STATUS_FAILED = 'failed';
    const STATUS_SKIPPED = 'skipped';
    const STATUS_CONFLICT = 'conflict';

    // Operation constants
    const OPERATION_CREATED = 'created';
    const OPERATION_UPDATED = 'updated';
    const OPERATION_DELETED = 'deleted';

    // Source system constants
    const SOURCE_TMS = 'tms';
    const SOURCE_ONESOCIETY = 'onesociety';

    /**
     * Boot the model.
     */
    protected static function boot()
    {
        parent::boot();

        static::creating(function ($log) {
            if (empty($log->correlation_id)) {
                $log->correlation_id = static::generateCorrelationId();
            }
            
            if (empty($log->scheduled_at)) {
                $log->scheduled_at = now();
            }
        });
    }

    /**
     * Generate a unique correlation ID.
     */
    public static function generateCorrelationId(): string
    {
        return 'sync_' . uniqid() . '_' . time();
    }

    /**
     * Create a new sync log entry.
     */
    public static function createSyncLog(
        string $entityType,
        ?int $entityId,
        string $operation,
        string $sourceSystem,
        array $payload,
        string $status = self::STATUS_PENDING,
        ?string $correlationId = null,
        ?array $metadata = null
    ): self {
        return static::create([
            'entity_type' => $entityType,
            'entity_id' => $entityId,
            'operation' => $operation,
            'source_system' => $sourceSystem,
            'payload' => $payload,
            'status' => $status,
            'correlation_id' => $correlationId,
            'metadata' => $metadata,
        ]);
    }

    /**
     * Mark the sync as processing.
     */
    public function markAsProcessing(): bool
    {
        return $this->update([
            'status' => self::STATUS_PROCESSING,
            'processed_at' => now(),
        ]);
    }

    /**
     * Mark the sync as successful.
     */
    public function markAsSuccess(?float $processingTimeMs = null, ?int $memoryUsage = null): bool
    {
        return $this->update([
            'status' => self::STATUS_SUCCESS,
            'processed_at' => now(),
            'processing_time_ms' => $processingTimeMs,
            'memory_usage_bytes' => $memoryUsage,
            'error_message' => null,
            'error_details' => null,
        ]);
    }

    /**
     * Mark the sync as failed.
     */
    public function markAsFailed(string $errorMessage, ?array $errorDetails = null): bool
    {
        return $this->update([
            'status' => self::STATUS_FAILED,
            'processed_at' => now(),
            'error_message' => $errorMessage,
            'error_details' => $errorDetails,
            'retry_count' => $this->retry_count + 1,
            'last_retry_at' => now(),
        ]);
    }

    /**
     * Mark the sync as skipped.
     */
    public function markAsSkipped(string $reason, ?array $details = null): bool
    {
        return $this->update([
            'status' => self::STATUS_SKIPPED,
            'processed_at' => now(),
            'error_message' => $reason,
            'error_details' => $details,
        ]);
    }

    /**
     * Mark the sync as conflict.
     */
    public function markAsConflict(string $reason, ?array $conflictDetails = null): bool
    {
        return $this->update([
            'status' => self::STATUS_CONFLICT,
            'processed_at' => now(),
            'error_message' => $reason,
            'error_details' => $conflictDetails,
        ]);
    }

    /**
     * Check if the sync can be retried.
     */
    public function canRetry(): bool
    {
        $maxRetries = config('sync.retry_attempts', 3);
        return $this->retry_count < $maxRetries && $this->status === self::STATUS_FAILED;
    }

    /**
     * Get the next retry time.
     */
    public function getNextRetryTime(): ?Carbon
    {
        if (!$this->canRetry()) {
            return null;
        }

        $delay = config('sync.retry_delay', 10);
        $exponentialDelay = $delay * pow(2, $this->retry_count);
        
        return $this->last_retry_at 
            ? $this->last_retry_at->addSeconds($exponentialDelay)
            : $this->created_at->addSeconds($exponentialDelay);
    }

    /**
     * Scope for filtering by entity.
     */
    public function scopeForEntity(Builder $query, string $entityType, ?int $entityId = null): Builder
    {
        $query->where('entity_type', $entityType);
        
        if ($entityId !== null) {
            $query->where('entity_id', $entityId);
        }
        
        return $query;
    }

    /**
     * Scope for filtering by status.
     */
    public function scopeWithStatus(Builder $query, string $status): Builder
    {
        return $query->where('status', $status);
    }

    /**
     * Scope for filtering by source system.
     */
    public function scopeFromSource(Builder $query, string $sourceSystem): Builder
    {
        return $query->where('source_system', $sourceSystem);
    }

    /**
     * Scope for filtering by operation.
     */
    public function scopeWithOperation(Builder $query, string $operation): Builder
    {
        return $query->where('operation', $operation);
    }

    /**
     * Scope for recent logs.
     */
    public function scopeRecent(Builder $query, int $hours = 24): Builder
    {
        return $query->where('created_at', '>=', now()->subHours($hours));
    }

    /**
     * Scope for failed logs that can be retried.
     */
    public function scopeRetryable(Builder $query): Builder
    {
        $maxRetries = config('sync.retry_attempts', 3);
        
        return $query->where('status', self::STATUS_FAILED)
                    ->where('retry_count', '<', $maxRetries);
    }

    /**
     * Scope for logs ready for retry.
     */
    public function scopeReadyForRetry(Builder $query): Builder
    {
        return $query->retryable()
                    ->where(function ($q) {
                        $q->whereNull('last_retry_at')
                          ->orWhere('last_retry_at', '<=', now()->subMinutes(1));
                    });
    }

    /**
     * Get performance metrics.
     */
    public static function getPerformanceMetrics(int $hours = 24): array
    {
        $logs = static::recent($hours)->get();
        
        return [
            'total_events' => $logs->count(),
            'success_rate' => $logs->where('status', self::STATUS_SUCCESS)->count() / max($logs->count(), 1) * 100,
            'failure_rate' => $logs->where('status', self::STATUS_FAILED)->count() / max($logs->count(), 1) * 100,
            'average_processing_time' => $logs->whereNotNull('processing_time_ms')->avg('processing_time_ms'),
            'max_processing_time' => $logs->whereNotNull('processing_time_ms')->max('processing_time_ms'),
            'average_memory_usage' => $logs->whereNotNull('memory_usage_bytes')->avg('memory_usage_bytes'),
            'max_memory_usage' => $logs->whereNotNull('memory_usage_bytes')->max('memory_usage_bytes'),
            'retry_rate' => $logs->where('retry_count', '>', 0)->count() / max($logs->count(), 1) * 100,
        ];
    }
}
