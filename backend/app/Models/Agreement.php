<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Agreement extends Model
{
    use HasFactory;

    protected $fillable = [
        'tenant_id',
        'unit_id',
        'template_id',
        'file_path',
        'status',
        'start_date',
        'end_date',
        'created_by',
        'metadata',
        'renewed_from',
        'history',
        'signed_file_path',
        'signature_data',
        'signature_hash',
        'signed_at',
        'signed_by_owner',
        'signed_by_tenant',
        'signature_request_id',
        'signature_workflow_started_at',
        'signature_workflow_completed_at',
        'signature_workflow_cancelled_at',
        'signature_workflow_cancelled_by',
        'signature_workflow_cancellation_reason',
        'renewal_reminder_sent',
        'renewal_reminder_sent_at',
        'renewal_reminder_history',
    ];

    protected $casts = [
        'metadata' => 'array',
        'history' => 'array',
        'start_date' => 'date',
        'end_date' => 'date',
        'signature_data' => 'array',
        'signed_at' => 'datetime',
        'signed_by_owner' => 'boolean',
        'signed_by_tenant' => 'boolean',
        'signature_workflow_started_at' => 'datetime',
        'signature_workflow_completed_at' => 'datetime',
        'signature_workflow_cancelled_at' => 'datetime',
        'renewal_reminder_sent' => 'boolean',
        'renewal_reminder_sent_at' => 'datetime',
        'renewal_reminder_history' => 'array',
    ];

    public function tenant()
    {
        return $this->belongsTo(Tenant::class);
    }

    public function unit()
    {
        return $this->belongsTo(Unit::class);
    }

    public function template()
    {
        return $this->belongsTo(AgreementTemplate::class, 'template_id');
    }

    public function creator()
    {
        return $this->belongsTo(User::class, 'created_by');
    }

    public function workflowCancelledBy()
    {
        return $this->belongsTo(User::class, 'signature_workflow_cancelled_by');
    }

    public function renewedFromAgreement()
    {
        return $this->belongsTo(Agreement::class, 'renewed_from');
    }

    public function renewals()
    {
        return $this->hasMany(Agreement::class, 'renewed_from');
    }

    /**
     * Get the audit logs for this agreement
     */
    public function auditLogs()
    {
        return $this->hasMany(AgreementAuditLog::class)->orderBy('performed_at', 'desc');
    }

    /**
     * Check if agreement is fully signed
     */
    public function isFullySigned(): bool
    {
        return $this->signed_by_owner && $this->signed_by_tenant;
    }

    /**
     * Check if agreement is partially signed
     */
    public function isPartiallySigned(): bool
    {
        return $this->signed_by_owner || $this->signed_by_tenant;
    }

    /**
     * Get signature status
     */
    public function getSignatureStatus(): string
    {
        if ($this->isFullySigned()) {
            return 'fully_signed';
        } elseif ($this->isPartiallySigned()) {
            return 'partially_signed';
        } else {
            return 'unsigned';
        }
    }

    /**
     * Check if agreement is expiring soon
     */
    public function isExpiringSoon(int $days = 30): bool
    {
        return $this->end_date->diffInDays(now()) <= $days;
    }

    /**
     * Check if agreement is expired
     */
    public function isExpired(): bool
    {
        return $this->end_date->isPast();
    }

    /**
     * Get days until expiry
     */
    public function getDaysUntilExpiry(): int
    {
        return $this->end_date->diffInDays(now());
    }

    /**
     * Scope for expiring agreements
     */
    public function scopeExpiringSoon($query, int $days = 30)
    {
        return $query->where('end_date', '<=', now()->addDays($days))
                    ->where('status', 'active');
    }

    /**
     * Scope for expired agreements
     */
    public function scopeExpired($query)
    {
        return $query->where('end_date', '<', now())
                    ->where('status', 'active');
    }

    /**
     * Scope for active agreements
     */
    public function scopeActive($query)
    {
        return $query->where('status', 'active');
    }

    /**
     * Scope for draft agreements
     */
    public function scopeDraft($query)
    {
        return $query->where('status', 'draft');
    }
}
