<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\SoftDeletes;

class BillingRecord extends Model
{
    use HasFactory, SoftDeletes;

    protected $fillable = [
        'unit_id',
        'tenant_id',
        'billing_rule_id',
        'charge_type',
        'amount',
        'period_start',
        'period_end',
        'due_date',
        'status',
        'breakdown',
        'notes',
        'paid_date',
        'payment_method',
        'transaction_id',
        'created_by',
        'metadata',
    ];

    protected $casts = [
        'amount' => 'decimal:2',
        'period_start' => 'date',
        'period_end' => 'date',
        'due_date' => 'date',
        'paid_date' => 'datetime',
        'breakdown' => 'array',
        'metadata' => 'array',
    ];

    /**
     * Get the unit this billing record belongs to.
     */
    public function unit(): BelongsTo
    {
        return $this->belongsTo(Unit::class);
    }

    /**
     * Get the tenant this billing record belongs to.
     */
    public function tenant(): BelongsTo
    {
        return $this->belongsTo(User::class, 'tenant_id');
    }

    /**
     * Get the billing rule this record is based on.
     */
    public function billingRule(): BelongsTo
    {
        return $this->belongsTo(BillingRule::class);
    }

    /**
     * Get the user who created this billing record.
     */
    public function creator(): BelongsTo
    {
        return $this->belongsTo(User::class, 'created_by');
    }

    /**
     * Scope to get pending records.
     */
    public function scopePending($query)
    {
        return $query->where('status', 'pending');
    }

    /**
     * Scope to get paid records.
     */
    public function scopePaid($query)
    {
        return $query->where('status', 'paid');
    }

    /**
     * Scope to get overdue records.
     */
    public function scopeOverdue($query)
    {
        return $query->where('status', 'overdue')
                    ->orWhere(function ($q) {
                        $q->where('status', 'pending')
                          ->where('due_date', '<', now());
                    });
    }

    /**
     * Scope to filter by charge type.
     */
    public function scopeOfType($query, string $type)
    {
        return $query->where('charge_type', $type);
    }

    /**
     * Scope to filter by period.
     */
    public function scopeForPeriod($query, $startDate, $endDate)
    {
        return $query->where(function ($q) use ($startDate, $endDate) {
            $q->whereBetween('period_start', [$startDate, $endDate])
              ->orWhereBetween('period_end', [$startDate, $endDate])
              ->orWhere(function ($subQ) use ($startDate, $endDate) {
                  $subQ->where('period_start', '<=', $startDate)
                       ->where('period_end', '>=', $endDate);
              });
        });
    }

    /**
     * Check if the record is overdue.
     */
    public function isOverdue(): bool
    {
        return $this->status === 'pending' && $this->due_date < now();
    }

    /**
     * Check if the record is paid.
     */
    public function isPaid(): bool
    {
        return $this->status === 'paid';
    }

    /**
     * Mark the record as paid.
     */
    public function markAsPaid(string $paymentMethod = null, string $transactionId = null): bool
    {
        return $this->update([
            'status' => 'paid',
            'paid_date' => now(),
            'payment_method' => $paymentMethod,
            'transaction_id' => $transactionId,
        ]);
    }

    /**
     * Get the period description.
     */
    public function getPeriodDescriptionAttribute(): string
    {
        return $this->period_start->format('M Y') . ' - ' . $this->period_end->format('M Y');
    }

    /**
     * Get the days overdue.
     */
    public function getDaysOverdueAttribute(): int
    {
        if (!$this->isOverdue()) {
            return 0;
        }

        return now()->diffInDays($this->due_date);
    }
}
