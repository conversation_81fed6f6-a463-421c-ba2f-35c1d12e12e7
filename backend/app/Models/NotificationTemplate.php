<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Factories\HasFactory;

class NotificationTemplate extends Model
{
    use HasFactory;

    /**
     * The attributes that are mass assignable.
     */
    protected $fillable = [
        'name',
        'type',
        'category',
        'title',
        'content',
        'variables',
        'channels',
        'is_active',
        'version',
        'metadata',
    ];

    /**
     * The attributes that should be cast.
     */
    protected $casts = [
        'variables' => 'array',
        'channels' => 'array',
        'metadata' => 'array',
        'is_active' => 'boolean',
    ];

    /**
     * Template types
     */
    const TYPE_EMAIL = 'email';
    const TYPE_SMS = 'sms';
    const TYPE_PUSH = 'push';
    const TYPE_IN_APP = 'in_app';

    /**
     * Template categories
     */
    const CATEGORY_SYSTEM = 'system';
    const CATEGORY_TENANT = 'tenant';
    const CATEGORY_UNIT = 'unit';
    const CATEGORY_ENQUIRY = 'enquiry';
    const CATEGORY_NOC = 'noc';
    const CATEGORY_AGREEMENT = 'agreement';
    const CATEGORY_BILLING = 'billing';
    const CATEGORY_MAINTENANCE = 'maintenance';

    /**
     * Get the notifications that use this template.
     */
    public function notifications(): HasMany
    {
        return $this->hasMany(Notification::class, 'template_id');
    }

    /**
     * Scope to get active templates
     */
    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }

    /**
     * Scope to get inactive templates
     */
    public function scopeInactive($query)
    {
        return $query->where('is_active', false);
    }

    /**
     * Scope to get templates by type
     */
    public function scopeByType($query, $type)
    {
        return $query->where('type', $type);
    }

    /**
     * Scope to get templates by category
     */
    public function scopeByCategory($query, $category)
    {
        return $query->where('category', $category);
    }

    /**
     * Scope to get templates by name
     */
    public function scopeByName($query, $name)
    {
        return $query->where('name', $name);
    }

    /**
     * Check if template is active
     */
    public function isActive(): bool
    {
        return $this->is_active;
    }

    /**
     * Check if template supports a specific channel
     */
    public function supportsChannel(string $channel): bool
    {
        return in_array($channel, $this->channels ?? []);
    }

    /**
     * Get available variables for this template
     */
    public function getVariables(): array
    {
        return $this->variables ?? [];
    }

    /**
     * Render template with variables
     */
    public function render(array $variables = []): string
    {
        $content = $this->content;
        
        foreach ($variables as $key => $value) {
            $content = str_replace("{{" . $key . "}}", $value, $content);
        }
        
        return $content;
    }

    /**
     * Render title with variables
     */
    public function renderTitle(array $variables = []): string
    {
        $title = $this->title;
        
        foreach ($variables as $key => $value) {
            $title = str_replace("{{" . $key . "}}", $value, $title);
        }
        
        return $title;
    }

    /**
     * Get all possible template types
     */
    public static function getTypes(): array
    {
        return [
            self::TYPE_EMAIL,
            self::TYPE_SMS,
            self::TYPE_PUSH,
            self::TYPE_IN_APP,
        ];
    }

    /**
     * Get all possible template categories
     */
    public static function getCategories(): array
    {
        return [
            self::CATEGORY_SYSTEM,
            self::CATEGORY_TENANT,
            self::CATEGORY_UNIT,
            self::CATEGORY_ENQUIRY,
            self::CATEGORY_NOC,
            self::CATEGORY_AGREEMENT,
            self::CATEGORY_BILLING,
            self::CATEGORY_MAINTENANCE,
        ];
    }
}
