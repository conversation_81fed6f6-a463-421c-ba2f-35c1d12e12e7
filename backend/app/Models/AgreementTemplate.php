<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class AgreementTemplate extends Model
{
    use HasFactory;

    protected $fillable = [
        'name',
        'description',
        'template_file',
        'placeholders',
        'created_by',
    ];

    protected $casts = [
        'placeholders' => 'array',
    ];

    public function creator()
    {
        return $this->belongsTo(User::class, 'created_by');
    }
}
