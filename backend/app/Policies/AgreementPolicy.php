<?php

namespace App\Policies;

use App\Models\User;
use App\Models\Agreement;

class AgreementPolicy
{
    /**
     * Determine whether the user can view any agreements.
     */
    public function viewAny(User $user): bool
    {
        return $user->isAdmin();
    }

    /**
     * Determine whether the user can view the agreement.
     */
    public function view(User $user, Agreement $agreement): bool
    {
        return $user->isAdmin();
    }

    /**
     * Determine whether the user can create agreements.
     */
    public function create(User $user): bool
    {
        return $user->isAdmin();
    }

    /**
     * Determine whether the user can update the agreement.
     */
    public function update(User $user, Agreement $agreement): bool
    {
        return $user->isAdmin();
    }

    /**
     * Determine whether the user can delete the agreement.
     */
    public function delete(User $user, Agreement $agreement): bool
    {
        return $user->isAdmin();
    }
} 