<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;
use App\Models\ThirdPartyApiConfig;

class ThirdPartyApiConfigRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true; // Authorization is handled by middleware
    }

    /**
     * Get the validation rules that apply to the request.
     */
    public function rules(): array
    {
        $rules = [
            'name' => ['required', 'string', 'max:255'],
            'description' => ['nullable', 'string', 'max:1000'],
            'base_url' => ['required', 'url', 'max:255'],
            'auth_type' => ['required', 'string', Rule::in(array_keys(ThirdPartyApiConfig::AUTH_TYPES))],
            'content_type' => ['nullable', 'string', Rule::in(array_keys(ThirdPartyApiConfig::CONTENT_TYPES))],
            'timeout' => ['nullable', 'integer', 'min:1', 'max:300'],
            'retries' => ['nullable', 'integer', 'min:0', 'max:10'],
            'environment' => ['nullable', 'string', Rule::in(array_keys(ThirdPartyApiConfig::ENVIRONMENTS))],
            'version' => ['nullable', 'string', 'max:50'],
            'documentation_url' => ['nullable', 'url', 'max:255'],
            'test_endpoint' => ['nullable', 'string', 'max:255'],
            'is_active' => ['nullable', 'boolean'],
            'test_connection' => ['nullable', 'boolean'],
            
            // Rate limiting
            'rate_limit_enabled' => ['nullable', 'boolean'],
            'rate_limit_requests' => ['nullable', 'integer', 'min:1'],
            'rate_limit_duration' => ['nullable', 'integer', 'min:1'],
            
            // Caching
            'cache_enabled' => ['nullable', 'boolean'],
            'cache_duration' => ['nullable', 'integer', 'min:1'],
            
            // Webhook
            'webhook_secret' => ['nullable', 'string', 'max:255'],
            'webhook_url' => ['nullable', 'url', 'max:255'],
            
            // Mappings
            'request_mapping' => ['nullable', 'array'],
            'response_mapping' => ['nullable', 'array'],
            'custom_headers' => ['nullable', 'array'],
            'settings' => ['nullable', 'array'],
        ];

        // Add authentication-specific rules
        $authType = $this->input('auth_type');
        
        switch ($authType) {
            case 'bearer':
            case 'api_key':
                $rules['auth_token'] = ['required', 'string', 'max:500'];
                if ($authType === 'api_key') {
                    $rules['auth_header'] = ['nullable', 'string', 'max:100'];
                }
                break;
                
            case 'basic':
                $rules['auth_username'] = ['required', 'string', 'max:255'];
                $rules['auth_password'] = ['required', 'string', 'max:255'];
                break;
                
            case 'oauth2':
                $rules['auth_token'] = ['required', 'string', 'max:500'];
                $rules['settings'] = ['required', 'array'];
                $rules['settings.client_id'] = ['required', 'string'];
                $rules['settings.client_secret'] = ['required', 'string'];
                break;
                
            case 'custom':
                $rules['custom_headers'] = ['required', 'array', 'min:1'];
                break;
        }

        // Add identifier uniqueness rule
        $identifierRule = ['nullable', 'string', 'max:100', 'regex:/^[a-zA-Z0-9_-]+$/'];
        
        if ($this->isMethod('POST')) {
            $identifierRule[] = 'unique:third_party_api_configs,identifier';
        } elseif ($this->isMethod('PUT') || $this->isMethod('PATCH')) {
            $identifierRule[] = Rule::unique('third_party_api_configs', 'identifier')->ignore($this->route('id'));
        }
        
        $rules['identifier'] = $identifierRule;

        return $rules;
    }

    /**
     * Get the error messages for the defined validation rules.
     */
    public function messages(): array
    {
        return [
            'name.required' => 'The API name is required.',
            'name.max' => 'The API name may not be greater than 255 characters.',
            'base_url.required' => 'The base URL is required.',
            'base_url.url' => 'The base URL must be a valid URL.',
            'auth_type.required' => 'The authentication type is required.',
            'auth_type.in' => 'The selected authentication type is invalid.',
            'auth_token.required' => 'The authentication token is required for this authentication type.',
            'auth_username.required' => 'The username is required for basic authentication.',
            'auth_password.required' => 'The password is required for basic authentication.',
            'custom_headers.required' => 'Custom headers are required for custom authentication.',
            'custom_headers.min' => 'At least one custom header is required.',
            'identifier.unique' => 'This API identifier is already in use.',
            'identifier.regex' => 'The identifier may only contain letters, numbers, underscores, and hyphens.',
            'timeout.min' => 'The timeout must be at least 1 second.',
            'timeout.max' => 'The timeout may not be greater than 300 seconds.',
            'retries.min' => 'The number of retries must be at least 0.',
            'retries.max' => 'The number of retries may not be greater than 10.',
            'rate_limit_requests.min' => 'The rate limit requests must be at least 1.',
            'rate_limit_duration.min' => 'The rate limit duration must be at least 1 second.',
            'cache_duration.min' => 'The cache duration must be at least 1 second.',
            'webhook_url.url' => 'The webhook URL must be a valid URL.',
            'documentation_url.url' => 'The documentation URL must be a valid URL.',
            'settings.client_id.required' => 'The client ID is required for OAuth2 authentication.',
            'settings.client_secret.required' => 'The client secret is required for OAuth2 authentication.',
        ];
    }

    /**
     * Configure the validator instance.
     */
    public function withValidator($validator)
    {
        $validator->after(function ($validator) {
            // Custom validation logic
            $this->validateRateLimit($validator);
            $this->validateCache($validator);
            $this->validateMappings($validator);
        });
    }

    /**
     * Validate rate limit configuration
     */
    protected function validateRateLimit($validator)
    {
        if ($this->input('rate_limit_enabled')) {
            if (!$this->input('rate_limit_requests')) {
                $validator->errors()->add('rate_limit_requests', 'Rate limit requests is required when rate limiting is enabled.');
            }
            
            if (!$this->input('rate_limit_duration')) {
                $validator->errors()->add('rate_limit_duration', 'Rate limit duration is required when rate limiting is enabled.');
            }
        }
    }

    /**
     * Validate cache configuration
     */
    protected function validateCache($validator)
    {
        if ($this->input('cache_enabled')) {
            if (!$this->input('cache_duration')) {
                $validator->errors()->add('cache_duration', 'Cache duration is required when caching is enabled.');
            }
        }
    }

    /**
     * Validate mapping configurations
     */
    protected function validateMappings($validator)
    {
        // Validate request mapping
        if ($this->input('request_mapping')) {
            $mapping = $this->input('request_mapping');
            if (!is_array($mapping)) {
                $validator->errors()->add('request_mapping', 'Request mapping must be an array.');
            } else {
                foreach ($mapping as $key => $value) {
                    if (!is_string($key) || !is_string($value)) {
                        $validator->errors()->add('request_mapping', 'Request mapping keys and values must be strings.');
                        break;
                    }
                }
            }
        }

        // Validate response mapping
        if ($this->input('response_mapping')) {
            $mapping = $this->input('response_mapping');
            if (!is_array($mapping)) {
                $validator->errors()->add('response_mapping', 'Response mapping must be an array.');
            } else {
                foreach ($mapping as $key => $value) {
                    if (!is_string($key) || !is_string($value)) {
                        $validator->errors()->add('response_mapping', 'Response mapping keys and values must be strings.');
                        break;
                    }
                }
            }
        }
    }

    /**
     * Get the validated data from the request.
     */
    public function validated($key = null, $default = null)
    {
        $validated = parent::validated($key, $default);

        // Set defaults for optional fields
        $validated['content_type'] = $validated['content_type'] ?? 'application/json';
        $validated['timeout'] = $validated['timeout'] ?? 30;
        $validated['retries'] = $validated['retries'] ?? 3;
        $validated['environment'] = $validated['environment'] ?? 'production';
        $validated['is_active'] = $validated['is_active'] ?? true;
        $validated['rate_limit_enabled'] = $validated['rate_limit_enabled'] ?? false;
        $validated['cache_enabled'] = $validated['cache_enabled'] ?? false;
        
        // Set rate limit defaults
        if ($validated['rate_limit_enabled']) {
            $validated['rate_limit_requests'] = $validated['rate_limit_requests'] ?? 100;
            $validated['rate_limit_duration'] = $validated['rate_limit_duration'] ?? 60;
        }

        // Set cache defaults
        if ($validated['cache_enabled']) {
            $validated['cache_duration'] = $validated['cache_duration'] ?? 300;
        }

        // Remove test_connection from validated data as it's not stored
        unset($validated['test_connection']);

        return $validated;
    }
} 