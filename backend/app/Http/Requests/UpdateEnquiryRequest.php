<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;

class UpdateEnquiryRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true; // Authorization handled by middleware
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            'status' => [
                'sometimes',
                'required',
                'string',
                Rule::in(['open', 'in_progress', 'resolved', 'closed', 'cancelled'])
            ],
            'priority' => [
                'sometimes',
                'required',
                'string',
                Rule::in(['low', 'medium', 'high', 'urgent'])
            ],
            'assigned_to' => 'sometimes|nullable|exists:users,id',
            'resolution_notes' => 'sometimes|nullable|string|max:2000',
            'internal_notes' => 'sometimes|nullable|string|max:2000',
            'estimated_resolution_date' => 'sometimes|nullable|date|after:today',
            'follow_up_required' => 'sometimes|boolean',
            'follow_up_date' => 'sometimes|nullable|date|after:today',
            'tags' => 'sometimes|nullable|array',
            'tags.*' => 'string|max:50',
        ];
    }

    /**
     * Get custom messages for validator errors.
     */
    public function messages(): array
    {
        return [
            'status.in' => 'Invalid status selected.',
            'priority.in' => 'Invalid priority level selected.',
            'assigned_to.exists' => 'The selected assignee does not exist.',
            'resolution_notes.max' => 'Resolution notes cannot exceed 2000 characters.',
            'internal_notes.max' => 'Internal notes cannot exceed 2000 characters.',
            'estimated_resolution_date.after' => 'Estimated resolution date must be in the future.',
            'follow_up_date.after' => 'Follow-up date must be in the future.',
            'tags.array' => 'Tags must be provided as an array.',
            'tags.*.string' => 'Each tag must be a string.',
            'tags.*.max' => 'Each tag cannot exceed 50 characters.',
        ];
    }

    /**
     * Get custom attributes for validator errors.
     */
    public function attributes(): array
    {
        return [
            'assigned_to' => 'assignee',
            'estimated_resolution_date' => 'estimated resolution date',
            'follow_up_date' => 'follow-up date',
        ];
    }

    /**
     * Configure the validator instance.
     */
    public function withValidator($validator): void
    {
        $validator->after(function ($validator) {
            $enquiry = $this->route('enquiry');
            
            if ($enquiry) {
                // Validate status transitions
                if ($this->has('status')) {
                    $currentStatus = $enquiry->status;
                    $newStatus = $this->status;
                    
                    $allowedTransitions = [
                        'open' => ['in_progress', 'cancelled'],
                        'in_progress' => ['resolved', 'open', 'cancelled'],
                        'resolved' => ['closed', 'open'],
                        'closed' => ['open'], // Can reopen if needed
                        'cancelled' => ['open'], // Can reopen if needed
                    ];

                    if (!in_array($newStatus, $allowedTransitions[$currentStatus] ?? [])) {
                        $validator->errors()->add('status', "Cannot change status from {$currentStatus} to {$newStatus}.");
                    }
                }

                // Resolution notes required when marking as resolved
                if ($this->status === 'resolved' && empty($this->resolution_notes)) {
                    $validator->errors()->add('resolution_notes', 'Resolution notes are required when marking enquiry as resolved.');
                }

                // Follow-up date required when follow-up is required
                if ($this->follow_up_required && empty($this->follow_up_date)) {
                    $validator->errors()->add('follow_up_date', 'Follow-up date is required when follow-up is marked as required.');
                }

                // Validate assignee permissions
                if ($this->assigned_to) {
                    $assignee = \App\Models\User::find($this->assigned_to);
                    if ($assignee && !$assignee->hasRole(['admin', 'manager', 'staff'])) {
                        $validator->errors()->add('assigned_to', 'Selected user cannot be assigned to enquiries.');
                    }
                }
            }
        });
    }
}
