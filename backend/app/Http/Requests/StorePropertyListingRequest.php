<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;

class StorePropertyListingRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true; // Authorization handled by middleware
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            'unit_id' => [
                'required',
                'integer',
                'exists:units,id',
                Rule::unique('property_listings', 'unit_id')->where(function ($query) {
                    return $query->whereIn('status', ['draft', 'pending_approval', 'approved', 'published']);
                })
            ],
            'title' => 'required|string|max:200',
            'description' => 'nullable|string|max:2000',
            'rent_amount' => 'required|numeric|min:0|max:999999.99',
            'deposit_amount' => 'required|numeric|min:0|max:999999.99',
            'amenities' => 'nullable|array',
            'amenities.*' => 'string|max:100',
            'preferences' => 'nullable|array',
            'preferences.family_type' => 'nullable|string|in:bachelor,family,any',
            'preferences.occupation' => 'nullable|string|max:100',
            'preferences.dietary_preference' => 'nullable|string|in:vegetarian,non_vegetarian,any',
            'preferences.pet_friendly' => 'nullable|boolean',
            'preferences.smoking_allowed' => 'nullable|boolean',
            'media_urls' => 'nullable|array|max:10',
            'media_urls.*' => 'url|max:500',
            'portal_specific_data' => 'nullable|array',
            'auto_publish' => 'nullable|boolean',
            'publish_to_portals' => 'nullable|array',
            'publish_to_portals.*' => 'string|in:99acres,magicbricks,housing,olx,nobroker',
        ];
    }

    /**
     * Get custom messages for validator errors.
     */
    public function messages(): array
    {
        return [
            'unit_id.required' => 'Unit selection is required.',
            'unit_id.exists' => 'Selected unit does not exist.',
            'unit_id.unique' => 'This unit already has an active listing.',
            'title.required' => 'Listing title is required.',
            'title.max' => 'Listing title cannot exceed 200 characters.',
            'description.max' => 'Description cannot exceed 2000 characters.',
            'rent_amount.required' => 'Monthly rent amount is required.',
            'rent_amount.numeric' => 'Rent amount must be a valid number.',
            'rent_amount.min' => 'Rent amount cannot be negative.',
            'deposit_amount.required' => 'Security deposit amount is required.',
            'deposit_amount.numeric' => 'Deposit amount must be a valid number.',
            'amenities.array' => 'Amenities must be provided as a list.',
            'preferences.family_type.in' => 'Family type must be bachelor, family, or any.',
            'preferences.dietary_preference.in' => 'Dietary preference must be vegetarian, non_vegetarian, or any.',
            'media_urls.max' => 'Maximum 10 media files are allowed.',
            'media_urls.*.url' => 'Each media URL must be a valid URL.',
            'publish_to_portals.*.in' => 'Invalid portal name provided.',
        ];
    }

    /**
     * Get custom attributes for validator errors.
     */
    public function attributes(): array
    {
        return [
            'unit_id' => 'unit',
            'rent_amount' => 'monthly rent',
            'deposit_amount' => 'security deposit',
            'media_urls' => 'media files',
            'publish_to_portals' => 'portals',
        ];
    }

    /**
     * Prepare the data for validation.
     */
    protected function prepareForValidation(): void
    {
        // Convert string booleans to actual booleans
        if ($this->has('auto_publish')) {
            $this->merge([
                'auto_publish' => filter_var($this->auto_publish, FILTER_VALIDATE_BOOLEAN, FILTER_NULL_ON_FAILURE),
            ]);
        }

        if ($this->has('preferences.pet_friendly')) {
            $preferences = $this->preferences ?? [];
            $preferences['pet_friendly'] = filter_var($preferences['pet_friendly'] ?? false, FILTER_VALIDATE_BOOLEAN, FILTER_NULL_ON_FAILURE);
            $this->merge(['preferences' => $preferences]);
        }

        if ($this->has('preferences.smoking_allowed')) {
            $preferences = $this->preferences ?? [];
            $preferences['smoking_allowed'] = filter_var($preferences['smoking_allowed'] ?? false, FILTER_VALIDATE_BOOLEAN, FILTER_NULL_ON_FAILURE);
            $this->merge(['preferences' => $preferences]);
        }
    }

    /**
     * Configure the validator instance.
     */
    public function withValidator($validator): void
    {
        $validator->after(function ($validator) {
            // Check if unit is available for listing
            if ($this->unit_id) {
                $unit = \App\Models\Unit::find($this->unit_id);
                if ($unit && $unit->status !== 'to-let') {
                    $validator->errors()->add('unit_id', 'Only units with "to-let" status can be listed.');
                }
            }

            // Validate rent amount against unit's market rent
            if ($this->unit_id && $this->rent_amount) {
                $unit = \App\Models\Unit::find($this->unit_id);
                if ($unit && $unit->market_rent && $this->rent_amount > ($unit->market_rent * 1.5)) {
                    $validator->errors()->add('rent_amount', 'Rent amount seems too high compared to market rate.');
                }
            }

            // Validate deposit amount (typically 1-12 months rent)
            if ($this->rent_amount && $this->deposit_amount) {
                $maxDeposit = $this->rent_amount * 12;
                if ($this->deposit_amount > $maxDeposit) {
                    $validator->errors()->add('deposit_amount', 'Deposit amount cannot exceed 12 months rent.');
                }
            }
        });
    }
}
