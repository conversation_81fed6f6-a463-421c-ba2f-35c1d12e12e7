<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;

class StoreEnquiryRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true; // Authorization handled by middleware
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            'unit_id' => 'required|exists:units,id',
            'type' => [
                'required',
                'string',
                Rule::in(['rental_inquiry', 'purchase_inquiry', 'general_inquiry', 'complaint', 'maintenance_request'])
            ],
            'subject' => 'required|string|max:200',
            'message' => 'required|string|max:2000',
            'priority' => [
                'nullable',
                'string',
                Rule::in(['low', 'medium', 'high', 'urgent'])
            ],
            'contact_preference' => [
                'nullable',
                'string',
                Rule::in(['email', 'phone', 'both'])
            ],
            'preferred_contact_time' => 'nullable|string|max:100',
            'budget_range' => 'nullable|string|max:50',
            'move_in_date' => 'nullable|date|after:today',
            'additional_requirements' => 'nullable|string|max:1000',
            'attachments' => 'nullable|array|max:5',
            'attachments.*' => 'file|mimes:pdf,doc,docx,jpg,jpeg,png|max:5120', // 5MB max
        ];
    }

    /**
     * Get custom messages for validator errors.
     */
    public function messages(): array
    {
        return [
            'unit_id.required' => 'Please select a unit for your enquiry.',
            'unit_id.exists' => 'The selected unit does not exist.',
            'type.required' => 'Please specify the type of enquiry.',
            'type.in' => 'Invalid enquiry type selected.',
            'subject.required' => 'Subject is required for the enquiry.',
            'subject.max' => 'Subject cannot exceed 200 characters.',
            'message.required' => 'Please provide a detailed message.',
            'message.max' => 'Message cannot exceed 2000 characters.',
            'priority.in' => 'Invalid priority level selected.',
            'contact_preference.in' => 'Invalid contact preference selected.',
            'move_in_date.after' => 'Move-in date must be in the future.',
            'budget_range.max' => 'Budget range cannot exceed 50 characters.',
            'additional_requirements.max' => 'Additional requirements cannot exceed 1000 characters.',
            'attachments.max' => 'Maximum 5 attachments are allowed.',
            'attachments.*.file' => 'Each attachment must be a valid file.',
            'attachments.*.mimes' => 'Attachments must be PDF, DOC, DOCX, JPG, JPEG, or PNG files.',
            'attachments.*.max' => 'Each attachment cannot exceed 5MB.',
        ];
    }

    /**
     * Get custom attributes for validator errors.
     */
    public function attributes(): array
    {
        return [
            'unit_id' => 'unit',
            'move_in_date' => 'preferred move-in date',
            'additional_requirements' => 'additional requirements',
        ];
    }

    /**
     * Prepare the data for validation.
     */
    protected function prepareForValidation(): void
    {
        // Set default priority if not provided
        if (!$this->has('priority')) {
            $this->merge(['priority' => 'medium']);
        }

        // Set default contact preference if not provided
        if (!$this->has('contact_preference')) {
            $this->merge(['contact_preference' => 'email']);
        }

        // Clean and format budget range
        if ($this->has('budget_range')) {
            $budgetRange = preg_replace('/[^\d\-\s]/', '', $this->budget_range);
            $this->merge(['budget_range' => trim($budgetRange)]);
        }
    }

    /**
     * Configure the validator instance.
     */
    public function withValidator($validator): void
    {
        $validator->after(function ($validator) {
            // Check if unit is available for the enquiry type
            if ($this->unit_id && $this->type) {
                $unit = \App\Models\Unit::find($this->unit_id);
                
                if ($unit) {
                    // Rental inquiries only for vacant or to-let units
                    if ($this->type === 'rental_inquiry' && !in_array($unit->status, ['vacant', 'to-let'])) {
                        $validator->errors()->add('unit_id', 'This unit is not available for rental.');
                    }
                    
                    // Purchase inquiries only for units owned by the society
                    if ($this->type === 'purchase_inquiry' && $unit->status === 'rented') {
                        $validator->errors()->add('unit_id', 'This unit is currently rented and not available for purchase.');
                    }
                }
            }

            // Validate budget range format
            if ($this->budget_range && !preg_match('/^\d+\s*-\s*\d+$/', $this->budget_range)) {
                $validator->errors()->add('budget_range', 'Budget range must be in format "min-max" (e.g., "10000-25000").');
            }

            // Validate move-in date for rental inquiries
            if ($this->type === 'rental_inquiry' && $this->move_in_date) {
                $moveInDate = \Carbon\Carbon::parse($this->move_in_date);
                if ($moveInDate->diffInDays(now()) > 90) {
                    $validator->errors()->add('move_in_date', 'Move-in date cannot be more than 90 days in the future.');
                }
            }
        });
    }
}
