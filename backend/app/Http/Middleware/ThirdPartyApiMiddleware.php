<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Illuminate\Http\Response;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\RateLimiter;
use App\Services\ThirdPartyApiService;
use App\Models\ThirdPartyApiConfig;
use App\Models\ThirdPartyApiLog;
use Carbon\Carbon;

class ThirdPartyApiMiddleware
{
    protected $thirdPartyApiService;

    public function __construct(ThirdPartyApiService $thirdPartyApiService)
    {
        $this->thirdPartyApiService = $thirdPartyApiService;
    }

    /**
     * Handle an incoming request.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \Closure  $next
     * @return mixed
     */
    public function handle(Request $request, Closure $next)
    {
        // Extract API identifier from route or headers
        $apiIdentifier = $this->extractApiIdentifier($request);
        
        if (!$apiIdentifier) {
            return response()->json([
                'error' => 'API identifier is required',
                'message' => 'Please provide api_id in headers or route parameters'
            ], 400);
        }

        // Load API configuration
        $apiConfig = ThirdPartyApiConfig::where('identifier', $apiIdentifier)
            ->where('is_active', true)
            ->first();

        if (!$apiConfig) {
            return response()->json([
                'error' => 'API configuration not found',
                'message' => 'The specified API is not configured or is inactive'
            ], 404);
        }

        // Check rate limiting
        if (!$this->checkRateLimit($apiConfig, $request)) {
            return response()->json([
                'error' => 'Rate limit exceeded',
                'message' => 'Too many requests. Please try again later.',
                'retry_after' => $this->getRateLimitRetryAfter($apiConfig)
            ], 429);
        }

        // Add API configuration to request
        $request->merge(['api_config' => $apiConfig]);

        // Log the request
        $logId = $this->logRequest($request, $apiConfig);

        try {
            // Process the request
            $response = $next($request);

            // Log successful response
            $this->logResponse($logId, $response, 'success');

            return $response;

        } catch (\Exception $e) {
            // Log error response
            $this->logResponse($logId, null, 'error', $e->getMessage());

            return response()->json([
                'error' => 'Third-party API integration error',
                'message' => config('app.debug') ? $e->getMessage() : 'An error occurred while processing your request'
            ], 500);
        }
    }

    /**
     * Extract API identifier from request
     */
    protected function extractApiIdentifier(Request $request): ?string
    {
        // Try to get from headers first
        if ($request->hasHeader('X-API-ID')) {
            return $request->header('X-API-ID');
        }

        // Try to get from route parameters
        if ($request->route('api_id')) {
            return $request->route('api_id');
        }

        // Try to get from query parameters
        if ($request->query('api_id')) {
            return $request->query('api_id');
        }

        return null;
    }

    /**
     * Check rate limiting for the API
     */
    protected function checkRateLimit(ThirdPartyApiConfig $apiConfig, Request $request): bool
    {
        $key = "api_rate_limit:{$apiConfig->identifier}:" . $request->ip();
        
        if (!$apiConfig->rate_limit_enabled) {
            return true;
        }

        return RateLimiter::attempt(
            $key,
            $apiConfig->rate_limit_requests,
            function () {
                // This callback is executed if the rate limit is not exceeded
            },
            $apiConfig->rate_limit_duration
        );
    }

    /**
     * Get rate limit retry after seconds
     */
    protected function getRateLimitRetryAfter(ThirdPartyApiConfig $apiConfig): int
    {
        $key = "api_rate_limit:{$apiConfig->identifier}:" . request()->ip();
        return RateLimiter::availableIn($key);
    }

    /**
     * Log the incoming request
     */
    protected function logRequest(Request $request, ThirdPartyApiConfig $apiConfig): string
    {
        $logId = uniqid('api_log_');
        
        ThirdPartyApiLog::create([
            'log_id' => $logId,
            'api_config_id' => $apiConfig->id,
            'request_method' => $request->method(),
            'request_url' => $request->fullUrl(),
            'request_headers' => $this->sanitizeHeaders($request->headers->all()),
            'request_body' => $this->sanitizeRequestBody($request->all()),
            'ip_address' => $request->ip(),
            'user_agent' => $request->userAgent(),
            'created_at' => Carbon::now(),
            'status' => 'pending'
        ]);

        return $logId;
    }

    /**
     * Log the response
     */
    protected function logResponse(string $logId, $response, string $status, ?string $errorMessage = null): void
    {
        $logData = [
            'status' => $status,
            'updated_at' => Carbon::now()
        ];

        if ($response) {
            $logData['response_status'] = $response->getStatusCode();
            $logData['response_headers'] = $response->headers->all();
            $logData['response_body'] = $response->getContent();
        }

        if ($errorMessage) {
            $logData['error_message'] = $errorMessage;
        }

        ThirdPartyApiLog::where('log_id', $logId)->update($logData);
    }

    /**
     * Sanitize headers to remove sensitive information
     */
    protected function sanitizeHeaders(array $headers): array
    {
        $sensitiveHeaders = ['authorization', 'x-api-key', 'x-auth-token', 'cookie'];
        
        foreach ($sensitiveHeaders as $header) {
            if (isset($headers[$header])) {
                $headers[$header] = ['[REDACTED]'];
            }
        }

        return $headers;
    }

    /**
     * Sanitize request body to remove sensitive information
     */
    protected function sanitizeRequestBody(array $body): array
    {
        $sensitiveFields = ['password', 'token', 'secret', 'api_key', 'auth_token'];
        
        foreach ($sensitiveFields as $field) {
            if (isset($body[$field])) {
                $body[$field] = '[REDACTED]';
            }
        }

        return $body;
    }
} 