<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Symfony\Component\HttpFoundation\Response;

class PermissionMiddleware
{
    /**
     * Handle an incoming request.
     *
     * @param  \Closure(\Illuminate\Http\Request): (\Symfony\Component\HttpFoundation\Response)  $next
     */
    public function handle(Request $request, Closure $next, ...$permissions): Response
    {
        // Check if user is authenticated
        if (!$request->user()) {
            return response()->json([
                'success' => false,
                'message' => 'Unauthorized. Please login first.',
            ], 401);
        }

        $user = $request->user();

        // Debug output for testing
        if (app()->environment('testing')) {
            \Log::info('PermissionMiddleware Debug', [
                'user_id' => $user->id,
                'user_role' => $user->role,
                'user_permissions' => $user->getPermissions(),
                'is_admin' => $user->isAdmin(),
                'is_active' => $user->isActive(),
                'is_verified' => $user->isVerified(),
                'current_token' => $user->currentAccessToken(),
                'token_abilities' => $user->currentAccessToken() ? $user->currentAccessToken()->abilities : 'no_token',
                'required_permissions' => $permissions,
            ]);
        }

        // Check if user account is active
        if (!$user->isActive()) {
            return response()->json([
                'success' => false,
                'message' => 'Account is not active. Please contact administrator.',
            ], 403);
        }

        // Check if user is verified
        if (!$user->isVerified()) {
            return response()->json([
                'success' => false,
                'message' => 'Account is not verified. Please verify your account first.',
            ], 403);
        }

        // If no permissions specified, just check if user is authenticated and active
        if (empty($permissions)) {
            return $next($request);
        }

        // Check if user has admin role (admin has all permissions)
        if ($user->isAdmin()) {
            return $next($request);
        }

        // Get user's token abilities
        $token = $user->currentAccessToken();
        if (!$token) {
            return response()->json([
                'success' => false,
                'message' => 'Invalid token. Please login again.',
            ], 401);
        }

        $userAbilities = $token->abilities ?? [];

        // Check if user has wildcard permission
        if (in_array('*', $userAbilities)) {
            return $next($request);
        }

        // Check if user has any of the required permissions
        $hasPermission = false;
        foreach ($permissions as $permission) {
            if (in_array($permission, $userAbilities)) {
                $hasPermission = true;
                break;
            }
        }

        if (!$hasPermission) {
            return response()->json([
                'success' => false,
                'message' => 'Insufficient permissions. Required: ' . implode(', ', $permissions),
            ], 403);
        }

        return $next($request);
    }
}
