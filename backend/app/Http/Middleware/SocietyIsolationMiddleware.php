<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;

class SocietyIsolationMiddleware
{
    public function handle(Request $request, Closure $next)
    {
        $user = Auth::user();
        
        if (!$user) {
            return $next($request);
        }

        // Super admins bypass society isolation
        if ($user->role === 'admin') {
            return $next($request);
        }

        // Ensure user has society_id for society-based roles
        if (in_array($user->role, ['society_admin', 'owner', 'tenant']) && !$user->society_id) {
            return response()->json([
                'success' => false,
                'message' => 'User not associated with any society'
            ], 403);
        }

        return $next($request);
    }
} 