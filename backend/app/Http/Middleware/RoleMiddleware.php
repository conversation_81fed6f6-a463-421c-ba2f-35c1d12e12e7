<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Symfony\Component\HttpFoundation\Response;

class RoleMiddleware
{
    /**
     * Handle an incoming request.
     *
     * @param  \Closure(\Illuminate\Http\Request): (\Symfony\Component\HttpFoundation\Response)  $next
     */
    public function handle(Request $request, Closure $next, ...$roles): Response
    {
        // Check if user is authenticated
        if (!$request->user()) {
            return response()->json([
                'success' => false,
                'message' => 'Unauthorized. Please login first.',
            ], 401);
        }

        $user = $request->user();

        // Check if user account is active
        if (!$user->isActive()) {
            return response()->json([
                'success' => false,
                'message' => 'Account is not active. Please contact administrator.',
            ], 403);
        }

        // Check if user is verified
        if (!$user->isVerified()) {
            return response()->json([
                'success' => false,
                'message' => 'Account is not verified. Please verify your account first.',
            ], 403);
        }

        // If no roles specified, just check if user is authenticated and active
        if (empty($roles)) {
            return $next($request);
        }

        // Check if user has required role
        if (!in_array($user->role, $roles)) {
            return response()->json([
                'success' => false,
                'message' => 'Insufficient permissions. Required role: ' . implode(', ', $roles),
            ], 403);
        }

        return $next($request);
    }
}
