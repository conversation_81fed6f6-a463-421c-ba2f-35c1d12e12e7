<?php

namespace App\Http\Resources;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class PropertyLeadResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        return [
            'id' => $this->id,
            'property_listing_id' => $this->property_listing_id,
            'portal_name' => $this->portal_name,
            'portal_lead_id' => $this->portal_lead_id,
            'enquirer_name' => $this->enquirer_name,
            'enquirer_email' => $this->enquirer_email,
            'enquirer_phone' => $this->enquirer_phone,
            'message' => $this->message,
            'status' => $this->status,
            'priority' => $this->priority,
            'assigned_to' => $this->assigned_to,
            'response_data' => $this->response_data ?? [],
            'communication_history' => $this->communication_history ?? [],
            'follow_up_date' => $this->follow_up_date?->toISOString(),
            'converted_at' => $this->converted_at?->toISOString(),
            'created_at' => $this->created_at->toISOString(),
            'updated_at' => $this->updated_at->toISOString(),
            
            // Relationships
            'property_listing' => new PropertyListingResource($this->whenLoaded('propertyListing')),
            'assigned_user' => new UserResource($this->whenLoaded('assignedUser')),
            
            // Computed fields
            'is_new' => $this->status === 'new',
            'is_overdue' => $this->follow_up_date && $this->follow_up_date->isPast(),
            'days_since_inquiry' => $this->created_at->diffInDays(now()),
            'response_time' => $this->getResponseTime(),
        ];
    }

    /**
     * Calculate response time for the lead.
     */
    private function getResponseTime(): ?array
    {
        if (empty($this->communication_history)) {
            return null;
        }

        $firstResponse = collect($this->communication_history)
            ->where('type', 'outgoing')
            ->sortBy('timestamp')
            ->first();

        if (!$firstResponse) {
            return null;
        }

        $responseTime = $this->created_at->diffInMinutes($firstResponse['timestamp']);
        
        return [
            'minutes' => $responseTime,
            'hours' => round($responseTime / 60, 2),
            'formatted' => $this->formatResponseTime($responseTime),
        ];
    }

    /**
     * Format response time in human-readable format.
     */
    private function formatResponseTime(int $minutes): string
    {
        if ($minutes < 60) {
            return "{$minutes} minutes";
        } elseif ($minutes < 1440) {
            $hours = round($minutes / 60, 1);
            return "{$hours} hours";
        } else {
            $days = round($minutes / 1440, 1);
            return "{$days} days";
        }
    }

    /**
     * Additional data to include with the resource.
     */
    public function with(Request $request): array
    {
        return [
            'meta' => [
                'status_options' => [
                    'new' => 'New - Just received',
                    'contacted' => 'Contacted - Initial contact made',
                    'interested' => 'Interested - Showing interest',
                    'not_interested' => 'Not Interested - Declined',
                    'converted' => 'Converted - Became tenant',
                    'lost' => 'Lost - Opportunity lost',
                ],
                'priority_options' => [
                    'low' => 'Low Priority',
                    'medium' => 'Medium Priority',
                    'high' => 'High Priority',
                    'urgent' => 'Urgent',
                ],
                'portal_info' => [
                    '99acres' => ['name' => '99acres', 'color' => '#e74c3c'],
                    'magicbricks' => ['name' => 'MagicBricks', 'color' => '#3498db'],
                    'housing' => ['name' => 'Housing.com', 'color' => '#2ecc71'],
                    'olx' => ['name' => 'OLX', 'color' => '#f39c12'],
                    'nobroker' => ['name' => 'NoBroker', 'color' => '#9b59b6'],
                ],
            ],
        ];
    }
}
