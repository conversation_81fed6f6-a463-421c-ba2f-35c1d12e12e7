<?php

namespace App\Http\Resources;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class UserResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        return [
            'id' => $this->id,
            'name' => $this->name,
            'email' => $this->email,
            'phone' => $this->phone,
            'role' => $this->role,
            'status' => $this->status,
            'address' => $this->when(
                $request->user()?->id === $this->id || 
                $request->user()?->hasRole(['admin', 'manager']),
                $this->address
            ),
            'emergency_contact_name' => $this->when(
                $request->user()?->id === $this->id || 
                $request->user()?->hasRole(['admin', 'manager']),
                $this->emergency_contact_name
            ),
            'emergency_contact_phone' => $this->when(
                $request->user()?->id === $this->id || 
                $request->user()?->hasRole(['admin', 'manager']),
                $this->emergency_contact_phone
            ),
            'verification_status' => $this->verification_status,
            'last_login_at' => $this->last_login_at?->toISOString(),
            'created_at' => $this->created_at->toISOString(),
            'updated_at' => $this->updated_at->toISOString(),
            
            // Computed fields
            'display_name' => $this->name,
            'initials' => $this->getInitials(),
            'is_verified' => $this->verification_status === 'verified',
        ];
    }

    /**
     * Get user initials for display.
     */
    private function getInitials(): string
    {
        $words = explode(' ', trim($this->name));
        $initials = '';
        
        foreach ($words as $word) {
            if (!empty($word)) {
                $initials .= strtoupper(substr($word, 0, 1));
            }
        }
        
        return substr($initials, 0, 2);
    }

    /**
     * Additional data to include with the resource.
     */
    public function with(Request $request): array
    {
        return [
            'meta' => [
                'role_options' => [
                    'admin' => 'System Administrator',
                    'owner' => 'Unit Owner',
                    'tenant' => 'Tenant',
                    'staff' => 'Society Staff',
                ],
                'status_options' => [
                    'active' => 'Active User',
                    'inactive' => 'Inactive User',
                    'suspended' => 'Suspended User',
                ],
                'verification_status_options' => [
                    'pending' => 'Verification Pending',
                    'verified' => 'Verified User',
                    'rejected' => 'Verification Rejected',
                ],
            ],
        ];
    }
}
