<?php

namespace App\Http\Resources;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class PropertyListingResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        return [
            'id' => $this->id,
            'unit_id' => $this->unit_id,
            'title' => $this->title,
            'description' => $this->description,
            'rent_amount' => $this->rent_amount,
            'deposit_amount' => $this->deposit_amount,
            'status' => $this->status,
            'amenities' => $this->amenities ?? [],
            'preferences' => $this->preferences ?? [],
            'media_urls' => $this->media_urls ?? [],
            'portal_mappings' => $this->when(
                $request->user()?->hasRole(['admin', 'manager']),
                $this->portal_mappings ?? []
            ),
            'portal_specific_data' => $this->when(
                $request->user()?->hasRole(['admin', 'manager']),
                $this->portal_specific_data ?? []
            ),
            'view_count' => $this->view_count ?? 0,
            'inquiry_count' => $this->inquiry_count ?? 0,
            'last_synced_at' => $this->last_synced_at?->toISOString(),
            'published_at' => $this->published_at?->toISOString(),
            'expires_at' => $this->expires_at?->toISOString(),
            'created_at' => $this->created_at->toISOString(),
            'updated_at' => $this->updated_at->toISOString(),
            
            // Relationships
            'unit' => new UnitResource($this->whenLoaded('unit')),
            'leads' => PropertyLeadResource::collection($this->whenLoaded('leads')),
            'owner' => new UserResource($this->whenLoaded('owner')),
            
            // Computed fields
            'is_active' => $this->status === 'published',
            'is_expired' => $this->expires_at && $this->expires_at->isPast(),
            'days_since_published' => $this->published_at ? 
                $this->published_at->diffInDays(now()) : null,
            'portal_sync_status' => $this->getPortalSyncStatus(),
            'performance_metrics' => $this->when(
                $request->user()?->hasRole(['admin', 'manager', 'owner']),
                $this->getPerformanceMetrics()
            ),
        ];
    }

    /**
     * Get portal sync status for all configured portals.
     */
    private function getPortalSyncStatus(): array
    {
        $portals = ['99acres', 'magicbricks', 'housing', 'olx', 'nobroker'];
        $status = [];
        
        foreach ($portals as $portal) {
            $mapping = $this->portal_mappings[$portal] ?? null;
            $status[$portal] = [
                'synced' => !is_null($mapping),
                'portal_listing_id' => $mapping['listing_id'] ?? null,
                'last_sync' => $mapping['last_sync'] ?? null,
                'sync_status' => $mapping['status'] ?? 'not_synced',
                'error_message' => $mapping['error'] ?? null,
            ];
        }
        
        return $status;
    }

    /**
     * Get performance metrics for the listing.
     */
    private function getPerformanceMetrics(): array
    {
        return [
            'total_views' => $this->view_count ?? 0,
            'total_inquiries' => $this->inquiry_count ?? 0,
            'conversion_rate' => $this->view_count > 0 ? 
                round(($this->inquiry_count / $this->view_count) * 100, 2) : 0,
            'views_per_day' => $this->published_at ? 
                round($this->view_count / max(1, $this->published_at->diffInDays(now())), 2) : 0,
            'inquiries_per_day' => $this->published_at ? 
                round($this->inquiry_count / max(1, $this->published_at->diffInDays(now())), 2) : 0,
            'portal_performance' => $this->getPortalPerformance(),
        ];
    }

    /**
     * Get performance metrics per portal.
     */
    private function getPortalPerformance(): array
    {
        $performance = [];
        $portals = ['99acres', 'magicbricks', 'housing', 'olx', 'nobroker'];
        
        foreach ($portals as $portal) {
            $mapping = $this->portal_mappings[$portal] ?? null;
            if ($mapping) {
                $performance[$portal] = [
                    'views' => $mapping['views'] ?? 0,
                    'inquiries' => $mapping['inquiries'] ?? 0,
                    'last_activity' => $mapping['last_activity'] ?? null,
                ];
            }
        }
        
        return $performance;
    }

    /**
     * Additional data to include with the resource.
     */
    public function with(Request $request): array
    {
        return [
            'meta' => [
                'available_portals' => [
                    '99acres' => 'https://www.99acres.com',
                    'magicbricks' => 'https://www.magicbricks.com',
                    'housing' => 'https://housing.com',
                    'olx' => 'https://www.olx.in',
                    'nobroker' => 'https://www.nobroker.in',
                ],
                'status_options' => [
                    'draft' => 'Draft - Not yet submitted for approval',
                    'pending_approval' => 'Pending Approval - Waiting for admin review',
                    'approved' => 'Approved - Ready to be published',
                    'published' => 'Published - Live on portals',
                    'expired' => 'Expired - No longer active',
                ],
                'amenity_options' => [
                    'parking', 'gym', 'swimming_pool', 'security', 'power_backup',
                    'lift', 'garden', 'playground', 'club_house', 'wifi',
                    'air_conditioning', 'furnished', 'semi_furnished'
                ],
            ],
        ];
    }
}
