<?php

namespace App\Http\Resources;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class UnitResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        return [
            'id' => $this->id,
            'unit_number' => $this->unit_number,
            'block' => $this->block,
            'floor' => $this->floor,
            'unit_type' => $this->unit_type,
            'status' => $this->status,
            'bedrooms' => $this->bedrooms,
            'bathrooms' => $this->bathrooms,
            'area_sqft' => $this->area_sqft,
            'area_carpet_sqft' => $this->area_carpet_sqft,
            'market_rent' => $this->market_rent,
            'security_deposit' => $this->security_deposit,
            'maintenance_charges' => $this->maintenance_charges,
            'owner_id' => $this->owner_id,
            'current_tenant_id' => $this->current_tenant_id,
            'amenities' => $this->amenities ?? [],
            'preferences' => $this->preferences ?? [],
            'photos' => $this->photos ?? [],
            'documents' => $this->documents ?? [],
            'created_at' => $this->created_at->toISOString(),
            'updated_at' => $this->updated_at->toISOString(),
            
            // Relationships
            'owner' => new UserResource($this->whenLoaded('owner')),
            'current_tenant' => new UserResource($this->whenLoaded('currentTenant')),
            'property_listing' => new PropertyListingResource($this->whenLoaded('propertyListing')),
            
            // Computed fields
            'is_available_for_listing' => $this->status === 'to-let',
            'has_active_listing' => $this->relationLoaded('propertyListing') && 
                $this->propertyListing && 
                in_array($this->propertyListing->status, ['published', 'approved']),
            'full_address' => $this->getFullAddress(),
            'rent_per_sqft' => $this->area_sqft > 0 ? round($this->market_rent / $this->area_sqft, 2) : null,
        ];
    }

    /**
     * Get the full formatted address of the unit.
     */
    private function getFullAddress(): string
    {
        $parts = array_filter([
            $this->unit_number,
            $this->block ? "Block {$this->block}" : null,
            $this->floor ? "Floor {$this->floor}" : null,
        ]);
        
        return implode(', ', $parts);
    }

    /**
     * Additional data to include with the resource.
     */
    public function with(Request $request): array
    {
        return [
            'meta' => [
                'status_options' => [
                    'occupied' => 'Occupied - Owner residing',
                    'vacant' => 'Vacant - Empty unit',
                    'to-let' => 'To-Let - Available for rent',
                    'rented' => 'Rented - Currently rented',
                ],
                'unit_type_options' => [
                    'apartment' => '1BHK/2BHK/3BHK Apartment',
                    'villa' => 'Independent Villa',
                    'studio' => 'Studio Apartment',
                    'penthouse' => 'Penthouse',
                ],
            ],
        ];
    }
}
