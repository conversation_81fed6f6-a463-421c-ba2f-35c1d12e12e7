<?php

namespace App\Http\Resources;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class EnquiryResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        return [
            'id' => $this->id,
            'unit_id' => $this->unit_id,
            'enquirer_id' => $this->enquirer_id,
            'owner_id' => $this->owner_id,
            'type' => $this->type,
            'subject' => $this->subject,
            'message' => $this->message,
            'status' => $this->status,
            'priority' => $this->priority,
            'contact_preference' => $this->contact_preference,
            'preferred_contact_time' => $this->preferred_contact_time,
            'budget_range' => $this->budget_range,
            'move_in_date' => $this->move_in_date?->toDateString(),
            'additional_requirements' => $this->additional_requirements,
            'assigned_to' => $this->assigned_to,
            'resolution_notes' => $this->when(
                $request->user()?->hasRole(['admin', 'manager', 'staff']) || 
                $request->user()?->id === $this->enquirer_id ||
                $request->user()?->id === $this->owner_id,
                $this->resolution_notes
            ),
            'internal_notes' => $this->when(
                $request->user()?->hasRole(['admin', 'manager', 'staff']),
                $this->internal_notes
            ),
            'estimated_resolution_date' => $this->estimated_resolution_date?->toDateString(),
            'follow_up_required' => $this->follow_up_required,
            'follow_up_date' => $this->follow_up_date?->toDateString(),
            'tags' => $this->tags ?? [],
            'attachments' => $this->attachments ?? [],
            'communication_history' => $this->communication_history ?? [],
            'resolved_at' => $this->resolved_at?->toISOString(),
            'closed_at' => $this->closed_at?->toISOString(),
            'created_at' => $this->created_at->toISOString(),
            'updated_at' => $this->updated_at->toISOString(),
            
            // Relationships
            'unit' => new UnitResource($this->whenLoaded('unit')),
            'enquirer' => new UserResource($this->whenLoaded('enquirer')),
            'owner' => new UserResource($this->whenLoaded('owner')),
            'assigned_user' => new UserResource($this->whenLoaded('assignedUser')),
            
            // Computed fields
            'is_overdue' => $this->getIsOverdue(),
            'response_time' => $this->getResponseTime(),
            'days_open' => $this->created_at->diffInDays(now()),
            'urgency_score' => $this->getUrgencyScore(),
            'can_be_assigned' => $this->status === 'open',
            'can_be_resolved' => in_array($this->status, ['open', 'in_progress']),
            'requires_follow_up' => $this->follow_up_required && 
                $this->follow_up_date && 
                $this->follow_up_date->isPast(),
        ];
    }

    /**
     * Check if enquiry is overdue.
     */
    private function getIsOverdue(): bool
    {
        if (!$this->estimated_resolution_date) {
            return false;
        }

        return $this->estimated_resolution_date->isPast() && 
               !in_array($this->status, ['resolved', 'closed', 'cancelled']);
    }

    /**
     * Calculate response time.
     */
    private function getResponseTime(): ?array
    {
        if (empty($this->communication_history)) {
            return null;
        }

        $firstResponse = collect($this->communication_history)
            ->where('type', 'response')
            ->sortBy('timestamp')
            ->first();

        if (!$firstResponse) {
            return null;
        }

        $responseTime = $this->created_at->diffInMinutes($firstResponse['timestamp']);
        
        return [
            'minutes' => $responseTime,
            'hours' => round($responseTime / 60, 2),
            'formatted' => $this->formatResponseTime($responseTime),
        ];
    }

    /**
     * Calculate urgency score based on priority, age, and type.
     */
    private function getUrgencyScore(): int
    {
        $score = 0;
        
        // Priority scoring
        $priorityScores = [
            'low' => 1,
            'medium' => 2,
            'high' => 3,
            'urgent' => 4,
        ];
        $score += $priorityScores[$this->priority] ?? 1;
        
        // Age scoring (older enquiries get higher scores)
        $daysOpen = $this->created_at->diffInDays(now());
        if ($daysOpen > 7) $score += 2;
        elseif ($daysOpen > 3) $score += 1;
        
        // Type scoring
        $typeScores = [
            'complaint' => 2,
            'maintenance_request' => 2,
            'rental_inquiry' => 1,
            'purchase_inquiry' => 1,
            'general_inquiry' => 0,
        ];
        $score += $typeScores[$this->type] ?? 0;
        
        return min($score, 10); // Cap at 10
    }

    /**
     * Format response time in human-readable format.
     */
    private function formatResponseTime(int $minutes): string
    {
        if ($minutes < 60) {
            return "{$minutes} minutes";
        } elseif ($minutes < 1440) {
            $hours = round($minutes / 60, 1);
            return "{$hours} hours";
        } else {
            $days = round($minutes / 1440, 1);
            return "{$days} days";
        }
    }

    /**
     * Additional data to include with the resource.
     */
    public function with(Request $request): array
    {
        return [
            'meta' => [
                'type_options' => [
                    'rental_inquiry' => 'Rental Inquiry',
                    'purchase_inquiry' => 'Purchase Inquiry',
                    'general_inquiry' => 'General Inquiry',
                    'complaint' => 'Complaint',
                    'maintenance_request' => 'Maintenance Request',
                ],
                'status_options' => [
                    'open' => 'Open - Awaiting response',
                    'in_progress' => 'In Progress - Being handled',
                    'resolved' => 'Resolved - Solution provided',
                    'closed' => 'Closed - Completed',
                    'cancelled' => 'Cancelled - No longer relevant',
                ],
                'priority_options' => [
                    'low' => 'Low Priority',
                    'medium' => 'Medium Priority',
                    'high' => 'High Priority',
                    'urgent' => 'Urgent',
                ],
                'contact_preference_options' => [
                    'email' => 'Email',
                    'phone' => 'Phone',
                    'both' => 'Email and Phone',
                ],
            ],
        ];
    }
}
