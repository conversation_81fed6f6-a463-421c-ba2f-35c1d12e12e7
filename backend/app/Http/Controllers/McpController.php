<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use App\Mcp\TmsTools;
use App\Mcp\UnitTool;
use App\Mcp\TenantTool;
use App\Mcp\AgreementTool;
use App\Mcp\PaymentTool;
use App\Mcp\EnquiryTool;
use App\Mcp\McpToolRegistry;

class McpController extends Controller
{
    private array $tools;

    public function __construct()
    {
        $this->tools = [
            'tms' => new TmsTools(),
            'unit' => new UnitTool(),
            'tenant' => new TenantTool(),
            'agreement' => new AgreementTool(),
            'payment' => new PaymentTool(),
            'enquiry' => new EnquiryTool(),
            'registry' => new McpToolRegistry(),
        ];
    }

    /**
     * List all available MCP tools
     */
    public function tools(): JsonResponse
    {
        try {
            $registry = new McpToolRegistry();
            $result = $registry->listAllMcpTools();
            
            return response()->json([
                'success' => true,
                'data' => $result,
                'endpoint' => 'tools',
                'timestamp' => now()->toISOString()
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to list MCP tools',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Execute an MCP tool
     */
    public function execute(Request $request, ?string $toolName = null): JsonResponse
    {
        try {
            // Get tool name from route parameter or request body
            $tool = $toolName ?: $request->input('tool');
            $parameters = $request->input('parameters', []);

            if (!$tool) {
                return response()->json([
                    'success' => false,
                    'message' => 'Tool name is required'
                ], 400);
            }

            $result = $this->callTool($tool, $parameters);

            return response()->json($result);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to execute MCP tool',
                'error' => $e->getMessage(),
                'tool' => $toolName ?: $request->input('tool'),
                'parameters' => $request->input('parameters', [])
            ], 500);
        }
    }

    /**
     * Health check endpoint
     */
    public function health(): JsonResponse
    {
        try {
            $tmsTools = new TmsTools();
            $healthResult = $tmsTools->healthCheck();

            return response()->json([
                'success' => true,
                'health' => $healthResult,
                'mcp_status' => 'operational',
                'timestamp' => now()->toISOString()
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Health check failed',
                'error' => $e->getMessage(),
                'mcp_status' => 'error'
            ], 500);
        }
    }

    /**
     * Get system dashboard
     */
    public function dashboard(): JsonResponse
    {
        try {
            $tmsTools = new TmsTools();
            $dashboardResult = $tmsTools->getSystemDashboard();

            return response()->json([
                'success' => true,
                'dashboard' => $dashboardResult,
                'timestamp' => now()->toISOString()
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to get dashboard data',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Call a specific MCP tool
     */
    private function callTool(string $toolName, array $parameters): array
    {
        // Map tool names to methods
        $toolMap = [
            // System Tools
            'health_check' => ['tms', 'healthCheck'],
            'get_system_info' => ['tms', 'getSystemInfo'],
            'get_system_dashboard' => ['tms', 'getSystemDashboard'],
            'get_system_performance' => ['tms', 'getSystemPerformance'],
            'run_maintenance_tasks' => ['tms', 'runMaintenanceTasks'],

            // Unit Tools
            'get_unit_info' => ['unit', 'getUnitInfo'],
            'list_units' => ['unit', 'listUnits'],
            'create_unit' => ['unit', 'createUnit'],
            'update_unit_status' => ['unit', 'updateUnitStatus'],
            'get_unit_statistics' => ['unit', 'getUnitStatistics'],

            // Tenant Tools
            'get_tenant_info' => ['tenant', 'getTenantInfo'],
            'list_tenants' => ['tenant', 'listTenants'],
            'create_tenant' => ['tenant', 'createTenant'],
            'update_tenant_kyc' => ['tenant', 'updateTenantKyc'],

            // Agreement Tools
            'get_agreement_info' => ['agreement', 'getAgreementInfo'],
            'list_agreements' => ['agreement', 'listAgreements'],
            'create_agreement' => ['agreement', 'createAgreement'],
            'update_agreement_status' => ['agreement', 'updateAgreementStatus'],
            'get_expiring_agreements' => ['agreement', 'getExpiringAgreements'],

            // Payment Tools
            'get_payment_info' => ['payment', 'getPaymentInfo'],
            'list_payments' => ['payment', 'listPayments'],
            'record_payment' => ['payment', 'recordPayment'],
            'get_payment_statistics' => ['payment', 'getPaymentStatistics'],
            'get_overdue_payments' => ['payment', 'getOverduePayments'],

            // Enquiry Tools
            'get_enquiry_info' => ['enquiry', 'getEnquiryInfo'],
            'list_enquiries' => ['enquiry', 'listEnquiries'],
            'create_enquiry' => ['enquiry', 'createEnquiry'],
            'update_enquiry_status' => ['enquiry', 'updateEnquiryStatus'],
            'get_enquiry_statistics' => ['enquiry', 'getEnquiryStatistics'],
            'get_available_units_for_enquiry' => ['enquiry', 'getAvailableUnitsForEnquiry'],

            // Registry Tools
            'list_all_mcp_tools' => ['registry', 'listAllMcpTools'],
            'get_tool_recommendations' => ['registry', 'getToolRecommendations'],
            'get_workflow_examples' => ['registry', 'getWorkflowExamples'],
        ];

        if (!isset($toolMap[$toolName])) {
            throw new \Exception("Unknown tool: {$toolName}");
        }

        [$toolClass, $method] = $toolMap[$toolName];
        
        if (!isset($this->tools[$toolClass])) {
            throw new \Exception("Tool class not found: {$toolClass}");
        }

        $tool = $this->tools[$toolClass];
        
        if (!method_exists($tool, $method)) {
            throw new \Exception("Method {$method} not found in tool {$toolClass}");
        }

        // Call the method with parameters
        if (empty($parameters)) {
            return $tool->$method();
        } else {
            // Use reflection to call method with named parameters
            $reflection = new \ReflectionMethod($tool, $method);
            $methodParams = $reflection->getParameters();
            
            $args = [];
            foreach ($methodParams as $param) {
                $paramName = $param->getName();
                if (isset($parameters[$paramName])) {
                    $args[] = $parameters[$paramName];
                } elseif ($param->isDefaultValueAvailable()) {
                    $args[] = $param->getDefaultValue();
                } elseif (!$param->isOptional()) {
                    throw new \Exception("Required parameter missing: {$paramName}");
                }
            }
            
            return $tool->$method(...$args);
        }
    }

    /**
     * Get tool information
     */
    public function toolInfo(string $toolName): JsonResponse
    {
        try {
            $registry = new McpToolRegistry();
            $allTools = $registry->listAllMcpTools();
            
            // Find the specific tool
            $toolInfo = null;
            foreach ($allTools['data'] as $category => $categoryData) {
                if (isset($categoryData['tools']) && isset($categoryData['tools'][$toolName])) {
                    $toolInfo = [
                        'name' => $toolName,
                        'description' => $categoryData['tools'][$toolName],
                        'category' => $category,
                        'class' => $categoryData['class'] ?? 'Unknown'
                    ];
                    break;
                }
            }

            if (!$toolInfo) {
                return response()->json([
                    'success' => false,
                    'message' => "Tool not found: {$toolName}"
                ], 404);
            }

            return response()->json([
                'success' => true,
                'tool' => $toolInfo,
                'timestamp' => now()->toISOString()
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to get tool information',
                'error' => $e->getMessage()
            ], 500);
        }
    }
}
