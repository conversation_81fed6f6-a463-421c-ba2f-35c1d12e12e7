<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Services\SyncService;
use App\Models\SyncAuditLog;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;

class SyncController extends Controller
{
    protected SyncService $syncService;

    public function __construct(SyncService $syncService)
    {
        $this->syncService = $syncService;
    }

    /**
     * Get sync dashboard data.
     */
    public function dashboard(Request $request): JsonResponse
    {
        $hours = $request->get('hours', 24);
        
        try {
            $stats = $this->syncService->getSyncStatistics($hours);
            
            return response()->json([
                'success' => true,
                'data' => $stats,
            ]);
            
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to load dashboard data',
                'error' => $e->getMessage(),
            ], 500);
        }
    }

    /**
     * Get sync audit logs with filtering.
     */
    public function logs(Request $request): JsonResponse
    {
        $request->validate([
            'page' => 'integer|min:1',
            'per_page' => 'integer|min:1|max:100',
            'entity_type' => 'string|in:Society,Unit,Tenant,Agreement,RentPayment',
            'status' => 'string|in:pending,processing,success,failed,skipped,conflict',
            'operation' => 'string|in:created,updated,deleted',
            'source_system' => 'string|in:tms,onesociety',
            'date_from' => 'date',
            'date_to' => 'date',
        ]);

        try {
            $query = SyncAuditLog::query();

            // Apply filters
            if ($request->filled('entity_type')) {
                $query->where('entity_type', $request->entity_type);
            }

            if ($request->filled('status')) {
                $query->where('status', $request->status);
            }

            if ($request->filled('operation')) {
                $query->where('operation', $request->operation);
            }

            if ($request->filled('source_system')) {
                $query->where('source_system', $request->source_system);
            }

            if ($request->filled('date_from')) {
                $query->whereDate('created_at', '>=', $request->date_from);
            }

            if ($request->filled('date_to')) {
                $query->whereDate('created_at', '<=', $request->date_to);
            }

            // Order by most recent first
            $query->orderBy('created_at', 'desc');

            // Paginate
            $perPage = $request->get('per_page', 20);
            $logs = $query->paginate($perPage);

            return response()->json([
                'success' => true,
                'data' => $logs,
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to load sync logs',
                'error' => $e->getMessage(),
            ], 500);
        }
    }

    /**
     * Get specific sync log details.
     */
    public function logDetails(int $id): JsonResponse
    {
        try {
            $log = SyncAuditLog::findOrFail($id);
            
            return response()->json([
                'success' => true,
                'data' => $log,
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Sync log not found',
                'error' => $e->getMessage(),
            ], 404);
        }
    }

    /**
     * Retry failed sync operations.
     */
    public function retryFailed(Request $request): JsonResponse
    {
        $request->validate([
            'max_retries' => 'integer|min:1|max:10',
            'limit' => 'integer|min:1|max:1000',
        ]);

        try {
            $maxRetries = $request->get('max_retries', 3);
            $results = $this->syncService->retryFailedSyncs($maxRetries);
            
            $successful = collect($results)->where('success', true)->count();
            $failed = collect($results)->where('success', false)->count();
            
            return response()->json([
                'success' => true,
                'message' => "Retry completed: {$successful} successful, {$failed} failed",
                'data' => [
                    'total_processed' => count($results),
                    'successful' => $successful,
                    'failed' => $failed,
                    'results' => $results,
                ],
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to retry sync operations',
                'error' => $e->getMessage(),
            ], 500);
        }
    }

    /**
     * Retry specific sync log.
     */
    public function retryLog(int $id): JsonResponse
    {
        try {
            $log = SyncAuditLog::findOrFail($id);
            
            if (!$log->canRetry()) {
                return response()->json([
                    'success' => false,
                    'message' => 'This sync operation cannot be retried',
                ], 400);
            }

            // Create a new sync event based on the log
            $entityClass = $this->getEntityClass($log->entity_type);
            if (!$entityClass) {
                return response()->json([
                    'success' => false,
                    'message' => 'Unknown entity type',
                ], 400);
            }

            $entity = $entityClass::find($log->entity_id);
            if (!$entity) {
                return response()->json([
                    'success' => false,
                    'message' => 'Entity not found',
                ], 404);
            }

            $success = $this->syncService->syncEntity($entity, $log->operation, $log->correlation_id);
            
            if ($success) {
                return response()->json([
                    'success' => true,
                    'message' => 'Sync retry triggered successfully',
                ]);
            } else {
                return response()->json([
                    'success' => false,
                    'message' => 'Failed to trigger sync retry',
                ], 500);
            }

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to retry sync log',
                'error' => $e->getMessage(),
            ], 500);
        }
    }

    /**
     * Get sync configuration.
     */
    public function config(): JsonResponse
    {
        try {
            $config = [
                'enabled' => config('sync.enabled'),
                'source_system' => config('sync.source_system'),
                'batch_size' => config('sync.batch_size'),
                'retry_attempts' => config('sync.retry_attempts'),
                'retry_delay' => config('sync.retry_delay'),
                'timeout' => config('sync.timeout'),
                'entities' => config('sync.entities'),
                'conflict_resolution' => config('sync.conflict_resolution'),
                'security' => config('sync.security'),
                'monitoring' => config('sync.monitoring'),
            ];
            
            return response()->json([
                'success' => true,
                'data' => $config,
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to load sync configuration',
                'error' => $e->getMessage(),
            ], 500);
        }
    }

    /**
     * Get entity class by type.
     */
    protected function getEntityClass(string $entityType): ?string
    {
        $entityMap = [
            'Society' => \App\Models\Society::class,
            'Unit' => \App\Models\Unit::class,
            'Tenant' => \App\Models\Tenant::class,
            'Agreement' => \App\Models\Agreement::class,
            'RentPayment' => \App\Models\RentPayment::class,
        ];

        return $entityMap[$entityType] ?? null;
    }
}
