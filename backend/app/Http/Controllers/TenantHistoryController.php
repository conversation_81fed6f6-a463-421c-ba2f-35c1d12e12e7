<?php

namespace App\Http\Controllers;

use App\Models\Tenant;
use App\Models\TenantHistory;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\Validation\Rule;
use Carbon\Carbon;

class TenantHistoryController extends Controller
{
    /**
     * Get history for a specific tenant
     */
    public function getTenantHistory(Request $request, Tenant $tenant): JsonResponse
    {
        try {
            $request->validate([
                'activity_type' => 'sometimes|string|max:100',
                'severity' => ['sometimes', Rule::in(['low', 'medium', 'high'])],
                'start_date' => 'sometimes|date',
                'end_date' => 'sometimes|date|after_or_equal:start_date',
                'limit' => 'sometimes|integer|min:1|max:100',
                'page' => 'sometimes|integer|min:1',
            ]);

            $query = $tenant->history()->with(['user:id,name,email']);

            // Apply filters
            if ($request->filled('activity_type')) {
                $query->ofType($request->activity_type);
            }

            if ($request->filled('severity')) {
                $query->bySeverity($request->severity);
            }

            if ($request->filled('start_date') && $request->filled('end_date')) {
                $query->dateRange($request->start_date, $request->end_date);
            }

            // Pagination
            $limit = $request->input('limit', 20);
            $history = $query->paginate($limit);

            // Transform data for response
            $transformedHistory = $history->getCollection()->map(function ($item) {
                return [
                    'id' => $item->id,
                    'activity_type' => $item->activity_type,
                    'activity_type_display' => $item->activity_type_display,
                    'activity_description' => $item->activity_description,
                    'formatted_description' => $item->formatted_description,
                    'severity' => $item->severity,
                    'is_critical' => $item->is_critical,
                    'metadata' => $item->metadata,
                    'old_values' => $item->old_values,
                    'new_values' => $item->new_values,
                    'performed_at' => $item->performed_at->toISOString(),
                    'time_ago' => $item->time_ago,
                    'user' => $item->user ? [
                        'id' => $item->user->id,
                        'name' => $item->user->name,
                        'email' => $item->user->email,
                    ] : null,
                    'ip_address' => $item->ip_address,
                ];
            });

            return response()->json([
                'success' => true,
                'data' => [
                    'tenant' => [
                        'id' => $tenant->id,
                        'tenant_code' => $tenant->tenant_code,
                        'full_name' => $tenant->full_name,
                    ],
                    'history' => $transformedHistory,
                    'pagination' => [
                        'current_page' => $history->currentPage(),
                        'last_page' => $history->lastPage(),
                        'per_page' => $history->perPage(),
                        'total' => $history->total(),
                        'from' => $history->firstItem(),
                        'to' => $history->lastItem(),
                    ],
                ],
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Error fetching tenant history',
                'error' => $e->getMessage(),
            ], 500);
        }
    }

    /**
     * Get activity timeline for a tenant (grouped by date)
     */
    public function getTenantTimeline(Request $request, Tenant $tenant): JsonResponse
    {
        try {
            $request->validate([
                'days' => 'sometimes|integer|min:1|max:365',
            ]);

            $days = $request->input('days', 30);
            
            $history = $tenant->history()
                ->with(['user:id,name'])
                ->recent($days)
                ->get()
                ->groupBy(function ($item) {
                    return $item->performed_at->format('Y-m-d');
                })
                ->map(function ($dayActivities) {
                    return $dayActivities->map(function ($activity) {
                        return [
                            'id' => $activity->id,
                            'activity_type' => $activity->activity_type,
                            'activity_type_display' => $activity->activity_type_display,
                            'activity_description' => $activity->activity_description,
                            'severity' => $activity->severity,
                            'performed_at' => $activity->performed_at->format('H:i:s'),
                            'user' => $activity->user ? $activity->user->name : 'System',
                        ];
                    });
                });

            return response()->json([
                'success' => true,
                'data' => [
                    'tenant' => [
                        'id' => $tenant->id,
                        'tenant_code' => $tenant->tenant_code,
                        'full_name' => $tenant->full_name,
                    ],
                    'timeline' => $history,
                    'period' => "{$days} days",
                ],
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Error fetching tenant timeline',
                'error' => $e->getMessage(),
            ], 500);
        }
    }

    /**
     * Get activity statistics for a tenant
     */
    public function getTenantStatistics(Request $request, Tenant $tenant): JsonResponse
    {
        try {
            $request->validate([
                'days' => 'sometimes|integer|min:1|max:365',
            ]);

            $days = $request->input('days', 30);
            $startDate = Carbon::now()->subDays($days);

            $baseQuery = $tenant->history()->where('performed_at', '>=', $startDate);

            // Total activities
            $totalActivities = $baseQuery->count();

            // Activities by type
            $activitiesByType = $baseQuery->select('activity_type')
                ->selectRaw('COUNT(*) as count')
                ->groupBy('activity_type')
                ->get()
                ->mapWithKeys(function ($item) {
                    return [$item->activity_type => [
                        'count' => $item->count,
                        'display_name' => TenantHistory::ACTIVITY_TYPES[$item->activity_type] ?? 
                                       ucwords(str_replace('_', ' ', $item->activity_type)),
                    ]];
                });

            // Activities by severity
            $activitiesBySeverity = $baseQuery->select('severity')
                ->selectRaw('COUNT(*) as count')
                ->groupBy('severity')
                ->get()
                ->mapWithKeys(function ($item) {
                    return [$item->severity => $item->count];
                });

            // Activities by day (last 7 days)
            $activitiesByDay = $baseQuery->where('performed_at', '>=', Carbon::now()->subDays(7))
                ->selectRaw('DATE(performed_at) as date, COUNT(*) as count')
                ->groupBy('date')
                ->orderBy('date')
                ->get()
                ->mapWithKeys(function ($item) {
                    return [Carbon::parse($item->date)->format('M d') => $item->count];
                });

            // Recent critical activities
            $criticalActivities = $tenant->history()
                ->where('severity', 'high')
                ->where('performed_at', '>=', $startDate)
                ->with(['user:id,name'])
                ->orderBy('performed_at', 'desc')
                ->limit(5)
                ->get()
                ->map(function ($activity) {
                    return [
                        'id' => $activity->id,
                        'activity_type_display' => $activity->activity_type_display,
                        'activity_description' => $activity->activity_description,
                        'performed_at' => $activity->performed_at->toISOString(),
                        'time_ago' => $activity->time_ago,
                        'user' => $activity->user ? $activity->user->name : 'System',
                    ];
                });

            return response()->json([
                'success' => true,
                'data' => [
                    'tenant' => [
                        'id' => $tenant->id,
                        'tenant_code' => $tenant->tenant_code,
                        'full_name' => $tenant->full_name,
                    ],
                    'statistics' => [
                        'total_activities' => $totalActivities,
                        'period_days' => $days,
                        'activities_by_type' => $activitiesByType,
                        'activities_by_severity' => $activitiesBySeverity,
                        'activities_by_day' => $activitiesByDay,
                        'recent_critical_activities' => $criticalActivities,
                    ],
                ],
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Error fetching tenant statistics',
                'error' => $e->getMessage(),
            ], 500);
        }
    }

    /**
     * Get system-wide activity statistics
     */
    public function getSystemStatistics(Request $request): JsonResponse
    {
        try {
            $request->validate([
                'days' => 'sometimes|integer|min:1|max:365',
            ]);

            $days = $request->input('days', 30);
            $startDate = Carbon::now()->subDays($days);

            $baseQuery = TenantHistory::where('performed_at', '>=', $startDate);

            // Total activities
            $totalActivities = $baseQuery->count();

            // Most active tenants
            $mostActiveTenants = $baseQuery->select('tenant_id')
                ->selectRaw('COUNT(*) as activity_count')
                ->with(['tenant:id,tenant_code,user_id', 'tenant.user:id,name'])
                ->groupBy('tenant_id')
                ->orderBy('activity_count', 'desc')
                ->limit(10)
                ->get()
                ->map(function ($item) {
                    return [
                        'tenant_id' => $item->tenant_id,
                        'tenant_code' => $item->tenant->tenant_code ?? 'N/A',
                        'tenant_name' => $item->tenant->user->name ?? 'N/A',
                        'activity_count' => $item->activity_count,
                    ];
                });

            // Activities by type (system-wide)
            $activitiesByType = $baseQuery->select('activity_type')
                ->selectRaw('COUNT(*) as count')
                ->groupBy('activity_type')
                ->orderBy('count', 'desc')
                ->get()
                ->map(function ($item) {
                    return [
                        'activity_type' => $item->activity_type,
                        'display_name' => TenantHistory::ACTIVITY_TYPES[$item->activity_type] ?? 
                                       ucwords(str_replace('_', ' ', $item->activity_type)),
                        'count' => $item->count,
                    ];
                });

            // Activities by severity
            $activitiesBySeverity = $baseQuery->select('severity')
                ->selectRaw('COUNT(*) as count')
                ->groupBy('severity')
                ->get()
                ->mapWithKeys(function ($item) {
                    return [$item->severity => $item->count];
                });

            return response()->json([
                'success' => true,
                'data' => [
                    'statistics' => [
                        'total_activities' => $totalActivities,
                        'period_days' => $days,
                        'most_active_tenants' => $mostActiveTenants,
                        'activities_by_type' => $activitiesByType,
                        'activities_by_severity' => $activitiesBySeverity,
                    ],
                ],
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Error fetching system statistics',
                'error' => $e->getMessage(),
            ], 500);
        }
    }

    /**
     * Get available activity types
     */
    public function getActivityTypes(): JsonResponse
    {
        try {
            $activityTypes = collect(TenantHistory::ACTIVITY_TYPES)->map(function ($displayName, $key) {
                return [
                    'value' => $key,
                    'label' => $displayName,
                ];
            })->values();

            return response()->json([
                'success' => true,
                'data' => [
                    'activity_types' => $activityTypes,
                ],
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Error fetching activity types',
                'error' => $e->getMessage(),
            ], 500);
        }
    }
}
