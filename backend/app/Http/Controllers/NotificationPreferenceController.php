<?php

namespace App\Http\Controllers;

use App\Models\NotificationPreference;
use App\Models\User;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\Auth;
use Illuminate\Validation\Rule;

class NotificationPreferenceController extends Controller
{
    /**
     * Get notification preferences for the authenticated user
     */
    public function index(Request $request): JsonResponse
    {
        $user = Auth::user();
        
        $preferences = NotificationPreference::forUser($user->id)
            ->when($request->get('type'), function ($query, $type) {
                return $query->byType($type);
            })
            ->when($request->get('channel'), function ($query, $channel) {
                return $query->byChannel($channel);
            })
            ->when($request->get('enabled_only'), function ($query) {
                return $query->enabled();
            })
            ->get();

        return response()->json([
            'success' => true,
            'message' => 'Notification preferences retrieved successfully',
            'data' => $preferences
        ]);
    }

    /**
     * Store a new notification preference
     */
    public function store(Request $request): JsonResponse
    {
        $user = Auth::user();
        
        $validated = $request->validate([
            'notification_type' => ['required', Rule::in(NotificationPreference::getTypes())],
            'channel' => ['required', Rule::in(NotificationPreference::getChannels())],
            'enabled' => 'boolean',
            'settings' => 'nullable|array',
            'frequency' => ['nullable', Rule::in(NotificationPreference::getFrequencies())],
            'quiet_hours_start' => 'nullable|date_format:H:i',
            'quiet_hours_end' => 'nullable|date_format:H:i',
            'metadata' => 'nullable|array',
        ]);

        // Check if preference already exists
        $existingPreference = NotificationPreference::forUser($user->id)
            ->byType($validated['notification_type'])
            ->byChannel($validated['channel'])
            ->first();

        if ($existingPreference) {
            return response()->json([
                'success' => false,
                'message' => 'Notification preference already exists for this type and channel',
                'data' => $existingPreference
            ], 422);
        }

        $preference = NotificationPreference::create([
            'user_id' => $user->id,
            ...$validated,
        ]);

        return response()->json([
            'success' => true,
            'message' => 'Notification preference created successfully',
            'data' => $preference
        ], 201);
    }

    /**
     * Update a notification preference
     */
    public function update(Request $request, NotificationPreference $preference): JsonResponse
    {
        $user = Auth::user();
        
        // Ensure user owns this preference
        if ($preference->user_id !== $user->id) {
            return response()->json([
                'success' => false,
                'message' => 'Unauthorized to update this preference'
            ], 403);
        }

        $validated = $request->validate([
            'enabled' => 'boolean',
            'settings' => 'nullable|array',
            'frequency' => ['nullable', Rule::in(NotificationPreference::getFrequencies())],
            'quiet_hours_start' => 'nullable|date_format:H:i',
            'quiet_hours_end' => 'nullable|date_format:H:i',
            'metadata' => 'nullable|array',
        ]);

        $preference->update($validated);

        return response()->json([
            'success' => true,
            'message' => 'Notification preference updated successfully',
            'data' => $preference->fresh()
        ]);
    }

    /**
     * Delete a notification preference
     */
    public function destroy(NotificationPreference $preference): JsonResponse
    {
        $user = Auth::user();
        
        // Ensure user owns this preference
        if ($preference->user_id !== $user->id) {
            return response()->json([
                'success' => false,
                'message' => 'Unauthorized to delete this preference'
            ], 403);
        }

        $preference->delete();

        return response()->json([
            'success' => true,
            'message' => 'Notification preference deleted successfully'
        ]);
    }

    /**
     * Bulk update notification preferences
     */
    public function bulkUpdate(Request $request): JsonResponse
    {
        $user = Auth::user();
        
        $validated = $request->validate([
            'preferences' => 'required|array',
            'preferences.*.notification_type' => ['required', Rule::in(NotificationPreference::getTypes())],
            'preferences.*.channel' => ['required', Rule::in(NotificationPreference::getChannels())],
            'preferences.*.enabled' => 'boolean',
            'preferences.*.settings' => 'nullable|array',
            'preferences.*.frequency' => ['nullable', Rule::in(NotificationPreference::getFrequencies())],
            'preferences.*.quiet_hours_start' => 'nullable|date_format:H:i',
            'preferences.*.quiet_hours_end' => 'nullable|date_format:H:i',
            'preferences.*.metadata' => 'nullable|array',
        ]);

        $updatedPreferences = [];
        $errors = [];

        foreach ($validated['preferences'] as $prefData) {
            $preference = NotificationPreference::forUser($user->id)
                ->byType($prefData['notification_type'])
                ->byChannel($prefData['channel'])
                ->first();

            if ($preference) {
                $preference->update($prefData);
                $updatedPreferences[] = $preference->fresh();
            } else {
                // Create new preference if it doesn't exist
                $newPreference = NotificationPreference::create([
                    'user_id' => $user->id,
                    ...$prefData,
                ]);
                $updatedPreferences[] = $newPreference;
            }
        }

        return response()->json([
            'success' => true,
            'message' => 'Notification preferences updated successfully',
            'data' => $updatedPreferences,
            'errors' => $errors
        ]);
    }

    /**
     * Get available notification types and channels
     */
    public function getOptions(): JsonResponse
    {
        return response()->json([
            'success' => true,
            'data' => [
                'types' => NotificationPreference::getTypes(),
                'channels' => NotificationPreference::getChannels(),
                'frequencies' => NotificationPreference::getFrequencies(),
            ],
            'message' => 'Notification options retrieved successfully'
        ]);
    }

    /**
     * Get default preferences for a user
     */
    public function getDefaults(): JsonResponse
    {
        $user = Auth::user();
        
        $defaultPreferences = [];
        
        foreach (NotificationPreference::getTypes() as $type) {
            foreach (NotificationPreference::getChannels() as $channel) {
                $defaultPreferences[] = [
                    'notification_type' => $type,
                    'channel' => $channel,
                    'enabled' => $this->getDefaultEnabled($type, $channel),
                    'frequency' => NotificationPreference::FREQUENCY_IMMEDIATE,
                    'settings' => $this->getDefaultSettings($type, $channel),
                ];
            }
        }

        return response()->json([
            'success' => true,
            'data' => $defaultPreferences,
            'message' => 'Default notification preferences retrieved successfully'
        ]);
    }

    /**
     * Reset user preferences to defaults
     */
    public function resetToDefaults(): JsonResponse
    {
        $user = Auth::user();
        
        // Delete existing preferences
        NotificationPreference::forUser($user->id)->delete();
        
        // Create default preferences
        $defaultPreferences = [];
        
        foreach (NotificationPreference::getTypes() as $type) {
            foreach (NotificationPreference::getChannels() as $channel) {
                $defaultPreferences[] = NotificationPreference::create([
                    'user_id' => $user->id,
                    'notification_type' => $type,
                    'channel' => $channel,
                    'enabled' => $this->getDefaultEnabled($type, $channel),
                    'frequency' => NotificationPreference::FREQUENCY_IMMEDIATE,
                    'settings' => $this->getDefaultSettings($type, $channel),
                ]);
            }
        }

        return response()->json([
            'success' => true,
            'message' => 'Notification preferences reset to defaults successfully',
            'data' => $defaultPreferences
        ]);
    }

    /**
     * Get notification preference statistics
     */
    public function statistics(): JsonResponse
    {
        $user = Auth::user();
        
        $stats = [
            'total_preferences' => NotificationPreference::forUser($user->id)->count(),
            'enabled_preferences' => NotificationPreference::forUser($user->id)->enabled()->count(),
            'disabled_preferences' => NotificationPreference::forUser($user->id)->disabled()->count(),
            'by_type' => [],
            'by_channel' => [],
        ];

        // Statistics by type
        foreach (NotificationPreference::getTypes() as $type) {
            $stats['by_type'][$type] = [
                'total' => NotificationPreference::forUser($user->id)->byType($type)->count(),
                'enabled' => NotificationPreference::forUser($user->id)->byType($type)->enabled()->count(),
            ];
        }

        // Statistics by channel
        foreach (NotificationPreference::getChannels() as $channel) {
            $stats['by_channel'][$channel] = [
                'total' => NotificationPreference::forUser($user->id)->byChannel($channel)->count(),
                'enabled' => NotificationPreference::forUser($user->id)->byChannel($channel)->enabled()->count(),
            ];
        }

        return response()->json([
            'success' => true,
            'message' => 'Notification preference statistics retrieved successfully',
            'data' => $stats
        ]);
    }

    /**
     * Get default enabled status for a notification type and channel
     */
    private function getDefaultEnabled(string $type, string $channel): bool
    {
        // Default enabled combinations
        $defaultEnabled = [
            'system' => ['email', 'in_app'],
            'tenant' => ['email', 'in_app'],
            'unit' => ['email', 'in_app'],
            'enquiry' => ['email', 'in_app'],
            'noc' => ['email', 'in_app'],
            'agreement' => ['email', 'in_app'],
            'billing' => ['email', 'in_app'],
            'maintenance' => ['email', 'in_app'],
        ];

        return in_array($channel, $defaultEnabled[$type] ?? ['email', 'in_app']);
    }

    /**
     * Get default settings for a notification type and channel
     */
    private function getDefaultSettings(string $type, string $channel): array
    {
        $defaultSettings = [
            'email' => [
                'include_attachments' => true,
                'format' => 'html',
            ],
            'sms' => [
                'include_links' => false,
                'max_length' => 160,
            ],
            'push' => [
                'sound' => true,
                'vibration' => true,
                'badge' => true,
            ],
            'in_app' => [
                'show_toast' => true,
                'auto_dismiss' => true,
                'dismiss_after' => 5000,
            ],
        ];

        return $defaultSettings[$channel] ?? [];
    }
} 