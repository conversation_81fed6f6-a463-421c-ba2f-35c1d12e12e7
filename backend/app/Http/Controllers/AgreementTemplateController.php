<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use App\Models\AgreementTemplate;
use App\Models\User;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Facades\Gate;
use Illuminate\Validation\Rule;

class AgreementTemplateController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index()
    {
        $this->authorize('viewAny', AgreementTemplate::class);
        $templates = AgreementTemplate::with('creator')->get();
        return response()->json($templates);
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        $this->authorize('create', AgreementTemplate::class);
        $validated = $request->validate([
            'name' => 'required|string|max:255',
            'description' => 'nullable|string',
            'template_file' => 'required|file|mimes:pdf,doc,docx|max:2048',
            'placeholders' => 'nullable|array',
            'placeholders.*' => 'string',
        ]);

        $file = $request->file('template_file');
        $path = $file->store('agreements/templates');

        $template = AgreementTemplate::create([
            'name' => $validated['name'],
            'description' => $validated['description'] ?? null,
            'template_file' => $path,
            'placeholders' => $validated['placeholders'] ?? [],
            'created_by' => Auth::id(),
        ]);

        return response()->json($template, 201);
    }

    /**
     * Display the specified resource.
     */
    public function show(string $id)
    {
        $template = AgreementTemplate::with('creator')->findOrFail($id);
        $this->authorize('view', $template);
        return response()->json($template);
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, string $id)
    {
        $template = AgreementTemplate::findOrFail($id);
        $this->authorize('update', $template);
        $validated = $request->validate([
            'name' => 'sometimes|required|string|max:255',
            'description' => 'nullable|string',
            'template_file' => 'nullable|file|mimes:pdf,doc,docx|max:2048',
            'placeholders' => 'nullable|array',
            'placeholders.*' => 'string',
        ]);

        if ($request->hasFile('template_file')) {
            // Delete old file
            if ($template->template_file) {
                Storage::delete($template->template_file);
            }
            $file = $request->file('template_file');
            $path = $file->store('agreements/templates');
            $template->template_file = $path;
        }
        if (isset($validated['name'])) $template->name = $validated['name'];
        if (array_key_exists('description', $validated)) $template->description = $validated['description'];
        if (isset($validated['placeholders'])) $template->placeholders = $validated['placeholders'];
        $template->save();
        return response()->json($template);
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(string $id)
    {
        $template = AgreementTemplate::findOrFail($id);
        $this->authorize('delete', $template);
        if ($template->template_file) {
            Storage::delete($template->template_file);
        }
        $template->delete();
        return response()->json(['message' => 'Deleted'], 204);
    }
}
