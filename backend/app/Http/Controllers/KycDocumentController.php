<?php

namespace App\Http\Controllers;

use App\Models\Document;
use App\Models\Tenant;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Facades\Validator;
use Illuminate\Validation\Rule;
use Illuminate\Http\UploadedFile;

class KycDocumentController extends Controller
{
    /**
     * KYC document types and their requirements
     */
    const KYC_DOCUMENT_TYPES = [
        'id_proof' => [
            'label' => 'ID Proof',
            'description' => 'Government issued photo ID (Aadhar, PAN, Passport, Driving License)',
            'required' => true,
            'max_files' => 2,
            'allowed_types' => ['pdf', 'jpg', 'jpeg', 'png'],
            'max_size' => 5120, // 5MB
        ],
        'address_proof' => [
            'label' => 'Address Proof',
            'description' => 'Address verification document (Utility bill, Bank statement, Rental agreement)',
            'required' => true,
            'max_files' => 2,
            'allowed_types' => ['pdf', 'jpg', 'jpeg', 'png'],
            'max_size' => 5120, // 5MB
        ],
        'income_proof' => [
            'label' => 'Income Proof',
            'description' => 'Income verification (Salary slip, ITR, Bank statement)',
            'required' => false,
            'max_files' => 3,
            'allowed_types' => ['pdf', 'jpg', 'jpeg', 'png'],
            'max_size' => 5120, // 5MB
        ],
        'photo' => [
            'label' => 'Passport Size Photo',
            'description' => 'Recent passport size photograph',
            'required' => true,
            'max_files' => 1,
            'allowed_types' => ['jpg', 'jpeg', 'png'],
            'max_size' => 2048, // 2MB
        ],
        'bank_statement' => [
            'label' => 'Bank Statement',
            'description' => 'Recent bank statements (last 3 months)',
            'required' => false,
            'max_files' => 3,
            'allowed_types' => ['pdf'],
            'max_size' => 10240, // 10MB
        ],
        'salary_slip' => [
            'label' => 'Salary Slip',
            'description' => 'Recent salary slips (last 3 months)',
            'required' => false,
            'max_files' => 3,
            'allowed_types' => ['pdf', 'jpg', 'jpeg', 'png'],
            'max_size' => 5120, // 5MB
        ],
    ];

    /**
     * Get KYC document requirements and current status
     */
    public function getRequirements(Request $request): JsonResponse
    {
        $user = auth()->user();
        $tenant = $user->tenant;

        if (!$tenant) {
            return response()->json([
                'success' => false,
                'message' => 'Please complete onboarding first'
            ], 400);
        }

        // Get current documents
        $currentDocuments = $tenant->documents()
            ->whereIn('type', array_keys(self::KYC_DOCUMENT_TYPES))
            ->get()
            ->groupBy('type');

        $requirements = [];
        foreach (self::KYC_DOCUMENT_TYPES as $type => $config) {
            $uploaded = $currentDocuments->get($type, collect());
            
            $requirements[$type] = [
                'type' => $type,
                'label' => $config['label'],
                'description' => $config['description'],
                'required' => $config['required'],
                'max_files' => $config['max_files'],
                'allowed_types' => $config['allowed_types'],
                'max_size_mb' => $config['max_size'] / 1024,
                'uploaded_count' => $uploaded->count(),
                'can_upload_more' => $uploaded->count() < $config['max_files'],
                'status' => $this->getDocumentTypeStatus($uploaded),
                'documents' => $uploaded->map(function ($doc) {
                    return [
                        'id' => $doc->id,
                        'title' => $doc->title,
                        'file_name' => $doc->file_name,
                        'status' => $doc->status,
                        'uploaded_at' => $doc->created_at,
                        'verified_at' => $doc->verified_at,
                    ];
                }),
            ];
        }

        return response()->json([
            'success' => true,
            'data' => [
                'requirements' => $requirements,
                'overall_status' => $this->getOverallKycStatus($requirements),
                'completion_percentage' => $this->calculateKycCompletion($requirements),
            ],
            'message' => 'KYC requirements retrieved successfully'
        ]);
    }

    /**
     * Upload KYC document
     */
    public function uploadDocument(Request $request): JsonResponse
    {
        $validator = Validator::make($request->all(), [
            'type' => ['required', Rule::in(array_keys(self::KYC_DOCUMENT_TYPES))],
            'file' => 'required|file',
            'title' => 'nullable|string|max:200',
            'description' => 'nullable|string|max:500',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => 'Validation failed',
                'errors' => $validator->errors()
            ], 422);
        }

        $user = auth()->user();
        $tenant = $user->tenant;

        if (!$tenant) {
            return response()->json([
                'success' => false,
                'message' => 'Please complete onboarding first'
            ], 400);
        }

        $type = $request->type;
        $file = $request->file('file');
        $config = self::KYC_DOCUMENT_TYPES[$type];

        // Validate file type
        $fileExtension = strtolower($file->getClientOriginalExtension());
        if (!in_array($fileExtension, $config['allowed_types'])) {
            return response()->json([
                'success' => false,
                'message' => "Invalid file type. Allowed types: " . implode(', ', $config['allowed_types'])
            ], 422);
        }

        // Validate file size
        $fileSizeKB = $file->getSize() / 1024;
        if ($fileSizeKB > $config['max_size']) {
            return response()->json([
                'success' => false,
                'message' => "File size too large. Maximum allowed: " . ($config['max_size'] / 1024) . "MB"
            ], 422);
        }

        // Check if user can upload more files of this type
        $existingCount = $tenant->documents()->where('type', $type)->count();
        if ($existingCount >= $config['max_files']) {
            return response()->json([
                'success' => false,
                'message' => "Maximum number of files reached for this document type. Limit: " . $config['max_files']
            ], 422);
        }

        try {
            // Upload file
            $uploadResult = $this->uploadFile($file, $type);

            // Create document record
            $document = Document::create([
                'title' => $request->get('title', $config['label']),
                'description' => $request->get('description', $config['description']),
                'type' => $type,
                'category' => 'kyc',
                'documentable_type' => 'App\Models\Tenant',
                'documentable_id' => $tenant->id,
                'file_name' => $file->getClientOriginalName(),
                'file_path' => $uploadResult['path'],
                'file_type' => $fileExtension,
                'file_size' => $file->getSize(),
                'mime_type' => $file->getMimeType(),
                'file_hash' => hash_file('sha256', $file->getPathname()),
                'uploaded_by' => $user->id,
                'visibility' => Document::VISIBILITY_PRIVATE,
                'is_required' => $config['required'],
                'is_sensitive' => true, // KYC documents are always sensitive
            ]);

            return response()->json([
                'success' => true,
                'data' => [
                    'document' => $document,
                    'upload_info' => $uploadResult,
                ],
                'message' => 'KYC document uploaded successfully'
            ], 201);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to upload document',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Delete KYC document
     */
    public function deleteDocument(Request $request, Document $document): JsonResponse
    {
        $user = auth()->user();
        $tenant = $user->tenant;

        if (!$tenant) {
            return response()->json([
                'success' => false,
                'message' => 'Tenant record not found'
            ], 400);
        }

        // Check if document belongs to this tenant
        if ($document->documentable_type !== 'App\Models\Tenant' || 
            $document->documentable_id !== $tenant->id) {
            return response()->json([
                'success' => false,
                'message' => 'Document not found or access denied'
            ], 404);
        }

        // Check if document is not verified (can't delete verified documents)
        if ($document->status === Document::STATUS_VERIFIED) {
            return response()->json([
                'success' => false,
                'message' => 'Cannot delete verified documents. Please contact admin.'
            ], 403);
        }

        try {
            $document->delete();

            return response()->json([
                'success' => true,
                'message' => 'Document deleted successfully'
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to delete document',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Get tenant's KYC documents
     */
    public function getDocuments(Request $request): JsonResponse
    {
        $user = auth()->user();
        $tenant = $user->tenant;

        if (!$tenant) {
            return response()->json([
                'success' => false,
                'message' => 'Please complete onboarding first'
            ], 400);
        }

        $query = $tenant->documents()
            ->whereIn('type', array_keys(self::KYC_DOCUMENT_TYPES))
            ->with(['uploadedBy', 'verifiedBy']);

        // Filter by type if specified
        if ($request->filled('type')) {
            $query->where('type', $request->type);
        }

        // Filter by status if specified
        if ($request->filled('status')) {
            $query->where('status', $request->status);
        }

        $documents = $query->get();

        return response()->json([
            'success' => true,
            'data' => [
                'documents' => $documents,
                'summary' => [
                    'total' => $documents->count(),
                    'pending' => $documents->where('status', Document::STATUS_PENDING)->count(),
                    'verified' => $documents->where('status', Document::STATUS_VERIFIED)->count(),
                    'rejected' => $documents->where('status', Document::STATUS_REJECTED)->count(),
                ],
            ],
            'message' => 'Documents retrieved successfully'
        ]);
    }

    /**
     * Submit KYC for review
     */
    public function submitForReview(Request $request): JsonResponse
    {
        $user = auth()->user();
        $tenant = $user->tenant;

        if (!$tenant) {
            return response()->json([
                'success' => false,
                'message' => 'Please complete onboarding first'
            ], 400);
        }

        // Check if all required documents are uploaded
        $requirements = [];
        foreach (self::KYC_DOCUMENT_TYPES as $type => $config) {
            if ($config['required']) {
                $count = $tenant->documents()->where('type', $type)->count();
                if ($count === 0) {
                    $requirements[] = $config['label'];
                }
            }
        }

        if (!empty($requirements)) {
            return response()->json([
                'success' => false,
                'message' => 'Please upload all required documents before submitting',
                'data' => ['missing_documents' => $requirements]
            ], 400);
        }

        try {
            // Update tenant KYC status
            $tenant->update([
                'kyc_status' => Tenant::KYC_SUBMITTED,
                'kyc_submitted_at' => now(),
                'kyc_documents' => $tenant->documents()
                    ->whereIn('type', array_keys(self::KYC_DOCUMENT_TYPES))
                    ->pluck('id')
                    ->toArray(),
            ]);

            return response()->json([
                'success' => true,
                'data' => [
                    'tenant' => $tenant->fresh(),
                    'message' => 'KYC documents submitted for review successfully. You will be notified once the review is complete.'
                ],
                'message' => 'KYC submitted for review successfully'
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to submit KYC for review',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Upload file to storage
     */
    private function uploadFile(UploadedFile $file, string $type): array
    {
        $directory = "kyc-documents/{$type}/" . date('Y/m');
        $filename = time() . '_' . uniqid() . '_' . $file->getClientOriginalName();
        $path = $file->storeAs($directory, $filename, 'public');

        return [
            'path' => $path,
            'url' => Storage::url($path),
            'directory' => $directory,
            'filename' => $filename,
        ];
    }

    /**
     * Get document type status
     */
    private function getDocumentTypeStatus($documents): string
    {
        if ($documents->isEmpty()) {
            return 'not_uploaded';
        }

        $statuses = $documents->pluck('status')->unique();
        
        if ($statuses->contains(Document::STATUS_VERIFIED)) {
            return 'verified';
        }
        
        if ($statuses->contains(Document::STATUS_REJECTED)) {
            return 'rejected';
        }
        
        return 'pending';
    }

    /**
     * Get overall KYC status
     */
    private function getOverallKycStatus(array $requirements): string
    {
        $requiredTypes = array_filter($requirements, fn($req) => $req['required']);
        
        $allVerified = true;
        $anyRejected = false;
        $anyMissing = false;

        foreach ($requiredTypes as $req) {
            if ($req['uploaded_count'] === 0) {
                $anyMissing = true;
            } elseif ($req['status'] === 'rejected') {
                $anyRejected = true;
            } elseif ($req['status'] !== 'verified') {
                $allVerified = false;
            }
        }

        if ($anyMissing) {
            return 'incomplete';
        }
        
        if ($anyRejected) {
            return 'rejected';
        }
        
        if ($allVerified) {
            return 'verified';
        }
        
        return 'pending';
    }

    /**
     * Calculate KYC completion percentage
     */
    private function calculateKycCompletion(array $requirements): float
    {
        $requiredTypes = array_filter($requirements, fn($req) => $req['required']);
        $totalRequired = count($requiredTypes);
        
        if ($totalRequired === 0) {
            return 100.0;
        }
        
        $completed = 0;
        foreach ($requiredTypes as $req) {
            if ($req['uploaded_count'] > 0) {
                $completed++;
            }
        }
        
        return round(($completed / $totalRequired) * 100, 2);
    }
} 