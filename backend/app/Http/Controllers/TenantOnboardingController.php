<?php

namespace App\Http\Controllers;

use App\Models\Tenant;
use App\Models\User;
use App\Models\Unit;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Facades\Hash;
use Illuminate\Validation\Rule;

class TenantOnboardingController extends Controller
{
    /**
     * Get onboarding progress for a tenant
     */
    public function getProgress(Request $request): JsonResponse
    {
        $user = auth()->user();
        
        // Get tenant record if exists
        $tenant = $user->tenant;
        
        if (!$tenant) {
            return response()->json([
                'success' => true,
                'data' => [
                    'progress_percentage' => 0,
                    'current_step' => 'personal_details',
                    'completed_steps' => [],
                    'pending_steps' => $this->getAllSteps(),
                    'can_proceed' => true
                ],
                'message' => 'Onboarding not started'
            ]);
        }

        $progress = $this->calculateProgress($tenant);

        return response()->json([
            'success' => true,
            'data' => $progress,
            'message' => 'Progress retrieved successfully'
        ]);
    }

    /**
     * Start onboarding process - Step 1: Personal Details
     */
    public function startOnboarding(Request $request): JsonResponse
    {
        $validator = Validator::make($request->all(), [
            'first_name' => 'required|string|max:100',
            'last_name' => 'required|string|max:100',
            'phone' => 'required|string|max:15|unique:users,phone,' . auth()->id(),
            'alternate_phone' => 'nullable|string|max:15',
            'date_of_birth' => 'required|date|before:today',
            'gender' => 'required|in:male,female,other',
            'address' => 'required|string',
            'city' => 'required|string|max:100',
            'state' => 'required|string|max:100',
            'country' => 'nullable|string|max:100',
            'pincode' => 'required|string|max:10',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => 'Validation failed',
                'errors' => $validator->errors()
            ], 422);
        }

        try {
            DB::beginTransaction();

            $user = auth()->user();
            
            // Update user details
            $user->update(array_merge($validator->validated(), [
                'country' => $request->get('country', 'India')
            ]));

            // Create or update tenant record
            $tenant = $user->tenant;
            if (!$tenant) {
                $tenant = Tenant::create([
                    'user_id' => $user->id,
                    'unit_id' => null, // Will be assigned later
                    'owner_id' => null, // Will be assigned when unit is selected
                    'family_members' => 1, // Default, will be updated in family details step
                ]);
            }

            DB::commit();

            return response()->json([
                'success' => true,
                'data' => [
                    'tenant' => $tenant->load('user'),
                    'progress' => $this->calculateProgress($tenant),
                    'next_step' => 'family_details'
                ],
                'message' => 'Personal details saved successfully'
            ]);

        } catch (\Exception $e) {
            DB::rollBack();
            return response()->json([
                'success' => false,
                'message' => 'Failed to save personal details',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Step 2: Family Details
     */
    public function saveFamilyDetails(Request $request): JsonResponse
    {
        $validator = Validator::make($request->all(), [
            'family_members' => 'required|integer|min:1|max:20',
            'family_details' => 'required|array',
            'family_details.*.name' => 'required|string|max:100',
            'family_details.*.relationship' => 'required|string|max:50',
            'family_details.*.age' => 'required|integer|min:0|max:120',
            'family_details.*.gender' => 'required|in:male,female,other',
            'family_details.*.id_proof_type' => 'nullable|string|max:50',
            'family_details.*.id_proof_number' => 'nullable|string|max:100',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => 'Validation failed',
                'errors' => $validator->errors()
            ], 422);
        }

        try {
            $user = auth()->user();
            $tenant = $user->tenant;

            if (!$tenant) {
                return response()->json([
                    'success' => false,
                    'message' => 'Please complete personal details first'
                ], 400);
            }

            $tenant->update([
                'family_members' => $request->family_members,
                'family_details' => $request->family_details,
            ]);

            return response()->json([
                'success' => true,
                'data' => [
                    'tenant' => $tenant->fresh()->load('user'),
                    'progress' => $this->calculateProgress($tenant->fresh()),
                    'next_step' => 'employment_details'
                ],
                'message' => 'Family details saved successfully'
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to save family details',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Step 3: Employment Details
     */
    public function saveEmploymentDetails(Request $request): JsonResponse
    {
        $validator = Validator::make($request->all(), [
            'occupation' => 'required|string|max:100',
            'company_name' => 'required|string|max:200',
            'company_address' => 'required|string',
            'monthly_income' => 'required|numeric|min:0',
            'employment_type' => 'required|in:permanent,contract,freelance,business,retired,unemployed',
            'work_experience_years' => 'nullable|integer|min:0|max:50',
            'designation' => 'nullable|string|max:100',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => 'Validation failed',
                'errors' => $validator->errors()
            ], 422);
        }

        try {
            $user = auth()->user();
            $tenant = $user->tenant;

            if (!$tenant) {
                return response()->json([
                    'success' => false,
                    'message' => 'Please complete previous steps first'
                ], 400);
            }

            $tenant->update($validator->validated());

            return response()->json([
                'success' => true,
                'data' => [
                    'tenant' => $tenant->fresh()->load('user'),
                    'progress' => $this->calculateProgress($tenant->fresh()),
                    'next_step' => 'references'
                ],
                'message' => 'Employment details saved successfully'
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to save employment details',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Step 4: References
     */
    public function saveReferences(Request $request): JsonResponse
    {
        $validator = Validator::make($request->all(), [
            'references' => 'required|array|min:2|max:5',
            'references.*.name' => 'required|string|max:100',
            'references.*.relationship' => 'required|string|max:50',
            'references.*.phone' => 'required|string|max:15',
            'references.*.email' => 'nullable|email|max:100',
            'references.*.address' => 'required|string',
            'references.*.known_since_years' => 'required|integer|min:0|max:50',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => 'Validation failed',
                'errors' => $validator->errors()
            ], 422);
        }

        try {
            $user = auth()->user();
            $tenant = $user->tenant;

            if (!$tenant) {
                return response()->json([
                    'success' => false,
                    'message' => 'Please complete previous steps first'
                ], 400);
            }

            $tenant->update([
                'references' => $request->references,
            ]);

            return response()->json([
                'success' => true,
                'data' => [
                    'tenant' => $tenant->fresh()->load('user'),
                    'progress' => $this->calculateProgress($tenant->fresh()),
                    'next_step' => 'emergency_contacts'
                ],
                'message' => 'References saved successfully'
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to save references',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Step 5: Emergency Contacts
     */
    public function saveEmergencyContacts(Request $request): JsonResponse
    {
        $validator = Validator::make($request->all(), [
            'emergency_contact' => 'required|array',
            'emergency_contact.primary' => 'required|array',
            'emergency_contact.primary.name' => 'required|string|max:100',
            'emergency_contact.primary.relationship' => 'required|string|max:50',
            'emergency_contact.primary.phone' => 'required|string|max:15',
            'emergency_contact.primary.address' => 'required|string',
            'emergency_contact.secondary' => 'nullable|array',
            'emergency_contact.secondary.name' => 'nullable|string|max:100',
            'emergency_contact.secondary.relationship' => 'nullable|string|max:50',
            'emergency_contact.secondary.phone' => 'nullable|string|max:15',
            'emergency_contact.secondary.address' => 'nullable|string',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => 'Validation failed',
                'errors' => $validator->errors()
            ], 422);
        }

        try {
            $user = auth()->user();
            $tenant = $user->tenant;

            if (!$tenant) {
                return response()->json([
                    'success' => false,
                    'message' => 'Please complete previous steps first'
                ], 400);
            }

            // Update user's emergency contact
            $user->update([
                'emergency_contact' => $request->emergency_contact,
            ]);

            return response()->json([
                'success' => true,
                'data' => [
                    'tenant' => $tenant->fresh()->load('user'),
                    'progress' => $this->calculateProgress($tenant->fresh()),
                    'next_step' => 'preferences'
                ],
                'message' => 'Emergency contacts saved successfully'
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to save emergency contacts',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Step 6: Preferences and Requirements
     */
    public function savePreferences(Request $request): JsonResponse
    {
        $validator = Validator::make($request->all(), [
            'preferences' => 'nullable|array',
            'preferences.unit_type' => 'nullable|in:1bhk,2bhk,3bhk,4bhk,studio',
            'preferences.floor_preference' => 'nullable|in:ground,low,middle,high,any',
            'preferences.furnished' => 'nullable|in:fully,semi,unfurnished,any',
            'preferences.parking_required' => 'boolean',
            'preferences.pet_friendly' => 'boolean',
            'preferences.max_budget' => 'nullable|numeric|min:0',
            'special_requirements' => 'nullable|string',
            'police_verification_required' => 'boolean',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => 'Validation failed',
                'errors' => $validator->errors()
            ], 422);
        }

        try {
            $user = auth()->user();
            $tenant = $user->tenant;

            if (!$tenant) {
                return response()->json([
                    'success' => false,
                    'message' => 'Please complete previous steps first'
                ], 400);
            }

            $tenant->update([
                'preferences' => $request->preferences,
                'special_requirements' => $request->special_requirements,
                'police_verification_required' => $request->boolean('police_verification_required'),
            ]);

            return response()->json([
                'success' => true,
                'data' => [
                    'tenant' => $tenant->fresh()->load('user'),
                    'progress' => $this->calculateProgress($tenant->fresh()),
                    'next_step' => 'documents'
                ],
                'message' => 'Preferences saved successfully'
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to save preferences',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Complete onboarding (after documents are uploaded)
     */
    public function completeOnboarding(Request $request): JsonResponse
    {
        try {
            $user = auth()->user();
            $tenant = $user->tenant;

            if (!$tenant) {
                return response()->json([
                    'success' => false,
                    'message' => 'No tenant record found'
                ], 400);
            }

            // Check if all required documents are uploaded
            $requiredDocuments = ['id_proof', 'address_proof', 'photo'];
            $uploadedDocuments = $tenant->documents()->whereIn('type', $requiredDocuments)->pluck('type')->toArray();
            
            $missingDocuments = array_diff($requiredDocuments, $uploadedDocuments);
            
            if (!empty($missingDocuments)) {
                return response()->json([
                    'success' => false,
                    'message' => 'Please upload all required documents',
                    'data' => ['missing_documents' => $missingDocuments]
                ], 400);
            }

            // Update KYC status to submitted
            $tenant->update([
                'kyc_status' => Tenant::KYC_SUBMITTED,
                'kyc_submitted_at' => now(),
            ]);

            return response()->json([
                'success' => true,
                'data' => [
                    'tenant' => $tenant->fresh()->load('user'),
                    'progress' => $this->calculateProgress($tenant->fresh()),
                    'message' => 'Onboarding completed successfully. Your application is now under review.'
                ],
                'message' => 'Onboarding completed successfully'
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to complete onboarding',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Get available units for tenant selection
     */
    public function getAvailableUnits(Request $request): JsonResponse
    {
        $query = Unit::with(['owner'])
            ->whereIn('status', ['vacant', 'to-let'])
            ->active();

        // Apply filters based on tenant preferences
        $user = auth()->user();
        $tenant = $user->tenant;
        
        if ($tenant && $tenant->preferences) {
            $preferences = $tenant->preferences;
            
            if (isset($preferences['unit_type'])) {
                $bedrooms = $this->getBedroomsFromType($preferences['unit_type']);
                if ($bedrooms) {
                    $query->where('bedrooms', $bedrooms);
                }
            }
            
            if (isset($preferences['max_budget'])) {
                $query->where('market_rent', '<=', $preferences['max_budget']);
            }
        }

        $units = $query->paginate($request->get('per_page', 10));

        return response()->json([
            'success' => true,
            'data' => $units,
            'message' => 'Available units retrieved successfully'
        ]);
    }

    /**
     * Request unit assignment
     */
    public function requestUnitAssignment(Request $request): JsonResponse
    {
        $validator = Validator::make($request->all(), [
            'unit_id' => 'required|exists:units,id',
            'message' => 'nullable|string|max:500',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => 'Validation failed',
                'errors' => $validator->errors()
            ], 422);
        }

        try {
            $user = auth()->user();
            $tenant = $user->tenant;

            if (!$tenant) {
                return response()->json([
                    'success' => false,
                    'message' => 'Please complete onboarding first'
                ], 400);
            }

            $unit = Unit::find($request->unit_id);
            
            if (!$unit->isAvailable()) {
                return response()->json([
                    'success' => false,
                    'message' => 'Unit is no longer available'
                ], 400);
            }

            // Update tenant with requested unit and owner
            $tenant->update([
                'unit_id' => $unit->id,
                'owner_id' => $unit->owner_id,
                'admin_notes' => ($tenant->admin_notes ? $tenant->admin_notes . "\n" : '') . 
                               "Unit assignment requested on " . now()->format('Y-m-d H:i:s') . 
                               ($request->message ? ": " . $request->message : ""),
            ]);

            return response()->json([
                'success' => true,
                'data' => [
                    'tenant' => $tenant->fresh()->load(['user', 'unit', 'owner']),
                    'message' => 'Unit assignment request submitted successfully. The owner will review your application.'
                ],
                'message' => 'Unit assignment requested successfully'
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to request unit assignment',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Calculate onboarding progress
     */
    private function calculateProgress(Tenant $tenant): array
    {
        $steps = $this->getAllSteps();
        $completedSteps = [];
        $pendingSteps = [];
        $currentStep = 'personal_details';
        $stepDetails = [];

        // Check personal details
        $user = $tenant->user;
        $personalDetailsComplete = $user->first_name && $user->last_name && $user->phone && $user->date_of_birth;
        if ($personalDetailsComplete) {
            $completedSteps[] = 'personal_details';
            $currentStep = 'family_details';
            $stepDetails['personal_details'] = [
                'status' => 'completed',
                'completion_percentage' => 100,
                'last_updated' => $user->updated_at,
                'missing_fields' => []
            ];
        } else {
            $pendingSteps[] = 'personal_details';
            $missingFields = [];
            if (!$user->first_name) $missingFields[] = 'first_name';
            if (!$user->last_name) $missingFields[] = 'last_name';
            if (!$user->phone) $missingFields[] = 'phone';
            if (!$user->date_of_birth) $missingFields[] = 'date_of_birth';
            
            $stepDetails['personal_details'] = [
                'status' => 'pending',
                'completion_percentage' => 0,
                'missing_fields' => $missingFields,
                'next_action' => 'Complete personal information'
            ];
        }

        // Check family details
        $familyDetailsComplete = $tenant->family_details && $tenant->family_members > 0;
        if ($familyDetailsComplete) {
            $completedSteps[] = 'family_details';
            $currentStep = 'employment_details';
            $stepDetails['family_details'] = [
                'status' => 'completed',
                'completion_percentage' => 100,
                'last_updated' => $tenant->updated_at,
                'missing_fields' => []
            ];
        } else {
            $pendingSteps[] = 'family_details';
            $stepDetails['family_details'] = [
                'status' => 'pending',
                'completion_percentage' => 0,
                'missing_fields' => ['family_members', 'family_details'],
                'next_action' => 'Add family member details'
            ];
        }

        // Check employment details
        $employmentComplete = $tenant->occupation && $tenant->company_name && $tenant->monthly_income;
        if ($employmentComplete) {
            $completedSteps[] = 'employment_details';
            $currentStep = 'references';
            $stepDetails['employment_details'] = [
                'status' => 'completed',
                'completion_percentage' => 100,
                'last_updated' => $tenant->updated_at,
                'missing_fields' => []
            ];
        } else {
            $pendingSteps[] = 'employment_details';
            $missingFields = [];
            if (!$tenant->occupation) $missingFields[] = 'occupation';
            if (!$tenant->company_name) $missingFields[] = 'company_name';
            if (!$tenant->monthly_income) $missingFields[] = 'monthly_income';
            
            $stepDetails['employment_details'] = [
                'status' => 'pending',
                'completion_percentage' => 0,
                'missing_fields' => $missingFields,
                'next_action' => 'Complete employment information'
            ];
        }

        // Check references
        $referencesComplete = $tenant->references && count($tenant->references) >= 2;
        if ($referencesComplete) {
            $completedSteps[] = 'references';
            $currentStep = 'emergency_contacts';
            $stepDetails['references'] = [
                'status' => 'completed',
                'completion_percentage' => 100,
                'last_updated' => $tenant->updated_at,
                'reference_count' => count($tenant->references),
                'missing_fields' => []
            ];
        } else {
            $pendingSteps[] = 'references';
            $referenceCount = $tenant->references ? count($tenant->references) : 0;
            $stepDetails['references'] = [
                'status' => 'pending',
                'completion_percentage' => ($referenceCount / 2) * 100,
                'reference_count' => $referenceCount,
                'required_count' => 2,
                'missing_fields' => ['references'],
                'next_action' => 'Add at least 2 character references'
            ];
        }

        // Check emergency contacts
        $emergencyContactsComplete = $user->emergency_contact && isset($user->emergency_contact['primary']);
        if ($emergencyContactsComplete) {
            $completedSteps[] = 'emergency_contacts';
            $currentStep = 'preferences';
            $stepDetails['emergency_contacts'] = [
                'status' => 'completed',
                'completion_percentage' => 100,
                'last_updated' => $user->updated_at,
                'contact_count' => count($user->emergency_contact),
                'missing_fields' => []
            ];
        } else {
            $pendingSteps[] = 'emergency_contacts';
            $stepDetails['emergency_contacts'] = [
                'status' => 'pending',
                'completion_percentage' => 0,
                'missing_fields' => ['emergency_contact'],
                'next_action' => 'Add emergency contact information'
            ];
        }

        // Check preferences
        $preferencesComplete = $tenant->preferences;
        if ($preferencesComplete) {
            $completedSteps[] = 'preferences';
            $currentStep = 'documents';
            $stepDetails['preferences'] = [
                'status' => 'completed',
                'completion_percentage' => 100,
                'last_updated' => $tenant->updated_at,
                'missing_fields' => []
            ];
        } else {
            $pendingSteps[] = 'preferences';
            $stepDetails['preferences'] = [
                'status' => 'pending',
                'completion_percentage' => 0,
                'missing_fields' => ['preferences'],
                'next_action' => 'Set your preferences and requirements'
            ];
        }

        // Check documents
        $requiredDocuments = ['id_proof', 'address_proof', 'photo'];
        $uploadedDocuments = $tenant->documents()->whereIn('type', $requiredDocuments)->pluck('type')->toArray();
        $documentsComplete = count($uploadedDocuments) >= count($requiredDocuments);
        
        if ($documentsComplete) {
            $completedSteps[] = 'documents';
            $currentStep = 'review';
            $stepDetails['documents'] = [
                'status' => 'completed',
                'completion_percentage' => 100,
                'uploaded_documents' => $uploadedDocuments,
                'missing_documents' => [],
                'missing_fields' => []
            ];
        } else {
            $pendingSteps[] = 'documents';
            $missingDocuments = array_diff($requiredDocuments, $uploadedDocuments);
            $stepDetails['documents'] = [
                'status' => 'pending',
                'completion_percentage' => (count($uploadedDocuments) / count($requiredDocuments)) * 100,
                'uploaded_documents' => $uploadedDocuments,
                'missing_documents' => $missingDocuments,
                'missing_fields' => $missingDocuments,
                'next_action' => 'Upload required documents: ' . implode(', ', $missingDocuments)
            ];
        }

        // Check if onboarding is complete
        $reviewComplete = $tenant->kyc_status === Tenant::KYC_SUBMITTED || $tenant->kyc_status === Tenant::KYC_VERIFIED;
        if ($reviewComplete) {
            $completedSteps[] = 'review';
            $currentStep = 'completed';
            $stepDetails['review'] = [
                'status' => 'completed',
                'completion_percentage' => 100,
                'kyc_status' => $tenant->kyc_status,
                'kyc_submitted_at' => $tenant->kyc_submitted_at,
                'kyc_verified_at' => $tenant->kyc_verified_at,
                'missing_fields' => []
            ];
        } else {
            $pendingSteps[] = 'review';
            $stepDetails['review'] = [
                'status' => 'pending',
                'completion_percentage' => 0,
                'kyc_status' => $tenant->kyc_status,
                'missing_fields' => ['kyc_submission'],
                'next_action' => 'Submit KYC for review'
            ];
        }

        $progressPercentage = (count($completedSteps) / count($steps)) * 100;

        // Determine next steps
        $nextSteps = [];
        foreach ($stepDetails as $stepName => $details) {
            if ($details['status'] === 'pending') {
                $nextSteps[] = $details['next_action'];
            }
        }

        // Calculate time-based metrics
        $onboardingStarted = $tenant->created_at;
        $daysSinceStart = $onboardingStarted ? $onboardingStarted->diffInDays(now()) : 0;
        $averageCompletionRate = $daysSinceStart > 0 ? ($progressPercentage / $daysSinceStart) : 0;

        return [
            'progress_percentage' => round($progressPercentage, 2),
            'current_step' => $currentStep,
            'completed_steps' => $completedSteps,
            'pending_steps' => array_diff($steps, $completedSteps),
            'can_proceed' => !empty($completedSteps),
            'kyc_status' => $tenant->kyc_status,
            'missing_documents' => array_diff($requiredDocuments, $uploadedDocuments),
            'step_details' => $stepDetails,
            'next_steps' => $nextSteps,
            'metrics' => [
                'days_since_start' => $daysSinceStart,
                'average_completion_rate' => round($averageCompletionRate, 2),
                'estimated_completion_days' => $averageCompletionRate > 0 ? round((100 - $progressPercentage) / $averageCompletionRate, 1) : null,
            ],
            'is_complete' => $progressPercentage >= 100,
            'last_activity' => $tenant->updated_at,
        ];
    }

    /**
     * Get all onboarding steps
     */
    private function getAllSteps(): array
    {
        return [
            'personal_details',
            'family_details',
            'employment_details',
            'references',
            'emergency_contacts',
            'preferences',
            'documents',
            'review'
        ];
    }

    /**
     * Convert unit type to bedroom count
     */
    private function getBedroomsFromType(string $unitType): ?int
    {
        return match($unitType) {
            'studio' => 0,
            '1bhk' => 1,
            '2bhk' => 2,
            '3bhk' => 3,
            '4bhk' => 4,
            default => null,
        };
    }

    /**
     * Get admin dashboard for onboarding progress across all tenants
     */
    public function getAdminDashboard(Request $request): JsonResponse
    {
        try {
            $query = Tenant::with(['user', 'unit', 'owner'])
                ->whereHas('user');

            // Apply filters
            if ($request->has('status')) {
                $status = $request->get('status');
                if ($status === 'incomplete') {
                    $query->where('kyc_status', '!=', Tenant::KYC_VERIFIED);
                } elseif ($status === 'complete') {
                    $query->where('kyc_status', Tenant::KYC_VERIFIED);
                }
            }

            if ($request->has('days_incomplete')) {
                $days = $request->get('days_incomplete');
                $query->where('created_at', '<=', now()->subDays($days));
            }

            $tenants = $query->get();
            $dashboardData = $this->calculateDashboardMetrics($tenants);

            return response()->json([
                'success' => true,
                'data' => $dashboardData,
                'message' => 'Admin dashboard data retrieved successfully'
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to retrieve admin dashboard',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Get onboarding progress for a specific tenant (admin view)
     */
    public function getTenantProgress(Request $request, Tenant $tenant): JsonResponse
    {
        try {
            $progress = $this->calculateProgress($tenant);
            
            // Add additional admin-specific data
            $progress['admin_notes'] = $tenant->admin_notes;
            $progress['owner_notes'] = $tenant->owner_notes;
            $progress['created_at'] = $tenant->created_at;
            $progress['updated_at'] = $tenant->updated_at;

            return response()->json([
                'success' => true,
                'data' => $progress,
                'message' => 'Tenant progress retrieved successfully'
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to retrieve tenant progress',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Send automated reminders for incomplete onboarding steps
     */
    public function sendReminders(Request $request): JsonResponse
    {
        try {
            $validator = Validator::make($request->all(), [
                'tenant_ids' => 'nullable|array',
                'tenant_ids.*' => 'exists:tenants,id',
                'reminder_type' => 'required|in:email,sms,both',
                'message' => 'nullable|string|max:500'
            ]);

            if ($validator->fails()) {
                return response()->json([
                    'success' => false,
                    'message' => 'Validation failed',
                    'errors' => $validator->errors()
                ], 422);
            }

            $query = Tenant::with(['user'])
                ->where('kyc_status', '!=', Tenant::KYC_VERIFIED);

            if ($request->has('tenant_ids')) {
                $query->whereIn('id', $request->tenant_ids);
            }

            $tenants = $query->get();
            $remindersSent = 0;

            foreach ($tenants as $tenant) {
                $progress = $this->calculateProgress($tenant);
                
                if ($progress['progress_percentage'] < 100) {
                    // Send reminder based on type
                    $this->sendReminderToTenant($tenant, $request->reminder_type, $request->message);
                    $remindersSent++;
                }
            }

            return response()->json([
                'success' => true,
                'data' => [
                    'reminders_sent' => $remindersSent,
                    'total_tenants' => $tenants->count(),
                    'reminder_type' => $request->reminder_type
                ],
                'message' => "Successfully sent {$remindersSent} reminders"
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to send reminders',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Calculate dashboard metrics for admin view
     */
    private function calculateDashboardMetrics($tenants): array
    {
        $totalTenants = $tenants->count();
        $completedTenants = $tenants->where('kyc_status', Tenant::KYC_VERIFIED)->count();
        $incompleteTenants = $totalTenants - $completedTenants;

        $progressRanges = [
            '0-25' => 0,
            '26-50' => 0,
            '51-75' => 0,
            '76-99' => 0,
            '100' => 0
        ];

        $stepCompletion = [
            'personal_details' => 0,
            'family_details' => 0,
            'employment_details' => 0,
            'references' => 0,
            'emergency_contacts' => 0,
            'preferences' => 0,
            'documents' => 0,
            'review' => 0
        ];

        $averageProgress = 0;
        $stuckTenants = [];

        foreach ($tenants as $tenant) {
            $progress = $this->calculateProgress($tenant);
            $progressPercentage = $progress['progress_percentage'];
            $averageProgress += $progressPercentage;

            // Categorize by progress range
            if ($progressPercentage <= 25) {
                $progressRanges['0-25']++;
            } elseif ($progressPercentage <= 50) {
                $progressRanges['26-50']++;
            } elseif ($progressPercentage <= 75) {
                $progressRanges['51-75']++;
            } elseif ($progressPercentage < 100) {
                $progressRanges['76-99']++;
            } else {
                $progressRanges['100']++;
            }

            // Count step completion
            foreach ($progress['completed_steps'] as $step) {
                if (isset($stepCompletion[$step])) {
                    $stepCompletion[$step]++;
                }
            }

            // Identify stuck tenants (incomplete for more than 7 days)
            if ($progressPercentage < 100 && $tenant->created_at->diffInDays(now()) > 7) {
                $stuckTenants[] = [
                    'id' => $tenant->id,
                    'user_name' => $tenant->user->full_name,
                    'progress' => $progressPercentage,
                    'days_since_start' => $tenant->created_at->diffInDays(now()),
                    'current_step' => $progress['current_step'],
                    'last_activity' => $tenant->updated_at
                ];
            }
        }

        $averageProgress = $totalTenants > 0 ? round($averageProgress / $totalTenants, 2) : 0;

        // Calculate step completion percentages
        $stepCompletionPercentages = [];
        foreach ($stepCompletion as $step => $count) {
            $stepCompletionPercentages[$step] = $totalTenants > 0 ? round(($count / $totalTenants) * 100, 2) : 0;
        }

        return [
            'overview' => [
                'total_tenants' => $totalTenants,
                'completed_tenants' => $completedTenants,
                'incomplete_tenants' => $incompleteTenants,
                'completion_rate' => $totalTenants > 0 ? round(($completedTenants / $totalTenants) * 100, 2) : 0,
                'average_progress' => $averageProgress
            ],
            'progress_distribution' => $progressRanges,
            'step_completion' => $stepCompletionPercentages,
            'stuck_tenants' => $stuckTenants,
            'recent_activity' => [
                'last_24_hours' => $tenants->where('updated_at', '>=', now()->subDay())->count(),
                'last_7_days' => $tenants->where('updated_at', '>=', now()->subWeek())->count(),
                'last_30_days' => $tenants->where('updated_at', '>=', now()->subMonth())->count()
            ]
        ];
    }

    /**
     * Send reminder to a specific tenant
     */
    private function sendReminderToTenant(Tenant $tenant, string $type, ?string $customMessage): void
    {
        $progress = $this->calculateProgress($tenant);
        $nextSteps = implode(', ', array_slice($progress['next_steps'], 0, 3));
        
        $defaultMessage = "Your onboarding is {$progress['progress_percentage']}% complete. Please complete: {$nextSteps}";
        $message = $customMessage ?: $defaultMessage;

        // TODO: Implement actual notification sending
        // For now, we'll just log the reminder
        \Log::info("Reminder sent to tenant {$tenant->id}: {$message}");
        
        // Update tenant with reminder sent timestamp
        $tenant->update([
            'last_reminder_sent_at' => now(),
            'reminder_count' => ($tenant->reminder_count ?? 0) + 1
        ]);
    }
} 