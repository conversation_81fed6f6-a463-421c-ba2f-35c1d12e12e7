<?php

namespace App\Http\Controllers;

use App\Models\User;
use App\Models\Unit;
use App\Events\UnitStatusChanged;
use App\Notifications\UnitStatusChangedNotification;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\Auth;

class NotificationController extends Controller
{
    /**
     * Get notifications for the authenticated user
     */
    public function index(Request $request): JsonResponse
    {
        $user = Auth::user();
        
        $notifications = $user->notifications()
            ->when($request->get('unread_only'), function ($query) {
                return $query->whereNull('read_at');
            })
            ->when($request->get('type'), function ($query, $type) {
                return $query->where('type', $type);
            })
            ->orderBy('created_at', 'desc')
            ->paginate($request->get('per_page', 15));

        return response()->json([
            'success' => true,
            'message' => 'Notifications retrieved successfully',
            'data' => $notifications
        ]);
    }

    /**
     * Mark notification as read
     */
    public function markAsRead(Request $request, string $id): JsonResponse
    {
        $user = Auth::user();
        
        $notification = $user->notifications()->findOrFail($id);
        $notification->markAsRead();

        return response()->json([
            'success' => true,
            'message' => 'Notification marked as read',
            'data' => $notification
        ]);
    }

    /**
     * Mark all notifications as read
     */
    public function markAllAsRead(): JsonResponse
    {
        $user = Auth::user();
        
        $user->unreadNotifications->markAsRead();

        return response()->json([
            'success' => true,
            'message' => 'All notifications marked as read'
        ]);
    }

    /**
     * Test the notification system by manually triggering a unit status change notification
     */
    public function testNotification(Request $request): JsonResponse
    {
        $request->validate([
            'unit_id' => 'required|exists:units,id',
            'previous_status' => 'required|string',
            'new_status' => 'required|string',
            'reason' => 'nullable|string'
        ]);

        $unit = Unit::findOrFail($request->unit_id);
        $user = Auth::user();

        // Create the event
        $event = new UnitStatusChanged(
            $unit,
            $request->previous_status,
            $request->new_status,
            $user,
            $request->reason ?? 'Manual test notification'
        );

        // Send notification directly to the current user for testing
        $user->notify(new UnitStatusChangedNotification($event));

        return response()->json([
            'success' => true,
            'message' => 'Test notification sent successfully',
            'data' => [
                'unit_id' => $unit->id,
                'unit_number' => $unit->unit_number,
                'previous_status' => $request->previous_status,
                'new_status' => $request->new_status,
                'notified_user' => $user->name
            ]
        ]);
    }

    /**
     * Get notification statistics
     */
    public function statistics(): JsonResponse
    {
        $user = Auth::user();

        $stats = [
            'total_notifications' => $user->notifications()->count(),
            'unread_notifications' => $user->unreadNotifications()->count(),
            'read_notifications' => $user->readNotifications()->count(),
            'recent_notifications' => $user->notifications()
                ->orderBy('created_at', 'desc')
                ->limit(5)
                ->get()
                ->map(function ($notification) {
                    return [
                        'id' => $notification->id,
                        'type' => $notification->type,
                        'data' => $notification->data,
                        'read_at' => $notification->read_at,
                        'created_at' => $notification->created_at
                    ];
                })
        ];

        return response()->json([
            'success' => true,
            'message' => 'Notification statistics retrieved successfully',
            'data' => $stats
        ]);
    }
} 