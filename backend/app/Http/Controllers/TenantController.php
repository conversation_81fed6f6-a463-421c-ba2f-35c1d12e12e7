<?php

namespace App\Http\Controllers;

use App\Models\Tenant;
use App\Models\User;
use App\Models\Unit;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Validator;
use Illuminate\Validation\Rule;

class TenantController extends Controller
{
    /**
     * Display a listing of tenants.
     */
    public function index(Request $request): JsonResponse
    {
        $query = Tenant::with(['user', 'unit', 'owner']);

        // Apply filters
        if ($request->filled('status')) {
            $query->byStatus($request->status);
        }

        if ($request->filled('kyc_status')) {
            $query->byKycStatus($request->kyc_status);
        }

        if ($request->filled('unit_id')) {
            $query->byUnit($request->unit_id);
        }

        if ($request->filled('owner_id')) {
            $query->byOwner($request->owner_id);
        }

        if ($request->filled('search')) {
            $search = $request->search;
            $query->whereHas('user', function ($q) use ($search) {
                $q->where('first_name', 'like', "%{$search}%")
                  ->orWhere('last_name', 'like', "%{$search}%")
                  ->orWhere('email', 'like', "%{$search}%")
                  ->orWhere('phone', 'like', "%{$search}%");
            })->orWhere('tenant_code', 'like', "%{$search}%");
        }

        // Apply sorting
        $sortBy = $request->get('sort_by', 'created_at');
        $sortOrder = $request->get('sort_order', 'desc');
        $query->orderBy($sortBy, $sortOrder);

        // Paginate results
        $perPage = $request->get('per_page', 15);
        $tenants = $query->paginate($perPage);

        return response()->json([
            'success' => true,
            'data' => $tenants,
            'message' => 'Tenants retrieved successfully'
        ]);
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        //
    }

    /**
     * Store a newly created tenant.
     */
    public function store(Request $request): JsonResponse
    {
        $validator = Validator::make($request->all(), [
            'user_id' => 'required|exists:users,id',
            'unit_id' => 'required|exists:units,id',
            'owner_id' => 'required|exists:users,id',
            'occupation' => 'nullable|string|max:100',
            'company_name' => 'nullable|string|max:200',
            'company_address' => 'nullable|string',
            'family_members' => 'integer|min:1|max:20',
            'family_details' => 'nullable|array',
            'monthly_income' => 'nullable|numeric|min:0',
            'security_deposit' => 'nullable|numeric|min:0',
            'advance_amount' => 'nullable|numeric|min:0',
            'move_in_date' => 'nullable|date',
            'move_out_date' => 'nullable|date|after:move_in_date',
            'agreement_start_date' => 'nullable|date',
            'agreement_end_date' => 'nullable|date|after:agreement_start_date',
            'agreement_type' => 'nullable|string|max:50',
            'police_verification_required' => 'boolean',
            'references' => 'nullable|array',
            'preferences' => 'nullable|array',
            'special_requirements' => 'nullable|string',
            'admin_notes' => 'nullable|string',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => 'Validation failed',
                'errors' => $validator->errors()
            ], 422);
        }

        try {
            DB::beginTransaction();

            // Check if unit is available
            $unit = Unit::find($request->unit_id);
            if (!$unit->isAvailable()) {
                return response()->json([
                    'success' => false,
                    'message' => 'Unit is not available for rent'
                ], 400);
            }

            // Create tenant
            $tenant = Tenant::create($validator->validated());

            // Update unit status to rented
            $unit->updateStatus('rented', auth()->user(), 'Tenant assigned');

            // Update unit's current tenant
            $unit->update(['current_tenant_id' => $request->user_id]);

            DB::commit();

            return response()->json([
                'success' => true,
                'data' => $tenant->load(['user', 'unit', 'owner']),
                'message' => 'Tenant created successfully'
            ], 201);

        } catch (\Exception $e) {
            DB::rollBack();
            return response()->json([
                'success' => false,
                'message' => 'Failed to create tenant',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Display the specified tenant.
     */
    public function show(Tenant $tenant): JsonResponse
    {
        $tenant->load(['user', 'unit', 'owner', 'documents', 'kycVerifiedBy', 'terminatedBy']);

        return response()->json([
            'success' => true,
            'data' => $tenant,
            'message' => 'Tenant retrieved successfully'
        ]);
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(string $id)
    {
        //
    }

    /**
     * Update the specified tenant.
     */
    public function update(Request $request, Tenant $tenant): JsonResponse
    {
        $validator = Validator::make($request->all(), [
            'occupation' => 'nullable|string|max:100',
            'company_name' => 'nullable|string|max:200',
            'company_address' => 'nullable|string',
            'family_members' => 'integer|min:1|max:20',
            'family_details' => 'nullable|array',
            'monthly_income' => 'nullable|numeric|min:0',
            'security_deposit' => 'nullable|numeric|min:0',
            'advance_amount' => 'nullable|numeric|min:0',
            'move_in_date' => 'nullable|date',
            'move_out_date' => 'nullable|date|after:move_in_date',
            'agreement_start_date' => 'nullable|date',
            'agreement_end_date' => 'nullable|date|after:agreement_start_date',
            'agreement_type' => 'nullable|string|max:50',
            'police_verification_required' => 'boolean',
            'references' => 'nullable|array',
            'behavior_rating' => 'nullable|in:excellent,good,average,poor',
            'behavior_notes' => 'nullable|string',
            'preferences' => 'nullable|array',
            'special_requirements' => 'nullable|string',
            'admin_notes' => 'nullable|string',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => 'Validation failed',
                'errors' => $validator->errors()
            ], 422);
        }

        try {
            $tenant->update($validator->validated());

            return response()->json([
                'success' => true,
                'data' => $tenant->load(['user', 'unit', 'owner']),
                'message' => 'Tenant updated successfully'
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to update tenant',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Remove the specified tenant.
     */
    public function destroy(Tenant $tenant): JsonResponse
    {
        try {
            DB::beginTransaction();

            // Update unit status to vacant
            $unit = $tenant->unit;
            $unit->updateStatus('vacant', auth()->user(), 'Tenant removed');
            $unit->update(['current_tenant_id' => null]);

            // Soft delete tenant
            $tenant->delete();

            DB::commit();

            return response()->json([
                'success' => true,
                'message' => 'Tenant deleted successfully'
            ]);

        } catch (\Exception $e) {
            DB::rollBack();
            return response()->json([
                'success' => false,
                'message' => 'Failed to delete tenant',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Submit KYC documents for tenant.
     */
    public function submitKyc(Request $request, Tenant $tenant): JsonResponse
    {
        $validator = Validator::make($request->all(), [
            'documents' => 'required|array',
            'documents.*' => 'integer|exists:documents,id',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => 'Validation failed',
                'errors' => $validator->errors()
            ], 422);
        }

        try {
            $tenant->submitKyc($request->documents);

            return response()->json([
                'success' => true,
                'data' => $tenant->fresh(),
                'message' => 'KYC documents submitted successfully'
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to submit KYC documents',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Verify tenant KYC.
     */
    public function verifyKyc(Request $request, Tenant $tenant): JsonResponse
    {
        $validator = Validator::make($request->all(), [
            'action' => 'required|in:verify,reject',
            'reason' => 'required_if:action,reject|string',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => 'Validation failed',
                'errors' => $validator->errors()
            ], 422);
        }

        try {
            $user = auth()->user();

            if ($request->action === 'verify') {
                $tenant->verifyKyc($user);
                $message = 'KYC verified successfully';
            } else {
                $tenant->rejectKyc($request->reason, $user);
                $message = 'KYC rejected successfully';
            }

            return response()->json([
                'success' => true,
                'data' => $tenant->fresh(),
                'message' => $message
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to process KYC verification',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Terminate tenant.
     */
    public function terminate(Request $request, Tenant $tenant): JsonResponse
    {
        $validator = Validator::make($request->all(), [
            'reason' => 'required|string',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => 'Validation failed',
                'errors' => $validator->errors()
            ], 422);
        }

        try {
            DB::beginTransaction();

            $tenant->terminate($request->reason, auth()->user());

            // Update unit status to vacant
            $unit = $tenant->unit;
            $unit->updateStatus('vacant', auth()->user(), 'Tenant terminated');
            $unit->update(['current_tenant_id' => null]);

            DB::commit();

            return response()->json([
                'success' => true,
                'data' => $tenant->fresh(),
                'message' => 'Tenant terminated successfully'
            ]);

        } catch (\Exception $e) {
            DB::rollBack();
            return response()->json([
                'success' => false,
                'message' => 'Failed to terminate tenant',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Start notice period for tenant.
     */
    public function startNoticePeriod(Request $request, Tenant $tenant): JsonResponse
    {
        $validator = Validator::make($request->all(), [
            'notice_period_days' => 'required|integer|min:1|max:365',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => 'Validation failed',
                'errors' => $validator->errors()
            ], 422);
        }

        try {
            $tenant->startNoticePeriod($request->notice_period_days);

            return response()->json([
                'success' => true,
                'data' => $tenant->fresh(),
                'message' => 'Notice period started successfully'
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to start notice period',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Get tenant statistics.
     */
    public function statistics(): JsonResponse
    {
        try {
            $stats = [
                'total_tenants' => Tenant::count(),
                'active_tenants' => Tenant::active()->count(),
                'terminated_tenants' => Tenant::byStatus(Tenant::STATUS_TERMINATED)->count(),
                'notice_period_tenants' => Tenant::byStatus(Tenant::STATUS_NOTICE_PERIOD)->count(),
                'kyc_pending' => Tenant::byKycStatus(Tenant::KYC_PENDING)->count(),
                'kyc_submitted' => Tenant::byKycStatus(Tenant::KYC_SUBMITTED)->count(),
                'kyc_verified' => Tenant::byKycStatus(Tenant::KYC_VERIFIED)->count(),
                'kyc_rejected' => Tenant::byKycStatus(Tenant::KYC_REJECTED)->count(),
                'expiring_agreements' => Tenant::expiringAgreements(30)->count(),
            ];

            return response()->json([
                'success' => true,
                'data' => $stats,
                'message' => 'Statistics retrieved successfully'
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to retrieve statistics',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Get tenants with expiring agreements.
     */
    public function expiringAgreements(Request $request): JsonResponse
    {
        $days = $request->get('days', 30);
        
        $tenants = Tenant::with(['user', 'unit', 'owner'])
            ->expiringAgreements($days)
            ->get();

        return response()->json([
            'success' => true,
            'data' => $tenants,
            'message' => 'Expiring agreements retrieved successfully'
        ]);
    }

    /**
     * Bulk update tenant status.
     */
    public function bulkUpdateStatus(Request $request): JsonResponse
    {
        $validator = Validator::make($request->all(), [
            'tenant_ids' => 'required|array',
            'tenant_ids.*' => 'integer|exists:tenants,id',
            'status' => ['required', Rule::in(Tenant::getAllStatuses())],
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => 'Validation failed',
                'errors' => $validator->errors()
            ], 422);
        }

        try {
            $updated = Tenant::whereIn('id', $request->tenant_ids)
                ->update(['status' => $request->status]);

            return response()->json([
                'success' => true,
                'data' => ['updated_count' => $updated],
                'message' => 'Tenant statuses updated successfully'
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to update tenant statuses',
                'error' => $e->getMessage()
            ], 500);
        }
    }
}
