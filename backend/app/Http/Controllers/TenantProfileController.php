<?php

namespace App\Http\Controllers;

use App\Models\Tenant;
use App\Models\User;
use App\Services\TenantActivityLogger;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Validator;
use Illuminate\Validation\Rule;

class TenantProfileController extends Controller
{
    /**
     * Get tenant profile with completion status
     */
    public function getProfile(Request $request): JsonResponse
    {
        try {
            $user = Auth::user();
            $tenant = $user->tenant ?? Tenant::where('user_id', $user->id)->first();

            if (!$tenant) {
                return response()->json([
                    'success' => false,
                    'message' => 'Tenant profile not found',
                ], 404);
            }

            $profileData = $this->getProfileData($tenant);
            $completionStatus = $this->calculateProfileCompletion($tenant);

            return response()->json([
                'success' => true,
                'data' => [
                    'profile' => $profileData,
                    'completion' => $completionStatus,
                ],
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Error fetching tenant profile',
                'error' => $e->getMessage(),
            ], 500);
        }
    }

    /**
     * Update tenant personal information
     */
    public function updatePersonalInfo(Request $request): JsonResponse
    {
        try {
            $user = Auth::user();
            $tenant = $user->tenant ?? Tenant::where('user_id', $user->id)->first();

            if (!$tenant) {
                return response()->json([
                    'success' => false,
                    'message' => 'Tenant profile not found',
                ], 404);
            }

            $validator = Validator::make($request->all(), [
                'occupation' => 'sometimes|string|max:100',
                'company_name' => 'sometimes|string|max:100',
                'company_address' => 'sometimes|string|max:500',
                'monthly_income' => 'sometimes|numeric|min:0',
                'move_in_date' => 'sometimes|date|after_or_equal:today',
                'special_requirements' => 'sometimes|string|max:1000',
            ]);

            if ($validator->fails()) {
                return response()->json([
                    'success' => false,
                    'message' => 'Validation failed',
                    'errors' => $validator->errors(),
                ], 422);
            }

            // Store old values for activity logging
            $oldValues = $tenant->only([
                'occupation', 'company_name', 'company_address', 
                'monthly_income', 'move_in_date', 'special_requirements'
            ]);

            // Update tenant data
            $tenant->update($validator->validated());

            // Log activity
            TenantActivityLogger::logPersonalDetailsUpdated(
                $tenant,
                $oldValues,
                $tenant->only(array_keys($validator->validated())),
                $user,
                $request
            );

            $profileData = $this->getProfileData($tenant);
            $completionStatus = $this->calculateProfileCompletion($tenant);

            return response()->json([
                'success' => true,
                'message' => 'Personal information updated successfully',
                'data' => [
                    'profile' => $profileData,
                    'completion' => $completionStatus,
                ],
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Error updating personal information',
                'error' => $e->getMessage(),
            ], 500);
        }
    }

    /**
     * Update tenant family details
     */
    public function updateFamilyDetails(Request $request): JsonResponse
    {
        try {
            $user = Auth::user();
            $tenant = $user->tenant ?? Tenant::where('user_id', $user->id)->first();

            if (!$tenant) {
                return response()->json([
                    'success' => false,
                    'message' => 'Tenant profile not found',
                ], 404);
            }

            $validator = Validator::make($request->all(), [
                'family_members' => 'sometimes|integer|min:0|max:20',
                'family_details' => 'sometimes|array',
                'family_details.*.name' => 'required_with:family_details|string|max:100',
                'family_details.*.relationship' => 'required_with:family_details|string|max:50',
                'family_details.*.age' => 'required_with:family_details|integer|min:0|max:120',
                'family_details.*.occupation' => 'sometimes|string|max:100',
                'family_details.*.phone' => 'sometimes|string|max:20',
            ]);

            if ($validator->fails()) {
                return response()->json([
                    'success' => false,
                    'message' => 'Validation failed',
                    'errors' => $validator->errors(),
                ], 422);
            }

            // Store old values for activity logging
            $oldValues = $tenant->only(['family_members', 'family_details']);

            // Update tenant data
            $tenant->update($validator->validated());

            // Log activity
            TenantActivityLogger::logFamilyDetailsUpdated(
                $tenant,
                $oldValues,
                $tenant->only(array_keys($validator->validated())),
                $user,
                $request
            );

            $profileData = $this->getProfileData($tenant);
            $completionStatus = $this->calculateProfileCompletion($tenant);

            return response()->json([
                'success' => true,
                'message' => 'Family details updated successfully',
                'data' => [
                    'profile' => $profileData,
                    'completion' => $completionStatus,
                ],
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Error updating family details',
                'error' => $e->getMessage(),
            ], 500);
        }
    }

    /**
     * Update tenant preferences
     */
    public function updatePreferences(Request $request): JsonResponse
    {
        try {
            $user = Auth::user();
            $tenant = $user->tenant ?? Tenant::where('user_id', $user->id)->first();

            if (!$tenant) {
                return response()->json([
                    'success' => false,
                    'message' => 'Tenant profile not found',
                ], 404);
            }

            $validator = Validator::make($request->all(), [
                'preferences' => 'required|array',
                'preferences.notifications' => 'sometimes|array',
                'preferences.notifications.email' => 'sometimes|boolean',
                'preferences.notifications.sms' => 'sometimes|boolean',
                'preferences.notifications.push' => 'sometimes|boolean',
                'preferences.notifications.newsletter' => 'sometimes|boolean',
                'preferences.privacy' => 'sometimes|array',
                'preferences.privacy.profile_visibility' => ['sometimes', Rule::in(['public', 'private', 'contacts_only'])],
                'preferences.privacy.contact_sharing' => 'sometimes|boolean',
                'preferences.communication' => 'sometimes|array',
                'preferences.communication.preferred_language' => 'sometimes|string|max:10',
                'preferences.communication.preferred_contact_method' => ['sometimes', Rule::in(['email', 'sms', 'phone', 'app'])],
                'preferences.maintenance' => 'sometimes|array',
                'preferences.maintenance.preferred_time_slots' => 'sometimes|array',
                'preferences.maintenance.emergency_contact_priority' => 'sometimes|boolean',
            ]);

            if ($validator->fails()) {
                return response()->json([
                    'success' => false,
                    'message' => 'Validation failed',
                    'errors' => $validator->errors(),
                ], 422);
            }

            // Store old values for activity logging
            $oldValues = ['preferences' => $tenant->preferences];

            // Update preferences
            $currentPreferences = $tenant->preferences ?? [];
            $newPreferences = array_merge($currentPreferences, $request->preferences);
            $tenant->update(['preferences' => $newPreferences]);

            // Log activity
            TenantActivityLogger::logProfileUpdated(
                $tenant,
                $oldValues,
                ['preferences' => $newPreferences],
                $user,
                $request
            );

            $profileData = $this->getProfileData($tenant);
            $completionStatus = $this->calculateProfileCompletion($tenant);

            return response()->json([
                'success' => true,
                'message' => 'Preferences updated successfully',
                'data' => [
                    'profile' => $profileData,
                    'completion' => $completionStatus,
                ],
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Error updating preferences',
                'error' => $e->getMessage(),
            ], 500);
        }
    }

    /**
     * Update tenant references
     */
    public function updateReferences(Request $request): JsonResponse
    {
        try {
            $user = Auth::user();
            $tenant = $user->tenant ?? Tenant::where('user_id', $user->id)->first();

            if (!$tenant) {
                return response()->json([
                    'success' => false,
                    'message' => 'Tenant profile not found',
                ], 404);
            }

            $validator = Validator::make($request->all(), [
                'references' => 'required|array|min:1|max:3',
                'references.*.name' => 'required|string|max:100',
                'references.*.relationship' => 'required|string|max:50',
                'references.*.phone' => 'required|string|max:20',
                'references.*.email' => 'sometimes|email|max:100',
                'references.*.address' => 'sometimes|string|max:500',
                'references.*.occupation' => 'sometimes|string|max:100',
                'references.*.years_known' => 'sometimes|integer|min:0|max:50',
            ]);

            if ($validator->fails()) {
                return response()->json([
                    'success' => false,
                    'message' => 'Validation failed',
                    'errors' => $validator->errors(),
                ], 422);
            }

            // Store old values for activity logging
            $oldValues = ['references' => $tenant->references];

            // Update references
            $tenant->update(['references' => $request->references]);

            // Log activity
            TenantActivityLogger::logProfileUpdated(
                $tenant,
                $oldValues,
                ['references' => $request->references],
                $user,
                $request
            );

            $profileData = $this->getProfileData($tenant);
            $completionStatus = $this->calculateProfileCompletion($tenant);

            return response()->json([
                'success' => true,
                'message' => 'References updated successfully',
                'data' => [
                    'profile' => $profileData,
                    'completion' => $completionStatus,
                ],
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Error updating references',
                'error' => $e->getMessage(),
            ], 500);
        }
    }

    /**
     * Get profile completion status
     */
    public function getCompletionStatus(Request $request): JsonResponse
    {
        try {
            $user = Auth::user();
            $tenant = $user->tenant ?? Tenant::where('user_id', $user->id)->first();

            if (!$tenant) {
                return response()->json([
                    'success' => false,
                    'message' => 'Tenant profile not found',
                ], 404);
            }

            $completionStatus = $this->calculateProfileCompletion($tenant);

            return response()->json([
                'success' => true,
                'data' => $completionStatus,
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Error fetching completion status',
                'error' => $e->getMessage(),
            ], 500);
        }
    }

    /**
     * Get profile requirements and validation rules
     */
    public function getRequirements(): JsonResponse
    {
        try {
            $requirements = [
                'personal_info' => [
                    'required_fields' => ['occupation', 'monthly_income'],
                    'optional_fields' => ['company_name', 'company_address', 'move_in_date', 'special_requirements'],
                    'validation_rules' => [
                        'occupation' => 'required|string|max:100',
                        'company_name' => 'nullable|string|max:100',
                        'company_address' => 'nullable|string|max:500',
                        'monthly_income' => 'required|numeric|min:0',
                        'move_in_date' => 'nullable|date|after_or_equal:today',
                        'special_requirements' => 'nullable|string|max:1000',
                    ],
                ],
                'family_details' => [
                    'required_fields' => ['family_members'],
                    'optional_fields' => ['family_details'],
                    'validation_rules' => [
                        'family_members' => 'required|integer|min:0|max:20',
                        'family_details' => 'nullable|array',
                    ],
                ],
                'emergency_contacts' => [
                    'required_count' => 1,
                    'max_count' => 5,
                    'required_fields' => ['name', 'relationship', 'primary_phone'],
                ],
                'kyc_documents' => [
                    'required_types' => ['id_proof', 'address_proof', 'income_proof'],
                    'optional_types' => ['photo', 'bank_statement', 'other'],
                ],
                'references' => [
                    'required_count' => 2,
                    'max_count' => 3,
                    'required_fields' => ['name', 'relationship', 'phone'],
                ],
                'preferences' => [
                    'categories' => ['notifications', 'privacy', 'communication', 'maintenance'],
                    'optional' => true,
                ],
            ];

            return response()->json([
                'success' => true,
                'data' => $requirements,
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Error fetching requirements',
                'error' => $e->getMessage(),
            ], 500);
        }
    }

    /**
     * Get profile statistics for admin
     */
    public function getProfileStatistics(Request $request): JsonResponse
    {
        try {
            $request->validate([
                'period' => 'sometimes|integer|min:1|max:365',
            ]);

            $period = $request->input('period', 30);

            // Get all tenants
            $totalTenants = Tenant::count();
            
            // Profile completion statistics
            $completionStats = [];
            $tenants = Tenant::with(['user', 'emergencyContacts', 'documents'])->get();
            
            $completionLevels = [
                'incomplete' => 0,
                'basic' => 0,
                'partial' => 0,
                'complete' => 0,
            ];

            $averageCompletion = 0;
            $fieldCompletionStats = [
                'personal_info' => 0,
                'family_details' => 0,
                'emergency_contacts' => 0,
                'kyc_documents' => 0,
                'references' => 0,
                'preferences' => 0,
            ];

            foreach ($tenants as $tenant) {
                $completion = $this->calculateProfileCompletion($tenant);
                $averageCompletion += $completion['overall_percentage'];

                // Categorize completion level
                if ($completion['overall_percentage'] >= 90) {
                    $completionLevels['complete']++;
                } elseif ($completion['overall_percentage'] >= 70) {
                    $completionLevels['partial']++;
                } elseif ($completion['overall_percentage'] >= 40) {
                    $completionLevels['basic']++;
                } else {
                    $completionLevels['incomplete']++;
                }

                // Field completion stats
                foreach ($completion['sections'] as $section => $data) {
                    if (isset($fieldCompletionStats[$section])) {
                        $fieldCompletionStats[$section] += $data['completion_percentage'];
                    }
                }
            }

            $averageCompletion = $totalTenants > 0 ? round($averageCompletion / $totalTenants, 2) : 0;

            // Calculate field averages
            foreach ($fieldCompletionStats as $field => $total) {
                $fieldCompletionStats[$field] = $totalTenants > 0 ? round($total / $totalTenants, 2) : 0;
            }

            return response()->json([
                'success' => true,
                'data' => [
                    'overview' => [
                        'total_tenants' => $totalTenants,
                        'average_completion' => $averageCompletion,
                        'completion_levels' => $completionLevels,
                    ],
                    'field_completion' => $fieldCompletionStats,
                    'period_days' => $period,
                ],
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Error fetching profile statistics',
                'error' => $e->getMessage(),
            ], 500);
        }
    }

    /**
     * Get formatted profile data
     */
    private function getProfileData(Tenant $tenant): array
    {
        return [
            'id' => $tenant->id,
            'tenant_code' => $tenant->tenant_code,
            'user' => [
                'id' => $tenant->user->id,
                'name' => $tenant->user->name,
                'email' => $tenant->user->email,
                'phone' => $tenant->user->phone,
                'first_name' => $tenant->user->first_name,
                'last_name' => $tenant->user->last_name,
                'date_of_birth' => $tenant->user->date_of_birth,
                'gender' => $tenant->user->gender,
                'address' => $tenant->user->address,
                'city' => $tenant->user->city,
                'state' => $tenant->user->state,
                'country' => $tenant->user->country,
                'pincode' => $tenant->user->pincode,
            ],
            'personal_info' => [
                'occupation' => $tenant->occupation,
                'company_name' => $tenant->company_name,
                'company_address' => $tenant->company_address,
                'monthly_income' => $tenant->monthly_income,
                'move_in_date' => $tenant->move_in_date?->toDateString(),
                'special_requirements' => $tenant->special_requirements,
            ],
            'family_details' => [
                'family_members' => $tenant->family_members,
                'family_details' => $tenant->family_details,
            ],
            'emergency_contacts' => $tenant->emergencyContacts->map(function ($contact) {
                return [
                    'id' => $contact->id,
                    'name' => $contact->name,
                    'relationship' => $contact->relationship,
                    'primary_phone' => $contact->primary_phone,
                    'secondary_phone' => $contact->secondary_phone,
                    'email' => $contact->email,
                    'is_primary' => $contact->is_primary,
                    'priority' => $contact->priority,
                ];
            }),
            'references' => $tenant->references,
            'preferences' => $tenant->preferences,
            'status_info' => [
                'status' => $tenant->status,
                'kyc_status' => $tenant->kyc_status,
                'background_check_status' => $tenant->background_check_status,
                'police_verification_status' => $tenant->police_verification_status,
            ],
            'unit_info' => $tenant->unit ? [
                'id' => $tenant->unit->id,
                'unit_number' => $tenant->unit->unit_number,
                'floor' => $tenant->unit->floor,
                'type' => $tenant->unit->type,
            ] : null,
            'created_at' => $tenant->created_at->toISOString(),
            'updated_at' => $tenant->updated_at->toISOString(),
        ];
    }

    /**
     * Calculate profile completion percentage
     */
    private function calculateProfileCompletion(Tenant $tenant): array
    {
        $sections = [
            'personal_info' => [
                'weight' => 25,
                'required_fields' => ['occupation', 'monthly_income'],
                'optional_fields' => ['company_name', 'company_address', 'move_in_date'],
                'completed' => 0,
                'total' => 0,
            ],
            'family_details' => [
                'weight' => 15,
                'required_fields' => ['family_members'],
                'optional_fields' => ['family_details'],
                'completed' => 0,
                'total' => 0,
            ],
            'emergency_contacts' => [
                'weight' => 20,
                'required_count' => 1,
                'completed' => 0,
                'total' => 1,
            ],
            'kyc_documents' => [
                'weight' => 25,
                'required_types' => ['id_proof', 'address_proof', 'income_proof'],
                'completed' => 0,
                'total' => 3,
            ],
            'references' => [
                'weight' => 10,
                'required_count' => 2,
                'completed' => 0,
                'total' => 2,
            ],
            'preferences' => [
                'weight' => 5,
                'optional' => true,
                'completed' => 0,
                'total' => 1,
            ],
        ];

        // Calculate personal info completion
        $personalTotal = count($sections['personal_info']['required_fields']) + count($sections['personal_info']['optional_fields']);
        $personalCompleted = 0;
        
        foreach ($sections['personal_info']['required_fields'] as $field) {
            if (!empty($tenant->$field)) $personalCompleted++;
        }
        foreach ($sections['personal_info']['optional_fields'] as $field) {
            if (!empty($tenant->$field)) $personalCompleted++;
        }
        
        $sections['personal_info']['completed'] = $personalCompleted;
        $sections['personal_info']['total'] = $personalTotal;
        $sections['personal_info']['completion_percentage'] = $personalTotal > 0 ? round(($personalCompleted / $personalTotal) * 100, 2) : 0;

        // Calculate family details completion
        $familyTotal = count($sections['family_details']['required_fields']) + count($sections['family_details']['optional_fields']);
        $familyCompleted = 0;
        
        foreach ($sections['family_details']['required_fields'] as $field) {
            if (!is_null($tenant->$field)) $familyCompleted++;
        }
        foreach ($sections['family_details']['optional_fields'] as $field) {
            if (!empty($tenant->$field)) $familyCompleted++;
        }
        
        $sections['family_details']['completed'] = $familyCompleted;
        $sections['family_details']['total'] = $familyTotal;
        $sections['family_details']['completion_percentage'] = $familyTotal > 0 ? round(($familyCompleted / $familyTotal) * 100, 2) : 0;

        // Calculate emergency contacts completion
        $emergencyContactsCount = $tenant->emergencyContacts->count();
        $sections['emergency_contacts']['completed'] = min($emergencyContactsCount, $sections['emergency_contacts']['required_count']);
        $sections['emergency_contacts']['completion_percentage'] = $emergencyContactsCount >= $sections['emergency_contacts']['required_count'] ? 100 : round(($emergencyContactsCount / $sections['emergency_contacts']['required_count']) * 100, 2);

        // Calculate KYC documents completion
        $kycDocuments = $tenant->documents()->where('document_type', 'kyc')->get();
        $kycTypes = $kycDocuments->pluck('category')->unique();
        $kycCompleted = 0;
        
        foreach ($sections['kyc_documents']['required_types'] as $type) {
            if ($kycTypes->contains($type)) $kycCompleted++;
        }
        
        $sections['kyc_documents']['completed'] = $kycCompleted;
        $sections['kyc_documents']['completion_percentage'] = round(($kycCompleted / $sections['kyc_documents']['total']) * 100, 2);

        // Calculate references completion
        $referencesCount = is_array($tenant->references) ? count($tenant->references) : 0;
        $sections['references']['completed'] = min($referencesCount, $sections['references']['required_count']);
        $sections['references']['completion_percentage'] = $referencesCount >= $sections['references']['required_count'] ? 100 : round(($referencesCount / $sections['references']['required_count']) * 100, 2);

        // Calculate preferences completion
        $sections['preferences']['completed'] = !empty($tenant->preferences) ? 1 : 0;
        $sections['preferences']['completion_percentage'] = !empty($tenant->preferences) ? 100 : 0;

        // Calculate overall completion
        $totalWeight = 0;
        $weightedCompletion = 0;
        
        foreach ($sections as $section => $data) {
            $totalWeight += $data['weight'];
            $weightedCompletion += ($data['completion_percentage'] / 100) * $data['weight'];
        }
        
        $overallPercentage = $totalWeight > 0 ? round(($weightedCompletion / $totalWeight) * 100, 2) : 0;

        // Determine completion status
        $status = 'incomplete';
        if ($overallPercentage >= 90) {
            $status = 'complete';
        } elseif ($overallPercentage >= 70) {
            $status = 'partial';
        } elseif ($overallPercentage >= 40) {
            $status = 'basic';
        }

        // Get next steps
        $nextSteps = [];
        foreach ($sections as $sectionName => $data) {
            if ($data['completion_percentage'] < 100) {
                switch ($sectionName) {
                    case 'personal_info':
                        $nextSteps[] = 'Complete personal information (occupation, income details)';
                        break;
                    case 'family_details':
                        $nextSteps[] = 'Add family member details';
                        break;
                    case 'emergency_contacts':
                        $nextSteps[] = 'Add at least one emergency contact';
                        break;
                    case 'kyc_documents':
                        $nextSteps[] = 'Upload required KYC documents (ID, address, income proof)';
                        break;
                    case 'references':
                        $nextSteps[] = 'Provide at least 2 character references';
                        break;
                    case 'preferences':
                        $nextSteps[] = 'Set your communication and notification preferences';
                        break;
                }
            }
        }

        return [
            'overall_percentage' => $overallPercentage,
            'status' => $status,
            'sections' => $sections,
            'next_steps' => $nextSteps,
            'is_complete' => $overallPercentage >= 90,
            'missing_required_fields' => count($nextSteps),
        ];
    }
}
