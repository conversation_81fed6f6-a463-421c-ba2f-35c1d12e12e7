<?php

namespace App\Http\Controllers;

use App\Models\NotificationTemplate;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\Auth;
use Illuminate\Validation\Rule;

class NotificationTemplateController extends Controller
{
    /**
     * Display a listing of notification templates
     */
    public function index(Request $request): JsonResponse
    {
        $query = NotificationTemplate::query();

        // Filter by type
        if ($request->filled('type')) {
            $query->byType($request->type);
        }

        // Filter by category
        if ($request->filled('category')) {
            $query->byCategory($request->category);
        }

        // Filter by active status
        if ($request->boolean('active_only', true)) {
            $query->active();
        }

        $templates = $query->orderBy('created_at', 'desc')->get();

        return response()->json([
            'success' => true,
            'data' => $templates,
            'message' => 'Notification templates retrieved successfully'
        ]);
    }

    /**
     * Store a newly created notification template
     */
    public function store(Request $request): JsonResponse
    {
        $user = Auth::user();
        
        // Only admin/manager can create templates
        if (!$user->hasRole(['admin', 'manager'])) {
            return response()->json([
                'success' => false,
                'message' => 'Unauthorized. Only admin or manager can create notification templates.'
            ], 403);
        }

        $validated = $request->validate([
            'name' => 'required|string|max:255|unique:notification_templates',
            'type' => ['required', Rule::in(NotificationTemplate::getTypes())],
            'category' => ['required', Rule::in(NotificationTemplate::getCategories())],
            'title' => 'required|string|max:255',
            'content' => 'required|string',
            'variables' => 'nullable|array',
            'variables.*' => 'string',
            'channels' => 'nullable|array',
            'channels.*' => ['string', Rule::in(NotificationTemplate::getTypes())],
            'is_active' => 'boolean',
            'version' => 'nullable|string|max:20',
            'metadata' => 'nullable|array',
        ]);

        $template = NotificationTemplate::create($validated);

        return response()->json([
            'success' => true,
            'data' => $template,
            'message' => 'Notification template created successfully'
        ], 201);
    }

    /**
     * Display the specified notification template
     */
    public function show(NotificationTemplate $template): JsonResponse
    {
        return response()->json([
            'success' => true,
            'data' => $template,
            'message' => 'Notification template retrieved successfully'
        ]);
    }

    /**
     * Update the specified notification template
     */
    public function update(Request $request, NotificationTemplate $template): JsonResponse
    {
        $user = Auth::user();
        
        // Only admin/manager can update templates
        if (!$user->hasRole(['admin', 'manager'])) {
            return response()->json([
                'success' => false,
                'message' => 'Unauthorized. Only admin or manager can update notification templates.'
            ], 403);
        }

        $validated = $request->validate([
            'name' => ['sometimes', 'required', 'string', 'max:255', Rule::unique('notification_templates')->ignore($template->id)],
            'type' => ['sometimes', 'required', Rule::in(NotificationTemplate::getTypes())],
            'category' => ['sometimes', 'required', Rule::in(NotificationTemplate::getCategories())],
            'title' => 'sometimes|required|string|max:255',
            'content' => 'sometimes|required|string',
            'variables' => 'nullable|array',
            'variables.*' => 'string',
            'channels' => 'nullable|array',
            'channels.*' => ['string', Rule::in(NotificationTemplate::getTypes())],
            'is_active' => 'boolean',
            'version' => 'nullable|string|max:20',
            'metadata' => 'nullable|array',
        ]);

        $template->update($validated);

        return response()->json([
            'success' => true,
            'data' => $template->fresh(),
            'message' => 'Notification template updated successfully'
        ]);
    }

    /**
     * Remove the specified notification template
     */
    public function destroy(NotificationTemplate $template): JsonResponse
    {
        $user = Auth::user();
        
        // Only admin/manager can delete templates
        if (!$user->hasRole(['admin', 'manager'])) {
            return response()->json([
                'success' => false,
                'message' => 'Unauthorized. Only admin or manager can delete notification templates.'
            ], 403);
        }

        // Check if template is being used
        if ($template->notifications()->exists()) {
            return response()->json([
                'success' => false,
                'message' => 'Cannot delete template. It is being used by existing notifications.'
            ], 422);
        }

        $template->delete();

        return response()->json([
            'success' => true,
            'message' => 'Notification template deleted successfully'
        ]);
    }

    /**
     * Preview a notification template with sample data
     */
    public function preview(Request $request, NotificationTemplate $template): JsonResponse
    {
        $validated = $request->validate([
            'variables' => 'nullable|array',
            'channel' => ['nullable', Rule::in(NotificationTemplate::getTypes())],
        ]);

        $variables = $validated['variables'] ?? [];
        $channel = $validated['channel'] ?? $template->type;

        // Generate sample data if no variables provided
        if (empty($variables)) {
            $variables = $this->generateSampleVariables($template->category);
        }

        $preview = [
            'template' => $template,
            'rendered_title' => $this->renderTemplate($template->title, $variables),
            'rendered_content' => $this->renderTemplate($template->content, $variables),
            'variables_used' => $variables,
            'channel' => $channel,
        ];

        return response()->json([
            'success' => true,
            'data' => $preview,
            'message' => 'Template preview generated successfully'
        ]);
    }

    /**
     * Get available template types and categories
     */
    public function getOptions(): JsonResponse
    {
        return response()->json([
            'success' => true,
            'data' => [
                'types' => NotificationTemplate::getTypes(),
                'categories' => NotificationTemplate::getCategories(),
            ],
            'message' => 'Template options retrieved successfully'
        ]);
    }

    /**
     * Get templates by category
     */
    public function getByCategory(string $category): JsonResponse
    {
        $templates = NotificationTemplate::byCategory($category)
            ->active()
            ->orderBy('name')
            ->get();

        return response()->json([
            'success' => true,
            'data' => $templates,
            'message' => "Templates for category '{$category}' retrieved successfully"
        ]);
    }

    /**
     * Get templates by type
     */
    public function getByType(string $type): JsonResponse
    {
        $templates = NotificationTemplate::byType($type)
            ->active()
            ->orderBy('name')
            ->get();

        return response()->json([
            'success' => true,
            'data' => $templates,
            'message' => "Templates for type '{$type}' retrieved successfully"
        ]);
    }

    /**
     * Duplicate a notification template
     */
    public function duplicate(NotificationTemplate $template): JsonResponse
    {
        $user = Auth::user();
        
        // Only admin/manager can duplicate templates
        if (!$user->hasRole(['admin', 'manager'])) {
            return response()->json([
                'success' => false,
                'message' => 'Unauthorized. Only admin or manager can duplicate notification templates.'
            ], 403);
        }

        $newTemplate = $template->replicate();
        $newTemplate->name = $template->name . ' (Copy)';
        $newTemplate->is_active = false;
        $newTemplate->version = '1.0';
        $newTemplate->save();

        return response()->json([
            'success' => true,
            'data' => $newTemplate,
            'message' => 'Notification template duplicated successfully'
        ]);
    }

    /**
     * Get template statistics
     */
    public function statistics(): JsonResponse
    {
        $stats = [
            'total_templates' => NotificationTemplate::count(),
            'active_templates' => NotificationTemplate::active()->count(),
            'inactive_templates' => NotificationTemplate::inactive()->count(),
            'by_type' => [],
            'by_category' => [],
        ];

        // Statistics by type
        foreach (NotificationTemplate::getTypes() as $type) {
            $stats['by_type'][$type] = [
                'total' => NotificationTemplate::byType($type)->count(),
                'active' => NotificationTemplate::byType($type)->active()->count(),
            ];
        }

        // Statistics by category
        foreach (NotificationTemplate::getCategories() as $category) {
            $stats['by_category'][$category] = [
                'total' => NotificationTemplate::byCategory($category)->count(),
                'active' => NotificationTemplate::byCategory($category)->active()->count(),
            ];
        }

        return response()->json([
            'success' => true,
            'message' => 'Template statistics retrieved successfully',
            'data' => $stats
        ]);
    }

    /**
     * Render template with variables
     */
    private function renderTemplate(string $template, array $variables): string
    {
        foreach ($variables as $key => $value) {
            $template = str_replace("{{" . $key . "}}", $value, $template);
        }
        
        return $template;
    }

    /**
     * Generate sample variables based on template category
     */
    private function generateSampleVariables(string $category): array
    {
        $samples = [
            'system' => [
                'user_name' => 'John Doe',
                'system_name' => 'TMS',
                'action' => 'login',
                'timestamp' => now()->format('Y-m-d H:i:s'),
            ],
            'tenant' => [
                'tenant_name' => 'John Doe',
                'unit_number' => 'A-101',
                'action' => 'registration',
                'status' => 'approved',
            ],
            'unit' => [
                'unit_number' => 'A-101',
                'owner_name' => 'Jane Smith',
                'tenant_name' => 'John Doe',
                'status' => 'occupied',
            ],
            'enquiry' => [
                'enquirer_name' => 'Alice Johnson',
                'unit_number' => 'B-205',
                'enquiry_type' => 'rental',
                'message' => 'I am interested in this unit',
            ],
            'noc' => [
                'applicant_name' => 'Bob Wilson',
                'noc_type' => 'rental',
                'unit_number' => 'C-301',
                'status' => 'approved',
            ],
            'agreement' => [
                'tenant_name' => 'John Doe',
                'owner_name' => 'Jane Smith',
                'unit_number' => 'A-101',
                'agreement_number' => 'AGR-2024-001',
                'start_date' => '2024-01-01',
                'end_date' => '2024-12-31',
            ],
            'billing' => [
                'tenant_name' => 'John Doe',
                'unit_number' => 'A-101',
                'amount' => '5000.00',
                'due_date' => '2024-02-01',
                'bill_type' => 'rent',
            ],
            'maintenance' => [
                'tenant_name' => 'John Doe',
                'unit_number' => 'A-101',
                'issue_type' => 'plumbing',
                'priority' => 'high',
                'description' => 'Leaking faucet in kitchen',
            ],
        ];

        return $samples[$category] ?? [
            'user_name' => 'Sample User',
            'action' => 'sample_action',
            'timestamp' => now()->format('Y-m-d H:i:s'),
        ];
    }
} 