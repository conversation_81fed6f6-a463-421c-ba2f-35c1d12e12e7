<?php

namespace App\Http\Controllers;

use App\Models\RentPayment;
use App\Models\Unit;
use App\Models\User;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Log;

class PaymentController extends Controller
{
    /**
     * List all payments with filtering and pagination.
     *
     * @param Request $request
     * @return JsonResponse
     */
    public function index(Request $request): JsonResponse
    {
        $request->validate([
            'unit_id' => 'nullable|exists:units,id',
            'tenant_id' => 'nullable|exists:users,id',
            'status' => 'nullable|in:pending,completed,failed,cancelled',
            'payment_method' => 'nullable|string',
            'start_date' => 'nullable|date',
            'end_date' => 'nullable|date|after_or_equal:start_date',
            'search' => 'nullable|string|max:255',
            'per_page' => 'nullable|integer|min:1|max:100',
        ]);

        $query = RentPayment::with([
            'unit:id,unit_number',
            'tenant:id,name,email'
        ]);

        // Apply filters
        if ($request->filled('unit_id')) {
            $query->where('unit_id', $request->unit_id);
        }

        if ($request->filled('tenant_id')) {
            $query->where('tenant_id', $request->tenant_id);
        }

        if ($request->filled('status')) {
            $query->where('status', $request->status);
        }

        if ($request->filled('payment_method')) {
            $query->where('payment_method', 'like', '%' . $request->payment_method . '%');
        }

        if ($request->filled('start_date')) {
            $query->where('payment_date', '>=', $request->start_date);
        }

        if ($request->filled('end_date')) {
            $query->where('payment_date', '<=', $request->end_date);
        }

        if ($request->filled('search')) {
            $query->where(function ($q) use ($request) {
                $q->where('external_reference', 'like', '%' . $request->search . '%')
                  ->orWhere('notes', 'like', '%' . $request->search . '%')
                  ->orWhereHas('unit', function ($unitQuery) use ($request) {
                      $unitQuery->where('unit_number', 'like', '%' . $request->search . '%');
                  })
                  ->orWhereHas('tenant', function ($tenantQuery) use ($request) {
                      $tenantQuery->where('name', 'like', '%' . $request->search . '%');
                  });
            });
        }

        $payments = $query->orderBy('payment_date', 'desc')
                         ->paginate($request->get('per_page', 15));

        return response()->json([
            'success' => true,
            'data' => $payments,
            'message' => 'Payments retrieved successfully',
        ]);
    }

    /**
     * Create a new payment record.
     *
     * @param Request $request
     * @return JsonResponse
     */
    public function store(Request $request): JsonResponse
    {
        $validated = $request->validate([
            'unit_id' => 'required|exists:units,id',
            'tenant_id' => 'required|exists:users,id',
            'amount' => 'required|numeric|min:0',
            'payment_date' => 'required|date',
            'payment_method' => 'nullable|string|max:255',
            'external_reference' => 'nullable|string|max:255|unique:rent_payments,external_reference',
            'status' => 'nullable|in:pending,completed,failed,cancelled',
            'notes' => 'nullable|string',
            'metadata' => 'nullable|array',
        ]);

        try {
            $payment = RentPayment::create([
                ...$validated,
                'status' => $validated['status'] ?? 'pending',
            ]);

            return response()->json([
                'success' => true,
                'message' => 'Payment created successfully',
                'data' => $payment->load(['unit:id,unit_number', 'tenant:id,name,email']),
            ], 201);

        } catch (\Exception $e) {
            Log::error('Failed to create payment', [
                'error' => $e->getMessage(),
                'data' => $validated,
            ]);

            return response()->json([
                'success' => false,
                'message' => 'Failed to create payment',
                'error' => $e->getMessage(),
            ], 500);
        }
    }

    /**
     * Show a specific payment.
     *
     * @param RentPayment $payment
     * @return JsonResponse
     */
    public function show(RentPayment $payment): JsonResponse
    {
        return response()->json([
            'success' => true,
            'data' => $payment->load(['unit:id,unit_number', 'tenant:id,name,email']),
            'message' => 'Payment retrieved successfully',
        ]);
    }

    /**
     * Update a payment record.
     *
     * @param Request $request
     * @param RentPayment $payment
     * @return JsonResponse
     */
    public function update(Request $request, RentPayment $payment): JsonResponse
    {
        $validated = $request->validate([
            'amount' => 'sometimes|numeric|min:0',
            'payment_date' => 'sometimes|date',
            'payment_method' => 'sometimes|string|max:255',
            'external_reference' => 'sometimes|string|max:255|unique:rent_payments,external_reference,' . $payment->id,
            'status' => 'sometimes|in:pending,completed,failed,cancelled',
            'notes' => 'sometimes|string',
            'metadata' => 'sometimes|array',
        ]);

        try {
            $payment->update($validated);

            return response()->json([
                'success' => true,
                'message' => 'Payment updated successfully',
                'data' => $payment->fresh()->load(['unit:id,unit_number', 'tenant:id,name,email']),
            ]);

        } catch (\Exception $e) {
            Log::error('Failed to update payment', [
                'payment_id' => $payment->id,
                'error' => $e->getMessage(),
                'data' => $validated,
            ]);

            return response()->json([
                'success' => false,
                'message' => 'Failed to update payment',
                'error' => $e->getMessage(),
            ], 500);
        }
    }

    /**
     * Delete a payment record.
     *
     * @param RentPayment $payment
     * @return JsonResponse
     */
    public function destroy(RentPayment $payment): JsonResponse
    {
        try {
            $payment->delete();

            return response()->json([
                'success' => true,
                'message' => 'Payment deleted successfully',
            ]);

        } catch (\Exception $e) {
            Log::error('Failed to delete payment', [
                'payment_id' => $payment->id,
                'error' => $e->getMessage(),
            ]);

            return response()->json([
                'success' => false,
                'message' => 'Failed to delete payment',
                'error' => $e->getMessage(),
            ], 500);
        }
    }

    /**
     * Get payment statistics.
     *
     * @param Request $request
     * @return JsonResponse
     */
    public function statistics(Request $request): JsonResponse
    {
        $request->validate([
            'start_date' => 'nullable|date',
            'end_date' => 'nullable|date|after_or_equal:start_date',
        ]);

        $query = RentPayment::query();

        if ($request->filled('start_date')) {
            $query->where('payment_date', '>=', $request->start_date);
        }

        if ($request->filled('end_date')) {
            $query->where('payment_date', '<=', $request->end_date);
        }

        $stats = [
            'total_payments' => $query->count(),
            'total_amount' => $query->sum('amount'),
            'completed_payments' => $query->where('status', 'completed')->count(),
            'pending_payments' => $query->where('status', 'pending')->count(),
            'failed_payments' => $query->where('status', 'failed')->count(),
            'cancelled_payments' => $query->where('status', 'cancelled')->count(),
            'completed_amount' => $query->where('status', 'completed')->sum('amount'),
            'pending_amount' => $query->where('status', 'pending')->sum('amount'),
            'average_payment_amount' => $query->avg('amount'),
        ];

        return response()->json([
            'success' => true,
            'data' => $stats,
            'message' => 'Payment statistics retrieved successfully',
        ]);
    }

    /**
     * Get payment history for a specific unit.
     *
     * @param Request $request
     * @param Unit $unit
     * @return JsonResponse
     */
    public function getUnitPayments(Request $request, Unit $unit): JsonResponse
    {
        $request->validate([
            'status' => 'nullable|in:pending,completed,failed,cancelled',
            'start_date' => 'nullable|date',
            'end_date' => 'nullable|date|after_or_equal:start_date',
            'per_page' => 'nullable|integer|min:1|max:100',
        ]);

        $query = RentPayment::where('unit_id', $unit->id)
            ->with(['tenant:id,name,email']);

        // Apply filters
        if ($request->filled('status')) {
            $query->where('status', $request->status);
        }

        if ($request->filled('start_date')) {
            $query->where('payment_date', '>=', $request->start_date);
        }

        if ($request->filled('end_date')) {
            $query->where('payment_date', '<=', $request->end_date);
        }

        $perPage = $request->get('per_page', 15);
        $payments = $query->orderBy('payment_date', 'desc')
            ->paginate($perPage);

        return response()->json([
            'data' => $payments->items(),
            'pagination' => [
                'current_page' => $payments->currentPage(),
                'last_page' => $payments->lastPage(),
                'per_page' => $payments->perPage(),
                'total' => $payments->total(),
            ],
        ]);
    }

    /**
     * Get payment history for a specific tenant.
     *
     * @param Request $request
     * @param User $tenant
     * @return JsonResponse
     */
    public function getTenantPayments(Request $request, User $tenant): JsonResponse
    {
        $request->validate([
            'status' => 'nullable|in:pending,completed,failed,cancelled',
            'start_date' => 'nullable|date',
            'end_date' => 'nullable|date|after_or_equal:start_date',
            'per_page' => 'nullable|integer|min:1|max:100',
        ]);

        $query = RentPayment::where('tenant_id', $tenant->id)
            ->with(['unit:id,unit_number']);

        // Apply filters
        if ($request->filled('status')) {
            $query->where('status', $request->status);
        }

        if ($request->filled('start_date')) {
            $query->where('payment_date', '>=', $request->start_date);
        }

        if ($request->filled('end_date')) {
            $query->where('payment_date', '<=', $request->end_date);
        }

        $perPage = $request->get('per_page', 15);
        $payments = $query->orderBy('payment_date', 'desc')
            ->paginate($perPage);

        return response()->json([
            'data' => $payments->items(),
            'pagination' => [
                'current_page' => $payments->currentPage(),
                'last_page' => $payments->lastPage(),
                'per_page' => $payments->perPage(),
                'total' => $payments->total(),
            ],
        ]);
    }

    /**
     * Get payment statistics for a unit.
     *
     * @param Unit $unit
     * @return JsonResponse
     */
    public function getUnitPaymentStats(Unit $unit): JsonResponse
    {
        $stats = RentPayment::where('unit_id', $unit->id)
            ->selectRaw('
                COUNT(*) as total_payments,
                SUM(CASE WHEN status = "completed" THEN amount ELSE 0 END) as total_paid,
                SUM(CASE WHEN status = "pending" THEN amount ELSE 0 END) as total_pending,
                SUM(CASE WHEN status = "failed" THEN amount ELSE 0 END) as total_failed
            ')
            ->first();

        return response()->json([
            'data' => $stats,
        ]);
    }

    /**
     * Get payment statistics for a tenant.
     *
     * @param User $tenant
     * @return JsonResponse
     */
    public function getTenantPaymentStats(User $tenant): JsonResponse
    {
        $stats = RentPayment::where('tenant_id', $tenant->id)
            ->selectRaw('
                COUNT(*) as total_payments,
                SUM(CASE WHEN status = "completed" THEN amount ELSE 0 END) as total_paid,
                SUM(CASE WHEN status = "pending" THEN amount ELSE 0 END) as total_pending,
                SUM(CASE WHEN status = "failed" THEN amount ELSE 0 END) as total_failed
            ')
            ->first();

        return response()->json([
            'data' => $stats,
        ]);
    }
}
