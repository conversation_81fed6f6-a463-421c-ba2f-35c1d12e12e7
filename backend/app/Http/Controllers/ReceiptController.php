<?php

namespace App\Http\Controllers;

use App\Models\RentReceipt;
use App\Models\RentPayment;
use App\Services\ReceiptGenerationService;
use App\Services\NotificationService;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\DB;
use Carbon\Carbon;

class ReceiptController extends Controller
{
    protected $receiptGenerationService;
    protected $notificationService;

    public function __construct(
        ReceiptGenerationService $receiptGenerationService,
        NotificationService $notificationService
    ) {
        $this->receiptGenerationService = $receiptGenerationService;
        $this->notificationService = $notificationService;
    }

    /**
     * Generate receipt for a payment.
     *
     * @param Request $request
     * @param RentPayment $payment
     * @return JsonResponse
     */
    public function generateReceipt(Request $request, RentPayment $payment): JsonResponse
    {
        $request->validate([
            'recipient_email' => 'required|email',
            'template' => 'nullable|string',
        ]);

        try {
            $template = $request->get('template', 'receipts.receipt');
            $recipientEmail = $request->recipient_email;

            $receipt = $this->receiptGenerationService->generateReceiptWithTemplate(
                $payment,
                $recipientEmail,
                $template
            );

            // Log receipt generation
            $receipt->auditLogs()->create([
                'action' => 'receipt_generated',
                'description' => 'Receipt generated for payment',
                'user_id' => auth()->id(),
                'metadata' => [
                    'recipient_email' => $recipientEmail,
                    'template' => $template,
                ],
            ]);

            return response()->json([
                'message' => 'Receipt generated successfully',
                'data' => $receipt->load(['payment.tenant', 'payment.unit.property']),
            ], 201);

        } catch (\Exception $e) {
            Log::error('Failed to generate receipt', [
                'payment_id' => $payment->id,
                'error' => $e->getMessage(),
            ]);

            return response()->json([
                'message' => 'Failed to generate receipt',
                'error' => $e->getMessage(),
            ], 500);
        }
    }

    /**
     * Get receipt by ID.
     *
     * @param RentReceipt $receipt
     * @return JsonResponse
     */
    public function getReceipt(RentReceipt $receipt): JsonResponse
    {
        $receipt->load(['payment.tenant', 'payment.unit.property', 'auditLogs']);

        return response()->json([
            'data' => $receipt,
        ]);
    }

    /**
     * Download receipt PDF.
     *
     * @param RentReceipt $receipt
     * @return \Illuminate\Http\Response
     */
    public function downloadReceipt(RentReceipt $receipt)
    {
        if (!$receipt->pdf_path || !Storage::disk('public')->exists($receipt->pdf_path)) {
            return response()->json([
                'message' => 'Receipt PDF not found',
            ], 404);
        }

        $pdfContent = Storage::disk('public')->get($receipt->pdf_path);

        return response($pdfContent, 200, [
            'Content-Type' => 'application/pdf',
            'Content-Disposition' => 'attachment; filename="' . $receipt->receipt_number . '.pdf"',
        ]);
    }

    /**
     * List receipts with filtering and pagination.
     *
     * @param Request $request
     * @return JsonResponse
     */
    public function listReceipts(Request $request): JsonResponse
    {
        $request->validate([
            'payment_id' => 'nullable|exists:rent_payments,id',
            'tenant_id' => 'nullable|exists:users,id',
            'unit_id' => 'nullable|exists:units,id',
            'status' => 'nullable|in:pending,delivered,failed',
            'start_date' => 'nullable|date',
            'end_date' => 'nullable|date|after_or_equal:start_date',
            'per_page' => 'nullable|integer|min:1|max:100',
        ]);

        $query = RentReceipt::with(['payment.tenant', 'payment.unit']);

        // Apply filters
        if ($request->filled('payment_id')) {
            $query->where('payment_id', $request->payment_id);
        }

        if ($request->filled('tenant_id')) {
            $query->whereHas('payment', function ($q) use ($request) {
                $q->where('tenant_id', $request->tenant_id);
            });
        }

        if ($request->filled('unit_id')) {
            $query->whereHas('payment', function ($q) use ($request) {
                $q->where('unit_id', $request->unit_id);
            });
        }

        if ($request->filled('status')) {
            $query->where('delivery_status', $request->status);
        }

        if ($request->filled('start_date')) {
            $query->where('generated_at', '>=', $request->start_date);
        }

        if ($request->filled('end_date')) {
            $query->where('generated_at', '<=', $request->end_date);
        }

        $perPage = $request->get('per_page', 15);
        $receipts = $query->orderBy('generated_at', 'desc')
            ->paginate($perPage);

        return response()->json([
            'data' => $receipts->items(),
            'pagination' => [
                'current_page' => $receipts->currentPage(),
                'last_page' => $receipts->lastPage(),
                'per_page' => $receipts->perPage(),
                'total' => $receipts->total(),
            ],
        ]);
    }

    /**
     * Deliver receipt via email.
     *
     * @param Request $request
     * @param RentReceipt $receipt
     * @return JsonResponse
     */
    public function deliverReceipt(Request $request, RentReceipt $receipt): JsonResponse
    {
        $request->validate([
            'recipient_email' => 'nullable|email',
        ]);

        try {
            $recipientEmail = $request->get('recipient_email', $receipt->recipient_email);

            // Send email notification with receipt
            $this->notificationService->sendReceiptNotification(
                $receipt,
                $recipientEmail
            );

            // Update delivery status
            $receipt->update([
                'delivery_status' => 'delivered',
                'delivered_at' => now(),
            ]);

            // Log delivery
            $receipt->auditLogs()->create([
                'action' => 'receipt_delivered',
                'description' => 'Receipt delivered via email',
                'user_id' => auth()->id(),
                'metadata' => [
                    'recipient_email' => $recipientEmail,
                ],
            ]);

            return response()->json([
                'message' => 'Receipt delivered successfully',
                'data' => $receipt->fresh(),
            ]);

        } catch (\Exception $e) {
            Log::error('Failed to deliver receipt', [
                'receipt_id' => $receipt->id,
                'error' => $e->getMessage(),
            ]);

            // Update delivery status to failed
            $receipt->update([
                'delivery_status' => 'failed',
            ]);

            return response()->json([
                'message' => 'Failed to deliver receipt',
                'error' => $e->getMessage(),
            ], 500);
        }
    }

    /**
     * Regenerate receipt PDF.
     *
     * @param RentReceipt $receipt
     * @return JsonResponse
     */
    public function regenerateReceipt(RentReceipt $receipt): JsonResponse
    {
        try {
            $success = $this->receiptGenerationService->regeneratePdf($receipt);

            if ($success) {
                // Log regeneration
                $receipt->auditLogs()->create([
                    'action' => 'receipt_regenerated',
                    'description' => 'Receipt PDF regenerated',
                    'user_id' => auth()->id(),
                ]);

                return response()->json([
                    'message' => 'Receipt regenerated successfully',
                    'data' => $receipt->fresh(),
                ]);
            } else {
                return response()->json([
                    'message' => 'Failed to regenerate receipt',
                ], 500);
            }

        } catch (\Exception $e) {
            Log::error('Failed to regenerate receipt', [
                'receipt_id' => $receipt->id,
                'error' => $e->getMessage(),
            ]);

            return response()->json([
                'message' => 'Failed to regenerate receipt',
                'error' => $e->getMessage(),
            ], 500);
        }
    }

    /**
     * Verify receipt by verification code.
     *
     * @param Request $request
     * @return JsonResponse
     */
    public function verifyReceipt(Request $request): JsonResponse
    {
        $request->validate([
            'code' => 'required|string',
        ]);

        $receipt = RentReceipt::where('verification_code', $request->code)
            ->with(['payment.tenant', 'payment.unit'])
            ->first();

        if (!$receipt) {
            return response()->json([
                'message' => 'Invalid verification code',
            ], 404);
        }

        // Log verification
        $receipt->auditLogs()->create([
            'action' => 'receipt_verified',
            'description' => 'Receipt verified via QR code',
            'user_id' => auth()->id(),
            'metadata' => [
                'ip_address' => $request->ip(),
                'user_agent' => $request->userAgent(),
            ],
        ]);

        return response()->json([
            'message' => 'Receipt verified successfully',
            'data' => $receipt,
        ]);
    }

    /**
     * Get receipt analytics and detailed statistics.
     *
     * @param Request $request
     * @return JsonResponse
     */
    public function getReceiptAnalytics(Request $request): JsonResponse
    {
        $request->validate([
            'start_date' => 'nullable|date',
            'end_date' => 'nullable|date|after_or_equal:start_date',
            'group_by' => 'nullable|in:day,week,month',
        ]);

        try {
            $startDate = $request->start_date ? Carbon::parse($request->start_date) : Carbon::now()->subMonths(6);
            $endDate = $request->end_date ? Carbon::parse($request->end_date) : Carbon::now();
            $groupBy = $request->group_by ?? 'month';

            $query = RentReceipt::whereBetween('created_at', [$startDate, $endDate]);

            // Group by time period
            $groupByFormat = match($groupBy) {
                'day' => '%Y-%m-%d',
                'week' => '%Y-%u',
                'month' => '%Y-%m',
                default => '%Y-%m',
            };

            $timeSeriesData = $query->select(
                DB::raw("DATE_FORMAT(created_at, '{$groupByFormat}') as period"),
                DB::raw('COUNT(*) as receipt_count'),
                DB::raw('SUM(CASE WHEN status = "delivered" THEN 1 ELSE 0 END) as delivered_count'),
                DB::raw('SUM(CASE WHEN status = "failed" THEN 1 ELSE 0 END) as failed_count')
            )
            ->groupBy('period')
            ->orderBy('period')
            ->get();

            // Get overall statistics
            $totalReceipts = RentReceipt::whereBetween('created_at', [$startDate, $endDate])->count();
            $deliveredReceipts = RentReceipt::where('status', 'delivered')
                ->whereBetween('created_at', [$startDate, $endDate])->count();
            $failedReceipts = RentReceipt::where('status', 'failed')
                ->whereBetween('created_at', [$startDate, $endDate])->count();
            $pendingReceipts = RentReceipt::where('status', 'pending')
                ->whereBetween('created_at', [$startDate, $endDate])->count();

            $deliveryRate = $totalReceipts > 0 ? round(($deliveredReceipts / $totalReceipts) * 100, 2) : 0;

            return response()->json([
                'success' => true,
                'message' => 'Receipt analytics retrieved successfully',
                'data' => [
                    'overview' => [
                        'total_receipts' => $totalReceipts,
                        'delivered_receipts' => $deliveredReceipts,
                        'failed_receipts' => $failedReceipts,
                        'pending_receipts' => $pendingReceipts,
                        'delivery_rate' => $deliveryRate,
                    ],
                    'time_series' => $timeSeriesData,
                    'filters' => [
                        'start_date' => $startDate->toDateString(),
                        'end_date' => $endDate->toDateString(),
                        'group_by' => $groupBy,
                    ],
                ],
            ]);

        } catch (\Exception $e) {
            Log::error('Failed to get receipt analytics', [
                'error' => $e->getMessage(),
            ]);

            return response()->json([
                'success' => false,
                'message' => 'Failed to retrieve receipt analytics',
                'error' => $e->getMessage(),
            ], 500);
        }
    }

    /**
     * Get receipt statistics.
     *
     * @param Request $request
     * @return JsonResponse
     */
    public function getReceiptStats(Request $request): JsonResponse
    {
        $request->validate([
            'start_date' => 'nullable|date',
            'end_date' => 'nullable|date|after_or_equal:start_date',
        ]);

        $query = RentReceipt::query();

        if ($request->filled('start_date')) {
            $query->where('generated_at', '>=', $request->start_date);
        }

        if ($request->filled('end_date')) {
            $query->where('generated_at', '<=', $request->end_date);
        }

        $stats = $query->selectRaw('
            COUNT(*) as total_receipts,
            SUM(CASE WHEN delivery_status = "delivered" THEN 1 ELSE 0 END) as delivered_receipts,
            SUM(CASE WHEN delivery_status = "pending" THEN 1 ELSE 0 END) as pending_receipts,
            SUM(CASE WHEN delivery_status = "failed" THEN 1 ELSE 0 END) as failed_receipts
        ')->first();

        return response()->json([
            'data' => $stats,
        ]);
    }

    /**
     * Delete receipt.
     *
     * @param RentReceipt $receipt
     * @return JsonResponse
     */
    public function deleteReceipt(RentReceipt $receipt): JsonResponse
    {
        try {
            // Delete receipt files
            $this->receiptGenerationService->deleteReceiptFiles($receipt);

            // Log deletion
            $receipt->auditLogs()->create([
                'action' => 'receipt_deleted',
                'description' => 'Receipt deleted',
                'user_id' => auth()->id(),
            ]);

            // Delete receipt record
            $receipt->delete();

            return response()->json([
                'message' => 'Receipt deleted successfully',
            ]);

        } catch (\Exception $e) {
            Log::error('Failed to delete receipt', [
                'receipt_id' => $receipt->id,
                'error' => $e->getMessage(),
            ]);

            return response()->json([
                'message' => 'Failed to delete receipt',
                'error' => $e->getMessage(),
            ], 500);
        }
    }
} 