<?php

namespace App\Http\Controllers;

use App\Models\Unit;
use App\Models\User;
use App\Models\RentPayment;
use App\Models\BillingRecord;
use App\Models\RentReceipt;
use App\Models\Agreement;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\DB;
use Carbon\Carbon;

class ReportController extends Controller
{
    /**
     * Get comprehensive reports dashboard data
     */
    public function dashboard(): JsonResponse
    {
        try {
            $data = [
                'overview' => $this->getOverviewStats(),
                'financial_summary' => $this->getFinancialSummary(),
                'occupancy_trends' => $this->getOccupancyTrends(),
                'recent_activities' => $this->getRecentActivities(),
            ];

            return response()->json([
                'success' => true,
                'message' => 'Reports dashboard data retrieved successfully',
                'data' => $data,
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to retrieve reports dashboard data',
                'error' => $e->getMessage(),
            ], 500);
        }
    }

    /**
     * Get financial reports
     */
    public function financialReports(Request $request): JsonResponse
    {
        $request->validate([
            'start_date' => 'nullable|date',
            'end_date' => 'nullable|date|after_or_equal:start_date',
            'report_type' => 'nullable|in:revenue,expenses,profit_loss,cash_flow',
            'group_by' => 'nullable|in:day,week,month,quarter,year',
        ]);

        try {
            $startDate = $request->start_date ? Carbon::parse($request->start_date) : Carbon::now()->subMonths(12);
            $endDate = $request->end_date ? Carbon::parse($request->end_date) : Carbon::now();
            $reportType = $request->report_type ?? 'revenue';
            $groupBy = $request->group_by ?? 'month';

            $data = match($reportType) {
                'revenue' => $this->getRevenueReport($startDate, $endDate, $groupBy),
                'expenses' => $this->getExpensesReport($startDate, $endDate, $groupBy),
                'profit_loss' => $this->getProfitLossReport($startDate, $endDate, $groupBy),
                'cash_flow' => $this->getCashFlowReport($startDate, $endDate, $groupBy),
                default => $this->getRevenueReport($startDate, $endDate, $groupBy),
            };

            return response()->json([
                'success' => true,
                'message' => 'Financial report retrieved successfully',
                'data' => $data,
                'filters' => [
                    'start_date' => $startDate->toDateString(),
                    'end_date' => $endDate->toDateString(),
                    'report_type' => $reportType,
                    'group_by' => $groupBy,
                ],
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to retrieve financial report',
                'error' => $e->getMessage(),
            ], 500);
        }
    }

    /**
     * Get occupancy reports
     */
    public function occupancyReports(Request $request): JsonResponse
    {
        $request->validate([
            'start_date' => 'nullable|date',
            'end_date' => 'nullable|date|after_or_equal:start_date',
            'unit_type' => 'nullable|string',
        ]);

        try {
            $startDate = $request->start_date ? Carbon::parse($request->start_date) : Carbon::now()->subMonths(12);
            $endDate = $request->end_date ? Carbon::parse($request->end_date) : Carbon::now();

            $data = [
                'occupancy_rate' => $this->getOccupancyRate($startDate, $endDate),
                'vacancy_trends' => $this->getVacancyTrends($startDate, $endDate),
                'unit_turnover' => $this->getUnitTurnover($startDate, $endDate),
                'lease_expirations' => $this->getLeaseExpirations($startDate, $endDate),
            ];

            if ($request->unit_type) {
                $data['by_unit_type'] = $this->getOccupancyByUnitType($startDate, $endDate, $request->unit_type);
            }

            return response()->json([
                'success' => true,
                'message' => 'Occupancy report retrieved successfully',
                'data' => $data,
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to retrieve occupancy report',
                'error' => $e->getMessage(),
            ], 500);
        }
    }

    /**
     * Get tenant reports
     */
    public function tenantReports(Request $request): JsonResponse
    {
        $request->validate([
            'start_date' => 'nullable|date',
            'end_date' => 'nullable|date|after_or_equal:start_date',
            'status' => 'nullable|in:active,inactive,pending',
        ]);

        try {
            $startDate = $request->start_date ? Carbon::parse($request->start_date) : Carbon::now()->subMonths(12);
            $endDate = $request->end_date ? Carbon::parse($request->end_date) : Carbon::now();

            $data = [
                'tenant_statistics' => $this->getTenantStatistics($startDate, $endDate),
                'payment_behavior' => $this->getPaymentBehavior($startDate, $endDate),
                'lease_renewals' => $this->getLeaseRenewals($startDate, $endDate),
                'tenant_satisfaction' => $this->getTenantSatisfaction($startDate, $endDate),
            ];

            return response()->json([
                'success' => true,
                'message' => 'Tenant report retrieved successfully',
                'data' => $data,
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to retrieve tenant report',
                'error' => $e->getMessage(),
            ], 500);
        }
    }

    /**
     * Export report data
     */
    public function exportReport(Request $request): JsonResponse
    {
        $request->validate([
            'report_type' => 'required|in:financial,occupancy,tenant,comprehensive',
            'format' => 'required|in:csv,excel,pdf',
            'start_date' => 'nullable|date',
            'end_date' => 'nullable|date|after_or_equal:start_date',
        ]);

        try {
            // This would typically generate and return a file download
            // For now, return a success message with download URL
            $filename = sprintf(
                '%s_report_%s_%s.%s',
                $request->report_type,
                $request->start_date ?? Carbon::now()->subMonths(12)->format('Y-m-d'),
                $request->end_date ?? Carbon::now()->format('Y-m-d'),
                $request->format
            );

            return response()->json([
                'success' => true,
                'message' => 'Report export initiated successfully',
                'data' => [
                    'filename' => $filename,
                    'download_url' => "/api/v1/reports/download/{$filename}",
                    'expires_at' => Carbon::now()->addHours(24)->toISOString(),
                ],
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to export report',
                'error' => $e->getMessage(),
            ], 500);
        }
    }

    /**
     * Get report statistics for dashboard
     */
    public function getReportStatistics(): JsonResponse
    {
        try {
            $totalReports = 150; // Mock data - would come from a reports table
            $reportsThisMonth = 25;
            $mostPopularReport = 'Financial Report';
            $lastGenerated = now()->subHours(2);

            return response()->json([
                'success' => true,
                'message' => 'Report statistics retrieved successfully',
                'data' => [
                    'total_reports' => $totalReports,
                    'reports_this_month' => $reportsThisMonth,
                    'most_popular_report' => $mostPopularReport,
                    'last_generated' => $lastGenerated->toISOString(),
                    'report_types' => [
                        'financial' => 45,
                        'occupancy' => 35,
                        'tenant' => 30,
                        'comprehensive' => 40,
                    ],
                ],
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to retrieve report statistics',
                'error' => $e->getMessage(),
            ], 500);
        }
    }

    /**
     * Get overview statistics
     */
    private function getOverviewStats(): array
    {
        return [
            'total_units' => Unit::count(),
            'occupied_units' => Unit::where('status', 'rented')->count(),
            'total_tenants' => User::where('role', 'tenant')->count(),
            'active_agreements' => Agreement::where('status', 'active')->count(),
            'monthly_revenue' => RentPayment::where('status', 'completed')
                ->whereMonth('payment_date', Carbon::now()->month)
                ->sum('amount'),
            'pending_payments' => RentPayment::where('status', 'pending')->count(),
        ];
    }

    /**
     * Get financial summary
     */
    private function getFinancialSummary(): array
    {
        $currentMonth = Carbon::now();
        $lastMonth = Carbon::now()->subMonth();

        return [
            'current_month_revenue' => RentPayment::where('status', 'completed')
                ->whereMonth('payment_date', $currentMonth->month)
                ->whereYear('payment_date', $currentMonth->year)
                ->sum('amount'),
            'last_month_revenue' => RentPayment::where('status', 'completed')
                ->whereMonth('payment_date', $lastMonth->month)
                ->whereYear('payment_date', $lastMonth->year)
                ->sum('amount'),
            'outstanding_dues' => BillingRecord::where('status', 'pending')->sum('amount'),
            'collection_rate' => $this->calculateCollectionRate(),
        ];
    }

    /**
     * Get occupancy trends
     */
    private function getOccupancyTrends(): array
    {
        $totalUnits = Unit::count();
        $occupiedUnits = Unit::where('status', 'rented')->count();
        
        return [
            'current_occupancy_rate' => $totalUnits > 0 ? round(($occupiedUnits / $totalUnits) * 100, 2) : 0,
            'vacant_units' => $totalUnits - $occupiedUnits,
            'maintenance_units' => Unit::where('status', 'maintenance')->count(),
        ];
    }

    /**
     * Get recent activities
     */
    private function getRecentActivities(): array
    {
        return [
            'recent_payments' => RentPayment::with(['tenant', 'unit'])
                ->latest()
                ->limit(5)
                ->get(),
            'recent_agreements' => Agreement::with(['tenant', 'unit'])
                ->latest()
                ->limit(5)
                ->get(),
        ];
    }

    /**
     * Get revenue report data
     */
    private function getRevenueReport(Carbon $startDate, Carbon $endDate, string $groupBy): array
    {
        $query = RentPayment::where('status', 'completed')
            ->whereBetween('payment_date', [$startDate, $endDate]);

        $groupByFormat = match($groupBy) {
            'day' => '%Y-%m-%d',
            'week' => '%Y-%u',
            'month' => '%Y-%m',
            'quarter' => '%Y-Q%q',
            'year' => '%Y',
            default => '%Y-%m',
        };

        $data = $query->select(
            DB::raw("DATE_FORMAT(payment_date, '{$groupByFormat}') as period"),
            DB::raw('SUM(amount) as total_revenue'),
            DB::raw('COUNT(*) as payment_count')
        )
        ->groupBy('period')
        ->orderBy('period')
        ->get();

        return [
            'chart_data' => $data,
            'total_revenue' => $data->sum('total_revenue'),
            'total_payments' => $data->sum('payment_count'),
            'average_payment' => $data->sum('payment_count') > 0 ? 
                round($data->sum('total_revenue') / $data->sum('payment_count'), 2) : 0,
        ];
    }

    /**
     * Calculate collection rate
     */
    private function calculateCollectionRate(): float
    {
        $totalBilled = BillingRecord::sum('amount');
        $totalCollected = RentPayment::where('status', 'completed')->sum('amount');
        
        return $totalBilled > 0 ? round(($totalCollected / $totalBilled) * 100, 2) : 0;
    }

    // Additional helper methods would be implemented here...
    private function getExpensesReport(Carbon $startDate, Carbon $endDate, string $groupBy): array
    {
        // Mock implementation for expenses report
        $periods = $this->generatePeriods($startDate, $endDate, $groupBy);
        $data = [];

        foreach ($periods as $period) {
            $data[] = [
                'period' => $period,
                'maintenance_expenses' => rand(5000, 15000),
                'utility_expenses' => rand(3000, 8000),
                'administrative_expenses' => rand(2000, 5000),
                'total_expenses' => rand(10000, 28000),
            ];
        }

        return [
            'expenses_breakdown' => $data,
            'total_expenses' => array_sum(array_column($data, 'total_expenses')),
            'average_monthly_expenses' => array_sum(array_column($data, 'total_expenses')) / count($data),
        ];
    }

    private function getProfitLossReport(Carbon $startDate, Carbon $endDate, string $groupBy): array
    {
        $revenueData = $this->getRevenueReport($startDate, $endDate, $groupBy);
        $expensesData = $this->getExpensesReport($startDate, $endDate, $groupBy);

        $data = [];
        $revenueBreakdown = $revenueData['revenue_breakdown'] ?? [];
        $expensesBreakdown = $expensesData['expenses_breakdown'] ?? [];

        for ($i = 0; $i < min(count($revenueBreakdown), count($expensesBreakdown)); $i++) {
            $revenue = $revenueBreakdown[$i]['total_revenue'] ?? 0;
            $expenses = $expensesBreakdown[$i]['total_expenses'] ?? 0;
            $netIncome = $revenue - $expenses;

            $data[] = [
                'period' => $revenueBreakdown[$i]['period'] ?? $expensesBreakdown[$i]['period'],
                'revenue' => $revenue,
                'expenses' => $expenses,
                'net_income' => $netIncome,
                'profit_margin' => $revenue > 0 ? round(($netIncome / $revenue) * 100, 2) : 0,
            ];
        }

        return [
            'profit_loss_breakdown' => $data,
            'total_revenue' => array_sum(array_column($data, 'revenue')),
            'total_expenses' => array_sum(array_column($data, 'expenses')),
            'total_net_income' => array_sum(array_column($data, 'net_income')),
            'average_profit_margin' => count($data) > 0 ? array_sum(array_column($data, 'profit_margin')) / count($data) : 0,
        ];
    }

    private function getCashFlowReport(Carbon $startDate, Carbon $endDate, string $groupBy): array
    {
        $periods = $this->generatePeriods($startDate, $endDate, $groupBy);
        $data = [];

        foreach ($periods as $period) {
            $cashInflow = rand(25000, 45000);
            $cashOutflow = rand(15000, 30000);

            $data[] = [
                'period' => $period,
                'cash_inflow' => $cashInflow,
                'cash_outflow' => $cashOutflow,
                'net_cash_flow' => $cashInflow - $cashOutflow,
                'cumulative_cash_flow' => 0, // Will be calculated below
            ];
        }

        // Calculate cumulative cash flow
        $cumulative = 0;
        foreach ($data as &$item) {
            $cumulative += $item['net_cash_flow'];
            $item['cumulative_cash_flow'] = $cumulative;
        }

        return [
            'cash_flow_breakdown' => $data,
            'total_inflow' => array_sum(array_column($data, 'cash_inflow')),
            'total_outflow' => array_sum(array_column($data, 'cash_outflow')),
            'net_cash_flow' => array_sum(array_column($data, 'net_cash_flow')),
            'ending_cash_balance' => $cumulative,
        ];
    }

    private function getOccupancyRate(Carbon $startDate, Carbon $endDate): array
    {
        // Implementation for occupancy rate calculation
        return ['message' => 'Occupancy rate calculation implementation pending'];
    }

    private function getVacancyTrends(Carbon $startDate, Carbon $endDate): array
    {
        // Implementation for vacancy trends
        return ['message' => 'Vacancy trends implementation pending'];
    }

    private function getUnitTurnover(Carbon $startDate, Carbon $endDate): array
    {
        // Implementation for unit turnover
        return ['message' => 'Unit turnover implementation pending'];
    }

    private function getLeaseExpirations(Carbon $startDate, Carbon $endDate): array
    {
        // Implementation for lease expirations
        return ['message' => 'Lease expirations implementation pending'];
    }

    private function getOccupancyByUnitType(Carbon $startDate, Carbon $endDate, string $unitType): array
    {
        $totalUnits = Unit::where('type', $unitType)->count();
        $occupiedUnits = Unit::where('type', $unitType)
            ->where('status', 'rented')
            ->count();

        $occupancyRate = $totalUnits > 0 ? round(($occupiedUnits / $totalUnits) * 100, 2) : 0;

        return [
            'unit_type' => $unitType,
            'total_units' => $totalUnits,
            'occupied_units' => $occupiedUnits,
            'vacant_units' => $totalUnits - $occupiedUnits,
            'occupancy_rate' => $occupancyRate,
        ];
    }

    private function getTenantStatistics(Carbon $startDate, Carbon $endDate): array
    {
        $totalTenants = User::where('role', 'tenant')->count();
        $activeTenants = User::where('role', 'tenant')
            ->whereHas('agreements', function ($query) {
                $query->where('status', 'active');
            })
            ->count();

        $newTenants = User::where('role', 'tenant')
            ->whereBetween('created_at', [$startDate, $endDate])
            ->count();

        return [
            'total_tenants' => $totalTenants,
            'active_tenants' => $activeTenants,
            'new_tenants' => $newTenants,
            'tenant_retention_rate' => $totalTenants > 0 ? round(($activeTenants / $totalTenants) * 100, 2) : 0,
        ];
    }

    private function getPaymentBehavior(Carbon $startDate, Carbon $endDate): array
    {
        $totalPayments = RentPayment::whereBetween('payment_date', [$startDate, $endDate])->count();
        $onTimePayments = RentPayment::whereBetween('payment_date', [$startDate, $endDate])
            ->where('status', 'completed')
            ->whereRaw('payment_date <= due_date')
            ->count();

        $latePayments = RentPayment::whereBetween('payment_date', [$startDate, $endDate])
            ->where('status', 'completed')
            ->whereRaw('payment_date > due_date')
            ->count();

        $onTimeRate = $totalPayments > 0 ? round(($onTimePayments / $totalPayments) * 100, 2) : 0;

        return [
            'total_payments' => $totalPayments,
            'on_time_payments' => $onTimePayments,
            'late_payments' => $latePayments,
            'on_time_rate' => $onTimeRate,
            'average_delay_days' => $latePayments > 0 ? rand(3, 15) : 0, // Mock calculation
        ];
    }

    private function getLeaseRenewals(Carbon $startDate, Carbon $endDate): array
    {
        $totalRenewals = Agreement::whereBetween('end_date', [$startDate, $endDate])
            ->where('status', 'renewed')
            ->count();

        $expiredAgreements = Agreement::whereBetween('end_date', [$startDate, $endDate])
            ->where('status', 'expired')
            ->count();

        $renewalRate = ($totalRenewals + $expiredAgreements) > 0
            ? round(($totalRenewals / ($totalRenewals + $expiredAgreements)) * 100, 2)
            : 0;

        return [
            'total_renewals' => $totalRenewals,
            'expired_agreements' => $expiredAgreements,
            'renewal_rate' => $renewalRate,
        ];
    }

    private function getTenantSatisfaction(Carbon $startDate, Carbon $endDate): array
    {
        // Mock implementation - in real scenario, this would come from surveys/feedback
        return [
            'average_satisfaction_score' => rand(70, 95),
            'total_responses' => rand(50, 200),
            'satisfaction_breakdown' => [
                'very_satisfied' => rand(30, 50),
                'satisfied' => rand(20, 40),
                'neutral' => rand(5, 15),
                'dissatisfied' => rand(0, 10),
                'very_dissatisfied' => rand(0, 5),
            ],
        ];
    }

    /**
     * Generate periods for reports based on groupBy parameter
     */
    private function generatePeriods(Carbon $startDate, Carbon $endDate, string $groupBy): array
    {
        $periods = [];
        $current = $startDate->copy();

        while ($current->lte($endDate)) {
            switch ($groupBy) {
                case 'day':
                    $periods[] = $current->format('Y-m-d');
                    $current->addDay();
                    break;
                case 'week':
                    $periods[] = $current->format('Y-W');
                    $current->addWeek();
                    break;
                case 'month':
                    $periods[] = $current->format('Y-m');
                    $current->addMonth();
                    break;
                case 'year':
                    $periods[] = $current->format('Y');
                    $current->addYear();
                    break;
                default:
                    $periods[] = $current->format('Y-m');
                    $current->addMonth();
                    break;
            }
        }

        return $periods;
    }
}
