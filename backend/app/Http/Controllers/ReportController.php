<?php

namespace App\Http\Controllers;

use App\Models\Unit;
use App\Models\User;
use App\Models\RentPayment;
use App\Models\BillingRecord;
use App\Models\RentReceipt;
use App\Models\Agreement;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\DB;
use Carbon\Carbon;

class ReportController extends Controller
{
    /**
     * Get comprehensive reports dashboard data
     */
    public function dashboard(): JsonResponse
    {
        try {
            $data = [
                'overview' => $this->getOverviewStats(),
                'financial_summary' => $this->getFinancialSummary(),
                'occupancy_trends' => $this->getOccupancyTrends(),
                'recent_activities' => $this->getRecentActivities(),
            ];

            return response()->json([
                'success' => true,
                'message' => 'Reports dashboard data retrieved successfully',
                'data' => $data,
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to retrieve reports dashboard data',
                'error' => $e->getMessage(),
            ], 500);
        }
    }

    /**
     * Get financial reports
     */
    public function financialReports(Request $request): JsonResponse
    {
        $request->validate([
            'start_date' => 'nullable|date',
            'end_date' => 'nullable|date|after_or_equal:start_date',
            'report_type' => 'nullable|in:revenue,expenses,profit_loss,cash_flow',
            'group_by' => 'nullable|in:day,week,month,quarter,year',
        ]);

        try {
            $startDate = $request->start_date ? Carbon::parse($request->start_date) : Carbon::now()->subMonths(12);
            $endDate = $request->end_date ? Carbon::parse($request->end_date) : Carbon::now();
            $reportType = $request->report_type ?? 'revenue';
            $groupBy = $request->group_by ?? 'month';

            $data = match($reportType) {
                'revenue' => $this->getRevenueReport($startDate, $endDate, $groupBy),
                'expenses' => $this->getExpensesReport($startDate, $endDate, $groupBy),
                'profit_loss' => $this->getProfitLossReport($startDate, $endDate, $groupBy),
                'cash_flow' => $this->getCashFlowReport($startDate, $endDate, $groupBy),
                default => $this->getRevenueReport($startDate, $endDate, $groupBy),
            };

            return response()->json([
                'success' => true,
                'message' => 'Financial report retrieved successfully',
                'data' => $data,
                'filters' => [
                    'start_date' => $startDate->toDateString(),
                    'end_date' => $endDate->toDateString(),
                    'report_type' => $reportType,
                    'group_by' => $groupBy,
                ],
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to retrieve financial report',
                'error' => $e->getMessage(),
            ], 500);
        }
    }

    /**
     * Get occupancy reports
     */
    public function occupancyReports(Request $request): JsonResponse
    {
        $request->validate([
            'start_date' => 'nullable|date',
            'end_date' => 'nullable|date|after_or_equal:start_date',
            'unit_type' => 'nullable|string',
        ]);

        try {
            $startDate = $request->start_date ? Carbon::parse($request->start_date) : Carbon::now()->subMonths(12);
            $endDate = $request->end_date ? Carbon::parse($request->end_date) : Carbon::now();

            $data = [
                'occupancy_rate' => $this->getOccupancyRate($startDate, $endDate),
                'vacancy_trends' => $this->getVacancyTrends($startDate, $endDate),
                'unit_turnover' => $this->getUnitTurnover($startDate, $endDate),
                'lease_expirations' => $this->getLeaseExpirations($startDate, $endDate),
            ];

            if ($request->unit_type) {
                $data['by_unit_type'] = $this->getOccupancyByUnitType($startDate, $endDate, $request->unit_type);
            }

            return response()->json([
                'success' => true,
                'message' => 'Occupancy report retrieved successfully',
                'data' => $data,
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to retrieve occupancy report',
                'error' => $e->getMessage(),
            ], 500);
        }
    }

    /**
     * Get tenant reports
     */
    public function tenantReports(Request $request): JsonResponse
    {
        $request->validate([
            'start_date' => 'nullable|date',
            'end_date' => 'nullable|date|after_or_equal:start_date',
            'status' => 'nullable|in:active,inactive,pending',
        ]);

        try {
            $startDate = $request->start_date ? Carbon::parse($request->start_date) : Carbon::now()->subMonths(12);
            $endDate = $request->end_date ? Carbon::parse($request->end_date) : Carbon::now();

            $data = [
                'tenant_statistics' => $this->getTenantStatistics($startDate, $endDate),
                'payment_behavior' => $this->getPaymentBehavior($startDate, $endDate),
                'lease_renewals' => $this->getLeaseRenewals($startDate, $endDate),
                'tenant_satisfaction' => $this->getTenantSatisfaction($startDate, $endDate),
            ];

            return response()->json([
                'success' => true,
                'message' => 'Tenant report retrieved successfully',
                'data' => $data,
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to retrieve tenant report',
                'error' => $e->getMessage(),
            ], 500);
        }
    }

    /**
     * Export report data
     */
    public function exportReport(Request $request): JsonResponse
    {
        $request->validate([
            'report_type' => 'required|in:financial,occupancy,tenant,comprehensive',
            'format' => 'required|in:csv,excel,pdf',
            'start_date' => 'nullable|date',
            'end_date' => 'nullable|date|after_or_equal:start_date',
        ]);

        try {
            // This would typically generate and return a file download
            // For now, return a success message with download URL
            $filename = sprintf(
                '%s_report_%s_%s.%s',
                $request->report_type,
                $request->start_date ?? Carbon::now()->subMonths(12)->format('Y-m-d'),
                $request->end_date ?? Carbon::now()->format('Y-m-d'),
                $request->format
            );

            return response()->json([
                'success' => true,
                'message' => 'Report export initiated successfully',
                'data' => [
                    'filename' => $filename,
                    'download_url' => "/api/v1/reports/download/{$filename}",
                    'expires_at' => Carbon::now()->addHours(24)->toISOString(),
                ],
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to export report',
                'error' => $e->getMessage(),
            ], 500);
        }
    }

    /**
     * Get overview statistics
     */
    private function getOverviewStats(): array
    {
        return [
            'total_units' => Unit::count(),
            'occupied_units' => Unit::where('status', 'rented')->count(),
            'total_tenants' => User::where('role', 'tenant')->count(),
            'active_agreements' => Agreement::where('status', 'active')->count(),
            'monthly_revenue' => RentPayment::where('status', 'completed')
                ->whereMonth('payment_date', Carbon::now()->month)
                ->sum('amount'),
            'pending_payments' => RentPayment::where('status', 'pending')->count(),
        ];
    }

    /**
     * Get financial summary
     */
    private function getFinancialSummary(): array
    {
        $currentMonth = Carbon::now();
        $lastMonth = Carbon::now()->subMonth();

        return [
            'current_month_revenue' => RentPayment::where('status', 'completed')
                ->whereMonth('payment_date', $currentMonth->month)
                ->whereYear('payment_date', $currentMonth->year)
                ->sum('amount'),
            'last_month_revenue' => RentPayment::where('status', 'completed')
                ->whereMonth('payment_date', $lastMonth->month)
                ->whereYear('payment_date', $lastMonth->year)
                ->sum('amount'),
            'outstanding_dues' => BillingRecord::where('status', 'pending')->sum('amount'),
            'collection_rate' => $this->calculateCollectionRate(),
        ];
    }

    /**
     * Get occupancy trends
     */
    private function getOccupancyTrends(): array
    {
        $totalUnits = Unit::count();
        $occupiedUnits = Unit::where('status', 'rented')->count();
        
        return [
            'current_occupancy_rate' => $totalUnits > 0 ? round(($occupiedUnits / $totalUnits) * 100, 2) : 0,
            'vacant_units' => $totalUnits - $occupiedUnits,
            'maintenance_units' => Unit::where('status', 'maintenance')->count(),
        ];
    }

    /**
     * Get recent activities
     */
    private function getRecentActivities(): array
    {
        return [
            'recent_payments' => RentPayment::with(['tenant', 'unit'])
                ->latest()
                ->limit(5)
                ->get(),
            'recent_agreements' => Agreement::with(['tenant', 'unit'])
                ->latest()
                ->limit(5)
                ->get(),
        ];
    }

    /**
     * Get revenue report data
     */
    private function getRevenueReport(Carbon $startDate, Carbon $endDate, string $groupBy): array
    {
        $query = RentPayment::where('status', 'completed')
            ->whereBetween('payment_date', [$startDate, $endDate]);

        $groupByFormat = match($groupBy) {
            'day' => '%Y-%m-%d',
            'week' => '%Y-%u',
            'month' => '%Y-%m',
            'quarter' => '%Y-Q%q',
            'year' => '%Y',
            default => '%Y-%m',
        };

        $data = $query->select(
            DB::raw("DATE_FORMAT(payment_date, '{$groupByFormat}') as period"),
            DB::raw('SUM(amount) as total_revenue'),
            DB::raw('COUNT(*) as payment_count')
        )
        ->groupBy('period')
        ->orderBy('period')
        ->get();

        return [
            'chart_data' => $data,
            'total_revenue' => $data->sum('total_revenue'),
            'total_payments' => $data->sum('payment_count'),
            'average_payment' => $data->sum('payment_count') > 0 ? 
                round($data->sum('total_revenue') / $data->sum('payment_count'), 2) : 0,
        ];
    }

    /**
     * Calculate collection rate
     */
    private function calculateCollectionRate(): float
    {
        $totalBilled = BillingRecord::sum('amount');
        $totalCollected = RentPayment::where('status', 'completed')->sum('amount');
        
        return $totalBilled > 0 ? round(($totalCollected / $totalBilled) * 100, 2) : 0;
    }

    // Additional helper methods would be implemented here...
    private function getExpensesReport(Carbon $startDate, Carbon $endDate, string $groupBy): array
    {
        // Implementation for expenses report
        return ['message' => 'Expenses report implementation pending'];
    }

    private function getProfitLossReport(Carbon $startDate, Carbon $endDate, string $groupBy): array
    {
        // Implementation for profit/loss report
        return ['message' => 'Profit/Loss report implementation pending'];
    }

    private function getCashFlowReport(Carbon $startDate, Carbon $endDate, string $groupBy): array
    {
        // Implementation for cash flow report
        return ['message' => 'Cash flow report implementation pending'];
    }

    private function getOccupancyRate(Carbon $startDate, Carbon $endDate): array
    {
        // Implementation for occupancy rate calculation
        return ['message' => 'Occupancy rate calculation implementation pending'];
    }

    private function getVacancyTrends(Carbon $startDate, Carbon $endDate): array
    {
        // Implementation for vacancy trends
        return ['message' => 'Vacancy trends implementation pending'];
    }

    private function getUnitTurnover(Carbon $startDate, Carbon $endDate): array
    {
        // Implementation for unit turnover
        return ['message' => 'Unit turnover implementation pending'];
    }

    private function getLeaseExpirations(Carbon $startDate, Carbon $endDate): array
    {
        // Implementation for lease expirations
        return ['message' => 'Lease expirations implementation pending'];
    }

    private function getOccupancyByUnitType(Carbon $startDate, Carbon $endDate, string $unitType): array
    {
        // Implementation for occupancy by unit type
        return ['message' => 'Occupancy by unit type implementation pending'];
    }

    private function getTenantStatistics(Carbon $startDate, Carbon $endDate): array
    {
        // Implementation for tenant statistics
        return ['message' => 'Tenant statistics implementation pending'];
    }

    private function getPaymentBehavior(Carbon $startDate, Carbon $endDate): array
    {
        // Implementation for payment behavior analysis
        return ['message' => 'Payment behavior analysis implementation pending'];
    }

    private function getLeaseRenewals(Carbon $startDate, Carbon $endDate): array
    {
        // Implementation for lease renewals
        return ['message' => 'Lease renewals implementation pending'];
    }

    private function getTenantSatisfaction(Carbon $startDate, Carbon $endDate): array
    {
        // Implementation for tenant satisfaction
        return ['message' => 'Tenant satisfaction implementation pending'];
    }
}
