<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use App\Models\Agreement;
use App\Models\AgreementTemplate;
use App\Models\Tenant;
use App\Models\Unit;
use App\Services\DigitalSignatureService;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Facades\Gate;
use Illuminate\Http\Response;
use Illuminate\Support\Str;
use Dompdf\Dompdf;
use Illuminate\Foundation\Auth\Access\AuthorizesRequests;
use App\Services\AgreementPdfService;
use App\Services\AgreementStorageService;
use App\Services\DigitalSignatureWorkflowService;
use App\Mail\AgreementMail;
use Illuminate\Support\Facades\Mail;
use App\Services\AgreementRenewalReminderService;
use App\Services\AgreementAuditService;
use App\Models\AgreementAuditLog;

class AgreementController extends Controller
{
    use AuthorizesRequests;

    protected $digitalSignatureService;

    public function __construct(DigitalSignatureService $digitalSignatureService)
    {
        $this->digitalSignatureService = $digitalSignatureService;
    }

    /**
     * Display a listing of the resource.
     */
    public function index(Request $request)
    {
        // Temporarily disabled for development
        // $this->authorize('viewAny', Agreement::class);

        $query = Agreement::with(['tenant', 'unit', 'template', 'creator']);

        // Apply search filter
        if ($request->has('search') && !empty($request->search)) {
            $search = $request->search;
            $query->where(function($q) use ($search) {
                $q->where('status', 'like', "%{$search}%")
                  ->orWhereHas('tenant', function($q) use ($search) {
                      $q->where('tenant_code', 'like', "%{$search}%");
                  })
                  ->orWhereHas('unit', function($q) use ($search) {
                      $q->where('unit_number', 'like', "%{$search}%");
                  });
            });
        }

        // Apply status filter
        if ($request->has('status') && !empty($request->status)) {
            $query->where('status', $request->status);
        }

        // Apply sorting
        $sortBy = $request->get('sort_by', 'created_at');
        $sortOrder = $request->get('sort_order', 'desc');
        $query->orderBy($sortBy, $sortOrder);

        // Apply pagination
        $perPage = $request->get('per_page', 10);
        $agreements = $query->paginate($perPage);

        return response()->json([
            'data' => $agreements->items(),
            'total' => $agreements->total(),
            'per_page' => $agreements->perPage(),
            'current_page' => $agreements->currentPage(),
            'last_page' => $agreements->lastPage(),
            'from' => $agreements->firstItem(),
            'to' => $agreements->lastItem(),
        ]);
    }

    /**
     * Store a newly created resource in storage (generate agreement).
     */
    public function store(Request $request)
    {
        // Temporarily disabled for development
        // $this->authorize('create', Agreement::class);
        $validated = $request->validate([
            'tenant_id' => 'required|exists:tenants,id',
            'unit_id' => 'required|exists:units,id',
            'template_id' => 'required|exists:agreement_templates,id',
            'start_date' => 'required|date',
            'end_date' => 'nullable|date',
            'placeholders' => 'required|array',
        ]);

        $startDate = $validated['start_date'];
        $endDate = $validated['end_date'] ?? (date('Y-m-d', strtotime("$startDate +11 months")));

        if ($startDate >= $endDate) {
            return response()->json(['error' => 'End date must be after start date.'], 422);
        }

        // Prevent overlapping agreements for the same unit or tenant
        $overlap = Agreement::where(function($q) use ($validated, $startDate, $endDate) {
            $q->where('unit_id', $validated['unit_id'])
              ->orWhere('tenant_id', $validated['tenant_id']);
        })
        ->whereIn('status', ['draft', 'active'])
        ->where(function($q) use ($startDate, $endDate) {
            $q->where(function($q2) use ($startDate, $endDate) {
                $q2->where('start_date', '<=', $endDate)
                   ->where('end_date', '>=', $startDate);
            });
        })
        ->exists();

        if ($overlap) {
            return response()->json(['error' => 'An active or draft agreement already exists for this unit or tenant with overlapping dates.'], 409);
        }

        $template = AgreementTemplate::findOrFail($validated['template_id']);
        $tenant = Tenant::findOrFail($validated['tenant_id']);
        $unit = Unit::findOrFail($validated['unit_id']);
        $placeholders = $validated['placeholders'];

        // Load template file content (assume HTML for dompdf)
        $templatePath = storage_path('app/' . $template->template_file);
        $templateHtml = file_get_contents($templatePath);

        // Replace placeholders in template
        foreach ($placeholders as $key => $value) {
            $templateHtml = str_replace('{' . $key . '}', $value, $templateHtml);
        }

        // Generate PDF using dompdf
        $dompdf = new Dompdf();
        $dompdf->loadHtml($templateHtml);
        $dompdf->setPaper('A4', 'portrait');
        $dompdf->render();
        $pdfOutput = $dompdf->output();

        // Store generated PDF
        $fileName = 'agreements/generated/' . Str::uuid() . '.pdf';
        Storage::put($fileName, $pdfOutput);

        // Create Agreement record
        $agreement = Agreement::create([
            'tenant_id' => $tenant->id,
            'unit_id' => $unit->id,
            'template_id' => $template->id,
            'file_path' => $fileName,
            'status' => 'draft',
            'start_date' => $startDate,
            'end_date' => $endDate,
            'created_by' => Auth::id() ?? 1, // Default to user ID 1 for development
            'metadata' => $placeholders,
        ]);

        return response()->json($agreement, 201);
    }

    /**
     * Display the specified resource.
     */
    public function show(string $id)
    {
        $agreement = Agreement::with(['tenant', 'unit', 'template', 'creator'])->findOrFail($id);
        $this->authorize('view', $agreement);
        return response()->json($agreement);
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, string $id)
    {
        //
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(string $id)
    {
        $agreement = Agreement::findOrFail($id);
        $this->authorize('delete', $agreement);
        if ($agreement->file_path) {
            Storage::delete($agreement->file_path);
        }
        if ($agreement->signed_file_path) {
            Storage::delete($agreement->signed_file_path);
        }
        $agreement->delete();
        return response()->json(['message' => 'Deleted'], 204);
    }

    /**
     * Download the generated agreement PDF.
     */
    public function download($id)
    {
        $agreement = Agreement::findOrFail($id);
        $this->authorize('view', $agreement);
        
        // If signed version exists, download that
        $filePath = $agreement->signed_file_path ?? $agreement->file_path;
        
        if (!Storage::exists($filePath)) {
            return response()->json(['error' => 'File not found'], 404);
        }
        
        $fileName = $agreement->isFullySigned() ? 'signed_agreement_' : 'agreement_';
        $fileName .= $agreement->id . '.pdf';
        
        return Storage::download($filePath, $fileName);
    }

    /**
     * Renew an agreement (create a new agreement based on an existing one).
     */
    public function renew(Request $request, $id)
    {
        $original = Agreement::findOrFail($id);
        $this->authorize('create', Agreement::class);

        $validated = $request->validate([
            'start_date' => 'required|date',
            'end_date' => 'nullable|date',
            'placeholders' => 'nullable|array',
        ]);

        $startDate = $validated['start_date'];
        $endDate = $validated['end_date'] ?? (date('Y-m-d', strtotime("$startDate +11 months")));
        if ($startDate >= $endDate) {
            return response()->json(['error' => 'End date must be after start date.'], 422);
        }
        // Prevent overlap
        $overlap = Agreement::where(function($q) use ($original, $startDate, $endDate) {
            $q->where('unit_id', $original->unit_id)
              ->orWhere('tenant_id', $original->tenant_id);
        })
        ->whereIn('status', ['draft', 'active'])
        ->where(function($q) use ($startDate, $endDate) {
            $q->where(function($q2) use ($startDate, $endDate) {
                $q2->where('start_date', '<=', $endDate)
                   ->where('end_date', '>=', $startDate);
            });
        })
        ->exists();
        if ($overlap) {
            return response()->json(['error' => 'Overlapping agreement exists for this unit or tenant.'], 409);
        }
        $template = AgreementTemplate::findOrFail($original->template_id);
        $placeholders = $validated['placeholders'] ?? $original->metadata ?? [];
        // Merge placeholders if provided
        if ($validated['placeholders']) {
            $placeholders = array_merge($original->metadata ?? [], $validated['placeholders']);
        }
        // Load template file
        $templatePath = storage_path('app/' . $template->template_file);
        $templateHtml = file_get_contents($templatePath);
        foreach ($placeholders as $key => $value) {
            $templateHtml = str_replace('{' . $key . '}', $value, $templateHtml);
        }
        $dompdf = new \Dompdf\Dompdf();
        $dompdf->loadHtml($templateHtml);
        $dompdf->setPaper('A4', 'portrait');
        $dompdf->render();
        $pdfOutput = $dompdf->output();
        $fileName = 'agreements/generated/' . \Illuminate\Support\Str::uuid() . '.pdf';
        \Storage::put($fileName, $pdfOutput);
        // Build history
        $history = $original->history ?? [];
        $history[] = [
            'action' => 'renewed',
            'from' => $original->id,
            'at' => now()->toDateTimeString(),
            'by' => \Auth::id(),
        ];
        $agreement = Agreement::create([
            'tenant_id' => $original->tenant_id,
            'unit_id' => $original->unit_id,
            'template_id' => $template->id,
            'file_path' => $fileName,
            'status' => 'draft',
            'start_date' => $startDate,
            'end_date' => $endDate,
            'created_by' => \Auth::id(),
            'metadata' => $placeholders,
            'renewed_from' => $original->id,
            'history' => $history,
        ]);
        return response()->json($agreement, 201);
    }

    /**
     * Extend an agreement (update end_date and regenerate PDF).
     */
    public function extend(Request $request, $id)
    {
        $agreement = Agreement::findOrFail($id);
        $this->authorize('update', $agreement);
        $validated = $request->validate([
            'end_date' => 'required|date|after:' . $agreement->end_date,
            'placeholders' => 'nullable|array',
        ]);
        $newEndDate = $validated['end_date'];
        // Prevent overlap
        $overlap = Agreement::where(function($q) use ($agreement, $newEndDate) {
            $q->where('unit_id', $agreement->unit_id)
              ->orWhere('tenant_id', $agreement->tenant_id);
        })
        ->whereIn('status', ['draft', 'active'])
        ->where('id', '!=', $agreement->id)
        ->where(function($q) use ($agreement, $newEndDate) {
            $q->where(function($q2) use ($agreement, $newEndDate) {
                $q2->where('start_date', '<=', $newEndDate)
                   ->where('end_date', '>=', $agreement->start_date);
            });
        })
        ->exists();
        if ($overlap) {
            return response()->json(['error' => 'Overlapping agreement exists for this unit or tenant.'], 409);
        }
        $placeholders = $validated['placeholders'] ?? $agreement->metadata ?? [];
        if ($validated['placeholders']) {
            $placeholders = array_merge($agreement->metadata ?? [], $validated['placeholders']);
        }
        $template = AgreementTemplate::findOrFail($agreement->template_id);
        $templatePath = storage_path('app/' . $template->template_file);
        $templateHtml = file_get_contents($templatePath);
        foreach ($placeholders as $key => $value) {
            $templateHtml = str_replace('{' . $key . '}', $value, $templateHtml);
        }
        $dompdf = new \Dompdf\Dompdf();
        $dompdf->loadHtml($templateHtml);
        $dompdf->setPaper('A4', 'portrait');
        $dompdf->render();
        $pdfOutput = $dompdf->output();
        $fileName = 'agreements/generated/' . \Illuminate\Support\Str::uuid() . '.pdf';
        \Storage::put($fileName, $pdfOutput);
        // Build history
        $history = $agreement->history ?? [];
        $history[] = [
            'action' => 'extended',
            'at' => now()->toDateTimeString(),
            'by' => \Auth::id(),
            'old_end_date' => $agreement->end_date,
            'new_end_date' => $newEndDate,
        ];
        // Update agreement
        $agreement->end_date = $newEndDate;
        $agreement->file_path = $fileName;
        $agreement->metadata = $placeholders;
        $agreement->history = $history;
        $agreement->save();
        return response()->json($agreement);
    }

    /**
     * Activate an agreement (move from draft to active).
     */
    public function activate($id)
    {
        $agreement = Agreement::findOrFail($id);
        $this->authorize('update', $agreement);
        if ($agreement->status !== 'draft') {
            return response()->json(['error' => 'Only draft agreements can be activated.'], 422);
        }
        $today = now()->toDateString();
        if ($agreement->start_date > $today || $agreement->end_date <= $today) {
            return response()->json(['error' => 'Agreement dates are not valid for activation.'], 422);
        }
        $oldStatus = $agreement->status;
        $agreement->status = 'active';
        $history = $agreement->history ?? [];
        $history[] = [
            'action' => 'activated',
            'old_status' => $oldStatus,
            'new_status' => 'active',
            'at' => now()->toDateTimeString(),
            'by' => Auth::id(),
        ];
        $agreement->history = $history;
        $agreement->save();
        return response()->json($agreement);
    }

    /**
     * Terminate an agreement (move from active to terminated).
     */
    public function terminate($id)
    {
        $agreement = Agreement::findOrFail($id);
        $this->authorize('update', $agreement);
        if ($agreement->status !== 'active') {
            return response()->json(['error' => 'Only active agreements can be terminated.'], 422);
        }
        $oldStatus = $agreement->status;
        $agreement->status = 'terminated';
        $history = $agreement->history ?? [];
        $history[] = [
            'action' => 'terminated',
            'old_status' => $oldStatus,
            'new_status' => 'terminated',
            'at' => now()->toDateTimeString(),
            'by' => Auth::id(),
        ];
        $agreement->history = $history;
        $agreement->save();
        return response()->json($agreement);
    }

    /**
     * Sign agreement (add digital signature).
     */
    public function sign(Request $request, $id)
    {
        $agreement = Agreement::findOrFail($id);
        $this->authorize('update', $agreement);
        
        $validated = $request->validate([
            'signer_type' => 'required|in:owner,tenant',
            'signer_name' => 'required|string|max:255',
            'signature_date' => 'required|date',
        ]);

        $signerType = $validated['signer_type'];
        $signerName = $validated['signer_name'];
        $signatureDate = $validated['signature_date'];

        // Check if already signed by this party
        if (($signerType === 'owner' && $agreement->signed_by_owner) ||
            ($signerType === 'tenant' && $agreement->signed_by_tenant)) {
            return response()->json(['error' => 'Agreement already signed by ' . $signerType], 422);
        }

        // Prepare signature data
        $signatureData = [
            'agreement_id' => $agreement->id,
            'signature_date' => $signatureDate,
            'signer_type' => $signerType,
            'signer_name' => $signerName,
        ];

        if ($signerType === 'owner') {
            $signatureData['owner_name'] = $signerName;
            $signatureData['tenant_name'] = $agreement->tenant->user->name ?? 'Tenant';
        } else {
            $signatureData['owner_name'] = $agreement->unit->owner->user->name ?? 'Owner';
            $signatureData['tenant_name'] = $signerName;
        }

        // Generate signed PDF
        $signedPdfPath = $this->digitalSignatureService->addSignatureToPdf($agreement->file_path, $signatureData);
        
        // Generate signature certificate
        $signatureCertificate = $this->digitalSignatureService->generateSignatureCertificate($signatureData);

        // Update agreement
        $agreement->signed_file_path = $signedPdfPath;
        $agreement->signature_data = $signatureData;
        $agreement->signature_hash = $signatureCertificate['signature_hash'];
        $agreement->signed_at = now();

        if ($signerType === 'owner') {
            $agreement->signed_by_owner = true;
        } else {
            $agreement->signed_by_tenant = true;
        }

        // Add to history
        $history = $agreement->history ?? [];
        $history[] = [
            'action' => 'signed',
            'signer_type' => $signerType,
            'signer_name' => $signerName,
            'at' => now()->toDateTimeString(),
            'by' => Auth::id(),
        ];
        $agreement->history = $history;
        $agreement->save();

        return response()->json([
            'agreement' => $agreement->load(['tenant', 'unit', 'template']),
            'signature_certificate' => $signatureCertificate,
            'message' => 'Agreement signed successfully'
        ]);
    }

    /**
     * Verify digital signature.
     */
    public function verifySignature($signatureHash)
    {
        $agreement = Agreement::where('signature_hash', $signatureHash)->first();
        
        if (!$agreement) {
            return response()->json(['error' => 'Signature not found'], 404);
        }

        $isValid = $this->digitalSignatureService->verifySignature($signatureHash, $agreement->signature_data);

        return response()->json([
            'signature_hash' => $signatureHash,
            'is_valid' => $isValid,
            'agreement_id' => $agreement->id,
            'signed_at' => $agreement->signed_at,
            'signature_status' => $agreement->getSignatureStatus(),
        ]);
    }

    /**
     * Get agreement signature status.
     */
    public function getSignatureStatus($id)
    {
        $agreement = Agreement::findOrFail($id);
        $this->authorize('view', $agreement);

        return response()->json([
            'agreement_id' => $agreement->id,
            'signature_status' => $agreement->getSignatureStatus(),
            'signed_by_owner' => $agreement->signed_by_owner,
            'signed_by_tenant' => $agreement->signed_by_tenant,
            'signed_at' => $agreement->signed_at,
            'signature_hash' => $agreement->signature_hash,
        ]);
    }

    /**
     * Get expiring agreements.
     */
    public function expiringAgreements(Request $request)
    {
        $this->authorize('viewAny', Agreement::class);
        
        $days = $request->get('days', 30);
        $agreements = Agreement::with(['tenant', 'unit', 'template'])
            ->expiringSoon($days)
            ->get();

        return response()->json([
            'agreements' => $agreements,
            'count' => $agreements->count(),
            'days_threshold' => $days,
        ]);
    }

    /**
     * Get agreement statistics.
     */
    public function statistics()
    {
        // Temporarily disable authorization for development
        // $this->authorize('viewAny', Agreement::class);

        $stats = [
            'total' => Agreement::count(),
            'draft' => Agreement::draft()->count(),
            'active' => Agreement::active()->count(),
            'expired' => Agreement::expired()->count(),
            'expiring_soon' => Agreement::expiringSoon(30)->count(),
            'fully_signed' => Agreement::where('signed_by_owner', true)->where('signed_by_tenant', true)->count(),
            'partially_signed' => Agreement::where(function($q) {
                $q->where('signed_by_owner', true)->orWhere('signed_by_tenant', true);
            })->where(function($q) {
                $q->where('signed_by_owner', false)->orWhere('signed_by_tenant', false);
            })->count(),
        ];

        return response()->json($stats);
    }

    /**
     * Download PDF for the specified agreement.
     */
    public function downloadPdf(Agreement $agreement, AgreementPdfService $pdfService)
    {
        $pdf = $pdfService->generate($agreement);
        return $pdf->download("agreement-{$agreement->id}.pdf");
    }

    /**
     * Send agreement via email to tenant.
     */
    public function sendEmail(Agreement $agreement, AgreementPdfService $pdfService)
    {
        // Generate PDF
        $pdf = $pdfService->generate($agreement);
        
        // Send email with PDF attachment
        Mail::to($agreement->tenant->email)
            ->send(new AgreementMail($agreement))
            ->attachData($pdf->output(), "agreement-{$agreement->id}.pdf", [
                'mime' => 'application/pdf',
            ]);
        
        return response()->json([
            'success' => true,
            'message' => 'Agreement sent via email successfully'
        ]);
    }

    /**
     * Upload signed agreement file
     */
    public function uploadSignedAgreement(Request $request, $id, AgreementStorageService $storageService)
    {
        $agreement = Agreement::findOrFail($id);
        $this->authorize('update', $agreement);
        
        $request->validate([
            'signed_file' => 'required|file|mimes:pdf,jpg,jpeg,png|max:10240', // 10MB max
        ]);
        
        try {
            $filePath = $storageService->storeSignedAgreement($agreement, $request->file('signed_file'));
            
            return response()->json([
                'message' => 'Signed agreement uploaded successfully',
                'file_path' => $filePath,
                'agreement' => $agreement->fresh()->load(['tenant', 'unit', 'template'])
            ]);
        } catch (\InvalidArgumentException $e) {
            return response()->json([
                'error' => $e->getMessage()
            ], 422);
        } catch (\Exception $e) {
            return response()->json([
                'error' => 'Failed to upload signed agreement: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Download signed agreement file
     */
    public function downloadSignedAgreement($id, AgreementStorageService $storageService)
    {
        $agreement = Agreement::findOrFail($id);
        $this->authorize('view', $agreement);
        
        $response = $storageService->downloadSignedAgreement($agreement);
        
        if (!$response) {
            return response()->json([
                'error' => 'Signed agreement file not found'
            ], 404);
        }
        
        return $response;
    }

    /**
     * Delete signed agreement file
     */
    public function deleteSignedAgreement($id, AgreementStorageService $storageService)
    {
        $agreement = Agreement::findOrFail($id);
        $this->authorize('update', $agreement);
        
        $deleted = $storageService->deleteSignedAgreement($agreement);
        
        if ($deleted) {
            return response()->json([
                'message' => 'Signed agreement deleted successfully'
            ]);
        }
        
        return response()->json([
            'error' => 'Failed to delete signed agreement'
        ], 500);
    }

    /**
     * Initiate digital signature workflow
     */
    public function initiateDigitalSignature($id, DigitalSignatureWorkflowService $workflowService)
    {
        $agreement = Agreement::findOrFail($id);
        $this->authorize('update', $agreement);

        try {
            $result = $workflowService->initiateWorkflow($agreement, Auth::user());
            
            return response()->json([
                'message' => 'Digital signature workflow initiated successfully',
                'data' => $result
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'error' => 'Failed to initiate digital signature workflow: ' . $e->getMessage()
            ], 422);
        }
    }

    /**
     * Get digital signature workflow status
     */
    public function getDigitalSignatureStatus($id, DigitalSignatureWorkflowService $workflowService)
    {
        $agreement = Agreement::findOrFail($id);
        $this->authorize('view', $agreement);

        try {
            $status = $workflowService->getWorkflowStatus($agreement);
            
            return response()->json($status);
        } catch (\Exception $e) {
            return response()->json([
                'error' => 'Failed to get workflow status: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Cancel digital signature workflow
     */
    public function cancelDigitalSignature(Request $request, $id, DigitalSignatureWorkflowService $workflowService)
    {
        $agreement = Agreement::findOrFail($id);
        $this->authorize('update', $agreement);

        $request->validate([
            'reason' => 'nullable|string|max:500'
        ]);

        try {
            $result = $workflowService->cancelWorkflow($agreement, Auth::user(), $request->reason);
            
            return response()->json([
                'message' => 'Digital signature workflow cancelled successfully',
                'data' => $result
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'error' => 'Failed to cancel workflow: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Resend signature invitations
     */
    public function resendSignatureInvitations($id, DigitalSignatureWorkflowService $workflowService)
    {
        $agreement = Agreement::findOrFail($id);
        $this->authorize('update', $agreement);

        try {
            $result = $workflowService->resendInvitations($agreement);
            
            return response()->json([
                'message' => 'Signature invitations resent successfully',
                'data' => $result
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'error' => 'Failed to resend invitations: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Get signature reminders
     */
    public function getSignatureReminders(DigitalSignatureWorkflowService $workflowService)
    {
        $this->authorize('viewAny', Agreement::class);

        try {
            $reminders = $workflowService->getSignatureReminders();
            
            return response()->json([
                'reminders' => $reminders,
                'count' => count($reminders)
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'error' => 'Failed to get signature reminders: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Process signature from external provider
     */
    public function processExternalSignature(Request $request, $signatureRequestId, DigitalSignatureWorkflowService $workflowService)
    {
        $request->validate([
            'signature_data' => 'required|array',
            'signature_data.signer_id' => 'required|string',
            'signature_data.signature_hash' => 'required|string',
            'signature_data.signed_at' => 'required|string',
            'signature_data.signer_role' => 'required|in:owner,tenant',
            'signature_data.signer_name' => 'required|string'
        ]);

        try {
            $result = $workflowService->processSignature($signatureRequestId, $request->signature_data);
            
            return response()->json([
                'message' => 'Signature processed successfully',
                'data' => $result
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'error' => 'Failed to process signature: ' . $e->getMessage()
            ], 422);
        }
    }

    /**
     * Get renewal reminder statistics
     */
    public function getRenewalStatistics(Request $request)
    {
        $service = new AgreementRenewalReminderService();
        $statistics = $service->getRenewalStatistics();

        return response()->json([
            'success' => true,
            'data' => $statistics
        ]);
    }

    /**
     * Get agreements expiring soon
     */
    public function getExpiringAgreements(Request $request)
    {
        $days = $request->get('days', 30);
        $service = new AgreementRenewalReminderService();
        $agreements = $service->getExpiringAgreements($days);

        return response()->json([
            'success' => true,
            'data' => $agreements
        ]);
    }

    /**
     * Send renewal reminders manually
     */
    public function sendRenewalReminders(Request $request)
    {
        $request->validate([
            'days_advance' => 'nullable|integer|min:1|max:365',
            'agreement_id' => 'nullable|integer|exists:agreements,id'
        ]);

        $service = new AgreementRenewalReminderService();
        
        if ($request->has('agreement_id')) {
            // Send reminder for specific agreement
            $agreement = Agreement::findOrFail($request->agreement_id);
            $reminder = [
                'agreement_id' => $agreement->id,
                'tenant_name' => $agreement->tenant->name ?? 'Unknown',
                'unit_number' => $agreement->unit->unit_number ?? 'Unknown',
                'end_date' => $agreement->end_date->format('Y-m-d'),
                'days_until_expiry' => $agreement->end_date->diffInDays(now()),
                'tenant_email' => $agreement->tenant->email ?? null,
                'owner_email' => $agreement->unit->owner->email ?? null,
                'creator_email' => $agreement->creator->email ?? null,
            ];
            
            $service->sendReminderForAgreement($reminder);
            $service->markReminderAsSent($agreement->id);
            
            return response()->json([
                'success' => true,
                'message' => 'Renewal reminder sent successfully',
                'data' => $reminder
            ]);
        } else {
            // Send reminders for all eligible agreements
            $result = $service->sendRenewalReminders($request->get('days_advance'));
            
            return response()->json([
                'success' => true,
                'message' => "Renewal reminders sent: {$result['sent_count']} of {$result['total_reminders']}",
                'data' => $result
            ]);
        }
    }

    /**
     * Reset reminder flags for testing
     */
    public function resetReminderFlags(Request $request)
    {
        $request->validate([
            'agreement_id' => 'nullable|integer|exists:agreements,id'
        ]);

        $service = new AgreementRenewalReminderService();
        $updated = $service->resetReminderFlags($request->get('agreement_id'));

        return response()->json([
            'success' => true,
            'message' => "Reset reminder flags for {$updated} agreement(s)",
            'data' => ['updated_count' => $updated]
        ]);
    }

    /**
     * Get agreements needing reminders
     */
    public function getAgreementsNeedingReminders(Request $request)
    {
        $request->validate([
            'days_advance' => 'nullable|integer|min:1|max:365'
        ]);

        $service = new AgreementRenewalReminderService();
        $reminders = $service->getAgreementsNeedingReminders($request->get('days_advance'));

        return response()->json([
            'success' => true,
            'data' => $reminders
        ]);
    }

    /**
     * Get agreement status dashboard data for admin
     */
    public function getStatusDashboard(Request $request)
    {
        // Aggregate agreement status counts
        $statusCounts = \App\Models\Agreement::selectRaw('status, COUNT(*) as count')
            ->groupBy('status')
            ->pluck('count', 'status');

        // Expiring soon (next 30 days)
        $expiringSoon = \App\Models\Agreement::where('status', 'active')
            ->where('end_date', '<=', now()->addDays(30))
            ->where('end_date', '>', now())
            ->count();

        // Recently signed (last 30 days)
        $recentlySigned = \App\Models\Agreement::where('status', 'active')
            ->where('signed_at', '>=', now()->subDays(30))
            ->count();

        // Renewal reminders sent (last 30 days)
        $remindersSent = \App\Models\Agreement::where('renewal_reminder_sent', true)
            ->where('renewal_reminder_sent_at', '>=', now()->subDays(30))
            ->count();

        // Total agreements
        $total = \App\Models\Agreement::count();

        // Top 5 expiring agreements
        $topExpiring = \App\Models\Agreement::with(['tenant', 'unit'])
            ->where('status', 'active')
            ->where('end_date', '>', now())
            ->orderBy('end_date')
            ->limit(5)
            ->get();

        return response()->json([
            'success' => true,
            'data' => [
                'status_counts' => $statusCounts,
                'expiring_soon' => $expiringSoon,
                'recently_signed' => $recentlySigned,
                'reminders_sent' => $remindersSent,
                'total' => $total,
                'top_expiring' => $topExpiring,
            ]
        ]);
    }

    /**
     * Get audit logs for a specific agreement
     */
    public function getAuditLogs(Request $request, int $agreementId)
    {
        $request->validate([
            'action' => 'nullable|string',
            'user_id' => 'nullable|integer|exists:users,id',
            'start_date' => 'nullable|date',
            'end_date' => 'nullable|date|after_or_equal:start_date',
            'recent_days' => 'nullable|integer|min:1|max:365',
        ]);

        $agreement = Agreement::findOrFail($agreementId);
        
        $auditService = new AgreementAuditService();
        $auditLogs = $auditService->getAgreementAuditLogs($agreementId, $request->only([
            'action', 'user_id', 'start_date', 'end_date', 'recent_days'
        ]));

        return response()->json([
            'success' => true,
            'data' => [
                'agreement_id' => $agreementId,
                'agreement_status' => $agreement->status,
                'logs' => $auditLogs->map(function ($log) {
                    return [
                        'id' => $log->id,
                        'action' => $log->action,
                        'action_description' => $log->action_description,
                        'user' => $log->user ? [
                            'id' => $log->user->id,
                            'name' => $log->user->name,
                            'email' => $log->user->email,
                        ] : null,
                        'old_values' => $log->old_values,
                        'new_values' => $log->new_values,
                        'changes' => $log->changes,
                        'metadata' => $log->metadata,
                        'ip_address' => $log->ip_address,
                        'performed_at' => $log->performed_at->toISOString(),
                    ];
                }),
                'total_logs' => $auditLogs->count(),
            ]
        ]);
    }

    /**
     * Get audit statistics for agreements
     */
    public function getAuditStatistics(Request $request, int $agreementId = null)
    {
        $auditService = new AgreementAuditService();
        $statistics = $auditService->getAuditStatistics($agreementId);

        return response()->json([
            'success' => true,
            'data' => $statistics
        ]);
    }

    /**
     * Get recent audit activity
     */
    public function getRecentAuditActivity(Request $request)
    {
        $request->validate([
            'limit' => 'nullable|integer|min:1|max:100',
            'days' => 'nullable|integer|min:1|max:30',
        ]);

        $limit = $request->get('limit', 20);
        $days = $request->get('days', 7);

        $recentLogs = AgreementAuditLog::with(['agreement', 'user'])
            ->recent($days)
            ->limit($limit)
            ->get();

        return response()->json([
            'success' => true,
            'data' => [
                'recent_activity' => $recentLogs->map(function ($log) {
                    return [
                        'id' => $log->id,
                        'action' => $log->action,
                        'action_description' => $log->action_description,
                        'agreement' => [
                            'id' => $log->agreement->id,
                            'status' => $log->agreement->status,
                            'tenant_name' => $log->agreement->tenant->name ?? 'Unknown',
                            'unit_number' => $log->agreement->unit->unit_number ?? 'Unknown',
                        ],
                        'user' => $log->user ? [
                            'id' => $log->user->id,
                            'name' => $log->user->name,
                        ] : null,
                        'performed_at' => $log->performed_at->toISOString(),
                    ];
                }),
                'period_days' => $days,
                'total_activity' => $recentLogs->count(),
            ]
        ]);
    }
}
