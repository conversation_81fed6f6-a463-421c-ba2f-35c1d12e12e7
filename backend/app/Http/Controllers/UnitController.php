<?php

namespace App\Http\Controllers;

use App\Models\Unit;
use App\Models\UnitStatusHistory;
use App\Models\Tenant;
use App\Events\UnitStatusChanged;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\Validation\Rule;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;

class UnitController extends Controller
{
    /**
     * Display a listing of units with filtering and pagination
     */
    public function index(Request $request): JsonResponse
    {
        $query = Unit::with(['owner', 'currentTenant', 'statusChangedBy'])
            ->active();

        // Apply filters
        if ($request->has('status')) {
            $query->where('status', $request->status);
        }

        if ($request->has('block')) {
            $query->where('block', $request->block);
        }

        if ($request->has('floor')) {
            $query->where('floor', $request->floor);
        }

        if ($request->has('type')) {
            $query->where('type', $request->type);
        }

        if ($request->has('owner_id')) {
            $query->where('owner_id', $request->owner_id);
        }

        if ($request->has('available')) {
            $query->available();
        }

        // Search functionality
        if ($request->has('search')) {
            $search = $request->search;
            $query->where(function ($q) use ($search) {
                $q->where('unit_number', 'like', "%{$search}%")
                  ->orWhere('block', 'like', "%{$search}%")
                  ->orWhere('floor', 'like', "%{$search}%")
                  ->orWhere('description', 'like', "%{$search}%");
            });
        }

        // Sorting
        $sortBy = $request->get('sort_by', 'unit_number');
        $sortOrder = $request->get('sort_order', 'asc');
        $query->orderBy($sortBy, $sortOrder);

        // Pagination
        $perPage = $request->get('per_page', 15);
        $units = $query->paginate($perPage);

        return response()->json([
            'success' => true,
            'data' => $units->items(),
            'pagination' => [
                'current_page' => $units->currentPage(),
                'last_page' => $units->lastPage(),
                'per_page' => $units->perPage(),
                'total' => $units->total(),
            ],
            'filters' => [
                'statuses' => Unit::getStatuses(),
                'types' => Unit::getTypes(),
            ],
        ]);
    }

    /**
     * Store a newly created unit
     */
    public function store(Request $request): JsonResponse
    {
        $validated = $request->validate([
            'unit_number' => 'required|string|max:255|unique:units',
            'block' => 'nullable|string|max:255',
            'floor' => 'nullable|string|max:255',
            'wing' => 'nullable|string|max:255',
            'type' => ['required', Rule::in(Unit::getTypes())],
            'bedrooms' => 'nullable|integer|min:0',
            'bathrooms' => 'nullable|integer|min:0',
            'area_sqft' => 'nullable|numeric|min:0',
            'carpet_area' => 'nullable|numeric|min:0',
            'built_up_area' => 'nullable|numeric|min:0',
            'status' => ['required', Rule::in(Unit::getStatuses())],
            'owner_id' => 'nullable|exists:users,id',
            'market_rent' => 'nullable|numeric|min:0',
            'security_deposit' => 'nullable|numeric|min:0',
            'maintenance_charge' => 'nullable|numeric|min:0',
            'available_from' => 'nullable|date',
            'description' => 'nullable|string',
            'amenities' => 'nullable|array',
            'preferences' => 'nullable|array',
            'notes' => 'nullable|string',
        ]);

        DB::beginTransaction();
        try {
            $unit = Unit::create($validated);
            
            // Create initial status history
            UnitStatusHistory::create([
                'unit_id' => $unit->id,
                'previous_status' => null,
                'new_status' => $unit->status,
                'changed_by' => Auth::id(),
                'reason' => 'Unit created',
                'changed_at' => now(),
                'change_type' => UnitStatusHistory::CHANGE_TYPE_SYSTEM,
            ]);

            DB::commit();

            return response()->json([
                'success' => true,
                'message' => 'Unit created successfully',
                'data' => $unit->load(['owner', 'currentTenant']),
            ], 201);
        } catch (\Exception $e) {
            DB::rollBack();
            return response()->json([
                'success' => false,
                'message' => 'Failed to create unit',
                'error' => $e->getMessage(),
            ], 500);
        }
    }

    /**
     * Display the specified unit
     */
    public function show(Unit $unit): JsonResponse
    {
        $unit->load([
            'owner',
            'currentTenant',
            'statusChangedBy',
            'statusHistory' => function ($query) {
                $query->with('changedBy')->latest('changed_at')->limit(10);
            },
            'agreements' => function ($query) {
                $query->latest()->limit(5);
            },
            'enquiries' => function ($query) {
                $query->latest()->limit(5);
            },
        ]);

        return response()->json([
            'success' => true,
            'data' => $unit,
        ]);
    }

    /**
     * Update the specified unit
     */
    public function update(Request $request, Unit $unit): JsonResponse
    {
        $validated = $request->validate([
            'unit_number' => 'required|string|max:255|unique:units,unit_number,' . $unit->id,
            'block' => 'nullable|string|max:255',
            'floor' => 'nullable|string|max:255',
            'wing' => 'nullable|string|max:255',
            'type' => ['required', Rule::in(Unit::getTypes())],
            'bedrooms' => 'nullable|integer|min:0',
            'bathrooms' => 'nullable|integer|min:0',
            'area_sqft' => 'nullable|numeric|min:0',
            'carpet_area' => 'nullable|numeric|min:0',
            'built_up_area' => 'nullable|numeric|min:0',
            'owner_id' => 'nullable|exists:users,id',
            'market_rent' => 'nullable|numeric|min:0',
            'security_deposit' => 'nullable|numeric|min:0',
            'maintenance_charge' => 'nullable|numeric|min:0',
            'available_from' => 'nullable|date',
            'description' => 'nullable|string',
            'amenities' => 'nullable|array',
            'preferences' => 'nullable|array',
            'notes' => 'nullable|string',
        ]);

        $unit->update($validated);

        return response()->json([
            'success' => true,
            'message' => 'Unit updated successfully',
            'data' => $unit->load(['owner', 'currentTenant']),
        ]);
    }

    /**
     * Remove the specified unit
     */
    public function destroy(Unit $unit): JsonResponse
    {
        // Check if unit has active relationships
        if ($unit->agreements()->exists() || $unit->enquiries()->exists()) {
            return response()->json([
                'success' => false,
                'message' => 'Cannot delete unit with active agreements or enquiries',
            ], 422);
        }

        $unit->delete();

        return response()->json([
            'success' => true,
            'message' => 'Unit deleted successfully',
        ]);
    }

    /**
     * Update unit status with validation and history tracking
     */
    public function updateStatus(Request $request, Unit $unit): JsonResponse
    {
        $validated = $request->validate([
            'status' => ['required', Rule::in(Unit::getStatuses())],
            'reason' => 'nullable|string|max:500',
        ]);

        $success = $unit->updateStatus(
            $validated['status'],
            $validated['reason'] ?? null,
            Auth::id()
        );

        if (!$success) {
            return response()->json([
                'success' => false,
                'message' => 'Invalid status transition',
            ], 422);
        }

        return response()->json([
            'success' => true,
            'message' => 'Unit status updated successfully',
            'data' => $unit->fresh(['owner', 'currentTenant', 'statusChangedBy']),
        ]);
    }

    /**
     * Get unit status history
     */
    public function statusHistory(Unit $unit): JsonResponse
    {
        $history = $unit->statusHistory()
            ->with('changedBy')
            ->orderBy('changed_at', 'desc')
            ->paginate(20);

        return response()->json([
            'success' => true,
            'data' => $history->items(),
            'pagination' => [
                'current_page' => $history->currentPage(),
                'last_page' => $history->lastPage(),
                'per_page' => $history->perPage(),
                'total' => $history->total(),
            ],
        ]);
    }

    /**
     * Get unit statistics
     */
    public function statistics(): JsonResponse
    {
        $stats = [
            'total_units' => Unit::active()->count(),
            'by_status' => Unit::active()
                ->select('status', DB::raw('count(*) as count'))
                ->groupBy('status')
                ->pluck('count', 'status'),
            'by_type' => Unit::active()
                ->select('type', DB::raw('count(*) as count'))
                ->groupBy('type')
                ->pluck('count', 'type'),
            'available_units' => Unit::available()->count(),
            'rented_units' => Unit::rented()->count(),
            'recent_changes' => UnitStatusHistory::with(['unit', 'changedBy'])
                ->recent(7)
                ->orderBy('changed_at', 'desc')
                ->limit(10)
                ->get(),
        ];

        return response()->json([
            'success' => true,
            'data' => $stats,
        ]);
    }

    /**
     * Bulk update unit statuses
     */
    public function bulkUpdateStatus(Request $request): JsonResponse
    {
        $validated = $request->validate([
            'unit_ids' => 'required|array|min:1',
            'unit_ids.*' => 'exists:units,id',
            'status' => ['required', Rule::in(Unit::getStatuses())],
            'reason' => 'nullable|string|max:500',
        ]);

        $units = Unit::whereIn('id', $validated['unit_ids'])->get();
        $updated = [];
        $failed = [];

        DB::beginTransaction();
        try {
            foreach ($units as $unit) {
                $success = $unit->updateStatus(
                    $validated['status'],
                    $validated['reason'] ?? 'Bulk update',
                    Auth::id()
                );

                if ($success) {
                    $updated[] = $unit->id;
                } else {
                    $failed[] = [
                        'unit_id' => $unit->id,
                        'unit_number' => $unit->unit_number,
                        'reason' => 'Invalid status transition',
                    ];
                }
            }

            DB::commit();

            return response()->json([
                'success' => true,
                'message' => 'Bulk status update completed',
                'data' => [
                    'updated_count' => count($updated),
                    'failed_count' => count($failed),
                    'updated_units' => $updated,
                    'failed_units' => $failed,
                ],
            ]);
        } catch (\Exception $e) {
            DB::rollBack();
            return response()->json([
                'success' => false,
                'message' => 'Bulk update failed',
                'error' => $e->getMessage(),
            ], 500);
        }
    }

    /**
     * Assign a tenant to a unit
     */
    public function assignTenant(Request $request, Unit $unit): JsonResponse
    {
        $request->validate([
            'tenant_id' => 'required|exists:tenants,id',
            'start_date' => 'required|date|after_or_equal:today',
            'end_date' => 'nullable|date|after:start_date',
            'rent_amount' => 'required|numeric|min:0',
            'security_deposit' => 'required|numeric|min:0',
            'maintenance_charge' => 'nullable|numeric|min:0',
            'reason' => 'nullable|string|max:500'
        ]);

        // Check if unit is available for assignment
        if (!$unit->isAvailable()) {
            return response()->json([
                'success' => false,
                'message' => 'Unit is not available for assignment',
                'data' => [
                    'current_status' => $unit->status,
                    'available_statuses' => [Unit::STATUS_VACANT, Unit::STATUS_TO_LET]
                ]
            ], 422);
        }

        // Check if tenant is already assigned to another unit
        $tenant = Tenant::find($request->tenant_id);
        if ($tenant->current_unit_id) {
            return response()->json([
                'success' => false,
                'message' => 'Tenant is already assigned to another unit',
                'data' => [
                    'current_unit_id' => $tenant->current_unit_id,
                    'tenant_name' => $tenant->name
                ]
            ], 422);
        }

        DB::beginTransaction();
        try {
            // Update unit status and assign tenant
            $unit->update([
                'current_tenant_id' => $request->tenant_id,
                'status' => Unit::STATUS_OCCUPIED,
                'last_status_change' => now(),
                'status_changed_by' => Auth::id(),
                'status_change_reason' => $request->reason ?? 'Tenant assigned to unit'
            ]);

            // Update tenant with unit assignment
            $tenant->update([
                'current_unit_id' => $unit->id,
                'status' => 'active',
                'move_in_date' => $request->start_date,
                'agreement_end_date' => $request->end_date,
                'rent_amount' => $request->rent_amount,
                'security_deposit' => $request->security_deposit,
                'maintenance_charge' => $request->maintenance_charge ?? $unit->maintenance_charge
            ]);

            // Log status change in history
            $unit->statusHistory()->create([
                'previous_status' => Unit::STATUS_VACANT, // or TO_LET
                'new_status' => Unit::STATUS_OCCUPIED,
                'changed_by' => Auth::id(),
                'reason' => $request->reason ?? 'Tenant assigned to unit',
                'changed_at' => now(),
            ]);

            // Fire the unit status changed event for notifications
            $changedBy = Auth::user();
            event(new \App\Events\UnitStatusChanged(
                $unit, 
                Unit::STATUS_VACANT, 
                Unit::STATUS_OCCUPIED, 
                $changedBy, 
                $request->reason ?? 'Tenant assigned to unit'
            ));

            DB::commit();

            $unit->load(['currentTenant', 'owner']);

            return response()->json([
                'success' => true,
                'message' => 'Tenant assigned to unit successfully',
                'data' => [
                    'unit' => $unit,
                    'assignment_details' => [
                        'tenant_id' => $request->tenant_id,
                        'tenant_name' => $tenant->name,
                        'start_date' => $request->start_date,
                        'end_date' => $request->end_date,
                        'rent_amount' => $request->rent_amount,
                        'security_deposit' => $request->security_deposit,
                        'maintenance_charge' => $request->maintenance_charge ?? $unit->maintenance_charge,
                        'assigned_by' => Auth::user()->name,
                        'assigned_at' => now()
                    ]
                ]
            ]);

        } catch (\Exception $e) {
            DB::rollback();
            
            return response()->json([
                'success' => false,
                'message' => 'Failed to assign tenant to unit',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Unassign a tenant from a unit
     */
    public function unassignTenant(Request $request, Unit $unit): JsonResponse
    {
        $request->validate([
            'move_out_date' => 'required|date|before_or_equal:today',
            'reason' => 'required|string|max:500',
            'new_status' => 'required|in:vacant,to-let'
        ]);

        // Check if unit has a tenant assigned
        if (!$unit->current_tenant_id) {
            return response()->json([
                'success' => false,
                'message' => 'No tenant is currently assigned to this unit'
            ], 422);
        }

        DB::beginTransaction();
        try {
            $tenant = $unit->currentTenant;
            $previousStatus = $unit->status;
            $newStatus = $request->new_status === 'vacant' ? Unit::STATUS_VACANT : Unit::STATUS_TO_LET;

            // Update unit status and remove tenant assignment
            $unit->update([
                'current_tenant_id' => null,
                'status' => $newStatus,
                'last_status_change' => now(),
                'status_changed_by' => Auth::id(),
                'status_change_reason' => $request->reason
            ]);

            // Update tenant status
            $tenant->update([
                'current_unit_id' => null,
                'status' => 'inactive',
                'move_out_date' => $request->move_out_date,
                'termination_reason' => $request->reason
            ]);

            // Log status change in history
            $unit->statusHistory()->create([
                'previous_status' => $previousStatus,
                'new_status' => $newStatus,
                'changed_by' => Auth::id(),
                'reason' => $request->reason,
                'changed_at' => now(),
            ]);

            // Fire the unit status changed event for notifications
            $changedBy = Auth::user();
            event(new \App\Events\UnitStatusChanged(
                $unit, 
                $previousStatus, 
                $newStatus, 
                $changedBy, 
                $request->reason
            ));

            DB::commit();

            return response()->json([
                'success' => true,
                'message' => 'Tenant unassigned from unit successfully',
                'data' => [
                    'unit' => $unit->fresh(),
                    'unassignment_details' => [
                        'former_tenant_id' => $tenant->id,
                        'former_tenant_name' => $tenant->name,
                        'move_out_date' => $request->move_out_date,
                        'reason' => $request->reason,
                        'new_unit_status' => $newStatus,
                        'unassigned_by' => Auth::user()->name,
                        'unassigned_at' => now()
                    ]
                ]
            ]);

        } catch (\Exception $e) {
            DB::rollback();
            
            return response()->json([
                'success' => false,
                'message' => 'Failed to unassign tenant from unit',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Get available units for tenant assignment
     */
    public function getAvailableUnits(Request $request): JsonResponse
    {
        $query = Unit::available()
            ->with(['owner:id,name,email'])
            ->select(['id', 'unit_number', 'block', 'floor', 'wing', 'type', 'bedrooms', 'bathrooms', 
                     'area_sqft', 'market_rent', 'security_deposit', 'maintenance_charge', 
                     'owner_id', 'status', 'amenities', 'preferences']);

        // Apply filters
        if ($request->filled('type')) {
            $query->where('type', $request->type);
        }

        if ($request->filled('bedrooms')) {
            $query->where('bedrooms', $request->bedrooms);
        }

        if ($request->filled('max_rent')) {
            $query->where('market_rent', '<=', $request->max_rent);
        }

        if ($request->filled('block')) {
            $query->where('block', $request->block);
        }

        if ($request->filled('floor')) {
            $query->where('floor', $request->floor);
        }

        if ($request->filled('amenities')) {
            $amenities = explode(',', $request->amenities);
            foreach ($amenities as $amenity) {
                $query->whereJsonContains('amenities', trim($amenity));
            }
        }

        $units = $query->orderBy('unit_number')->get();

        return response()->json([
            'success' => true,
            'message' => 'Available units retrieved successfully',
            'data' => [
                'units' => $units,
                'total_available' => $units->count(),
                'filters_applied' => array_filter([
                    'type' => $request->type,
                    'bedrooms' => $request->bedrooms,
                    'max_rent' => $request->max_rent,
                    'block' => $request->block,
                    'floor' => $request->floor,
                    'amenities' => $request->amenities
                ])
            ]
        ]);
    }
}
