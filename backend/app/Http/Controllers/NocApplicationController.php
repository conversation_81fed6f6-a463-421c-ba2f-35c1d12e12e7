<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use App\Models\NocApplication;
use App\Models\Unit;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Storage;
use Illuminate\Validation\Rule;
use App\Notifications\NocApplicationStatusChangedNotification;
use App\Models\User;
use App\Services\NocChargesNotificationService;

class NocApplicationController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index(Request $request)
    {
        // Temporarily disable auth for development
        // $user = Auth::user();
        // if ($user->hasRole(['admin', 'manager'])) {
        if (true) { // Allow access for development
            $query = NocApplication::with(['applicant', 'unit', 'template']);
            // Filters
            if ($request->filled('status')) {
                $query->where('status', $request->input('status'));
            }
            if ($request->filled('noc_type')) {
                $query->where('noc_type', $request->input('noc_type'));
            }
            if ($request->filled('applicant_id')) {
                $query->where('applicant_id', $request->input('applicant_id'));
            }
            if ($request->filled('unit_id')) {
                $query->where('unit_id', $request->input('unit_id'));
            }
            if ($request->filled('date_from')) {
                $query->whereDate('created_at', '>=', $request->input('date_from'));
            }
            if ($request->filled('date_to')) {
                $query->whereDate('created_at', '<=', $request->input('date_to'));
            }
            if ($request->filled('search')) {
                $search = $request->input('search');
                $query->where(function($q) use ($search) {
                    $q->where('id', $search)
                      ->orWhereHas('applicant', function($q2) use ($search) {
                          $q2->where('name', 'like', "%$search%")
                             ->orWhere('first_name', 'like', "%$search%")
                             ->orWhere('last_name', 'like', "%$search%")
                             ->orWhere('email', 'like', "%$search%")
                             ;
                      })
                      ->orWhereHas('unit', function($q2) use ($search) {
                          $q2->where('unit_number', 'like', "%$search%")
                             ->orWhere('id', $search);
                      });
                });
            }
            $perPage = $request->input('per_page', 20);
            $applications = $query->orderByDesc('created_at')->paginate($perPage);
        } else {
            $applications = NocApplication::with(['applicant', 'unit', 'template'])
                ->where('applicant_id', $user->id)
                ->orderByDesc('created_at')
                ->paginate($request->input('per_page', 20));
        }
        return response()->json($applications);
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        $user = Auth::user();
        $validated = $request->validate([
            'unit_id' => ['required', 'exists:units,id'],
            'noc_type' => ['required', Rule::in(['rental', 'residence', 'vehicle', 'renovation', 'transfer'])],
            'template_id' => ['nullable', 'exists:noc_templates,id'],
            'purpose' => ['required', 'string'],
            'form_data' => ['nullable', 'array'],
            'documents.*' => ['file', 'mimes:pdf,jpg,jpeg,png', 'max:2048'],
        ]);

        // Only allow tenants/owners to create
        if (!$user->hasRole(['tenant', 'owner'])) {
            return response()->json(['error' => 'Only tenants or owners can apply for NOC.'], 403);
        }

        // Validate template if provided
        if (isset($validated['template_id'])) {
            $template = \App\Models\NocTemplate::find($validated['template_id']);
            if (!$template || $template->noc_type !== $validated['noc_type']) {
                return response()->json(['error' => 'Invalid template for the selected NOC type.'], 422);
            }
        }

        $documents = [];
        if ($request->hasFile('documents')) {
            foreach ($request->file('documents') as $file) {
                $path = $file->store('noc_documents');
                $documents[] = $path;
            }
        }

        $application = NocApplication::create([
            'applicant_id' => $user->id,
            'unit_id' => $validated['unit_id'],
            'noc_type' => $validated['noc_type'],
            'template_id' => $validated['template_id'] ?? null,
            'purpose' => $validated['purpose'],
            'form_data' => $validated['form_data'] ?? null,
            'status' => 'draft',
            'documents' => $documents,
            'history' => [
                ['action' => 'created', 'by' => $user->id, 'at' => now()]
            ],
        ]);

        return response()->json($application->load(['template', 'applicant', 'unit']), 201);
    }

    /**
     * Display the specified resource.
     */
    public function show($id)
    {
        $user = Auth::user();
        $application = NocApplication::with(['applicant', 'unit', 'template'])->findOrFail($id);
        if ($user->hasRole(['admin', 'manager']) || $application->applicant_id === $user->id) {
            return response()->json($application);
        }
        return response()->json(['error' => 'Unauthorized'], 403);
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, $id)
    {
        $user = Auth::user();
        $application = NocApplication::findOrFail($id);
        if ($application->applicant_id !== $user->id || !in_array($application->status, ['draft', 'submitted'])) {
            return response()->json(['error' => 'Unauthorized or cannot update this application.'], 403);
        }
        $validated = $request->validate([
            'purpose' => ['sometimes', 'string'],
            'documents.*' => ['file', 'mimes:pdf,jpg,jpeg,png', 'max:2048'],
        ]);
        if ($request->has('purpose')) {
            $application->purpose = $validated['purpose'];
        }
        if ($request->hasFile('documents')) {
            $documents = $application->documents ?? [];
            foreach ($request->file('documents') as $file) {
                $path = $file->store('noc_documents');
                $documents[] = $path;
            }
            $application->documents = $documents;
        }
        $history = $application->history ?? [];
        $history[] = ['action' => 'updated', 'by' => $user->id, 'at' => now()];
        $application->history = $history;
        $application->save();
        return response()->json($application);
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy($id)
    {
        $user = Auth::user();
        $application = NocApplication::findOrFail($id);
        if ($application->applicant_id !== $user->id || $application->status !== 'draft') {
            return response()->json(['error' => 'Unauthorized or cannot delete this application.'], 403);
        }
        $application->delete();
        return response()->json(['message' => 'Application deleted.']);
    }

    /**
     * Applicant submits a draft application (draft → submitted)
     */
    public function submit($id)
    {
        $user = Auth::user();
        $application = NocApplication::findOrFail($id);
        if ($application->applicant_id !== $user->id || $application->status !== 'draft') {
            return response()->json(['success' => false, 'error' => 'Unauthorized or invalid status.'], 403);
        }
        $previousStatus = $application->status;
        $application->status = 'submitted';
        $history = $application->history ?? [];
        $history[] = ['action' => 'submitted', 'by' => $user->id, 'at' => now()];
        $application->history = $history;
        $application->save();
        // Notify applicant
        $application->applicant->notify(new NocApplicationStatusChangedNotification($application, $previousStatus, 'submitted', $user));
        // Notify all admins/managers
        $admins = User::whereIn('role', ['admin', 'manager'])->get();
        foreach ($admins as $admin) {
            $admin->notify(new NocApplicationStatusChangedNotification($application, $previousStatus, 'submitted', $user));
        }
        return response()->json(['success' => true, 'data' => $application]);
    }

    /**
     * Admin/manager moves application to under_review (submitted → under_review)
     */
    public function review($id)
    {
        $user = Auth::user();
        $application = NocApplication::findOrFail($id);
        if (!$user->hasRole(['admin', 'manager']) || $application->status !== 'submitted') {
            return response()->json(['success' => false, 'error' => 'Unauthorized or invalid status.'], 403);
        }
        $previousStatus = $application->status;
        $application->status = 'under_review';
        $history = $application->history ?? [];
        $history[] = ['action' => 'under_review', 'by' => $user->id, 'at' => now()];
        $application->history = $history;
        $application->save();
        // Notify applicant
        $application->applicant->notify(new NocApplicationStatusChangedNotification($application, $previousStatus, 'under_review', $user));
        return response()->json(['success' => true, 'data' => $application]);
    }

    /**
     * Admin/manager approves application (under_review → approved)
     */
    public function approve($id)
    {
        try {
            $user = Auth::user();
            $application = NocApplication::findOrFail($id);
            
            if (!$user->hasRole(['admin', 'manager']) || $application->status !== 'under_review') {
                return response()->json(['success' => false, 'error' => 'Unauthorized or invalid status.'], 403);
            }
            
            $previousStatus = $application->status;
            $application->status = 'approved';
            $history = $application->history ?? [];
            $history[] = ['action' => 'approved', 'by' => $user->id, 'at' => now()];
            $application->history = $history;
            $application->save();
            
            // Notify applicant
            $application->applicant->notify(new NocApplicationStatusChangedNotification($application, $previousStatus, 'approved', $user));
            
            // Send NOC charges notification to onesociety
            try {
                $nocChargesService = app(NocChargesNotificationService::class);
                $nocChargesService->notifyNocApproved($application);
            } catch (\Exception $e) {
                \Log::error('Failed to send NOC charges notification', [
                    'noc_id' => $application->id,
                    'error' => $e->getMessage()
                ]);
            }
            
            return response()->json(['success' => true, 'data' => $application]);
        } catch (\Throwable $e) {
            \Log::error('Exception in approve(): ' . $e->getMessage(), [
                'trace' => $e->getTraceAsString(),
                'application_id' => $id ?? 'unknown'
            ]);
            return response()->json(['success' => false, 'error' => $e->getMessage()], 500);
        }
    }

    /**
     * Admin/manager rejects application (under_review → rejected, with remarks)
     */
    public function reject(Request $request, $id)
    {
        try {
            $user = Auth::user();
            $application = NocApplication::findOrFail($id);
            
            if (!$user->hasRole(['admin', 'manager']) || $application->status !== 'under_review') {
                return response()->json(['success' => false, 'error' => 'Unauthorized or invalid status.'], 403);
            }
            
            $validated = $request->validate([
                'remarks' => ['required', 'string'],
            ]);
            
            $previousStatus = $application->status;
            $application->status = 'rejected';
            $application->remarks = $validated['remarks'];
            $history = $application->history ?? [];
            $history[] = ['action' => 'rejected', 'by' => $user->id, 'at' => now(), 'remarks' => $validated['remarks']];
            $application->history = $history;
            $application->save();
            
            // Notify applicant
            $application->applicant->notify(new NocApplicationStatusChangedNotification($application, $previousStatus, 'rejected', $user, $validated['remarks']));
            
            // Send NOC charges cancellation notification to onesociety
            try {
                $nocChargesService = app(NocChargesNotificationService::class);
                $nocChargesService->notifyNocRejected($application);
            } catch (\Exception $e) {
                \Log::error('Failed to send NOC charges cancellation notification', [
                    'noc_id' => $application->id,
                    'error' => $e->getMessage()
                ]);
            }
            
            return response()->json(['success' => true, 'data' => $application]);
        } catch (\Throwable $e) {
            \Log::error('Exception in reject(): ' . $e->getMessage(), [
                'trace' => $e->getTraceAsString(),
                'application_id' => $id ?? 'unknown'
            ]);
            return response()->json(['success' => false, 'error' => $e->getMessage()], 500);
        }
    }

    /**
     * Applicant cancels application (draft/submitted → cancelled)
     */
    public function cancel($id)
    {
        $user = Auth::user();
        $application = NocApplication::findOrFail($id);
        if ($application->applicant_id !== $user->id || !in_array($application->status, ['draft', 'submitted'])) {
            return response()->json(['success' => false, 'error' => 'Unauthorized or invalid status.'], 403);
        }
        $previousStatus = $application->status;
        $application->status = 'cancelled';
        $history = $application->history ?? [];
        $history[] = ['action' => 'cancelled', 'by' => $user->id, 'at' => now()];
        $application->history = $history;
        $application->save();
        // Notify applicant (self-cancel)
        $application->applicant->notify(new NocApplicationStatusChangedNotification($application, $previousStatus, 'cancelled', $user));
        // Optionally notify admins if needed
        return response()->json(['success' => true, 'data' => $application]);
    }

    /**
     * Get status history for a NOC application
     */
    public function statusHistory($id)
    {
        $user = Auth::user();
        $application = NocApplication::with(['applicant', 'unit', 'template'])->findOrFail($id);
        if (!$user->hasRole(['admin', 'manager']) && $application->applicant_id !== $user->id) {
            return response()->json(['error' => 'Unauthorized'], 403);
        }
        $history = $application->history ?? [];
        // Optionally enrich with user info
        $userIds = collect($history)->pluck('by')->unique()->filter();
        $users = $userIds->isNotEmpty() ? \App\Models\User::whereIn('id', $userIds)->get(['id', 'name', 'role']) : collect();
        $userMap = $users->keyBy('id');
        $enriched = collect($history)->map(function($item) use ($userMap) {
            $item['user'] = $userMap[$item['by']] ?? null;
            return $item;
        });
        return response()->json([
            'success' => true,
            'data' => $enriched,
            'application_id' => $application->id,
        ]);
    }

    /**
     * Get NOC application statistics for dashboard
     */
    public function statistics(Request $request)
    {
        // Temporarily disable auth for development
        // $user = Auth::user();
        $query = NocApplication::query();
        // RBAC: restrict to own applications for tenants/owners
        // if ($user->hasRole(['tenant', 'owner'])) {
        //     $query->where('applicant_id', $user->id);
        // }
        $total = $query->count();
        $byStatus = (clone $query)
            ->select('status', \DB::raw('count(*) as count'))
            ->groupBy('status')
            ->pluck('count', 'status');
        $byType = (clone $query)
            ->select('noc_type', \DB::raw('count(*) as count'))
            ->groupBy('noc_type')
            ->pluck('count', 'noc_type');
        $recent = (clone $query)
            ->orderBy('updated_at', 'desc')
            ->limit(10)
            ->get(['id', 'status', 'noc_type', 'updated_at', 'applicant_id']);
        return response()->json([
            'success' => true,
            'data' => [
                'total_applications' => $total,
                'by_status' => $byStatus,
                'by_type' => $byType,
                'recent_changes' => $recent,
            ],
        ]);
    }
}
