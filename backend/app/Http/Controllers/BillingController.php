<?php

namespace App\Http\Controllers;

use App\Models\Unit;
use App\Models\NocApplication;
use App\Models\BillingRule;
use App\Models\BillingRecord;
use App\Services\ExternalBillingService;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Log;

class BillingController extends Controller
{
    protected $billingService;

    public function __construct(ExternalBillingService $billingService)
    {
        $this->billingService = $billingService;
    }

    /**
     * List billing rules with filtering and pagination
     */
    public function listBillingRules(Request $request): JsonResponse
    {
        $request->validate([
            'type' => 'nullable|in:maintenance,utilities,parking,non_occupancy,noc_charges',
            'is_active' => 'nullable|boolean',
            'applies_to' => 'nullable|in:all_units,specific_units,tenant_type',
            'search' => 'nullable|string|max:255',
            'per_page' => 'nullable|integer|min:1|max:100',
        ]);

        $query = BillingRule::with('creator:id,name,email');

        // Apply filters
        if ($request->filled('type')) {
            $query->where('type', $request->type);
        }

        if ($request->has('is_active')) {
            $query->where('is_active', $request->boolean('is_active'));
        }

        if ($request->filled('applies_to')) {
            $query->where('applies_to', $request->applies_to);
        }

        if ($request->filled('search')) {
            $query->where(function ($q) use ($request) {
                $q->where('name', 'like', '%' . $request->search . '%')
                  ->orWhere('description', 'like', '%' . $request->search . '%');
            });
        }

        $rules = $query->orderBy('created_at', 'desc')
                      ->paginate($request->get('per_page', 15));

        return response()->json([
            'success' => true,
            'data' => $rules,
            'message' => 'Billing rules retrieved successfully',
        ]);
    }

    /**
     * Get billing rules for a specific charge type (legacy method)
     */
    public function getBillingRules(Request $request): JsonResponse
    {
        $validated = $request->validate([
            'charge_type' => 'required|string|in:non_occupancy,noc_charges,maintenance,utilities,parking',
            'context' => 'nullable|array',
        ]);

        try {
            $rules = BillingRule::active()
                               ->ofType($validated['charge_type'])
                               ->with('creator:id,name,email')
                               ->get();

            return response()->json([
                'success' => true,
                'message' => 'Billing rules retrieved successfully',
                'data' => $rules,
            ]);

        } catch (\Exception $e) {
            Log::error('Failed to fetch billing rules', [
                'charge_type' => $validated['charge_type'],
                'error' => $e->getMessage(),
            ]);

            return response()->json([
                'success' => false,
                'message' => 'Failed to fetch billing rules',
                'error' => $e->getMessage(),
            ], 500);
        }
    }

    /**
     * Create a new billing rule
     */
    public function createBillingRule(Request $request): JsonResponse
    {
        $validated = $request->validate([
            'name' => 'required|string|max:255',
            'type' => 'required|in:maintenance,utilities,parking,non_occupancy,noc_charges',
            'calculation_method' => 'required|in:per_sqft,fixed,percentage,per_unit',
            'amount' => 'required|numeric|min:0',
            'description' => 'nullable|string',
            'is_active' => 'boolean',
            'applies_to' => 'required|in:all_units,specific_units,tenant_type',
            'frequency' => 'required|in:monthly,quarterly,yearly,one_time',
            'due_day' => 'required|integer|min:1|max:28',
            'late_fee_amount' => 'nullable|numeric|min:0',
            'late_fee_days' => 'nullable|integer|min:1|max:30',
            'metadata' => 'nullable|array',
        ]);

        try {
            $billingRule = BillingRule::create([
                ...$validated,
                'created_by' => Auth::id(),
                'is_active' => $validated['is_active'] ?? true,
                'late_fee_amount' => $validated['late_fee_amount'] ?? 0,
                'late_fee_days' => $validated['late_fee_days'] ?? 7,
            ]);

            return response()->json([
                'success' => true,
                'message' => 'Billing rule created successfully',
                'data' => $billingRule->load('creator:id,name,email'),
            ], 201);

        } catch (\Exception $e) {
            Log::error('Failed to create billing rule', [
                'error' => $e->getMessage(),
                'data' => $validated,
            ]);

            return response()->json([
                'success' => false,
                'message' => 'Failed to create billing rule',
                'error' => $e->getMessage(),
            ], 500);
        }
    }

    /**
     * Show a specific billing rule
     */
    public function showBillingRule(BillingRule $billingRule): JsonResponse
    {
        return response()->json([
            'success' => true,
            'data' => $billingRule->load('creator:id,name,email'),
            'message' => 'Billing rule retrieved successfully',
        ]);
    }

    /**
     * Update a billing rule
     */
    public function updateBillingRule(Request $request, BillingRule $billingRule): JsonResponse
    {
        $validated = $request->validate([
            'name' => 'sometimes|string|max:255',
            'type' => 'sometimes|in:maintenance,utilities,parking,non_occupancy,noc_charges',
            'calculation_method' => 'sometimes|in:per_sqft,fixed,percentage,per_unit',
            'amount' => 'sometimes|numeric|min:0',
            'description' => 'nullable|string',
            'is_active' => 'sometimes|boolean',
            'applies_to' => 'sometimes|in:all_units,specific_units,tenant_type',
            'frequency' => 'sometimes|in:monthly,quarterly,yearly,one_time',
            'due_day' => 'sometimes|integer|min:1|max:28',
            'late_fee_amount' => 'sometimes|numeric|min:0',
            'late_fee_days' => 'sometimes|integer|min:1|max:30',
            'metadata' => 'sometimes|array',
        ]);

        try {
            $billingRule->update($validated);

            return response()->json([
                'success' => true,
                'message' => 'Billing rule updated successfully',
                'data' => $billingRule->fresh()->load('creator:id,name,email'),
            ]);

        } catch (\Exception $e) {
            Log::error('Failed to update billing rule', [
                'billing_rule_id' => $billingRule->id,
                'error' => $e->getMessage(),
                'data' => $validated,
            ]);

            return response()->json([
                'success' => false,
                'message' => 'Failed to update billing rule',
                'error' => $e->getMessage(),
            ], 500);
        }
    }

    /**
     * Delete a billing rule
     */
    public function deleteBillingRule(BillingRule $billingRule): JsonResponse
    {
        try {
            $billingRule->delete();

            return response()->json([
                'success' => true,
                'message' => 'Billing rule deleted successfully',
            ]);

        } catch (\Exception $e) {
            Log::error('Failed to delete billing rule', [
                'billing_rule_id' => $billingRule->id,
                'error' => $e->getMessage(),
            ]);

            return response()->json([
                'success' => false,
                'message' => 'Failed to delete billing rule',
                'error' => $e->getMessage(),
            ], 500);
        }
    }

    /**
     * Calculate charges for a unit
     */
    public function calculateUnitCharges(Request $request, Unit $unit): JsonResponse
    {
        $validated = $request->validate([
            'charge_types' => 'nullable|array',
            'charge_types.*' => 'string|in:non_occupancy,maintenance,utilities,parking',
            'period' => 'nullable|array',
            'period.start_date' => 'nullable|date',
            'period.end_date' => 'nullable|date|after:period.start_date',
        ]);

        try {
            $chargeTypes = $validated['charge_types'] ?? ['non_occupancy', 'maintenance', 'utilities', 'parking'];
            $period = $validated['period'] ?? [];

            $charges = [];

            foreach ($chargeTypes as $chargeType) {
                switch ($chargeType) {
                    case 'non_occupancy':
                        if ($unit->isEligibleForNonOccupancyCharge()) {
                            $charges[$chargeType] = $this->billingService->getNonOccupancyCharges($unit, $period);
                        }
                        break;
                    case 'maintenance':
                        $charges[$chargeType] = $this->billingService->getMaintenanceCharges($unit, $period);
                        break;
                    case 'utilities':
                        $charges[$chargeType] = $this->billingService->getUtilityCharges($unit, $period);
                        break;
                    case 'parking':
                        $charges[$chargeType] = $this->billingService->getParkingCharges($unit, $period);
                        break;
                }
            }

            // Calculate total
            $total = 0;
            foreach ($charges as $charge) {
                $total += $charge['amount'] ?? 0;
            }

            return response()->json([
                'success' => true,
                'message' => 'Charges calculated successfully',
                'data' => [
                    'unit_id' => $unit->id,
                    'unit_number' => $unit->unit_number,
                    'charges' => $charges,
                    'total_amount' => $total,
                    'currency' => 'INR',
                    'calculation_date' => now()->toISOString(),
                    'period' => $period,
                ],
            ]);

        } catch (\Exception $e) {
            Log::error('Failed to calculate unit charges', [
                'unit_id' => $unit->id,
                'error' => $e->getMessage(),
            ]);

            return response()->json([
                'success' => false,
                'message' => 'Failed to calculate charges',
                'error' => $e->getMessage(),
            ], 500);
        }
    }

    /**
     * Calculate NOC charges for an application
     */
    public function calculateNocCharges(Request $request, NocApplication $nocApplication): JsonResponse
    {
        try {
            $charges = $this->billingService->getNocCharges($nocApplication);

            return response()->json([
                'success' => true,
                'message' => 'NOC charges calculated successfully',
                'data' => [
                    'noc_id' => $nocApplication->id,
                    'noc_type' => $nocApplication->noc_type,
                    'charges' => $charges,
                ],
            ]);

        } catch (\Exception $e) {
            Log::error('Failed to calculate NOC charges', [
                'noc_id' => $nocApplication->id,
                'error' => $e->getMessage(),
            ]);

            return response()->json([
                'success' => false,
                'message' => 'Failed to calculate NOC charges',
                'error' => $e->getMessage(),
            ], 500);
        }
    }

    /**
     * Get all charges for a unit
     */
    public function getAllCharges(Request $request, Unit $unit): JsonResponse
    {
        $validated = $request->validate([
            'period' => 'nullable|array',
            'period.start_date' => 'nullable|date',
            'period.end_date' => 'nullable|date|after:period.start_date',
        ]);

        try {
            $period = $validated['period'] ?? [];
            $charges = $this->billingService->getAllCharges($unit, $period);

            return response()->json([
                'success' => true,
                'message' => 'All charges retrieved successfully',
                'data' => $charges,
            ]);

        } catch (\Exception $e) {
            Log::error('Failed to get all charges', [
                'unit_id' => $unit->id,
                'error' => $e->getMessage(),
            ]);

            return response()->json([
                'success' => false,
                'message' => 'Failed to get charges',
                'error' => $e->getMessage(),
            ], 500);
        }
    }

    /**
     * Create billing record
     */
    public function createBillingRecord(Request $request): JsonResponse
    {
        $validated = $request->validate([
            'unit_id' => 'required|exists:units,id',
            'charge_type' => 'required|string|in:non_occupancy,noc_charges,maintenance,utilities,parking',
            'amount' => 'required|numeric|min:0',
            'period' => 'required|array',
            'period.start_date' => 'required|date',
            'period.end_date' => 'required|date|after:period.start_date',
            'breakdown' => 'nullable|array',
            'notes' => 'nullable|string',
            'due_date' => 'nullable|date',
        ]);

        try {
            $billingData = [
                'unit_id' => $validated['unit_id'],
                'charge_type' => $validated['charge_type'],
                'amount' => $validated['amount'],
                'period' => $validated['period'],
                'breakdown' => $validated['breakdown'] ?? [],
                'notes' => $validated['notes'] ?? '',
                'due_date' => $validated['due_date'] ?? now()->addDays(30)->toDateString(),
                'created_by' => Auth::id(),
                'status' => 'pending',
            ];

            $record = $this->billingService->createBillingRecord($billingData);

            return response()->json([
                'success' => true,
                'message' => 'Billing record created successfully',
                'data' => $record,
            ], 201);

        } catch (\Exception $e) {
            Log::error('Failed to create billing record', [
                'data' => $validated,
                'error' => $e->getMessage(),
            ]);

            return response()->json([
                'success' => false,
                'message' => 'Failed to create billing record',
                'error' => $e->getMessage(),
            ], 500);
        }
    }

    /**
     * Get billing history for a unit
     */
    public function getBillingHistory(Request $request, Unit $unit): JsonResponse
    {
        $validated = $request->validate([
            'charge_type' => 'nullable|string|in:non_occupancy,noc_charges,maintenance,utilities,parking',
            'status' => 'nullable|string|in:pending,paid,overdue,cancelled',
            'date_from' => 'nullable|date',
            'date_to' => 'nullable|date|after:date_from',
            'limit' => 'nullable|integer|min:1|max:100',
        ]);

        try {
            $filters = array_filter($validated);
            $history = $this->billingService->getBillingHistory($unit, $filters);

            return response()->json([
                'success' => true,
                'message' => 'Billing history retrieved successfully',
                'data' => $history,
            ]);

        } catch (\Exception $e) {
            Log::error('Failed to get billing history', [
                'unit_id' => $unit->id,
                'error' => $e->getMessage(),
            ]);

            return response()->json([
                'success' => false,
                'message' => 'Failed to get billing history',
                'error' => $e->getMessage(),
            ], 500);
        }
    }

    /**
     * List billing records with filtering and pagination
     */
    public function listBillingRecords(Request $request): JsonResponse
    {
        $request->validate([
            'unit_id' => 'nullable|exists:units,id',
            'tenant_id' => 'nullable|exists:users,id',
            'charge_type' => 'nullable|in:maintenance,utilities,parking,non_occupancy,noc_charges',
            'status' => 'nullable|in:pending,paid,overdue,cancelled',
            'period_start' => 'nullable|date',
            'period_end' => 'nullable|date|after_or_equal:period_start',
            'search' => 'nullable|string|max:255',
            'per_page' => 'nullable|integer|min:1|max:100',
        ]);

        $query = BillingRecord::with([
            'unit:id,unit_number',
            'tenant:id,name,email',
            'billingRule:id,name,type',
            'creator:id,name,email'
        ]);

        // Apply filters
        if ($request->filled('unit_id')) {
            $query->where('unit_id', $request->unit_id);
        }

        if ($request->filled('tenant_id')) {
            $query->where('tenant_id', $request->tenant_id);
        }

        if ($request->filled('charge_type')) {
            $query->where('charge_type', $request->charge_type);
        }

        if ($request->filled('status')) {
            $query->where('status', $request->status);
        }

        if ($request->filled('period_start') && $request->filled('period_end')) {
            $query->forPeriod($request->period_start, $request->period_end);
        }

        if ($request->filled('search')) {
            $query->where(function ($q) use ($request) {
                $q->where('notes', 'like', '%' . $request->search . '%')
                  ->orWhereHas('unit', function ($unitQuery) use ($request) {
                      $unitQuery->where('unit_number', 'like', '%' . $request->search . '%');
                  })
                  ->orWhereHas('tenant', function ($tenantQuery) use ($request) {
                      $tenantQuery->where('name', 'like', '%' . $request->search . '%');
                  });
            });
        }

        $records = $query->orderBy('created_at', 'desc')
                        ->paginate($request->get('per_page', 15));

        return response()->json([
            'success' => true,
            'data' => $records,
            'message' => 'Billing records retrieved successfully',
        ]);
    }

    /**
     * Get billing record by ID
     */
    public function getBillingRecord(string $recordId): JsonResponse
    {
        try {
            $record = $this->billingService->getBillingRecord($recordId);

            return response()->json([
                'success' => true,
                'message' => 'Billing record retrieved successfully',
                'data' => $record,
            ]);

        } catch (\Exception $e) {
            Log::error('Failed to get billing record', [
                'record_id' => $recordId,
                'error' => $e->getMessage(),
            ]);

            return response()->json([
                'success' => false,
                'message' => 'Failed to get billing record',
                'error' => $e->getMessage(),
            ], 500);
        }
    }

    /**
     * Update billing record
     */
    public function updateBillingRecord(Request $request, string $recordId): JsonResponse
    {
        $validated = $request->validate([
            'amount' => 'nullable|numeric|min:0',
            'status' => 'nullable|string|in:pending,paid,overdue,cancelled',
            'notes' => 'nullable|string',
            'due_date' => 'nullable|date',
            'paid_date' => 'nullable|date',
            'payment_method' => 'nullable|string',
            'transaction_id' => 'nullable|string',
        ]);

        try {
            $record = $this->billingService->updateBillingRecord($recordId, $validated);

            return response()->json([
                'success' => true,
                'message' => 'Billing record updated successfully',
                'data' => $record,
            ]);

        } catch (\Exception $e) {
            Log::error('Failed to update billing record', [
                'record_id' => $recordId,
                'error' => $e->getMessage(),
            ]);

            return response()->json([
                'success' => false,
                'message' => 'Failed to update billing record',
                'error' => $e->getMessage(),
            ], 500);
        }
    }

    /**
     * Get billing statistics
     */
    public function getBillingStatistics(Request $request): JsonResponse
    {
        $validated = $request->validate([
            'unit_id' => 'nullable|exists:units,id',
            'charge_type' => 'nullable|string|in:non_occupancy,noc_charges,maintenance,utilities,parking',
            'period' => 'nullable|array',
            'period.start_date' => 'nullable|date',
            'period.end_date' => 'nullable|date|after:period.start_date',
        ]);

        try {
            $user = Auth::user();
            $query = Unit::query();

            // Filter by user role
            if ($user->hasRole('owner')) {
                $query->where('owner_id', $user->id);
            } elseif ($user->hasRole('tenant')) {
                $query->where('current_tenant_id', $user->id);
            }

            // Filter by unit if specified
            if (isset($validated['unit_id'])) {
                $query->where('id', $validated['unit_id']);
            }

            $units = $query->get();
            $statistics = [
                'total_units' => $units->count(),
                'total_charges' => 0,
                'pending_amount' => 0,
                'paid_amount' => 0,
                'overdue_amount' => 0,
                'charge_breakdown' => [],
            ];

            foreach ($units as $unit) {
                $period = $validated['period'] ?? [];
                $charges = $this->billingService->getAllCharges($unit, $period);

                $statistics['total_charges'] += $charges['total_amount'] ?? 0;

                // Get billing history for status breakdown
                $history = $this->billingService->getBillingHistory($unit, [
                    'charge_type' => $validated['charge_type'] ?? null,
                ]);

                foreach ($history['records'] ?? [] as $record) {
                    $amount = $record['amount'] ?? 0;
                    $status = $record['status'] ?? 'pending';

                    switch ($status) {
                        case 'pending':
                            $statistics['pending_amount'] += $amount;
                            break;
                        case 'paid':
                            $statistics['paid_amount'] += $amount;
                            break;
                        case 'overdue':
                            $statistics['overdue_amount'] += $amount;
                            break;
                    }
                }
            }

            return response()->json([
                'success' => true,
                'message' => 'Billing statistics retrieved successfully',
                'data' => $statistics,
            ]);

        } catch (\Exception $e) {
            Log::error('Failed to get billing statistics', [
                'error' => $e->getMessage(),
            ]);

            return response()->json([
                'success' => false,
                'message' => 'Failed to get billing statistics',
                'error' => $e->getMessage(),
            ], 500);
        }
    }

    /**
     * Get external billing service status
     */
    public function getServiceStatus(): JsonResponse
    {
        try {
            $status = $this->billingService->getServiceStatus();

            return response()->json([
                'success' => true,
                'message' => 'Service status retrieved successfully',
                'data' => $status,
            ]);

        } catch (\Exception $e) {
            Log::error('Failed to get service status', [
                'error' => $e->getMessage(),
            ]);

            return response()->json([
                'success' => false,
                'message' => 'Failed to get service status',
                'error' => $e->getMessage(),
            ], 500);
        }
    }

    /**
     * Test external billing service connection
     */
    public function testConnection(): JsonResponse
    {
        try {
            $available = $this->billingService->isServiceAvailable();

            return response()->json([
                'success' => true,
                'message' => 'Connection test completed',
                'data' => [
                    'available' => $available,
                    'timestamp' => now()->toISOString(),
                ],
            ]);

        } catch (\Exception $e) {
            Log::error('Failed to test external billing service connection', [
                'error' => $e->getMessage(),
            ]);

            return response()->json([
                'success' => false,
                'message' => 'Connection test failed',
                'error' => $e->getMessage(),
            ], 500);
        }
    }
} 