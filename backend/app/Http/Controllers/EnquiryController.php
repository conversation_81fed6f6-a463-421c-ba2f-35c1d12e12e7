<?php

namespace App\Http\Controllers;

use App\Models\Enquiry;
use App\Models\Unit;
use App\Models\User;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Validator;
use App\Http\Requests\StoreEnquiryRequest;
use App\Http\Requests\UpdateEnquiryRequest;
use App\Http\Resources\EnquiryResource;
use App\Services\EnquiryService;
use Illuminate\Support\Facades\Log;
use Spatie\QueryBuilder\QueryBuilder;
use Spatie\QueryBuilder\AllowedFilter;

class EnquiryController extends Controller
{
    protected EnquiryService $enquiryService;

    public function __construct(EnquiryService $enquiryService)
    {
        $this->enquiryService = $enquiryService;
    }
    /**
     * @OA\Get(
     *     path="/enquiries",
     *     summary="Get all enquiries",
     *     description="Retrieve a paginated list of enquiries with filtering options",
     *     operationId="getEnquiries",
     *     tags={"Enquiries"},
     *     security={{"bearerAuth": {}}},
     *     @OA\Parameter(
     *         name="status",
     *         in="query",
     *         description="Filter by status",
     *         required=false,
     *         @OA\Schema(type="string", enum={"open", "in_progress", "resolved", "closed", "cancelled"})
     *     ),
     *     @OA\Parameter(
     *         name="type",
     *         in="query",
     *         description="Filter by enquiry type",
     *         required=false,
     *         @OA\Schema(type="string", enum={"rental_inquiry", "purchase_inquiry", "general_inquiry", "complaint", "maintenance_request"})
     *     ),
     *     @OA\Parameter(
     *         name="priority",
     *         in="query",
     *         description="Filter by priority",
     *         required=false,
     *         @OA\Schema(type="string", enum={"low", "medium", "high", "urgent"})
     *     ),
     *     @OA\Response(
     *         response=200,
     *         description="Enquiries retrieved successfully"
     *     )
     * )
     */
    public function index(Request $request): JsonResponse
    {
        try {
            $user = Auth::user();

            // Build query with filters and relationships
            $query = QueryBuilder::for(Enquiry::class)
                ->allowedFilters([
                    AllowedFilter::exact('status'),
                    AllowedFilter::exact('type'),
                    AllowedFilter::exact('priority'),
                    AllowedFilter::exact('unit_id'),
                    AllowedFilter::exact('assigned_to'),
                    AllowedFilter::scope('overdue'),
                    AllowedFilter::scope('recent'),
                ])
                ->allowedSorts(['created_at', 'updated_at', 'priority', 'status'])
                ->defaultSort('-created_at')
                ->with(['unit', 'enquirer', 'owner', 'assignedUser']);

            // Apply user-based filtering
            if ($user->hasRole('owner')) {
                $query->where('owner_id', $user->id);
            } elseif (!$user->hasRole(['admin', 'manager'])) {
                $query->where('enquirer_id', $user->id);
            }

            // Paginate results
            $enquiries = $query->paginate($request->get('per_page', 15));

            return response()->json([
                'success' => true,
                'message' => 'Enquiries retrieved successfully',
                'data' => EnquiryResource::collection($enquiries->items()),
                'meta' => [
                    'current_page' => $enquiries->currentPage(),
                    'from' => $enquiries->firstItem(),
                    'last_page' => $enquiries->lastPage(),
                    'per_page' => $enquiries->perPage(),
                    'to' => $enquiries->lastItem(),
                    'total' => $enquiries->total(),
                ]
            ]);

        } catch (\Exception $e) {
            Log::error('Failed to retrieve enquiries', [
                'error' => $e->getMessage(),
                'user_id' => Auth::id(),
            ]);

            return response()->json([
                'success' => false,
                'message' => 'Failed to retrieve enquiries',
                'error' => config('app.debug') ? $e->getMessage() : 'Internal server error'
            ], 500);
        }
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request): JsonResponse
    {
        $validator = Validator::make($request->all(), [
            'unit_id' => 'required|exists:units,id',
            'enquiry_type' => 'required|in:rental,purchase,visit,other',
            'message' => 'required|string|max:1000',
            'priority' => 'sometimes|in:low,medium,high,urgent',
            'enquirer_details' => 'sometimes|array',
            'enquirer_details.name' => 'required_with:enquirer_details|string|max:255',
            'enquirer_details.email' => 'required_with:enquirer_details|email',
            'enquirer_details.phone' => 'required_with:enquirer_details|string|max:20',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'errors' => $validator->errors()
            ], 422);
        }

        // Get the unit and its owner
        $unit = Unit::findOrFail($request->unit_id);
        $owner = User::where('id', $unit->owner_id)->where('role', 'owner')->first();

        if (!$owner) {
            return response()->json([
                'success' => false,
                'error' => 'Unit owner not found'
            ], 404);
        }

        // Create enquiry
        $enquiry = Enquiry::create([
            'unit_id' => $request->unit_id,
            'enquirer_id' => Auth::id(),
            'owner_id' => $owner->id,
            'enquiry_type' => $request->enquiry_type,
            'status' => Enquiry::STATUS_NEW,
            'priority' => $request->get('priority', Enquiry::PRIORITY_MEDIUM),
            'message' => $request->message,
            'enquirer_details' => $request->enquirer_details,
            'communication_history' => [
                [
                    'type' => 'enquiry',
                    'message' => $request->message,
                    'user_id' => Auth::id(),
                    'timestamp' => now()->toISOString(),
                ]
            ],
        ]);

        // Load relationships
        $enquiry->load(['unit', 'enquirer', 'owner']);

        return response()->json([
            'success' => true,
            'data' => $enquiry,
            'message' => 'Enquiry created successfully'
        ], 201);
    }

    /**
     * Display the specified resource.
     */
    public function show(string $id): JsonResponse
    {
        $user = Auth::user();
        $enquiry = Enquiry::with(['unit', 'enquirer', 'owner'])->findOrFail($id);

        // Check permissions
        if ($user->hasRole('owner') && $enquiry->owner_id !== $user->id) {
            return response()->json([
                'success' => false,
                'error' => 'Unauthorized access'
            ], 403);
        }

        if (!$user->hasRole(['admin', 'manager', 'owner']) && $enquiry->enquirer_id !== $user->id) {
            return response()->json([
                'success' => false,
                'error' => 'Unauthorized access'
            ], 403);
        }

        return response()->json([
            'success' => true,
            'data' => $enquiry
        ]);
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, string $id): JsonResponse
    {
        $user = Auth::user();
        $enquiry = Enquiry::findOrFail($id);

        // Check permissions
        if ($user->hasRole('owner') && $enquiry->owner_id !== $user->id) {
            return response()->json([
                'success' => false,
                'error' => 'Unauthorized access'
            ], 403);
        }

        if (!$user->hasRole(['admin', 'manager', 'owner']) && $enquiry->enquirer_id !== $user->id) {
            return response()->json([
                'success' => false,
                'error' => 'Unauthorized access'
            ], 403);
        }

        $validator = Validator::make($request->all(), [
            'status' => 'sometimes|in:new,contacted,interested,not_interested,closed',
            'priority' => 'sometimes|in:low,medium,high,urgent',
            'notes' => 'sometimes|string|max:1000',
            'response_data' => 'sometimes|array',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'errors' => $validator->errors()
            ], 422);
        }

        // Update enquiry
        $enquiry->update($request->only(['status', 'priority', 'notes', 'response_data']));

        // Add communication entry if notes provided
        if ($request->has('notes') && $request->notes) {
            $enquiry->addCommunicationEntry('note', $request->notes, $user->id);
        }

        // Load relationships
        $enquiry->load(['unit', 'enquirer', 'owner']);

        return response()->json([
            'success' => true,
            'data' => $enquiry,
            'message' => 'Enquiry updated successfully'
        ]);
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(string $id): JsonResponse
    {
        $user = Auth::user();
        $enquiry = Enquiry::findOrFail($id);

        // Check permissions - only admins and managers can delete
        if (!$user->hasRole(['admin', 'manager'])) {
            return response()->json([
                'success' => false,
                'error' => 'Unauthorized access'
            ], 403);
        }

        $enquiry->delete();

        return response()->json([
            'success' => true,
            'message' => 'Enquiry deleted successfully'
        ]);
    }

    /**
     * Mark enquiry as contacted
     */
    public function markAsContacted(string $id): JsonResponse
    {
        $user = Auth::user();
        $enquiry = Enquiry::findOrFail($id);

        // Check permissions
        if ($user->hasRole('owner') && $enquiry->owner_id !== $user->id) {
            return response()->json([
                'success' => false,
                'error' => 'Unauthorized access'
            ], 403);
        }

        if (!$user->hasRole(['admin', 'manager', 'owner'])) {
            return response()->json([
                'success' => false,
                'error' => 'Unauthorized access'
            ], 403);
        }

        $enquiry->markAsContacted();
        $enquiry->addCommunicationEntry('status_change', 'Enquiry marked as contacted', $user->id);

        $enquiry->load(['unit', 'enquirer', 'owner']);

        return response()->json([
            'success' => true,
            'data' => $enquiry,
            'message' => 'Enquiry marked as contacted'
        ]);
    }

    /**
     * Mark enquiry as interested
     */
    public function markAsInterested(string $id): JsonResponse
    {
        $user = Auth::user();
        $enquiry = Enquiry::findOrFail($id);

        // Check permissions
        if ($user->hasRole('owner') && $enquiry->owner_id !== $user->id) {
            return response()->json([
                'success' => false,
                'error' => 'Unauthorized access'
            ], 403);
        }

        if (!$user->hasRole(['admin', 'manager', 'owner'])) {
            return response()->json([
                'success' => false,
                'error' => 'Unauthorized access'
            ], 403);
        }

        $enquiry->markAsInterested();
        $enquiry->addCommunicationEntry('status_change', 'Enquiry marked as interested', $user->id);

        $enquiry->load(['unit', 'enquirer', 'owner']);

        return response()->json([
            'success' => true,
            'data' => $enquiry,
            'message' => 'Enquiry marked as interested'
        ]);
    }

    /**
     * Mark enquiry as not interested
     */
    public function markAsNotInterested(string $id): JsonResponse
    {
        $user = Auth::user();
        $enquiry = Enquiry::findOrFail($id);

        // Check permissions
        if ($user->hasRole('owner') && $enquiry->owner_id !== $user->id) {
            return response()->json([
                'success' => false,
                'error' => 'Unauthorized access'
            ], 403);
        }

        if (!$user->hasRole(['admin', 'manager', 'owner'])) {
            return response()->json([
                'success' => false,
                'error' => 'Unauthorized access'
            ], 403);
        }

        $enquiry->markAsNotInterested();
        $enquiry->addCommunicationEntry('status_change', 'Enquiry marked as not interested', $user->id);

        $enquiry->load(['unit', 'enquirer', 'owner']);

        return response()->json([
            'success' => true,
            'data' => $enquiry,
            'message' => 'Enquiry marked as not interested'
        ]);
    }

    /**
     * Close enquiry
     */
    public function close(string $id): JsonResponse
    {
        $user = Auth::user();
        $enquiry = Enquiry::findOrFail($id);

        // Check permissions
        if ($user->hasRole('owner') && $enquiry->owner_id !== $user->id) {
            return response()->json([
                'success' => false,
                'error' => 'Unauthorized access'
            ], 403);
        }

        if (!$user->hasRole(['admin', 'manager', 'owner'])) {
            return response()->json([
                'success' => false,
                'error' => 'Unauthorized access'
            ], 403);
        }

        $enquiry->close();
        $enquiry->addCommunicationEntry('status_change', 'Enquiry closed', $user->id);

        $enquiry->load(['unit', 'enquirer', 'owner']);

        return response()->json([
            'success' => true,
            'data' => $enquiry,
            'message' => 'Enquiry closed'
        ]);
    }

    /**
     * Add communication entry
     */
    public function addCommunication(Request $request, string $id): JsonResponse
    {
        $user = Auth::user();
        $enquiry = Enquiry::findOrFail($id);

        // Check permissions
        if ($user->hasRole('owner') && $enquiry->owner_id !== $user->id) {
            return response()->json([
                'success' => false,
                'error' => 'Unauthorized access'
            ], 403);
        }

        if (!$user->hasRole(['admin', 'manager', 'owner']) && $enquiry->enquirer_id !== $user->id) {
            return response()->json([
                'success' => false,
                'error' => 'Unauthorized access'
            ], 403);
        }

        $validator = Validator::make($request->all(), [
            'type' => 'required|in:email,sms,call,note',
            'message' => 'required|string|max:1000',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'errors' => $validator->errors()
            ], 422);
        }

        $enquiry->addCommunicationEntry($request->type, $request->message, $user->id);

        $enquiry->load(['unit', 'enquirer', 'owner']);

        return response()->json([
            'success' => true,
            'data' => $enquiry,
            'message' => 'Communication entry added'
        ]);
    }

    /**
     * Get enquiry statistics
     */
    public function statistics(): JsonResponse
    {
        $user = Auth::user();
        $baseQuery = Enquiry::query();

        // Filter by user role
        if ($user->hasRole('owner')) {
            $baseQuery->forOwner($user->id);
        } elseif (!$user->hasRole(['admin', 'manager'])) {
            $baseQuery->where('enquirer_id', $user->id);
        }

        $statistics = [
            'total' => (clone $baseQuery)->count(),
            'new' => (clone $baseQuery)->byStatus(Enquiry::STATUS_NEW)->count(),
            'contacted' => (clone $baseQuery)->byStatus(Enquiry::STATUS_CONTACTED)->count(),
            'interested' => (clone $baseQuery)->byStatus(Enquiry::STATUS_INTERESTED)->count(),
            'not_interested' => (clone $baseQuery)->byStatus(Enquiry::STATUS_NOT_INTERESTED)->count(),
            'closed' => (clone $baseQuery)->byStatus(Enquiry::STATUS_CLOSED)->count(),
            'urgent' => (clone $baseQuery)->byPriority(Enquiry::PRIORITY_URGENT)->count(),
            'high_priority' => (clone $baseQuery)->byPriority(Enquiry::PRIORITY_HIGH)->count(),
            'recent_30_days' => (clone $baseQuery)->recent(30)->count(),
            'recent_7_days' => (clone $baseQuery)->recent(7)->count(),
        ];

        return response()->json([
            'success' => true,
            'data' => $statistics
        ]);
    }

    /**
     * Get available units for enquiry
     */
    public function availableUnits(): JsonResponse
    {
        $units = Unit::where('status', Unit::STATUS_VACANT)
            ->with(['owner'])
            ->get()
            ->map(function ($unit) {
                return [
                    'id' => $unit->id,
                    'unit_number' => $unit->unit_number,
                    'block' => $unit->block,
                    'floor' => $unit->floor,
                    'type' => $unit->type,
                    'owner' => $unit->owner ? [
                        'id' => $unit->owner->id,
                        'name' => $unit->owner->name,
                    ] : null,
                ];
            });

        return response()->json([
            'success' => true,
            'data' => $units
        ]);
    }
}
