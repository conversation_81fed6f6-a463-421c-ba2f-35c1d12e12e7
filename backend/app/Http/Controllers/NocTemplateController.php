<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use App\Models\NocTemplate;
use Illuminate\Support\Facades\Auth;
use Illuminate\Validation\Rule;
use Illuminate\Http\JsonResponse;

class NocTemplateController extends Controller
{
    /**
     * Display a listing of NOC templates
     */
    public function index(Request $request): JsonResponse
    {
        $query = NocTemplate::with('creator');

        // Filter by NOC type
        if ($request->filled('noc_type')) {
            $query->byType($request->noc_type);
        }

        // Filter by active status
        if ($request->boolean('active_only', true)) {
            $query->active();
        }

        $templates = $query->orderBy('created_at', 'desc')->get();

        return response()->json([
            'success' => true,
            'data' => $templates,
            'message' => 'NOC templates retrieved successfully'
        ]);
    }

    /**
     * Store a newly created NOC template
     */
    public function store(Request $request): JsonResponse
    {
        $user = Auth::user();
        
        // Only admin/manager can create templates
        if (!$user->hasRole(['admin', 'manager'])) {
            return response()->json([
                'success' => false,
                'message' => 'Unauthorized. Only admin or manager can create NOC templates.'
            ], 403);
        }

        $validated = $request->validate([
            'name' => 'required|string|max:255',
            'description' => 'nullable|string',
            'noc_type' => ['required', Rule::in(NocTemplate::getAllTypes())],
            'template_content' => 'required|string',
            'required_fields' => 'nullable|array',
            'document_requirements' => 'nullable|array',
            'placeholders' => 'nullable|array',
            'is_active' => 'boolean',
        ]);

        // If no custom fields provided, use defaults
        if (empty($validated['required_fields'])) {
            $validated['required_fields'] = NocTemplate::getDefaultRequiredFields($validated['noc_type']);
        }

        if (empty($validated['document_requirements'])) {
            $validated['document_requirements'] = NocTemplate::getDefaultDocumentRequirements($validated['noc_type']);
        }

        $template = NocTemplate::create([
            ...$validated,
            'created_by' => $user->id,
        ]);

        return response()->json([
            'success' => true,
            'data' => $template->load('creator'),
            'message' => 'NOC template created successfully'
        ], 201);
    }

    /**
     * Display the specified NOC template
     */
    public function show(NocTemplate $template): JsonResponse
    {
        return response()->json([
            'success' => true,
            'data' => $template->load('creator'),
            'message' => 'NOC template retrieved successfully'
        ]);
    }

    /**
     * Update the specified NOC template
     */
    public function update(Request $request, NocTemplate $template): JsonResponse
    {
        $user = Auth::user();
        
        // Only admin/manager can update templates
        if (!$user->hasRole(['admin', 'manager'])) {
            return response()->json([
                'success' => false,
                'message' => 'Unauthorized. Only admin or manager can update NOC templates.'
            ], 403);
        }

        $validated = $request->validate([
            'name' => 'sometimes|required|string|max:255',
            'description' => 'nullable|string',
            'noc_type' => ['sometimes', 'required', Rule::in(NocTemplate::getAllTypes())],
            'template_content' => 'sometimes|required|string',
            'required_fields' => 'nullable|array',
            'document_requirements' => 'nullable|array',
            'placeholders' => 'nullable|array',
            'is_active' => 'boolean',
        ]);

        $template->update($validated);

        return response()->json([
            'success' => true,
            'data' => $template->fresh(['creator']),
            'message' => 'NOC template updated successfully'
        ]);
    }

    /**
     * Remove the specified NOC template
     */
    public function destroy(NocTemplate $template): JsonResponse
    {
        $user = Auth::user();
        
        // Only admin/manager can delete templates
        if (!$user->hasRole(['admin', 'manager'])) {
            return response()->json([
                'success' => false,
                'message' => 'Unauthorized. Only admin or manager can delete NOC templates.'
            ], 403);
        }

        // Check if template is being used
        if ($template->applications()->exists()) {
            return response()->json([
                'success' => false,
                'message' => 'Cannot delete template. It is being used by existing NOC applications.'
            ], 422);
        }

        $template->delete();

        return response()->json([
            'success' => true,
            'message' => 'NOC template deleted successfully'
        ]);
    }

    /**
     * Get form configuration for a specific NOC type
     */
    public function getFormConfig(Request $request): JsonResponse
    {
        $validated = $request->validate([
            'noc_type' => ['required', Rule::in(NocTemplate::getAllTypes())],
            'template_id' => 'nullable|exists:noc_templates,id',
        ]);

        $nocType = $validated['noc_type'];
        $template = null;

        // If template_id provided, use that template
        if (isset($validated['template_id'])) {
            $template = NocTemplate::active()
                ->byType($nocType)
                ->find($validated['template_id']);
        } else {
            // Otherwise, get the first active template for this type
            $template = NocTemplate::active()
                ->byType($nocType)
                ->first();
        }

        $formConfig = [
            'noc_type' => $nocType,
            'type_display_name' => $template ? $template->getTypeDisplayName() : ucfirst($nocType) . ' NOC',
            'required_fields' => $template 
                ? $template->required_fields 
                : NocTemplate::getDefaultRequiredFields($nocType),
            'document_requirements' => $template 
                ? $template->document_requirements 
                : NocTemplate::getDefaultDocumentRequirements($nocType),
            'template' => $template,
        ];

        return response()->json([
            'success' => true,
            'data' => $formConfig,
            'message' => 'Form configuration retrieved successfully'
        ]);
    }

    /**
     * Get all available NOC types with their descriptions
     */
    public function getTypes(): JsonResponse
    {
        $types = [];
        foreach (NocTemplate::getAllTypes() as $type) {
            $types[] = [
                'value' => $type,
                'label' => match($type) {
                    'rental' => 'Rental NOC',
                    'residence' => 'Residence NOC',
                    'vehicle' => 'Vehicle Parking NOC',
                    'renovation' => 'Renovation NOC',
                    'transfer' => 'Transfer NOC',
                    default => ucfirst($type) . ' NOC',
                },
                'description' => match($type) {
                    'rental' => 'No Objection Certificate for renting out your property',
                    'residence' => 'No Objection Certificate for residing in the property',
                    'vehicle' => 'No Objection Certificate for vehicle parking',
                    'renovation' => 'No Objection Certificate for property renovation',
                    'transfer' => 'No Objection Certificate for property transfer',
                    default => 'No Objection Certificate for ' . $type,
                },
                'template_count' => NocTemplate::active()->byType($type)->count(),
            ];
        }

        return response()->json([
            'success' => true,
            'data' => $types,
            'message' => 'NOC types retrieved successfully'
        ]);
    }

    /**
     * Preview NOC document with sample data
     */
    public function preview(NocTemplate $template): JsonResponse
    {
        $sampleData = $this->getSampleDataForType($template->noc_type);
        
        // Replace placeholders in template content
        $previewContent = $template->template_content;
        foreach ($sampleData as $key => $value) {
            $previewContent = str_replace('{' . $key . '}', $value, $previewContent);
        }

        return response()->json([
            'success' => true,
            'data' => [
                'template' => $template,
                'preview_content' => $previewContent,
                'sample_data' => $sampleData,
            ],
            'message' => 'Template preview generated successfully'
        ]);
    }

    /**
     * Generate sample data for template preview
     */
    private function getSampleDataForType(string $type): array
    {
        $commonData = [
            'applicant_name' => 'John Doe',
            'unit_number' => 'A-101',
            'contact_number' => '+91-9876543210',
            'email' => '<EMAIL>',
            'application_date' => now()->format('d/m/Y'),
            'society_name' => 'Green Valley Housing Society',
        ];

        $typeSpecificData = match($type) {
            'rental' => [
                'tenant_name' => 'Jane Smith',
                'rental_amount' => '₹25,000',
                'lease_duration' => '12 months',
                'move_in_date' => now()->addDays(30)->format('d/m/Y'),
            ],
            'vehicle' => [
                'vehicle_type' => 'Car',
                'vehicle_number' => 'MH 01 AB 1234',
                'parking_slot' => 'P-15',
            ],
            'renovation' => [
                'renovation_type' => 'Interior',
                'start_date' => now()->addDays(15)->format('d/m/Y'),
                'end_date' => now()->addDays(45)->format('d/m/Y'),
                'contractor_name' => 'ABC Construction Company',
            ],
            default => [],
        };

        return array_merge($commonData, $typeSpecificData);
    }
} 