<?php

namespace App\Http\Controllers;

/**
 * @OA\Info(
 *     title="Housing Society TMS API",
 *     version="1.0.0",
 *     description="Comprehensive API for Housing Society Tenant Management System with multi-tenant architecture, Keycloak authentication, and Kong API Gateway integration.",
 *     @OA\Contact(
 *         email="<EMAIL>",
 *         name="TMS Development Team",
 *         url="https://tms.yourdomain.com"
 *     ),
 *     @OA\License(
 *         name="MIT",
 *         url="https://opensource.org/licenses/MIT"
 *     ),
 *     termsOfService="https://tms.yourdomain.com/terms"
 * )
 *
 * @OA\Server(
 *     url="http://localhost:8000/api/v1",
 *     description="Development server (Kong Gateway)"
 * )
 *
 * @OA\Server(
 *     url="http://localhost:8080/api/v1",
 *     description="Development server (Direct Laravel)"
 * )
 *
 * @OA\Server(
 *     url="https://api-staging.tms.com/api/v1",
 *     description="Staging server"
 * )
 *
 * @OA\Server(
 *     url="https://api.tms.com/api/v1",
 *     description="Production server"
 * )
 *
 * @OA\SecurityScheme(
 *     securityScheme="bearerAuth",
 *     type="http",
 *     scheme="bearer",
 *     bearerFormat="JWT",
 *     description="JWT token obtained from Keycloak authentication. Format: Bearer <token>"
 * )
 *
 * @OA\SecurityScheme(
 *     securityScheme="sanctumAuth",
 *     type="http",
 *     scheme="bearer",
 *     bearerFormat="Token",
 *     description="Laravel Sanctum token for API authentication"
 * )
 *
 * @OA\Tag(
 *     name="Authentication",
 *     description="User authentication and authorization endpoints"
 * )
 *
 * @OA\Tag(
 *     name="Units",
 *     description="Unit management operations with four-state system (Occupied, Vacant, To-Let, Rented)"
 * )
 *
 * @OA\Tag(
 *     name="Tenants",
 *     description="Tenant management, onboarding, and KYC verification"
 * )
 *
 * @OA\Tag(
 *     name="Agreements",
 *     description="Agreement generation, digital signatures, and renewal management"
 * )
 *
 * @OA\Tag(
 *     name="Property Listings",
 *     description="Property listing management and portal integration (99acres, MagicBricks, etc.)"
 * )
 *
 * @OA\Tag(
 *     name="NOC",
 *     description="No Objection Certificate workflow and approval management"
 * )
 *
 * @OA\Tag(
 *     name="Documents",
 *     description="Document management, KYC uploads, and verification workflow"
 * )
 *
 * @OA\Tag(
 *     name="Notifications",
 *     description="Notification system with email, SMS, and in-app notifications"
 * )
 *
 * @OA\Tag(
 *     name="Billing",
 *     description="Billing, payment management, and non-occupancy charges"
 * )
 *
 * @OA\Tag(
 *     name="Reports",
 *     description="Analytics, reporting, and dashboard metrics"
 * )
 */
abstract class Controller
{
    //
}
