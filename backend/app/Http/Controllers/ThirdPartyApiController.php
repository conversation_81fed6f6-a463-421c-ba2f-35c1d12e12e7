<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Illuminate\Http\Response;
use App\Models\ThirdPartyApiConfig;
use App\Models\ThirdPartyApiLog;
use App\Services\ThirdPartyApiService;
use App\Http\Requests\ThirdPartyApiConfigRequest;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Log;

class ThirdPartyApiController extends Controller
{
    protected $thirdPartyApiService;

    public function __construct(ThirdPartyApiService $thirdPartyApiService)
    {
        $this->thirdPartyApiService = $thirdPartyApiService;
    }

    /**
     * @OA\Get(
     *     path="/api/v1/third-party-apis",
     *     summary="List all third-party API configurations",
     *     description="Retrieve a paginated list of all third-party API configurations",
     *     operationId="listThirdPartyApis",
     *     tags={"Third Party APIs"},
     *     security={{"bearerAuth":{}}},
     *     @OA\Parameter(
     *         name="page",
     *         in="query",
     *         description="Page number",
     *         required=false,
     *         @OA\Schema(type="integer", example=1)
     *     ),
     *     @OA\Parameter(
     *         name="per_page",
     *         in="query",
     *         description="Items per page",
     *         required=false,
     *         @OA\Schema(type="integer", example=15)
     *     ),
     *     @OA\Parameter(
     *         name="environment",
     *         in="query",
     *         description="Filter by environment",
     *         required=false,
     *         @OA\Schema(type="string", enum={"production", "staging", "development", "testing"})
     *     ),
     *     @OA\Parameter(
     *         name="is_active",
     *         in="query",
     *         description="Filter by active status",
     *         required=false,
     *         @OA\Schema(type="boolean")
     *     ),
     *     @OA\Response(
     *         response=200,
     *         description="Successful operation",
     *         @OA\JsonContent(
     *             type="object",
     *             @OA\Property(property="success", type="boolean", example=true),
     *             @OA\Property(property="data", type="array", @OA\Items(ref="#/components/schemas/ThirdPartyApiConfig")),
     *             @OA\Property(property="pagination", type="object")
     *         )
     *     ),
     *     @OA\Response(response=401, description="Unauthorized"),
     *     @OA\Response(response=403, description="Forbidden")
     * )
     */
    public function index(Request $request)
    {
        $query = ThirdPartyApiConfig::query();

        // Apply filters
        if ($request->has('environment')) {
            $query->where('environment', $request->environment);
        }

        if ($request->has('is_active')) {
            $query->where('is_active', $request->boolean('is_active'));
        }

        if ($request->has('search')) {
            $query->where(function ($q) use ($request) {
                $q->where('name', 'like', '%' . $request->search . '%')
                  ->orWhere('description', 'like', '%' . $request->search . '%')
                  ->orWhere('identifier', 'like', '%' . $request->search . '%');
            });
        }

        $apis = $query->paginate($request->get('per_page', 15));

        return response()->json([
            'success' => true,
            'data' => $apis->items(),
            'pagination' => [
                'current_page' => $apis->currentPage(),
                'last_page' => $apis->lastPage(),
                'per_page' => $apis->perPage(),
                'total' => $apis->total(),
            ]
        ]);
    }

    /**
     * @OA\Post(
     *     path="/api/v1/third-party-apis",
     *     summary="Create new third-party API configuration",
     *     description="Create a new third-party API configuration",
     *     operationId="createThirdPartyApi",
     *     tags={"Third Party APIs"},
     *     security={{"bearerAuth":{}}},
     *     @OA\RequestBody(
     *         required=true,
     *         @OA\JsonContent(ref="#/components/schemas/ThirdPartyApiConfigRequest")
     *     ),
     *     @OA\Response(
     *         response=201,
     *         description="API configuration created successfully",
     *         @OA\JsonContent(
     *             type="object",
     *             @OA\Property(property="success", type="boolean", example=true),
     *             @OA\Property(property="message", type="string", example="API configuration created successfully"),
     *             @OA\Property(property="data", ref="#/components/schemas/ThirdPartyApiConfig")
     *         )
     *     ),
     *     @OA\Response(response=400, description="Bad request"),
     *     @OA\Response(response=401, description="Unauthorized"),
     *     @OA\Response(response=403, description="Forbidden")
     * )
     */
    public function store(ThirdPartyApiConfigRequest $request)
    {
        try {
            $data = $request->validated();

            // Create the API configuration
            $apiConfig = ThirdPartyApiConfig::create($data);

            // Test connection if requested
            if ($request->test_connection) {
                $testResult = $this->thirdPartyApiService->testConnection($apiConfig);
                if (!$testResult['success']) {
                    return response()->json([
                        'success' => false,
                        'message' => 'API configuration created but connection test failed',
                        'data' => $apiConfig->getDisplayConfig(),
                        'test_result' => $testResult
                    ], 201);
                }
            }

            return response()->json([
                'success' => true,
                'message' => 'API configuration created successfully',
                'data' => $apiConfig->getDisplayConfig()
            ], 201);

        } catch (\Exception $e) {
            Log::error('Failed to create API configuration', [
                'error' => $e->getMessage(),
                'data' => $request->all()
            ]);

            return response()->json([
                'success' => false,
                'message' => 'Failed to create API configuration',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * @OA\Get(
     *     path="/api/v1/third-party-apis/{id}",
     *     summary="Get third-party API configuration",
     *     description="Get details of a specific third-party API configuration",
     *     operationId="getThirdPartyApi",
     *     tags={"Third Party APIs"},
     *     security={{"bearerAuth":{}}},
     *     @OA\Parameter(
     *         name="id",
     *         in="path",
     *         description="API configuration ID",
     *         required=true,
     *         @OA\Schema(type="integer")
     *     ),
     *     @OA\Response(
     *         response=200,
     *         description="Successful operation",
     *         @OA\JsonContent(
     *             type="object",
     *             @OA\Property(property="success", type="boolean", example=true),
     *             @OA\Property(property="data", ref="#/components/schemas/ThirdPartyApiConfig")
     *         )
     *     ),
     *     @OA\Response(response=404, description="Not found"),
     *     @OA\Response(response=401, description="Unauthorized"),
     *     @OA\Response(response=403, description="Forbidden")
     * )
     */
    public function show($id)
    {
        $apiConfig = ThirdPartyApiConfig::find($id);

        if (!$apiConfig) {
            return response()->json([
                'success' => false,
                'message' => 'API configuration not found'
            ], 404);
        }

        return response()->json([
            'success' => true,
            'data' => $apiConfig->getDisplayConfig()
        ]);
    }

    /**
     * @OA\Put(
     *     path="/api/v1/third-party-apis/{id}",
     *     summary="Update third-party API configuration",
     *     description="Update an existing third-party API configuration",
     *     operationId="updateThirdPartyApi",
     *     tags={"Third Party APIs"},
     *     security={{"bearerAuth":{}}},
     *     @OA\Parameter(
     *         name="id",
     *         in="path",
     *         description="API configuration ID",
     *         required=true,
     *         @OA\Schema(type="integer")
     *     ),
     *     @OA\RequestBody(
     *         required=true,
     *         @OA\JsonContent(ref="#/components/schemas/ThirdPartyApiConfigRequest")
     *     ),
     *     @OA\Response(
     *         response=200,
     *         description="API configuration updated successfully",
     *         @OA\JsonContent(
     *             type="object",
     *             @OA\Property(property="success", type="boolean", example=true),
     *             @OA\Property(property="message", type="string", example="API configuration updated successfully"),
     *             @OA\Property(property="data", ref="#/components/schemas/ThirdPartyApiConfig")
     *         )
     *     ),
     *     @OA\Response(response=404, description="Not found"),
     *     @OA\Response(response=400, description="Bad request"),
     *     @OA\Response(response=401, description="Unauthorized"),
     *     @OA\Response(response=403, description="Forbidden")
     * )
     */
    public function update(ThirdPartyApiConfigRequest $request, $id)
    {
        try {
            $apiConfig = ThirdPartyApiConfig::find($id);

            if (!$apiConfig) {
                return response()->json([
                    'success' => false,
                    'message' => 'API configuration not found'
                ], 404);
            }

            $data = $request->validated();
            $apiConfig->update($data);

            // Test connection if requested
            if ($request->test_connection) {
                $testResult = $this->thirdPartyApiService->testConnection($apiConfig);
                if (!$testResult['success']) {
                    return response()->json([
                        'success' => true,
                        'message' => 'API configuration updated but connection test failed',
                        'data' => $apiConfig->getDisplayConfig(),
                        'test_result' => $testResult
                    ]);
                }
            }

            return response()->json([
                'success' => true,
                'message' => 'API configuration updated successfully',
                'data' => $apiConfig->getDisplayConfig()
            ]);

        } catch (\Exception $e) {
            Log::error('Failed to update API configuration', [
                'id' => $id,
                'error' => $e->getMessage(),
                'data' => $request->all()
            ]);

            return response()->json([
                'success' => false,
                'message' => 'Failed to update API configuration',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * @OA\Delete(
     *     path="/api/v1/third-party-apis/{id}",
     *     summary="Delete third-party API configuration",
     *     description="Delete a third-party API configuration",
     *     operationId="deleteThirdPartyApi",
     *     tags={"Third Party APIs"},
     *     security={{"bearerAuth":{}}},
     *     @OA\Parameter(
     *         name="id",
     *         in="path",
     *         description="API configuration ID",
     *         required=true,
     *         @OA\Schema(type="integer")
     *     ),
     *     @OA\Response(
     *         response=200,
     *         description="API configuration deleted successfully",
     *         @OA\JsonContent(
     *             type="object",
     *             @OA\Property(property="success", type="boolean", example=true),
     *             @OA\Property(property="message", type="string", example="API configuration deleted successfully")
     *         )
     *     ),
     *     @OA\Response(response=404, description="Not found"),
     *     @OA\Response(response=401, description="Unauthorized"),
     *     @OA\Response(response=403, description="Forbidden")
     * )
     */
    public function destroy($id)
    {
        try {
            $apiConfig = ThirdPartyApiConfig::find($id);

            if (!$apiConfig) {
                return response()->json([
                    'success' => false,
                    'message' => 'API configuration not found'
                ], 404);
            }

            $apiConfig->delete();

            return response()->json([
                'success' => true,
                'message' => 'API configuration deleted successfully'
            ]);

        } catch (\Exception $e) {
            Log::error('Failed to delete API configuration', [
                'id' => $id,
                'error' => $e->getMessage()
            ]);

            return response()->json([
                'success' => false,
                'message' => 'Failed to delete API configuration',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * @OA\Post(
     *     path="/api/v1/third-party-apis/{id}/test-connection",
     *     summary="Test API connection",
     *     description="Test the connection to a third-party API",
     *     operationId="testApiConnection",
     *     tags={"Third Party APIs"},
     *     security={{"bearerAuth":{}}},
     *     @OA\Parameter(
     *         name="id",
     *         in="path",
     *         description="API configuration ID",
     *         required=true,
     *         @OA\Schema(type="integer")
     *     ),
     *     @OA\Response(
     *         response=200,
     *         description="Connection test result",
     *         @OA\JsonContent(
     *             type="object",
     *             @OA\Property(property="success", type="boolean", example=true),
     *             @OA\Property(property="message", type="string", example="Connection successful"),
     *             @OA\Property(property="details", type="object")
     *         )
     *     ),
     *     @OA\Response(response=404, description="Not found"),
     *     @OA\Response(response=401, description="Unauthorized"),
     *     @OA\Response(response=403, description="Forbidden")
     * )
     */
    public function testConnection($id)
    {
        $apiConfig = ThirdPartyApiConfig::find($id);

        if (!$apiConfig) {
            return response()->json([
                'success' => false,
                'message' => 'API configuration not found'
            ], 404);
        }

        $result = $this->thirdPartyApiService->testConnection($apiConfig);

        return response()->json($result);
    }

    /**
     * @OA\Post(
     *     path="/api/v1/third-party-apis/{api_id}/request",
     *     summary="Make API request",
     *     description="Make a request to a third-party API",
     *     operationId="makeApiRequest",
     *     tags={"Third Party APIs"},
     *     security={{"bearerAuth":{}}},
     *     @OA\Parameter(
     *         name="api_id",
     *         in="path",
     *         description="API configuration ID or identifier",
     *         required=true,
     *         @OA\Schema(type="string")
     *     ),
     *     @OA\RequestBody(
     *         required=true,
     *         @OA\JsonContent(
     *             type="object",
     *             @OA\Property(property="endpoint", type="string", example="/users"),
     *             @OA\Property(property="method", type="string", example="GET"),
     *             @OA\Property(property="data", type="object"),
     *             @OA\Property(property="headers", type="object")
     *         )
     *     ),
     *     @OA\Response(
     *         response=200,
     *         description="API request successful",
     *         @OA\JsonContent(
     *             type="object",
     *             @OA\Property(property="success", type="boolean", example=true),
     *             @OA\Property(property="data", type="object"),
     *             @OA\Property(property="meta", type="object")
     *         )
     *     ),
     *     @OA\Response(response=400, description="Bad request"),
     *     @OA\Response(response=404, description="Not found"),
     *     @OA\Response(response=401, description="Unauthorized"),
     *     @OA\Response(response=403, description="Forbidden")
     * )
     */
    public function makeRequest(Request $request, $apiId)
    {
        // This method is called through the middleware
        // which has already set the api_config in the request
        $apiConfig = $request->api_config;

        $endpoint = $request->input('endpoint', '/');
        $method = $request->input('method', 'GET');
        $data = $request->input('data', []);
        $headers = $request->input('headers', []);

        // Transform request data if mapping is configured
        if ($apiConfig->request_mapping) {
            $data = $this->thirdPartyApiService->transformRequestData($apiConfig, $data);
        }

        // Check cache first
        if ($apiConfig->cache_enabled && strtoupper($method) === 'GET') {
            $cacheKey = $this->thirdPartyApiService->generateCacheKey($apiConfig, $endpoint, $method, $data);
            $cachedResponse = $this->thirdPartyApiService->getCachedResponse($apiConfig, $cacheKey);
            
            if ($cachedResponse) {
                return response()->json([
                    'success' => true,
                    'data' => $cachedResponse,
                    'meta' => [
                        'cached' => true,
                        'api_name' => $apiConfig->name
                    ]
                ]);
            }
        }

        // Make the API request
        $startTime = microtime(true);
        $result = $this->thirdPartyApiService->makeRequest($apiConfig, $endpoint, $method, $data, $headers);
        $responseTime = microtime(true) - $startTime;

        if ($result['success']) {
            // Transform response data if mapping is configured
            if ($apiConfig->response_mapping) {
                $result['body'] = $this->thirdPartyApiService->transformResponseData($apiConfig, $result['body']);
            }

            // Cache successful GET responses
            if ($apiConfig->cache_enabled && strtoupper($method) === 'GET') {
                $cacheKey = $this->thirdPartyApiService->generateCacheKey($apiConfig, $endpoint, $method, $data);
                $this->thirdPartyApiService->cacheResponse($apiConfig, $cacheKey, $result['body']);
            }

            return response()->json([
                'success' => true,
                'data' => $result['body'],
                'meta' => [
                    'status_code' => $result['status_code'],
                    'response_time' => round($responseTime, 3),
                    'api_name' => $apiConfig->name,
                    'cached' => false
                ]
            ]);
        } else {
            return response()->json([
                'success' => false,
                'error' => $result['error'],
                'meta' => [
                    'status_code' => $result['status_code'],
                    'response_time' => round($responseTime, 3),
                    'api_name' => $apiConfig->name
                ]
            ], $result['status_code'] ?: 500);
        }
    }

    /**
     * @OA\Get(
     *     path="/api/v1/third-party-apis/{id}/logs",
     *     summary="Get API logs",
     *     description="Get logs for a specific third-party API",
     *     operationId="getApiLogs",
     *     tags={"Third Party APIs"},
     *     security={{"bearerAuth":{}}},
     *     @OA\Parameter(
     *         name="id",
     *         in="path",
     *         description="API configuration ID",
     *         required=true,
     *         @OA\Schema(type="integer")
     *     ),
     *     @OA\Parameter(
     *         name="page",
     *         in="query",
     *         description="Page number",
     *         required=false,
     *         @OA\Schema(type="integer", example=1)
     *     ),
     *     @OA\Parameter(
     *         name="per_page",
     *         in="query",
     *         description="Items per page",
     *         required=false,
     *         @OA\Schema(type="integer", example=15)
     *     ),
     *     @OA\Parameter(
     *         name="status",
     *         in="query",
     *         description="Filter by status",
     *         required=false,
     *         @OA\Schema(type="string", enum={"pending", "success", "error", "timeout", "cancelled"})
     *     ),
     *     @OA\Response(
     *         response=200,
     *         description="API logs retrieved successfully",
     *         @OA\JsonContent(
     *             type="object",
     *             @OA\Property(property="success", type="boolean", example=true),
     *             @OA\Property(property="data", type="array", @OA\Items(ref="#/components/schemas/ThirdPartyApiLog")),
     *             @OA\Property(property="pagination", type="object")
     *         )
     *     ),
     *     @OA\Response(response=404, description="Not found"),
     *     @OA\Response(response=401, description="Unauthorized"),
     *     @OA\Response(response=403, description="Forbidden")
     * )
     */
    public function getLogs(Request $request, $id)
    {
        $apiConfig = ThirdPartyApiConfig::find($id);

        if (!$apiConfig) {
            return response()->json([
                'success' => false,
                'message' => 'API configuration not found'
            ], 404);
        }

        $query = $apiConfig->logs();

        if ($request->has('status')) {
            $query->where('status', $request->status);
        }

        if ($request->has('method')) {
            $query->where('request_method', strtoupper($request->method));
        }

        $logs = $query->orderBy('created_at', 'desc')
            ->paginate($request->get('per_page', 15));

        return response()->json([
            'success' => true,
            'data' => $logs->items(),
            'pagination' => [
                'current_page' => $logs->currentPage(),
                'last_page' => $logs->lastPage(),
                'per_page' => $logs->perPage(),
                'total' => $logs->total(),
            ]
        ]);
    }

    /**
     * @OA\Get(
     *     path="/api/v1/third-party-apis/{id}/statistics",
     *     summary="Get API statistics",
     *     description="Get statistics for a specific third-party API",
     *     operationId="getApiStatistics",
     *     tags={"Third Party APIs"},
     *     security={{"bearerAuth":{}}},
     *     @OA\Parameter(
     *         name="id",
     *         in="path",
     *         description="API configuration ID",
     *         required=true,
     *         @OA\Schema(type="integer")
     *     ),
     *     @OA\Parameter(
     *         name="days",
     *         in="query",
     *         description="Number of days to include in statistics",
     *         required=false,
     *         @OA\Schema(type="integer", example=7)
     *     ),
     *     @OA\Response(
     *         response=200,
     *         description="API statistics retrieved successfully",
     *         @OA\JsonContent(
     *             type="object",
     *             @OA\Property(property="success", type="boolean", example=true),
     *             @OA\Property(property="data", type="object")
     *         )
     *     ),
     *     @OA\Response(response=404, description="Not found"),
     *     @OA\Response(response=401, description="Unauthorized"),
     *     @OA\Response(response=403, description="Forbidden")
     * )
     */
    public function getStatistics(Request $request, $id)
    {
        $apiConfig = ThirdPartyApiConfig::find($id);

        if (!$apiConfig) {
            return response()->json([
                'success' => false,
                'message' => 'API configuration not found'
            ], 404);
        }

        $days = $request->get('days', 7);
        $statistics = ThirdPartyApiLog::getApiStatistics($id, $days);

        return response()->json([
            'success' => true,
            'data' => $statistics
        ]);
    }

    /**
     * @OA\Get(
     *     path="/api/v1/third-party-apis/system/statistics",
     *     summary="Get system-wide API statistics",
     *     description="Get statistics for all third-party APIs",
     *     operationId="getSystemStatistics",
     *     tags={"Third Party APIs"},
     *     security={{"bearerAuth":{}}},
     *     @OA\Parameter(
     *         name="days",
     *         in="query",
     *         description="Number of days to include in statistics",
     *         required=false,
     *         @OA\Schema(type="integer", example=7)
     *     ),
     *     @OA\Response(
     *         response=200,
     *         description="System statistics retrieved successfully",
     *         @OA\JsonContent(
     *             type="object",
     *             @OA\Property(property="success", type="boolean", example=true),
     *             @OA\Property(property="data", type="object")
     *         )
     *     ),
     *     @OA\Response(response=401, description="Unauthorized"),
     *     @OA\Response(response=403, description="Forbidden")
     * )
     */
    public function getSystemStatistics(Request $request)
    {
        $days = $request->get('days', 7);
        $statistics = ThirdPartyApiLog::getSystemStatistics($days);

        return response()->json([
            'success' => true,
            'data' => $statistics
        ]);
    }

    /**
     * @OA\Post(
     *     path="/api/v1/third-party-apis/{api_id}/webhook",
     *     summary="Handle webhook",
     *     description="Handle incoming webhook from third-party API",
     *     operationId="handleWebhook",
     *     tags={"Third Party APIs"},
     *     @OA\Parameter(
     *         name="api_id",
     *         in="path",
     *         description="API configuration identifier",
     *         required=true,
     *         @OA\Schema(type="string")
     *     ),
     *     @OA\RequestBody(
     *         required=true,
     *         @OA\JsonContent(
     *             type="object",
     *             description="Webhook payload (varies by API)"
     *         )
     *     ),
     *     @OA\Response(
     *         response=200,
     *         description="Webhook processed successfully",
     *         @OA\JsonContent(
     *             type="object",
     *             @OA\Property(property="success", type="boolean", example=true),
     *             @OA\Property(property="message", type="string", example="Webhook processed successfully")
     *         )
     *     ),
     *     @OA\Response(response=400, description="Bad request"),
     *     @OA\Response(response=404, description="Not found")
     * )
     */
    public function handleWebhook(Request $request, $apiId)
    {
        $apiConfig = ThirdPartyApiConfig::where('identifier', $apiId)
            ->where('is_active', true)
            ->first();

        if (!$apiConfig) {
            return response()->json([
                'success' => false,
                'message' => 'API configuration not found'
            ], 404);
        }

        $result = $this->thirdPartyApiService->handleWebhook($apiConfig, $request->all());

        if ($result['success']) {
            return response()->json([
                'success' => true,
                'message' => 'Webhook processed successfully'
            ]);
        } else {
            return response()->json([
                'success' => false,
                'message' => 'Webhook processing failed',
                'error' => $result['error']
            ], 400);
        }
    }

    /**
     * @OA\Get(
     *     path="/api/v1/third-party-apis/options",
     *     summary="Get API configuration options",
     *     description="Get available options for API configuration",
     *     operationId="getApiOptions",
     *     tags={"Third Party APIs"},
     *     security={{"bearerAuth":{}}},
     *     @OA\Response(
     *         response=200,
     *         description="Options retrieved successfully",
     *         @OA\JsonContent(
     *             type="object",
     *             @OA\Property(property="success", type="boolean", example=true),
     *             @OA\Property(property="data", type="object")
     *         )
     *     ),
     *     @OA\Response(response=401, description="Unauthorized"),
     *     @OA\Response(response=403, description="Forbidden")
     * )
     */
    public function getOptions()
    {
        return response()->json([
            'success' => true,
            'data' => [
                'auth_types' => ThirdPartyApiConfig::AUTH_TYPES,
                'content_types' => ThirdPartyApiConfig::CONTENT_TYPES,
                'environments' => ThirdPartyApiConfig::ENVIRONMENTS,
                'default_timeout' => 30,
                'default_retries' => 3,
                'default_rate_limit_requests' => 100,
                'default_rate_limit_duration' => 60,
                'default_cache_duration' => 300,
            ]
        ]);
    }
} 