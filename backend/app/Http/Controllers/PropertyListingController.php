<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use App\Models\PropertyListing;
use App\Models\Unit;
use App\Models\PropertyLead;
use App\Http\Requests\StorePropertyListingRequest;
use App\Http\Requests\UpdatePropertyListingRequest;
use App\Http\Resources\PropertyListingResource;
use App\Http\Resources\PropertyLeadResource;
use App\Http\Resources\UnitResource;
use App\Services\PortalIntegrationService;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Spatie\QueryBuilder\QueryBuilder;
use Spatie\QueryBuilder\AllowedFilter;

class PropertyListingController extends Controller
{
    protected PortalIntegrationService $portalService;

    public function __construct(PortalIntegrationService $portalService)
    {
        $this->portalService = $portalService;
    }
    /**
     * @OA\Get(
     *     path="/property-listings",
     *     summary="Get all property listings",
     *     description="Retrieve a paginated list of property listings with filtering and sorting options",
     *     operationId="getPropertyListings",
     *     tags={"Property Listings"},
     *     security={{"bearerAuth": {}}},
     *     @OA\Parameter(
     *         name="page",
     *         in="query",
     *         description="Page number for pagination",
     *         required=false,
     *         @OA\Schema(type="integer", minimum=1, default=1)
     *     ),
     *     @OA\Parameter(
     *         name="per_page",
     *         in="query",
     *         description="Number of items per page",
     *         required=false,
     *         @OA\Schema(type="integer", minimum=1, maximum=100, default=15)
     *     ),
     *     @OA\Parameter(
     *         name="status",
     *         in="query",
     *         description="Filter by listing status",
     *         required=false,
     *         @OA\Schema(type="string", enum={"draft", "pending_approval", "approved", "published", "expired"})
     *     ),
     *     @OA\Parameter(
     *         name="unit_type",
     *         in="query",
     *         description="Filter by unit type",
     *         required=false,
     *         @OA\Schema(type="string", enum={"apartment", "villa", "studio", "penthouse"})
     *     ),
     *     @OA\Response(
     *         response=200,
     *         description="Property listings retrieved successfully",
     *         @OA\JsonContent(
     *             @OA\Property(property="success", type="boolean", example=true),
     *             @OA\Property(property="message", type="string", example="Property listings retrieved successfully"),
     *             @OA\Property(
     *                 property="data",
     *                 type="array",
     *                 @OA\Items(ref="#/components/schemas/PropertyListing")
     *             ),
     *             @OA\Property(property="meta", ref="#/components/schemas/Meta")
     *         )
     *     ),
     *     @OA\Response(response=401, ref="#/components/responses/Unauthorized"),
     *     @OA\Response(response=403, ref="#/components/responses/Forbidden")
     * )
     */
    public function index(Request $request): JsonResponse
    {
        try {
            // Temporarily disable authentication for development
            // $user = Auth::user();

            // Build query with filters and relationships
            $query = QueryBuilder::for(PropertyListing::class)
                ->allowedFilters([
                    AllowedFilter::exact('status', 'listing_status'),
                    AllowedFilter::exact('unit.unit_type'),
                    AllowedFilter::scope('rent_range'),
                    AllowedFilter::scope('bedrooms'),
                    AllowedFilter::callback('owner_id', function ($query, $value) {
                        $query->whereHas('unit', function ($q) use ($value) {
                            $q->where('owner_id', $value);
                        });
                    }),
                ])
                ->allowedSorts(['created_at', 'updated_at', 'rent_amount', 'view_count', 'inquiry_count'])
                ->defaultSort('-created_at')
                ->with(['unit.owner', 'leads']);

            // Temporarily disable user-based filtering for development
            // Apply user-based filtering with society isolation
            // if ($user->role === 'owner') {
            //     // Owners can only see their own listings
            //     $query->whereHas('unit', function ($q) use ($user) {
            //         $q->where('owner_id', $user->id);
            //     });
            // } elseif ($user->role === 'society_admin') {
            //     // Society admins can only see listings from their society
            //     $query->where('society_id', $user->society_id);
            // } elseif ($user->role === 'tenant') {
            //     // Tenants can see all published listings in their society
            //     $query->where('society_id', $user->society_id)
            //           ->where('status', 'published');
            // } elseif ($user->role === 'admin') {
            //     // Super admins can see all listings (no filtering)
            //     // This is for system-wide administration
            // } else {
            //     // Default: only published listings in user's society
            //     $query->where('society_id', $user->society_id)
            //           ->where('status', 'published');
            // }

            // Paginate results
            $listings = $query->paginate($request->get('per_page', 15));

            return response()->json([
                'success' => true,
                'message' => 'Property listings retrieved successfully',
                'data' => PropertyListingResource::collection($listings->items()),
                'meta' => [
                    'current_page' => $listings->currentPage(),
                    'from' => $listings->firstItem(),
                    'last_page' => $listings->lastPage(),
                    'per_page' => $listings->perPage(),
                    'to' => $listings->lastItem(),
                    'total' => $listings->total(),
                ]
            ]);

        } catch (\Exception $e) {
            Log::error('Failed to retrieve property listings', [
                'error' => $e->getMessage(),
                'user_id' => Auth::id(),
            ]);

            return response()->json([
                'success' => false,
                'message' => 'Failed to retrieve property listings',
                'error' => config('app.debug') ? $e->getMessage() : 'Internal server error'
            ], 500);
        }
    }

    /**
     * @OA\Post(
     *     path="/property-listings",
     *     summary="Create a new property listing",
     *     description="Create a new property listing for a unit",
     *     operationId="createPropertyListing",
     *     tags={"Property Listings"},
     *     security={{"bearerAuth": {}}},
     *     @OA\RequestBody(
     *         required=true,
     *         description="Property listing data",
     *         @OA\JsonContent(
     *             required={"unit_id", "title", "rent_amount"},
     *             @OA\Property(property="unit_id", type="integer", description="Unit ID to list"),
     *             @OA\Property(property="title", type="string", description="Listing title", maxLength=200),
     *             @OA\Property(property="description", type="string", description="Property description"),
     *             @OA\Property(property="rent_amount", type="number", format="float", description="Monthly rent amount"),
     *             @OA\Property(property="deposit_amount", type="number", format="float", description="Security deposit amount"),
     *             @OA\Property(property="amenities", type="array", @OA\Items(type="string"), description="Property amenities"),
     *             @OA\Property(property="preferences", type="object", description="Tenant preferences"),
     *             @OA\Property(property="media_urls", type="array", @OA\Items(type="string"), description="Property media URLs")
     *         )
     *     ),
     *     @OA\Response(
     *         response=201,
     *         description="Property listing created successfully",
     *         @OA\JsonContent(
     *             @OA\Property(property="success", type="boolean", example=true),
     *             @OA\Property(property="message", type="string", example="Property listing created successfully"),
     *             @OA\Property(property="data", ref="#/components/schemas/PropertyListing")
     *         )
     *     ),
     *     @OA\Response(response=400, ref="#/components/responses/BadRequest"),
     *     @OA\Response(response=401, ref="#/components/responses/Unauthorized"),
     *     @OA\Response(response=403, ref="#/components/responses/Forbidden")
     * )
     */
    public function store(StorePropertyListingRequest $request): JsonResponse
    {
        try {
            $user = Auth::user();
            $validated = $request->validated();

            // Get the unit and check permissions
            $unit = Unit::findOrFail($validated['unit_id']);
            
            // Check society access
            if ($user->role === 'society_admin' && $unit->society_id !== $user->society_id) {
                return response()->json([
                    'success' => false,
                    'message' => 'You can only create listings for units in your society'
                ], 403);
            }
            
            // Check unit ownership for owners
            if ($user->role === 'owner' && $unit->owner_id !== $user->id) {
                return response()->json([
                    'success' => false,
                    'message' => 'You can only create listings for your own units'
                ], 403);
            }

            DB::beginTransaction();

            // Create property listing with society_id
            $listing = PropertyListing::create([
                'unit_id' => $validated['unit_id'],
                'society_id' => $unit->society_id, // Inherit from unit
                'title' => $validated['title'],
                'description' => $validated['description'] ?? null,
                'rent_amount' => $validated['rent_amount'],
                'deposit_amount' => $validated['deposit_amount'],
                'amenities' => $validated['amenities'] ?? [],
                'preferences' => $validated['preferences'] ?? [],
                'media_urls' => $validated['media_urls'] ?? [],
                'portal_specific_data' => $validated['portal_specific_data'] ?? [],
                'status' => 'draft',
                'created_by' => $user->id,
            ]);

            // Auto-publish if requested and user has permission
            if ($validated['auto_publish'] ?? false) {
                if ($user->hasRole(['admin', 'manager'])) {
                    $listing->update(['status' => 'published', 'published_at' => now()]);
                } else {
                    $listing->update(['status' => 'pending_approval']);
                }
            }

            // Sync to portals if specified
            if (!empty($validated['publish_to_portals']) && $listing->status === 'published') {
                $syncResults = $this->portalService->syncToPortals($listing, $validated['publish_to_portals']);
                $listing->update(['portal_mappings' => $syncResults]);
            }

            DB::commit();

            // Load relationships for response
            $listing->load(['unit.owner']);

            return response()->json([
                'success' => true,
                'message' => 'Property listing created successfully',
                'data' => new PropertyListingResource($listing)
            ], 201);

        } catch (\Exception $e) {
            DB::rollBack();

            Log::error('Failed to create property listing', [
                'error' => $e->getMessage(),
                'user_id' => Auth::id(),
                'data' => $request->all()
            ]);

            return response()->json([
                'success' => false,
                'message' => 'Failed to create property listing',
                'error' => config('app.debug') ? $e->getMessage() : 'Internal server error'
            ], 500);
        }
    }

    /**
     * @OA\Get(
     *     path="/property-listings/{listing}",
     *     summary="Get a specific property listing",
     *     description="Retrieve details of a specific property listing",
     *     operationId="getPropertyListing",
     *     tags={"Property Listings"},
     *     security={{"bearerAuth": {}}},
     *     @OA\Parameter(
     *         name="listing",
     *         in="path",
     *         description="Property listing ID",
     *         required=true,
     *         @OA\Schema(type="integer")
     *     ),
     *     @OA\Response(
     *         response=200,
     *         description="Property listing retrieved successfully",
     *         @OA\JsonContent(
     *             @OA\Property(property="success", type="boolean", example=true),
     *             @OA\Property(property="message", type="string", example="Property listing retrieved successfully"),
     *             @OA\Property(property="data", ref="#/components/schemas/PropertyListing")
     *         )
     *     ),
     *     @OA\Response(response=404, ref="#/components/responses/NotFound"),
     *     @OA\Response(response=401, ref="#/components/responses/Unauthorized")
     * )
     */
    public function show(string $id): JsonResponse
    {
        try {
            $user = Auth::user();

            $query = PropertyListing::with(['unit.owner', 'leads.assignedUser']);

            // Apply user-based filtering (skip for development when user is null)
            if ($user && $user->role === 'owner') {
                $query->whereHas('unit', function ($q) use ($user) {
                    $q->where('owner_id', $user->id);
                });
            }

            $listing = $query->findOrFail($id);

            // Increment view count for published listings
            if ($listing->status === 'published') {
                $listing->increment('view_count');
            }

            return response()->json([
                'success' => true,
                'message' => 'Property listing retrieved successfully',
                'data' => new PropertyListingResource($listing)
            ]);

        } catch (\Illuminate\Database\Eloquent\ModelNotFoundException $e) {
            return response()->json([
                'success' => false,
                'message' => 'Property listing not found'
            ], 404);
        } catch (\Exception $e) {
            Log::error('Failed to retrieve property listing', [
                'listing_id' => $id,
                'error' => $e->getMessage(),
                'user_id' => Auth::id(),
            ]);

            return response()->json([
                'success' => false,
                'message' => 'Failed to retrieve property listing',
                'error' => config('app.debug') ? $e->getMessage() : 'Internal server error'
            ], 500);
        }
    }

    /**
     * @OA\Put(
     *     path="/property-listings/{listing}",
     *     summary="Update a property listing",
     *     description="Update an existing property listing",
     *     operationId="updatePropertyListing",
     *     tags={"Property Listings"},
     *     security={{"bearerAuth": {}}},
     *     @OA\Parameter(
     *         name="listing",
     *         in="path",
     *         description="Property listing ID",
     *         required=true,
     *         @OA\Schema(type="integer")
     *     ),
     *     @OA\Response(
     *         response=200,
     *         description="Property listing updated successfully"
     *     )
     * )
     */
    public function update(UpdatePropertyListingRequest $request, string $id): JsonResponse
    {
        try {
            $user = Auth::user();
            $validated = $request->validated();

            $query = PropertyListing::query();

            // Apply user-based filtering
            if ($user->role === 'owner') {
                $query->whereHas('unit', function ($q) use ($user) {
                    $q->where('owner_id', $user->id);
                });
            }

            $listing = $query->findOrFail($id);

            DB::beginTransaction();

            // Update listing
            $listing->update($validated);

            // Handle status changes
            if (isset($validated['status'])) {
                $this->handleStatusChange($listing, $validated['status']);
            }

            // Re-sync to portals if listing is published and data changed
            if ($listing->status === 'published' && $this->shouldResync($validated)) {
                $syncResults = $this->portalService->syncToPortals($listing);
                $listing->update(['portal_mappings' => array_merge($listing->portal_mappings ?? [], $syncResults)]);
            }

            DB::commit();

            // Load relationships for response
            $listing->load(['unit.owner', 'leads']);

            return response()->json([
                'success' => true,
                'message' => 'Property listing updated successfully',
                'data' => new PropertyListingResource($listing)
            ]);

        } catch (\Illuminate\Database\Eloquent\ModelNotFoundException $e) {
            return response()->json([
                'success' => false,
                'message' => 'Property listing not found'
            ], 404);
        } catch (\Exception $e) {
            DB::rollBack();

            Log::error('Failed to update property listing', [
                'listing_id' => $id,
                'error' => $e->getMessage(),
                'user_id' => Auth::id(),
                'data' => $request->all()
            ]);

            return response()->json([
                'success' => false,
                'message' => 'Failed to update property listing',
                'error' => config('app.debug') ? $e->getMessage() : 'Internal server error'
            ], 500);
        }
    }

    /**
     * @OA\Delete(
     *     path="/property-listings/{listing}",
     *     summary="Delete a property listing",
     *     description="Delete an existing property listing",
     *     operationId="deletePropertyListing",
     *     tags={"Property Listings"},
     *     security={{"bearerAuth": {}}},
     *     @OA\Parameter(
     *         name="listing",
     *         in="path",
     *         description="Property listing ID",
     *         required=true,
     *         @OA\Schema(type="integer")
     *     ),
     *     @OA\Response(
     *         response=200,
     *         description="Property listing deleted successfully"
     *     )
     * )
     */
    public function destroy(string $id): JsonResponse
    {
        try {
            $user = Auth::user();

            $query = PropertyListing::query();

            // Apply user-based filtering
            if ($user->role === 'owner') {
                $query->whereHas('unit', function ($q) use ($user) {
                    $q->where('owner_id', $user->id);
                });
            }

            $listing = $query->findOrFail($id);

            DB::beginTransaction();

            // Remove from all portals first
            if (!empty($listing->portal_mappings)) {
                foreach (array_keys($listing->portal_mappings) as $portalName) {
                    $this->portalService->removeFromPortal($listing, $portalName);
                }
            }

            // Soft delete the listing
            $listing->delete();

            DB::commit();

            return response()->json([
                'success' => true,
                'message' => 'Property listing deleted successfully'
            ]);

        } catch (\Illuminate\Database\Eloquent\ModelNotFoundException $e) {
            return response()->json([
                'success' => false,
                'message' => 'Property listing not found'
            ], 404);
        } catch (\Exception $e) {
            DB::rollBack();

            Log::error('Failed to delete property listing', [
                'listing_id' => $id,
                'error' => $e->getMessage(),
                'user_id' => Auth::id(),
            ]);

            return response()->json([
                'success' => false,
                'message' => 'Failed to delete property listing',
                'error' => config('app.debug') ? $e->getMessage() : 'Internal server error'
            ], 500);
        }
    }

    /**
     * @OA\Post(
     *     path="/property-listings/{listing}/sync",
     *     summary="Sync listing to portals",
     *     description="Synchronize property listing to configured portals",
     *     operationId="syncPropertyListing",
     *     tags={"Property Listings"},
     *     security={{"bearerAuth": {}}},
     *     @OA\Response(
     *         response=200,
     *         description="Property listing synced successfully"
     *     )
     * )
     */
    public function syncToPortals(Request $request, string $id): JsonResponse
    {
        try {
            $user = Auth::user();

            $query = PropertyListing::query();

            // Apply user-based filtering
            if ($user->role === 'owner') {
                $query->whereHas('unit', function ($q) use ($user) {
                    $q->where('owner_id', $user->id);
                });
            }

            $listing = $query->findOrFail($id);

            // Check if listing is in publishable state
            if (!in_array($listing->status, ['approved', 'published'])) {
                return response()->json([
                    'success' => false,
                    'message' => 'Listing must be approved or published to sync to portals'
                ], 400);
            }

            // Get portals to sync to
            $portals = $request->input('portals', ['99acres', 'magicbricks', 'housing', 'olx', 'nobroker']);
            $forceUpdate = $request->boolean('force_update', false);

            // Perform sync
            $syncResults = $this->portalService->syncToPortals($listing, $portals);

            // Update listing status if not already published
            if ($listing->status === 'approved') {
                $listing->update(['status' => 'published', 'published_at' => now()]);
            }

            // Separate successful and failed syncs
            $syncedPortals = [];
            $failedPortals = [];

            foreach ($syncResults as $portal => $result) {
                if ($result['success']) {
                    $syncedPortals[] = $portal;
                } else {
                    $failedPortals[] = $portal;
                }
            }

            return response()->json([
                'success' => true,
                'message' => 'Portal synchronization completed',
                'data' => [
                    'synced_portals' => $syncedPortals,
                    'failed_portals' => $failedPortals,
                    'sync_results' => $syncResults,
                    'total_synced' => count($syncedPortals),
                    'total_failed' => count($failedPortals),
                ]
            ]);

        } catch (\Illuminate\Database\Eloquent\ModelNotFoundException $e) {
            return response()->json([
                'success' => false,
                'message' => 'Property listing not found'
            ], 404);
        } catch (\Exception $e) {
            Log::error('Failed to sync property listing to portals', [
                'listing_id' => $id,
                'error' => $e->getMessage(),
                'user_id' => Auth::id(),
            ]);

            return response()->json([
                'success' => false,
                'message' => 'Failed to sync to portals',
                'error' => config('app.debug') ? $e->getMessage() : 'Internal server error'
            ], 500);
        }
    }

    /**
     * @OA\Get(
     *     path="/available-units",
     *     summary="Get available units for listing",
     *     description="Retrieve units available for property listing",
     *     operationId="getAvailableUnitsForListing",
     *     tags={"Property Listings"},
     *     security={{"bearerAuth": {}}},
     *     @OA\Response(
     *         response=200,
     *         description="Available units retrieved successfully"
     *     )
     * )
     */
    public function availableUnits(): JsonResponse
    {
        try {
            $user = Auth::user();

            $query = Unit::where('status', 'to-let')
                ->whereDoesntHave('propertyListing', function ($q) {
                    $q->whereIn('status', ['draft', 'pending_approval', 'approved', 'published']);
                })
                ->with(['owner']);

            // Apply user-based filtering
            if ($user->role === 'owner') {
                $query->where('owner_id', $user->id);
            }

            $units = $query->get();

            return response()->json([
                'success' => true,
                'message' => 'Available units retrieved successfully',
                'data' => UnitResource::collection($units),
                'meta' => [
                    'total_units' => $units->count(),
                    'criteria' => [
                        'status' => 'to-let',
                        'no_active_listing' => true,
                        'user_owned_only' => $user->role === 'owner'
                    ]
                ]
            ]);

        } catch (\Exception $e) {
            Log::error('Failed to retrieve available units', [
                'error' => $e->getMessage(),
                'user_id' => Auth::id(),
            ]);

            return response()->json([
                'success' => false,
                'message' => 'Failed to retrieve available units',
                'error' => config('app.debug') ? $e->getMessage() : 'Internal server error'
            ], 500);
        }
    }

    /**
     * @OA\Post(
     *     path="/webhooks/portals/{portalName}",
     *     summary="Portal webhook endpoint",
     *     description="Receive webhooks from property portals",
     *     operationId="portalWebhook",
     *     tags={"Property Listings"},
     *     @OA\Response(
     *         response=200,
     *         description="Webhook processed successfully"
     *     )
     * )
     */
    public function webhook(Request $request, string $portalName): JsonResponse
    {
        try {
            // Validate portal name
            $supportedPortals = ['99acres', 'magicbricks', 'housing', 'olx', 'nobroker'];
            if (!in_array($portalName, $supportedPortals)) {
                return response()->json([
                    'success' => false,
                    'message' => 'Unsupported portal'
                ], 400);
            }

            // Process webhook
            $result = $this->portalService->processWebhook($portalName, $request->all());

            return response()->json($result);

        } catch (\Exception $e) {
            Log::error('Webhook processing failed', [
                'portal' => $portalName,
                'error' => $e->getMessage(),
                'payload' => $request->all()
            ]);

            return response()->json([
                'success' => false,
                'message' => 'Webhook processing failed',
                'error' => config('app.debug') ? $e->getMessage() : 'Internal server error'
            ], 500);
        }
    }

    /**
     * Get leads for a specific property listing.
     */
    public function leads(string $id): JsonResponse
    {
        try {
            $user = Auth::user();

            $query = PropertyListing::query();

            // Apply user-based filtering
            if ($user->role === 'owner') {
                $query->whereHas('unit', function ($q) use ($user) {
                    $q->where('owner_id', $user->id);
                });
            }

            $listing = $query->findOrFail($id);
            $leads = $listing->leads()->with(['assignedUser'])->latest()->get();

            return response()->json([
                'success' => true,
                'message' => 'Property listing leads retrieved successfully',
                'data' => PropertyLeadResource::collection($leads),
                'meta' => [
                    'total_leads' => $leads->count(),
                    'new_leads' => $leads->where('status', 'new')->count(),
                    'converted_leads' => $leads->where('status', 'converted')->count(),
                    'conversion_rate' => $leads->count() > 0 ?
                        round(($leads->where('status', 'converted')->count() / $leads->count()) * 100, 2) : 0
                ]
            ]);

        } catch (\Illuminate\Database\Eloquent\ModelNotFoundException $e) {
            return response()->json([
                'success' => false,
                'message' => 'Property listing not found'
            ], 404);
        } catch (\Exception $e) {
            Log::error('Failed to retrieve property listing leads', [
                'listing_id' => $id,
                'error' => $e->getMessage(),
                'user_id' => Auth::id(),
            ]);

            return response()->json([
                'success' => false,
                'message' => 'Failed to retrieve leads',
                'error' => config('app.debug') ? $e->getMessage() : 'Internal server error'
            ], 500);
        }
    }

    /**
     * Handle status changes for property listings.
     */
    protected function handleStatusChange(PropertyListing $listing, string $newStatus): void
    {
        $oldStatus = $listing->status;

        switch ($newStatus) {
            case 'published':
                if ($oldStatus === 'approved') {
                    $listing->published_at = now();
                    $listing->expires_at = now()->addDays(30); // Default 30-day expiry
                }
                break;

            case 'expired':
                if ($oldStatus === 'published') {
                    // Remove from all portals when expired
                    if (!empty($listing->portal_mappings)) {
                        foreach (array_keys($listing->portal_mappings) as $portalName) {
                            $this->portalService->removeFromPortal($listing, $portalName);
                        }
                    }
                }
                break;
        }
    }

    /**
     * Determine if listing should be re-synced to portals.
     */
    protected function shouldResync(array $updatedData): bool
    {
        $resyncFields = [
            'title', 'description', 'rent_amount', 'deposit_amount',
            'amenities', 'preferences', 'media_urls'
        ];

        return !empty(array_intersect(array_keys($updatedData), $resyncFields));
    }

    /**
     * Get property listing analytics and statistics.
     */
    public function analytics(): JsonResponse
    {
        try {
            // Get basic counts
            $totalListings = PropertyListing::count();
            $publishedListings = PropertyListing::where('listing_status', 'published')->count();
            $draftListings = PropertyListing::where('listing_status', 'draft')->count();
            $expiredListings = PropertyListing::where('listing_status', 'expired')->count();
            $pendingApprovalListings = PropertyListing::where('listing_status', 'pending_approval')->count();

            // Get view and inquiry totals
            $totalViews = PropertyListing::sum('view_count');
            $totalInquiries = PropertyListing::sum('inquiry_count');

            // Calculate average rent
            $averageRent = PropertyListing::where('listing_status', 'published')->avg('rent_amount');

            // Calculate conversion rate (inquiries to views)
            $conversionRate = $totalViews > 0 ? round(($totalInquiries / $totalViews) * 100, 2) : 0;

            return response()->json([
                'success' => true,
                'message' => 'Property analytics retrieved successfully',
                'data' => [
                    'total' => $totalListings,
                    'published' => $publishedListings,
                    'draft' => $draftListings,
                    'expired' => $expiredListings,
                    'pending_approval' => $pendingApprovalListings,
                    'total_views' => $totalViews,
                    'total_inquiries' => $totalInquiries,
                    'average_rent' => $averageRent ? round($averageRent, 2) : 0,
                    'conversion_rate' => $conversionRate,
                ]
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to retrieve property analytics',
                'error' => $e->getMessage()
            ], 500);
        }
    }
}
