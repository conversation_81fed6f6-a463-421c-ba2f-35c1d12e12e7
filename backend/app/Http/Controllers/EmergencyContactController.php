<?php

namespace App\Http\Controllers;

use App\Models\EmergencyContact;
use App\Models\Tenant;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Validator;
use Illuminate\Validation\Rule;

class EmergencyContactController extends Controller
{
    /**
     * Get emergency contacts for a tenant
     */
    public function index(Request $request, $tenantId = null): JsonResponse
    {
        $user = Auth::user();
        
        // If tenantId is provided, get contacts for that tenant
        if ($tenantId) {
            $tenant = Tenant::findOrFail($tenantId);
            
            // RBAC: Check if user can view this tenant's emergency contacts
            if (!$user->hasRole(['admin', 'manager']) && 
                $tenant->user_id !== $user->id && 
                $tenant->owner_id !== $user->id) {
                return response()->json(['error' => 'Unauthorized'], 403);
            }
            
            $query = $tenant->emergencyContacts();
        } else {
            // Get contacts for current user's tenant record
            $tenant = $user->tenant;
            if (!$tenant) {
                return response()->json(['error' => 'No tenant record found'], 404);
            }
            
            $query = $tenant->emergencyContacts();
        }

        // Apply filters
        if ($request->filled('is_active')) {
            $query->where('is_active', $request->boolean('is_active'));
        }

        if ($request->filled('priority')) {
            $query->where('priority', $request->input('priority'));
        }

        if ($request->filled('relationship')) {
            $query->where('relationship', $request->input('relationship'));
        }

        if ($request->filled('is_local')) {
            $query->where('is_local', $request->boolean('is_local'));
        }

        $contacts = $query->with(['createdBy', 'updatedBy'])->get();

        return response()->json([
            'success' => true,
            'data' => $contacts,
            'tenant' => [
                'id' => $tenant->id,
                'name' => $tenant->full_name,
                'unit' => $tenant->unit->unit_number ?? null,
            ],
        ]);
    }

    /**
     * Store a new emergency contact
     */
    public function store(Request $request, $tenantId = null): JsonResponse
    {
        $user = Auth::user();
        
        // Determine tenant
        if ($tenantId) {
            $tenant = Tenant::findOrFail($tenantId);
            
            // RBAC: Check if user can create emergency contacts for this tenant
            if (!$user->hasRole(['admin', 'manager']) && 
                $tenant->user_id !== $user->id && 
                $tenant->owner_id !== $user->id) {
                return response()->json(['error' => 'Unauthorized'], 403);
            }
        } else {
            $tenant = $user->tenant;
            if (!$tenant) {
                return response()->json(['error' => 'No tenant record found'], 404);
            }
        }

        $validator = Validator::make($request->all(), [
            'name' => 'required|string|max:255',
            'relationship' => ['required', 'string', Rule::in(EmergencyContact::getAllRelationships())],
            'primary_phone' => 'required|string|max:15',
            'secondary_phone' => 'nullable|string|max:15',
            'email' => 'nullable|email|max:255',
            'address' => 'nullable|string|max:500',
            'city' => 'nullable|string|max:100',
            'state' => 'nullable|string|max:100',
            'country' => 'nullable|string|max:100',
            'pincode' => 'nullable|string|max:10',
            'priority' => 'nullable|integer|min:1|max:10',
            'is_local' => 'nullable|boolean',
            'is_active' => 'nullable|boolean',
            'notes' => 'nullable|string|max:1000',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'errors' => $validator->errors(),
            ], 422);
        }

        $data = $validator->validated();
        $data['tenant_id'] = $tenant->id;
        $data['created_by'] = $user->id;
        $data['updated_by'] = $user->id;

        // Set default priority if not provided
        if (!isset($data['priority'])) {
            $maxPriority = $tenant->emergencyContacts()->max('priority') ?? 0;
            $data['priority'] = $maxPriority + 1;
        }

        // Set defaults
        $data['is_local'] = $data['is_local'] ?? true;
        $data['is_active'] = $data['is_active'] ?? true;
        $data['country'] = $data['country'] ?? 'India';

        $contact = EmergencyContact::create($data);
        $contact->load(['createdBy', 'updatedBy']);

        return response()->json([
            'success' => true,
            'message' => 'Emergency contact created successfully',
            'data' => $contact,
        ], 201);
    }

    /**
     * Show a specific emergency contact
     */
    public function show($id): JsonResponse
    {
        $user = Auth::user();
        $contact = EmergencyContact::with(['tenant.user', 'tenant.unit', 'createdBy', 'updatedBy'])
                                 ->findOrFail($id);

        // RBAC: Check if user can view this emergency contact
        if (!$user->hasRole(['admin', 'manager']) && 
            $contact->tenant->user_id !== $user->id && 
            $contact->tenant->owner_id !== $user->id) {
            return response()->json(['error' => 'Unauthorized'], 403);
        }

        return response()->json([
            'success' => true,
            'data' => $contact,
        ]);
    }

    /**
     * Update an emergency contact
     */
    public function update(Request $request, $id): JsonResponse
    {
        $user = Auth::user();
        $contact = EmergencyContact::with('tenant')->findOrFail($id);

        // RBAC: Check if user can update this emergency contact
        if (!$user->hasRole(['admin', 'manager']) && 
            $contact->tenant->user_id !== $user->id && 
            $contact->tenant->owner_id !== $user->id) {
            return response()->json(['error' => 'Unauthorized'], 403);
        }

        $validator = Validator::make($request->all(), [
            'name' => 'sometimes|required|string|max:255',
            'relationship' => ['sometimes', 'required', 'string', Rule::in(EmergencyContact::getAllRelationships())],
            'primary_phone' => 'sometimes|required|string|max:15',
            'secondary_phone' => 'nullable|string|max:15',
            'email' => 'nullable|email|max:255',
            'address' => 'nullable|string|max:500',
            'city' => 'nullable|string|max:100',
            'state' => 'nullable|string|max:100',
            'country' => 'nullable|string|max:100',
            'pincode' => 'nullable|string|max:10',
            'priority' => 'nullable|integer|min:1|max:10',
            'is_local' => 'nullable|boolean',
            'is_active' => 'nullable|boolean',
            'notes' => 'nullable|string|max:1000',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'errors' => $validator->errors(),
            ], 422);
        }

        $data = $validator->validated();
        $data['updated_by'] = $user->id;

        $contact->update($data);
        $contact->load(['createdBy', 'updatedBy']);

        return response()->json([
            'success' => true,
            'message' => 'Emergency contact updated successfully',
            'data' => $contact,
        ]);
    }

    /**
     * Delete an emergency contact
     */
    public function destroy($id): JsonResponse
    {
        $user = Auth::user();
        $contact = EmergencyContact::with('tenant')->findOrFail($id);

        // RBAC: Check if user can delete this emergency contact
        if (!$user->hasRole(['admin', 'manager']) && 
            $contact->tenant->user_id !== $user->id && 
            $contact->tenant->owner_id !== $user->id) {
            return response()->json(['error' => 'Unauthorized'], 403);
        }

        $contact->delete();

        return response()->json([
            'success' => true,
            'message' => 'Emergency contact deleted successfully',
        ]);
    }

    /**
     * Get relationship options
     */
    public function relationships(): JsonResponse
    {
        return response()->json([
            'success' => true,
            'data' => EmergencyContact::getRelationshipOptions(),
        ]);
    }

    /**
     * Get emergency contacts statistics
     */
    public function statistics(Request $request): JsonResponse
    {
        $user = Auth::user();
        $query = EmergencyContact::query();

        // RBAC: Restrict to own tenant's contacts for tenants/owners
        if ($user->hasRole(['tenant', 'owner'])) {
            $tenant = $user->tenant;
            if (!$tenant) {
                return response()->json(['error' => 'No tenant record found'], 404);
            }
            $query->where('tenant_id', $tenant->id);
        }

        $total = $query->count();
        $active = (clone $query)->where('is_active', true)->count();
        $inactive = $total - $active;

        $byPriority = (clone $query)
            ->selectRaw('priority, count(*) as count')
            ->groupBy('priority')
            ->orderBy('priority')
            ->pluck('count', 'priority');

        $byRelationship = (clone $query)
            ->selectRaw('relationship, count(*) as count')
            ->groupBy('relationship')
            ->pluck('count', 'relationship');

        $localContacts = (clone $query)->where('is_local', true)->count();
        $nonLocalContacts = $total - $localContacts;

        return response()->json([
            'success' => true,
            'data' => [
                'total_contacts' => $total,
                'active_contacts' => $active,
                'inactive_contacts' => $inactive,
                'local_contacts' => $localContacts,
                'non_local_contacts' => $nonLocalContacts,
                'by_priority' => $byPriority,
                'by_relationship' => $byRelationship,
            ],
        ]);
    }

    /**
     * Update contact priority
     */
    public function updatePriority(Request $request, $id): JsonResponse
    {
        $user = Auth::user();
        $contact = EmergencyContact::with('tenant')->findOrFail($id);

        // RBAC: Check if user can update this emergency contact
        if (!$user->hasRole(['admin', 'manager']) && 
            $contact->tenant->user_id !== $user->id && 
            $contact->tenant->owner_id !== $user->id) {
            return response()->json(['error' => 'Unauthorized'], 403);
        }

        $validator = Validator::make($request->all(), [
            'priority' => 'required|integer|min:1|max:10',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'errors' => $validator->errors(),
            ], 422);
        }

        $contact->update([
            'priority' => $request->input('priority'),
            'updated_by' => $user->id,
        ]);

        return response()->json([
            'success' => true,
            'message' => 'Contact priority updated successfully',
            'data' => $contact,
        ]);
    }

    /**
     * Toggle contact active status
     */
    public function toggleStatus($id): JsonResponse
    {
        $user = Auth::user();
        $contact = EmergencyContact::with('tenant')->findOrFail($id);

        // RBAC: Check if user can update this emergency contact
        if (!$user->hasRole(['admin', 'manager']) && 
            $contact->tenant->user_id !== $user->id && 
            $contact->tenant->owner_id !== $user->id) {
            return response()->json(['error' => 'Unauthorized'], 403);
        }

        $contact->update([
            'is_active' => !$contact->is_active,
            'updated_by' => $user->id,
        ]);

        return response()->json([
            'success' => true,
            'message' => 'Contact status updated successfully',
            'data' => $contact,
        ]);
    }
} 