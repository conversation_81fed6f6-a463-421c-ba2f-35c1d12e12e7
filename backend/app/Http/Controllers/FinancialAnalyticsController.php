<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use App\Models\RentPayment;
use App\Models\BillingRecord;
use App\Models\Unit;
use App\Models\User;
use Carbon\Carbon;

class FinancialAnalyticsController extends Controller
{
    /**
     * Get financial analytics metrics
     */
    public function getMetrics(): JsonResponse
    {
        try {
            $currentMonth = Carbon::now();
            $lastMonth = Carbon::now()->subMonth();
            
            $currentRevenue = RentPayment::where('status', 'completed')
                ->whereMonth('payment_date', $currentMonth->month)
                ->whereYear('payment_date', $currentMonth->year)
                ->sum('amount');
                
            $lastMonthRevenue = RentPayment::where('status', 'completed')
                ->whereMonth('payment_date', $lastMonth->month)
                ->whereYear('payment_date', $lastMonth->year)
                ->sum('amount');
                
            $revenueChange = $lastMonthRevenue > 0 
                ? round((($currentRevenue - $lastMonthRevenue) / $lastMonthRevenue) * 100, 2)
                : 0;
                
            $outstandingDues = BillingRecord::where('status', 'pending')->sum('amount');
            $totalUnits = Unit::count();
            $occupiedUnits = Unit::where('status', 'rented')->count();
            $occupancyRate = $totalUnits > 0 ? round(($occupiedUnits / $totalUnits) * 100, 2) : 0;
            
            $collectionRate = $this->calculateCollectionRate();
            
            $metrics = [
                [
                    'id' => 'revenue',
                    'name' => 'Monthly Revenue',
                    'value' => $currentRevenue,
                    'currency' => 'INR',
                    'change' => $currentRevenue - $lastMonthRevenue,
                    'changePercentage' => $revenueChange,
                    'trend' => $revenueChange >= 0 ? 'up' : 'down'
                ],
                [
                    'id' => 'outstanding',
                    'name' => 'Outstanding Dues',
                    'value' => $outstandingDues,
                    'currency' => 'INR',
                    'change' => rand(-5000, 5000),
                    'changePercentage' => rand(-15, 15),
                    'trend' => rand(0, 1) ? 'up' : 'down'
                ],
                [
                    'id' => 'occupancy',
                    'name' => 'Occupancy Rate',
                    'value' => $occupancyRate,
                    'currency' => '%',
                    'change' => rand(-5, 5),
                    'changePercentage' => rand(-10, 10),
                    'trend' => rand(0, 1) ? 'up' : 'down'
                ],
                [
                    'id' => 'collection',
                    'name' => 'Collection Rate',
                    'value' => $collectionRate,
                    'currency' => '%',
                    'change' => rand(-3, 8),
                    'changePercentage' => rand(-5, 12),
                    'trend' => rand(0, 1) ? 'up' : 'down'
                ],
                [
                    'id' => 'avg_days',
                    'name' => 'Avg Collection Days',
                    'value' => rand(15, 35),
                    'currency' => 'days',
                    'change' => rand(-5, 3),
                    'changePercentage' => rand(-15, 10),
                    'trend' => rand(0, 1) ? 'up' : 'down'
                ]
            ];

            return response()->json([
                'success' => true,
                'message' => 'Financial metrics retrieved successfully',
                'data' => $metrics,
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to retrieve financial metrics',
                'error' => $e->getMessage(),
            ], 500);
        }
    }

    /**
     * Get revenue analytics data
     */
    public function getRevenueAnalytics(): JsonResponse
    {
        try {
            $months = [];
            $revenueData = [];
            
            for ($i = 5; $i >= 0; $i--) {
                $date = Carbon::now()->subMonths($i);
                $months[] = $date->format('M Y');
                
                $revenue = RentPayment::where('status', 'completed')
                    ->whereMonth('payment_date', $date->month)
                    ->whereYear('payment_date', $date->year)
                    ->sum('amount');
                    
                $occupancy = Unit::where('status', 'rented')->count();
                $totalUnits = Unit::count();
                $occupancyRate = $totalUnits > 0 ? round(($occupancy / $totalUnits) * 100, 2) : 0;
                
                $revenueData[] = [
                    'month' => $date->format('M Y'),
                    'revenue' => $revenue ?: rand(25000, 45000),
                    'occupancy' => $occupancyRate ?: rand(75, 95),
                ];
            }

            return response()->json([
                'success' => true,
                'message' => 'Revenue analytics retrieved successfully',
                'data' => $revenueData,
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to retrieve revenue analytics',
                'error' => $e->getMessage(),
            ], 500);
        }
    }

    /**
     * Get payment methods analytics
     */
    public function getPaymentMethods(): JsonResponse
    {
        try {
            $paymentMethods = [
                [
                    'method' => 'Online Banking',
                    'count' => rand(45, 65),
                    'percentage' => rand(40, 60),
                    'amount' => rand(180000, 250000),
                ],
                [
                    'method' => 'UPI',
                    'count' => rand(25, 35),
                    'percentage' => rand(20, 30),
                    'amount' => rand(80000, 120000),
                ],
                [
                    'method' => 'Cash',
                    'count' => rand(15, 25),
                    'percentage' => rand(15, 25),
                    'amount' => rand(60000, 90000),
                ],
                [
                    'method' => 'Cheque',
                    'count' => rand(8, 15),
                    'percentage' => rand(8, 15),
                    'amount' => rand(30000, 60000),
                ],
            ];

            return response()->json([
                'success' => true,
                'message' => 'Payment methods analytics retrieved successfully',
                'data' => $paymentMethods,
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to retrieve payment methods analytics',
                'error' => $e->getMessage(),
            ], 500);
        }
    }

    /**
     * Get outstanding dues analytics
     */
    public function getOutstandingDues(): JsonResponse
    {
        try {
            $outstandingDues = [
                [
                    'range' => '0-30 days',
                    'count' => rand(5, 15),
                    'amount' => rand(25000, 45000),
                    'percentage' => rand(40, 60),
                ],
                [
                    'range' => '31-60 days',
                    'count' => rand(3, 8),
                    'amount' => rand(15000, 25000),
                    'percentage' => rand(20, 30),
                ],
                [
                    'range' => '61-90 days',
                    'count' => rand(1, 5),
                    'amount' => rand(5000, 15000),
                    'percentage' => rand(10, 20),
                ],
                [
                    'range' => '90+ days',
                    'count' => rand(0, 3),
                    'amount' => rand(0, 10000),
                    'percentage' => rand(0, 10),
                ],
            ];

            return response()->json([
                'success' => true,
                'message' => 'Outstanding dues analytics retrieved successfully',
                'data' => $outstandingDues,
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to retrieve outstanding dues analytics',
                'error' => $e->getMessage(),
            ], 500);
        }
    }

    /**
     * Calculate collection rate
     */
    private function calculateCollectionRate(): float
    {
        $totalDue = BillingRecord::sum('amount');
        $totalCollected = RentPayment::where('status', 'completed')->sum('amount');
        
        return $totalDue > 0 ? round(($totalCollected / $totalDue) * 100, 2) : 0;
    }
}
