<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use App\Models\NocApplication;
use App\Models\NocTemplate;
use App\Models\Document;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Storage;
use Illuminate\Validation\Rule;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\UploadedFile;

class NocDocumentController extends Controller
{
    /**
     * Get document requirements for a NOC application
     */
    public function getRequirements(Request $request): JsonResponse
    {
        $validated = $request->validate([
            'noc_type' => ['required', Rule::in(['rental', 'residence', 'vehicle', 'renovation', 'transfer'])],
            'template_id' => 'nullable|exists:noc_templates,id',
        ]);

        $nocType = $validated['noc_type'];
        $template = null;

        // Get template if specified
        if (isset($validated['template_id'])) {
            $template = NocTemplate::find($validated['template_id']);
        } else {
            // Get default active template for this type
            $template = NocTemplate::active()->byType($nocType)->first();
        }

        $requirements = $template 
            ? $template->document_requirements 
            : NocTemplate::getDefaultDocumentRequirements($nocType);

        return response()->json([
            'success' => true,
            'data' => [
                'noc_type' => $nocType,
                'template' => $template,
                'requirements' => $requirements,
            ],
            'message' => 'Document requirements retrieved successfully'
        ]);
    }

    /**
     * Upload document for NOC application
     */
    public function uploadDocument(Request $request): JsonResponse
    {
        $validated = $request->validate([
            'application_id' => 'required|exists:noc_applications,id',
            'document_type' => 'required|string|max:50',
            'file' => 'required|file|max:10240', // 10MB max
            'title' => 'nullable|string|max:200',
            'description' => 'nullable|string|max:500',
        ]);

        $user = Auth::user();
        $application = NocApplication::findOrFail($validated['application_id']);

        // Check authorization
        if (!$user->hasRole(['admin', 'manager']) && $application->applicant_id !== $user->id) {
            return response()->json([
                'success' => false,
                'message' => 'Unauthorized to upload documents for this application'
            ], 403);
        }

        // Check if application is in editable state
        if (!in_array($application->status, ['draft', 'submitted'])) {
            return response()->json([
                'success' => false,
                'message' => 'Cannot upload documents. Application is not in editable state.'
            ], 422);
        }

        $file = $request->file('file');
        $documentType = $validated['document_type'];

        // Get document requirements from template or defaults
        $requirements = $application->template 
            ? $application->template->document_requirements 
            : NocTemplate::getDefaultDocumentRequirements($application->noc_type);

        // Validate document type against requirements
        if (!isset($requirements[$documentType])) {
            return response()->json([
                'success' => false,
                'message' => 'Invalid document type for this NOC application'
            ], 422);
        }

        $requirement = $requirements[$documentType];

        // Validate file type
        $fileExtension = strtolower($file->getClientOriginalExtension());
        if (!in_array($fileExtension, $requirement['allowed_types'])) {
            return response()->json([
                'success' => false,
                'message' => "Invalid file type. Allowed types: " . implode(', ', $requirement['allowed_types'])
            ], 422);
        }

        // Validate file size (requirement max_size is in KB, file size is in bytes)
        $maxSizeBytes = ($requirement['max_size'] ?? 10240) * 1024;
        if ($file->getSize() > $maxSizeBytes) {
            return response()->json([
                'success' => false,
                'message' => "File size too large. Maximum allowed: " . ($maxSizeBytes / 1024 / 1024) . "MB"
            ], 422);
        }

        // Check if user can upload more files of this type
        $existingCount = Document::where('documentable_type', 'App\Models\NocApplication')
            ->where('documentable_id', $application->id)
            ->where('type', $documentType)
            ->count();

        if ($existingCount >= ($requirement['max_files'] ?? 1)) {
            return response()->json([
                'success' => false,
                'message' => "Maximum number of files reached for this document type. Limit: " . ($requirement['max_files'] ?? 1)
            ], 422);
        }

        try {
            // Upload file
            $uploadResult = $this->uploadFile($file, $application->noc_type, $documentType);

            // Create document record
            $document = Document::create([
                'title' => $validated['title'] ?? $requirement['label'],
                'description' => $validated['description'] ?? $requirement['description'],
                'type' => $documentType,
                'category' => 'noc',
                'documentable_type' => 'App\Models\NocApplication',
                'documentable_id' => $application->id,
                'file_name' => $file->getClientOriginalName(),
                'file_path' => $uploadResult['path'],
                'file_type' => $fileExtension,
                'file_size' => $file->getSize(),
                'mime_type' => $file->getMimeType(),
                'file_hash' => hash_file('sha256', $file->getPathname()),
                'uploaded_by' => $user->id,
                'visibility' => Document::VISIBILITY_PRIVATE,
                'is_required' => $requirement['required'] ?? false,
                'is_sensitive' => true, // NOC documents are sensitive
            ]);

            // Update application documents array
            $documents = $application->documents ?? [];
            $documents[] = [
                'document_id' => $document->id,
                'type' => $documentType,
                'file_path' => $uploadResult['path'],
                'uploaded_at' => now()->toISOString(),
            ];
            $application->update(['documents' => $documents]);

            return response()->json([
                'success' => true,
                'data' => [
                    'document' => $document,
                    'upload_info' => $uploadResult,
                ],
                'message' => 'NOC document uploaded successfully'
            ], 201);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to upload document',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Get documents for a NOC application
     */
    public function getDocuments(NocApplication $application): JsonResponse
    {
        $user = Auth::user();

        // Check authorization
        if (!$user->hasRole(['admin', 'manager']) && $application->applicant_id !== $user->id) {
            return response()->json([
                'success' => false,
                'message' => 'Unauthorized to view documents for this application'
            ], 403);
        }

        $documents = Document::where('documentable_type', 'App\Models\NocApplication')
            ->where('documentable_id', $application->id)
            ->with('uploadedBy')
            ->get()
            ->groupBy('type');

        // Get requirements to show completion status
        $requirements = $application->template 
            ? $application->template->document_requirements 
            : NocTemplate::getDefaultDocumentRequirements($application->noc_type);

        $documentStatus = [];
        foreach ($requirements as $type => $requirement) {
            $uploaded = $documents->get($type, collect());
            $documentStatus[$type] = [
                'requirement' => $requirement,
                'uploaded_count' => $uploaded->count(),
                'max_files' => $requirement['max_files'] ?? 1,
                'can_upload_more' => $uploaded->count() < ($requirement['max_files'] ?? 1),
                'is_complete' => $requirement['required'] ? $uploaded->count() > 0 : true,
                'documents' => $uploaded->map(function ($doc) {
                    return [
                        'id' => $doc->id,
                        'title' => $doc->title,
                        'file_name' => $doc->file_name,
                        'file_size' => $doc->file_size,
                        'status' => $doc->status,
                        'uploaded_at' => $doc->created_at,
                        'uploaded_by' => $doc->uploadedBy->name ?? 'Unknown',
                    ];
                }),
            ];
        }

        $overallCompletion = $this->calculateDocumentCompletion($documentStatus);

        return response()->json([
            'success' => true,
            'data' => [
                'application' => $application,
                'document_status' => $documentStatus,
                'completion_percentage' => $overallCompletion,
                'is_complete' => $overallCompletion === 100,
            ],
            'message' => 'NOC application documents retrieved successfully'
        ]);
    }

    /**
     * Delete a document from NOC application
     */
    public function deleteDocument(NocApplication $application, Document $document): JsonResponse
    {
        $user = Auth::user();

        // Check authorization
        if (!$user->hasRole(['admin', 'manager']) && $application->applicant_id !== $user->id) {
            return response()->json([
                'success' => false,
                'message' => 'Unauthorized to delete documents for this application'
            ], 403);
        }

        // Check if application is in editable state
        if (!in_array($application->status, ['draft', 'submitted'])) {
            return response()->json([
                'success' => false,
                'message' => 'Cannot delete documents. Application is not in editable state.'
            ], 422);
        }

        // Verify document belongs to this application
        if ($document->documentable_type !== 'App\Models\NocApplication' || 
            $document->documentable_id !== $application->id) {
            return response()->json([
                'success' => false,
                'message' => 'Document does not belong to this application'
            ], 422);
        }

        try {
            // Remove from application documents array
            $documents = $application->documents ?? [];
            $documents = array_filter($documents, function($doc) use ($document) {
                return ($doc['document_id'] ?? null) !== $document->id;
            });
            $application->update(['documents' => array_values($documents)]);

            // Delete the document
            $document->delete();

            return response()->json([
                'success' => true,
                'message' => 'Document deleted successfully'
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to delete document',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Upload file to storage
     */
    private function uploadFile(UploadedFile $file, string $nocType, string $documentType): array
    {
        $directory = "noc-documents/{$nocType}/{$documentType}/" . date('Y/m');
        $filename = time() . '_' . uniqid() . '_' . $file->getClientOriginalName();
        $path = $file->storeAs($directory, $filename, 'public');

        return [
            'path' => $path,
            'url' => Storage::url($path),
            'directory' => $directory,
            'filename' => $filename,
        ];
    }

    /**
     * Calculate document completion percentage
     */
    private function calculateDocumentCompletion(array $documentStatus): int
    {
        $totalRequired = 0;
        $completed = 0;

        foreach ($documentStatus as $status) {
            if ($status['requirement']['required']) {
                $totalRequired++;
                if ($status['is_complete']) {
                    $completed++;
                }
            }
        }

        return $totalRequired > 0 ? round(($completed / $totalRequired) * 100) : 100;
    }
} 