<?php

namespace App\Http\Controllers;

use App\Models\Document;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Facades\Validator;
use Illuminate\Validation\Rule;
use Illuminate\Http\UploadedFile;

class DocumentController extends Controller
{
    /**
     * Display a listing of documents.
     */
    public function index(Request $request): JsonResponse
    {
        $query = Document::with(['uploadedBy', 'verifiedBy', 'documentable']);

        // Apply filters
        if ($request->filled('type')) {
            $query->byType($request->type);
        }

        if ($request->filled('status')) {
            $query->byStatus($request->status);
        }

        if ($request->filled('documentable_type')) {
            $query->where('documentable_type', $request->documentable_type);
        }

        if ($request->filled('documentable_id')) {
            $query->where('documentable_id', $request->documentable_id);
        }

        if ($request->filled('search')) {
            $search = $request->search;
            $query->where(function ($q) use ($search) {
                $q->where('title', 'like', "%{$search}%")
                  ->orWhere('document_number', 'like', "%{$search}%")
                  ->orWhere('file_name', 'like', "%{$search}%");
            });
        }

        // Filter by expiry
        if ($request->filled('expiry_filter')) {
            switch ($request->expiry_filter) {
                case 'expired':
                    $query->expired();
                    break;
                case 'expiring':
                    $query->expiring($request->get('expiry_days', 30));
                    break;
            }
        }

        // Current versions only
        if ($request->boolean('current_versions_only')) {
            $query->currentVersions();
        }

        // Apply sorting
        $sortBy = $request->get('sort_by', 'created_at');
        $sortOrder = $request->get('sort_order', 'desc');
        $query->orderBy($sortBy, $sortOrder);

        // Paginate results
        $perPage = $request->get('per_page', 15);
        $documents = $query->paginate($perPage);

        return response()->json([
            'success' => true,
            'data' => $documents,
            'message' => 'Documents retrieved successfully'
        ]);
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        //
    }

    /**
     * Store a newly created document.
     */
    public function store(Request $request): JsonResponse
    {
        $validator = Validator::make($request->all(), [
            'title' => 'required|string|max:200',
            'description' => 'nullable|string',
            'type' => ['required', Rule::in(Document::getAllTypes())],
            'category' => 'nullable|string|max:50',
            'documentable_type' => 'required|string',
            'documentable_id' => 'required|integer',
            'file' => 'required|file|max:10240', // 10MB max
            'document_date' => 'nullable|date',
            'expiry_date' => 'nullable|date|after:today',
            'issuing_authority' => 'nullable|string|max:200',
            'document_id_number' => 'nullable|string|max:100',
            'visibility' => ['nullable', Rule::in(Document::getAllVisibilityOptions())],
            'is_required' => 'boolean',
            'is_sensitive' => 'boolean',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => 'Validation failed',
                'errors' => $validator->errors()
            ], 422);
        }

        try {
            $file = $request->file('file');
            $uploadResult = $this->uploadFile($file, $request->type);

            $document = Document::create(array_merge($validator->validated(), [
                'file_name' => $file->getClientOriginalName(),
                'file_path' => $uploadResult['path'],
                'file_type' => $file->getClientOriginalExtension(),
                'file_size' => $file->getSize(),
                'mime_type' => $file->getMimeType(),
                'file_hash' => hash_file('sha256', $file->getPathname()),
                'uploaded_by' => auth()->id(),
                'visibility' => $request->get('visibility', Document::VISIBILITY_PRIVATE),
            ]));

            return response()->json([
                'success' => true,
                'data' => $document->load(['uploadedBy', 'documentable']),
                'message' => 'Document uploaded successfully'
            ], 201);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to upload document',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Display the specified document.
     */
    public function show(Document $document): JsonResponse
    {
        $document->load(['uploadedBy', 'verifiedBy', 'documentable', 'versions']);

        return response()->json([
            'success' => true,
            'data' => $document,
            'message' => 'Document retrieved successfully'
        ]);
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(string $id)
    {
        //
    }

    /**
     * Update the specified document.
     */
    public function update(Request $request, Document $document): JsonResponse
    {
        $validator = Validator::make($request->all(), [
            'title' => 'sometimes|required|string|max:200',
            'description' => 'nullable|string',
            'category' => 'nullable|string|max:50',
            'document_date' => 'nullable|date',
            'expiry_date' => 'nullable|date|after:today',
            'issuing_authority' => 'nullable|string|max:200',
            'document_id_number' => 'nullable|string|max:100',
            'visibility' => ['nullable', Rule::in(Document::getAllVisibilityOptions())],
            'is_required' => 'boolean',
            'is_sensitive' => 'boolean',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => 'Validation failed',
                'errors' => $validator->errors()
            ], 422);
        }

        try {
            $document->update($validator->validated());

            return response()->json([
                'success' => true,
                'data' => $document->load(['uploadedBy', 'verifiedBy']),
                'message' => 'Document updated successfully'
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to update document',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Remove the specified document.
     */
    public function destroy(Document $document): JsonResponse
    {
        try {
            $document->delete();

            return response()->json([
                'success' => true,
                'message' => 'Document deleted successfully'
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to delete document',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Download the specified document.
     */
    public function download(Document $document): \Symfony\Component\HttpFoundation\StreamedResponse
    {
        if (!Storage::exists($document->file_path)) {
            abort(404, 'File not found');
        }

        return Storage::download($document->file_path, $document->file_name);
    }

    /**
     * Verify document.
     */
    public function verify(Request $request, Document $document): JsonResponse
    {
        $validator = Validator::make($request->all(), [
            'action' => 'required|in:verify,reject',
            'notes' => 'nullable|string',
            'reason' => 'required_if:action,reject|string',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => 'Validation failed',
                'errors' => $validator->errors()
            ], 422);
        }

        try {
            $user = auth()->user();

            if ($request->action === 'verify') {
                $document->verify($user, $request->notes);
                $message = 'Document verified successfully';
            } else {
                $document->reject($user, $request->reason);
                $message = 'Document rejected successfully';
            }

            return response()->json([
                'success' => true,
                'data' => $document->fresh(['verifiedBy']),
                'message' => $message
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to process document verification',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Create new version of document.
     */
    public function createVersion(Request $request, Document $document): JsonResponse
    {
        $validator = Validator::make($request->all(), [
            'file' => 'required|file|max:10240', // 10MB max
            'title' => 'nullable|string|max:200',
            'description' => 'nullable|string',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => 'Validation failed',
                'errors' => $validator->errors()
            ], 422);
        }

        try {
            $file = $request->file('file');
            $uploadResult = $this->uploadFile($file, $document->type);

            $newVersion = $document->createNewVersion([
                'title' => $request->get('title', $document->title),
                'description' => $request->get('description', $document->description),
                'type' => $document->type,
                'category' => $document->category,
                'documentable_type' => $document->documentable_type,
                'documentable_id' => $document->documentable_id,
                'file_name' => $file->getClientOriginalName(),
                'file_path' => $uploadResult['path'],
                'file_type' => $file->getClientOriginalExtension(),
                'file_size' => $file->getSize(),
                'mime_type' => $file->getMimeType(),
                'file_hash' => hash_file('sha256', $file->getPathname()),
                'uploaded_by' => auth()->id(),
                'visibility' => $document->visibility,
                'is_required' => $document->is_required,
                'is_sensitive' => $document->is_sensitive,
            ]);

            return response()->json([
                'success' => true,
                'data' => $newVersion->load(['uploadedBy']),
                'message' => 'New document version created successfully'
            ], 201);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to create document version',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Get document statistics.
     */
    public function statistics(): JsonResponse
    {
        try {
            $stats = [
                'total_documents' => Document::count(),
                'pending_verification' => Document::pending()->count(),
                'verified_documents' => Document::verified()->count(),
                'rejected_documents' => Document::byStatus(Document::STATUS_REJECTED)->count(),
                'expired_documents' => Document::expired()->count(),
                'expiring_soon' => Document::expiring(30)->count(),
                'by_type' => Document::selectRaw('type, COUNT(*) as count')
                    ->groupBy('type')
                    ->pluck('count', 'type'),
            ];

            return response()->json([
                'success' => true,
                'data' => $stats,
                'message' => 'Statistics retrieved successfully'
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to retrieve statistics',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Get documents by entity.
     */
    public function byEntity(Request $request): JsonResponse
    {
        $validator = Validator::make($request->all(), [
            'documentable_type' => 'required|string',
            'documentable_id' => 'required|integer',
            'type' => ['nullable', Rule::in(Document::getAllTypes())],
            'status' => ['nullable', Rule::in(Document::getAllStatuses())],
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => 'Validation failed',
                'errors' => $validator->errors()
            ], 422);
        }

        try {
            $query = Document::where('documentable_type', $request->documentable_type)
                ->where('documentable_id', $request->documentable_id)
                ->with(['uploadedBy', 'verifiedBy'])
                ->currentVersions();

            if ($request->filled('type')) {
                $query->byType($request->type);
            }

            if ($request->filled('status')) {
                $query->byStatus($request->status);
            }

            $documents = $query->get();

            return response()->json([
                'success' => true,
                'data' => $documents,
                'message' => 'Documents retrieved successfully'
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to retrieve documents',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Bulk verify documents.
     */
    public function bulkVerify(Request $request): JsonResponse
    {
        $validator = Validator::make($request->all(), [
            'document_ids' => 'required|array',
            'document_ids.*' => 'integer|exists:documents,id',
            'action' => 'required|in:verify,reject',
            'notes' => 'nullable|string',
            'reason' => 'required_if:action,reject|string',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => 'Validation failed',
                'errors' => $validator->errors()
            ], 422);
        }

        try {
            $user = auth()->user();
            $documents = Document::whereIn('id', $request->document_ids)->get();
            $processed = 0;

            foreach ($documents as $document) {
                if ($request->action === 'verify') {
                    $document->verify($user, $request->notes);
                } else {
                    $document->reject($user, $request->reason);
                }
                $processed++;
            }

            return response()->json([
                'success' => true,
                'data' => ['processed_count' => $processed],
                'message' => "Documents {$request->action}d successfully"
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to process documents',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * List all pending KYC documents for admin/owner review.
     */
    public function listPendingKycDocuments(Request $request): JsonResponse
    {
        $query = Document::with(['uploadedBy', 'verifiedBy', 'documentable'])
            ->where('type', 'kyc')
            ->pending();

        if ($request->filled('tenant_id')) {
            $query->where('documentable_type', 'App\\Models\\Tenant')
                  ->where('documentable_id', $request->tenant_id);
        }
        if ($request->filled('category')) {
            $query->where('category', $request->category);
        }
        if ($request->filled('from_date')) {
            $query->whereDate('created_at', '>=', $request->from_date);
        }
        if ($request->filled('to_date')) {
            $query->whereDate('created_at', '<=', $request->to_date);
        }
        $perPage = $request->get('per_page', 15);
        $documents = $query->orderBy('created_at', 'desc')->paginate($perPage);

        return response()->json([
            'success' => true,
            'data' => $documents,
            'message' => 'Pending KYC documents retrieved successfully'
        ]);
    }

    /**
     * Upload file to storage.
     */
    private function uploadFile(UploadedFile $file, string $type): array
    {
        $directory = "documents/{$type}/" . date('Y/m');
        $filename = time() . '_' . $file->getClientOriginalName();
        $path = $file->storeAs($directory, $filename, 'public');

        return [
            'path' => $path,
            'url' => Storage::url($path),
        ];
    }
}
