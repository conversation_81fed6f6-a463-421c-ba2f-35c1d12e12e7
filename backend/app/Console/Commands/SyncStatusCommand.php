<?php

namespace App\Console\Commands;

use App\Services\SyncService;
use App\Models\SyncAuditLog;
use Illuminate\Console\Command;

class SyncStatusCommand extends Command
{
    /**
     * The name and signature of the console command.
     */
    protected $signature = 'sync:status 
                            {--hours=24 : Number of hours to analyze}
                            {--detailed : Show detailed statistics}';

    /**
     * The console command description.
     */
    protected $description = 'Show sync system status and statistics';

    /**
     * Execute the console command.
     */
    public function handle(SyncService $syncService): int
    {
        $hours = (int) $this->option('hours');
        $detailed = $this->option('detailed');

        $this->info("Sync System Status (Last {$hours} hours)");
        $this->line(str_repeat('=', 50));

        try {
            $stats = $syncService->getSyncStatistics($hours);
            
            // Overall statistics
            $this->info("Overall Statistics:");
            $this->table(
                ['Metric', 'Value'],
                [
                    ['Total Events', number_format($stats['total_events'])],
                    ['Success Rate', number_format($stats['success_rate'], 2) . '%'],
                    ['Failure Rate', number_format($stats['failure_rate'], 2) . '%'],
                    ['Retry Rate', number_format($stats['retry_rate'], 2) . '%'],
                    ['Avg Processing Time', number_format($stats['average_processing_time'], 2) . ' ms'],
                    ['Max Processing Time', number_format($stats['max_processing_time'], 2) . ' ms'],
                    ['Avg Memory Usage', $this->formatBytes($stats['average_memory_usage'])],
                    ['Max Memory Usage', $this->formatBytes($stats['max_memory_usage'])],
                ]
            );

            // Queue health
            $this->line('');
            $this->info("Queue Health:");
            $queueHealth = $stats['queue_health'];
            $statusColor = match($queueHealth['status']) {
                'healthy' => 'info',
                'warning' => 'warn',
                'critical' => 'error',
                default => 'comment'
            };
            
            $this->line("Status: <{$statusColor}>{$queueHealth['status']}</{$statusColor}>");
            $this->line("Pending Jobs: " . ($queueHealth['pending_jobs'] ?? 'Unknown'));
            $this->line("Failed Jobs: " . ($queueHealth['failed_jobs'] ?? 'Unknown'));
            
            if (isset($queueHealth['last_processed'])) {
                $this->line("Last Processed: " . $queueHealth['last_processed']->diffForHumans());
            }

            if ($detailed) {
                $this->showDetailedStatistics($stats);
            }

            // Recent failures
            $this->showRecentFailures();

            return Command::SUCCESS;
            
        } catch (\Exception $e) {
            $this->error("Failed to get sync status: {$e->getMessage()}");
            return Command::FAILURE;
        }
    }

    /**
     * Show detailed statistics.
     */
    protected function showDetailedStatistics(array $stats): void
    {
        $this->line('');
        $this->info("Statistics by Entity Type:");
        
        $entityData = [];
        foreach ($stats['by_entity_type'] as $entityType => $data) {
            $entityData[] = [
                $entityType,
                $data['total'],
                $data['success'],
                $data['failed'],
                $data['skipped'],
                number_format(($data['success'] / max($data['total'], 1)) * 100, 1) . '%'
            ];
        }
        
        $this->table(
            ['Entity Type', 'Total', 'Success', 'Failed', 'Skipped', 'Success Rate'],
            $entityData
        );

        $this->line('');
        $this->info("Statistics by Operation:");
        
        $operationData = [];
        foreach ($stats['by_operation'] as $operation => $data) {
            $operationData[] = [
                $operation,
                $data['total'],
                number_format($data['success_rate'], 1) . '%'
            ];
        }
        
        $this->table(
            ['Operation', 'Total', 'Success Rate'],
            $operationData
        );
    }

    /**
     * Show recent failures.
     */
    protected function showRecentFailures(): void
    {
        $recentFailures = SyncAuditLog::withStatus(SyncAuditLog::STATUS_FAILED)
            ->recent(24)
            ->orderBy('created_at', 'desc')
            ->limit(10)
            ->get();

        if ($recentFailures->isNotEmpty()) {
            $this->line('');
            $this->warn("Recent Failures (Last 10):");
            
            $failureData = [];
            foreach ($recentFailures as $failure) {
                $failureData[] = [
                    $failure->entity_type,
                    $failure->entity_id ?? 'N/A',
                    $failure->operation,
                    $failure->retry_count,
                    $failure->created_at->diffForHumans(),
                    substr($failure->error_message ?? '', 0, 50) . '...'
                ];
            }
            
            $this->table(
                ['Entity', 'ID', 'Operation', 'Retries', 'Time', 'Error'],
                $failureData
            );
        }
    }

    /**
     * Format bytes to human readable format.
     */
    protected function formatBytes(?float $bytes): string
    {
        if ($bytes === null) {
            return 'N/A';
        }
        
        $units = ['B', 'KB', 'MB', 'GB'];
        $unitIndex = 0;
        
        while ($bytes >= 1024 && $unitIndex < count($units) - 1) {
            $bytes /= 1024;
            $unitIndex++;
        }
        
        return number_format($bytes, 2) . ' ' . $units[$unitIndex];
    }
}
