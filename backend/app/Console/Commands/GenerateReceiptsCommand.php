<?php

namespace App\Console\Commands;

use App\Models\RentPayment;
use App\Models\RentReceipt;
use App\Services\ReceiptGenerationService;
use App\Services\NotificationService;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Log;
use Carbon\Carbon;

class GenerateReceiptsCommand extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'receipts:generate 
                            {--payment-id= : Generate receipt for specific payment}
                            {--batch-size=50 : Number of payments to process in batch}
                            {--dry-run : Show what would be generated without actually generating}
                            {--force : Force generation even if receipt already exists}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Generate receipts for completed payments';

    protected $receiptGenerationService;
    protected $notificationService;

    public function __construct(
        ReceiptGenerationService $receiptGenerationService,
        NotificationService $notificationService
    ) {
        parent::__construct();
        $this->receiptGenerationService = $receiptGenerationService;
        $this->notificationService = $notificationService;
    }

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle(): int
    {
        $this->info('Starting automatic receipt generation...');

        try {
            if ($this->option('payment-id')) {
                return $this->generateForSpecificPayment();
            }

            return $this->generateForCompletedPayments();

        } catch (\Exception $e) {
            $this->error('Error during receipt generation: ' . $e->getMessage());
            Log::error('Receipt generation command failed', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
            ]);

            return 1;
        }
    }

    /**
     * Generate receipt for a specific payment.
     *
     * @return int
     */
    protected function generateForSpecificPayment(): int
    {
        $paymentId = $this->option('payment-id');
        $payment = RentPayment::with(['tenant', 'unit.property'])->find($paymentId);

        if (!$payment) {
            $this->error("Payment with ID {$paymentId} not found.");
            return 1;
        }

        if ($payment->status !== 'completed') {
            $this->error("Payment with ID {$paymentId} is not completed (status: {$payment->status}).");
            return 1;
        }

        // Check if receipt already exists
        $existingReceipt = RentReceipt::where('payment_id', $payment->id)->first();
        if ($existingReceipt && !$this->option('force')) {
            $this->warn("Receipt already exists for payment {$paymentId}. Use --force to regenerate.");
            return 0;
        }

        if ($this->option('dry-run')) {
            $this->info("Would generate receipt for payment {$paymentId}");
            $this->info("Recipient: {$payment->tenant->email}");
            $this->info("Amount: {$payment->amount}");
            return 0;
        }

        $this->info("Generating receipt for payment {$paymentId}...");

        try {
            $receipt = $this->receiptGenerationService->generateReceipt(
                $payment,
                $payment->tenant->email
            );

            $this->info("Receipt generated successfully: {$receipt->receipt_number}");

            // Log the generation
            $receipt->auditLogs()->create([
                'action' => 'receipt_generated_automatically',
                'description' => 'Receipt generated via command line',
                'user_id' => null, // System generated
                'metadata' => [
                    'command' => 'receipts:generate',
                    'payment_id' => $payment->id,
                ],
            ]);

            return 0;

        } catch (\Exception $e) {
            $this->error("Failed to generate receipt for payment {$paymentId}: " . $e->getMessage());
            Log::error("Failed to generate receipt for payment {$paymentId}", [
                'error' => $e->getMessage(),
            ]);

            return 1;
        }
    }

    /**
     * Generate receipts for all completed payments without receipts.
     *
     * @return int
     */
    protected function generateForCompletedPayments(): int
    {
        $batchSize = (int) $this->option('batch-size');
        $force = $this->option('force');

        // Get completed payments that don't have receipts (unless force is used)
        $query = RentPayment::where('status', 'completed')
            ->with(['tenant', 'unit.property']);

        if (!$force) {
            $query->whereDoesntHave('receipt');
        }

        $payments = $query->take($batchSize)->get();

        if ($payments->isEmpty()) {
            $this->info('No payments found that require receipt generation.');
            return 0;
        }

        $this->info("Found {$payments->count()} payments requiring receipt generation.");

        if ($this->option('dry-run')) {
            $this->info('DRY RUN - Would generate receipts for the following payments:');
            foreach ($payments as $payment) {
                $this->line("- Payment ID: {$payment->id}, Tenant: {$payment->tenant->email}, Amount: {$payment->amount}");
            }
            return 0;
        }

        $successCount = 0;
        $errorCount = 0;

        $progressBar = $this->output->createProgressBar($payments->count());
        $progressBar->start();

        foreach ($payments as $payment) {
            try {
                // Skip if receipt already exists and not forcing
                if (!$force && $payment->receipt()->exists()) {
                    $progressBar->advance();
                    continue;
                }

                $receipt = $this->receiptGenerationService->generateReceipt(
                    $payment,
                    $payment->tenant->email
                );

                // Log the generation
                $receipt->auditLogs()->create([
                    'action' => 'receipt_generated_automatically',
                    'description' => 'Receipt generated via command line',
                    'user_id' => null, // System generated
                    'metadata' => [
                        'command' => 'receipts:generate',
                        'payment_id' => $payment->id,
                    ],
                ]);

                $successCount++;

            } catch (\Exception $e) {
                $errorCount++;
                Log::error("Failed to generate receipt for payment {$payment->id}", [
                    'error' => $e->getMessage(),
                ]);
            }

            $progressBar->advance();
        }

        $progressBar->finish();
        $this->newLine();

        $this->info("Receipt generation completed:");
        $this->info("- Successfully generated: {$successCount}");
        $this->info("- Errors: {$errorCount}");

        return $errorCount > 0 ? 1 : 0;
    }
} 