<?php

namespace App\Console\Commands;

use App\Services\RentPaymentService;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Log;

class PaymentsSyncCommand extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'payments:sync {--days=7 : Number of days to sync}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Sync rent payments from external OneApp service';

    protected RentPaymentService $rentPaymentService;

    public function __construct(RentPaymentService $rentPaymentService)
    {
        parent::__construct();
        $this->rentPaymentService = $rentPaymentService;
    }

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $this->info('Starting rent payment sync...');

        try {
            $days = $this->option('days');
            $params = [
                'days' => $days,
                'sync_date' => now()->toDateString(),
            ];

            $result = $this->rentPaymentService->reconcilePayments($params);

            $this->info("Sync completed successfully!");
            $this->info("Reconciled: " . count($result['reconciled']) . " payments");
            $this->info("Discrepancies: " . count($result['discrepancies']) . " issues");

            if (!empty($result['discrepancies'])) {
                $this->warn("Discrepancies found:");
                foreach ($result['discrepancies'] as $discrepancy) {
                    $this->warn("- " . $discrepancy);
                }
            }

            Log::info('Payment sync command completed', [
                'reconciled_count' => count($result['reconciled']),
                'discrepancy_count' => count($result['discrepancies']),
            ]);

            return 0;
        } catch (\Exception $e) {
            $this->error("Sync failed: " . $e->getMessage());
            Log::error('Payment sync command failed', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
            ]);

            return 1;
        }
    }
}
