<?php

namespace App\Console\Commands;

use App\Services\SyncService;
use Illuminate\Console\Command;

class SyncRetryFailedCommand extends Command
{
    /**
     * The name and signature of the console command.
     */
    protected $signature = 'sync:retry-failed 
                            {--limit=100 : Maximum number of failed syncs to retry}
                            {--max-retries=3 : Maximum retry attempts per sync}';

    /**
     * The console command description.
     */
    protected $description = 'Retry failed sync operations';

    /**
     * Execute the console command.
     */
    public function handle(SyncService $syncService): int
    {
        $limit = (int) $this->option('limit');
        $maxRetries = (int) $this->option('max-retries');

        $this->info("Retrying failed sync operations (limit: {$limit}, max retries: {$maxRetries})...");

        try {
            $results = $syncService->retryFailedSyncs($maxRetries);
            
            $successful = collect($results)->where('success', true)->count();
            $failed = collect($results)->where('success', false)->count();
            
            $this->info("Retry completed:");
            $this->info("  - Successful: {$successful}");
            $this->info("  - Failed: {$failed}");
            
            if ($failed > 0) {
                $this->warn("Some retries failed. Check logs for details.");
                
                // Show failed items
                $failedItems = collect($results)->where('success', false);
                foreach ($failedItems as $item) {
                    $error = isset($item['error']) ? $item['error'] : 'Unknown error';
                    $this->error("  - {$item['entity_type']} #{$item['entity_id']}: {$error}");
                }
            }
            
            return Command::SUCCESS;
            
        } catch (\Exception $e) {
            $this->error("Failed to retry sync operations: {$e->getMessage()}");
            return Command::FAILURE;
        }
    }
}
