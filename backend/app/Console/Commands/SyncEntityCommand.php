<?php

namespace App\Console\Commands;

use App\Services\SyncService;
use App\Models\Society;
use App\Models\Unit;
use App\Models\Tenant;
use App\Models\Agreement;
use App\Models\RentPayment;
use Illuminate\Console\Command;

class SyncEntityCommand extends Command
{
    /**
     * The name and signature of the console command.
     */
    protected $signature = 'sync:entity 
                            {type : Entity type (society, unit, tenant, agreement, payment)}
                            {id : Entity ID}
                            {--operation=updated : Operation type (created, updated, deleted)}';

    /**
     * The console command description.
     */
    protected $description = 'Manually trigger sync for a specific entity';

    /**
     * Execute the console command.
     */
    public function handle(SyncService $syncService): int
    {
        $type = strtolower($this->argument('type'));
        $id = $this->argument('id');
        $operation = $this->option('operation');

        $this->info("Triggering sync for {$type} #{$id} with operation '{$operation}'...");

        try {
            $entity = $this->findEntity($type, $id);
            
            if (!$entity) {
                $this->error("Entity not found: {$type} #{$id}");
                return Command::FAILURE;
            }

            $success = $syncService->syncEntity($entity, $operation);
            
            if ($success) {
                $this->info("Sync triggered successfully!");
                $this->line("Entity: {$type} #{$id}");
                $this->line("Operation: {$operation}");
                $this->line("Check sync audit logs for processing status.");
            } else {
                $this->error("Failed to trigger sync. Check logs for details.");
                return Command::FAILURE;
            }

            return Command::SUCCESS;
            
        } catch (\Exception $e) {
            $this->error("Error triggering sync: {$e->getMessage()}");
            return Command::FAILURE;
        }
    }

    /**
     * Find entity by type and ID.
     */
    protected function findEntity(string $type, int $id)
    {
        return match($type) {
            'society' => Society::find($id),
            'unit' => Unit::find($id),
            'tenant' => Tenant::find($id),
            'agreement' => Agreement::find($id),
            'payment' => RentPayment::find($id),
            default => null
        };
    }
}
