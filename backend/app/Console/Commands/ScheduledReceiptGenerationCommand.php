<?php

namespace App\Console\Commands;

use App\Models\RentPayment;
use App\Models\RentReceipt;
use App\Services\ReceiptGenerationService;
use App\Services\NotificationService;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Log;
use Carbon\Carbon;

class ScheduledReceiptGenerationCommand extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'receipts:schedule 
                            {--hours=24 : Hours to look back for completed payments}
                            {--batch-size=100 : Number of payments to process in batch}
                            {--auto-deliver : Automatically deliver receipts via email}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Scheduled receipt generation for completed payments';

    protected $receiptGenerationService;
    protected $notificationService;

    public function __construct(
        ReceiptGenerationService $receiptGenerationService,
        NotificationService $notificationService
    ) {
        parent::__construct();
        $this->receiptGenerationService = $receiptGenerationService;
        $this->notificationService = $notificationService;
    }

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle(): int
    {
        $this->info('Starting scheduled receipt generation...');

        try {
            $hours = (int) $this->option('hours');
            $batchSize = (int) $this->option('batch-size');
            $autoDeliver = $this->option('auto-deliver');

            $cutoffTime = Carbon::now()->subHours($hours);

            // Get completed payments from the last N hours that don't have receipts
            $payments = RentPayment::where('status', 'completed')
                ->where('payment_date', '>=', $cutoffTime)
                ->whereDoesntHave('receipt')
                ->with(['tenant', 'unit.property'])
                ->take($batchSize)
                ->get();

            if ($payments->isEmpty()) {
                $this->info("No payments found from the last {$hours} hours that require receipt generation.");
                return 0;
            }

            $this->info("Found {$payments->count()} payments requiring receipt generation from the last {$hours} hours.");

            $successCount = 0;
            $errorCount = 0;
            $deliveredCount = 0;

            $progressBar = $this->output->createProgressBar($payments->count());
            $progressBar->start();

            foreach ($payments as $payment) {
                try {
                    // Generate receipt
                    $receipt = $this->receiptGenerationService->generateReceipt(
                        $payment,
                        $payment->tenant->email
                    );

                    // Log the generation
                    $receipt->auditLogs()->create([
                        'action' => 'receipt_generated_scheduled',
                        'description' => 'Receipt generated via scheduled command',
                        'user_id' => null, // System generated
                        'metadata' => [
                            'command' => 'receipts:schedule',
                            'payment_id' => $payment->id,
                            'hours_lookback' => $hours,
                        ],
                    ]);

                    $successCount++;

                    // Auto-deliver if requested
                    if ($autoDeliver) {
                        try {
                            $this->notificationService->sendReceiptNotification(
                                $receipt,
                                $payment->tenant->email
                            );

                            $receipt->update([
                                'delivery_status' => 'delivered',
                                'delivered_at' => now(),
                            ]);

                            // Log delivery
                            $receipt->auditLogs()->create([
                                'action' => 'receipt_delivered_automatically',
                                'description' => 'Receipt delivered automatically via scheduled command',
                                'user_id' => null, // System generated
                                'metadata' => [
                                    'command' => 'receipts:schedule',
                                    'recipient_email' => $payment->tenant->email,
                                ],
                            ]);

                            $deliveredCount++;

                        } catch (\Exception $e) {
                            Log::error("Failed to auto-deliver receipt for payment {$payment->id}", [
                                'error' => $e->getMessage(),
                            ]);

                            $receipt->update([
                                'delivery_status' => 'failed',
                            ]);
                        }
                    }

                } catch (\Exception $e) {
                    $errorCount++;
                    Log::error("Failed to generate receipt for payment {$payment->id}", [
                        'error' => $e->getMessage(),
                    ]);
                }

                $progressBar->advance();
            }

            $progressBar->finish();
            $this->newLine();

            $this->info("Scheduled receipt generation completed:");
            $this->info("- Successfully generated: {$successCount}");
            $this->info("- Errors: {$errorCount}");

            if ($autoDeliver) {
                $this->info("- Auto-delivered: {$deliveredCount}");
            }

            return $errorCount > 0 ? 1 : 0;

        } catch (\Exception $e) {
            $this->error('Error during scheduled receipt generation: ' . $e->getMessage());
            Log::error('Scheduled receipt generation command failed', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
            ]);

            return 1;
        }
    }
} 