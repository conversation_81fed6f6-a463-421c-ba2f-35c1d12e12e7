<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Models\Agreement;
use Carbon\Carbon;

class ExpireAgreements extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'agreements:expire';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Automatically expire agreements that have passed their end date';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $today = Carbon::today()->toDateString();
        
        // Find all active agreements that have expired (end_date < today)
        $expiredAgreements = Agreement::where('status', 'active')
            ->where('end_date', '<', $today)
            ->get();

        if ($expiredAgreements->isEmpty()) {
            $this->info('No agreements to expire.');
            return 0;
        }

        $expiredCount = 0;
        
        foreach ($expiredAgreements as $agreement) {
            try {
                // Update status to expired
                $oldStatus = $agreement->status;
                $agreement->status = 'expired';
                
                // Log the change in history
                $history = $agreement->history ?? [];
                $history[] = [
                    'action' => 'expired',
                    'old_status' => $oldStatus,
                    'new_status' => 'expired',
                    'at' => now()->toDateTimeString(),
                    'by' => 'system', // Automated expiry
                    'reason' => 'Agreement end date passed',
                ];
                
                $agreement->history = $history;
                $agreement->save();
                
                $expiredCount++;
                
                $this->line("Expired agreement ID {$agreement->id} (Tenant: {$agreement->tenant_id}, Unit: {$agreement->unit_id})");
                
            } catch (\Exception $e) {
                $this->error("Failed to expire agreement ID {$agreement->id}: " . $e->getMessage());
            }
        }
        
        $this->info("Successfully expired {$expiredCount} agreement(s).");
        
        return 0;
    }
}
