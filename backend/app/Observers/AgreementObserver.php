<?php

namespace App\Observers;

use App\Models\Agreement;
use App\Services\AgreementAuditService;
use Illuminate\Support\Facades\Auth;

class AgreementObserver
{
    protected $auditService;

    public function __construct(AgreementAuditService $auditService)
    {
        $this->auditService = $auditService;
    }

    /**
     * Handle the Agreement "created" event.
     */
    public function created(Agreement $agreement): void
    {
        $this->auditService->logCreation($agreement);
    }

    /**
     * Handle the Agreement "updated" event.
     */
    public function updated(Agreement $agreement): void
    {
        $oldValues = $agreement->getOriginal();
        $newValues = $agreement->getDirty();
        
        if (!empty($newValues)) {
            $this->auditService->logUpdate($agreement, $oldValues, $newValues);
            
            // Log specific events based on what changed
            if (isset($newValues['status'])) {
                $this->auditService->logStatusChange(
                    $agreement,
                    $oldValues['status'],
                    $newValues['status']
                );
            }
            
            if (isset($newValues['signed_by_owner']) && $newValues['signed_by_owner']) {
                $this->auditService->logSigning($agreement, 'owner');
            }
            
            if (isset($newValues['signed_by_tenant']) && $newValues['signed_by_tenant']) {
                $this->auditService->logSigning($agreement, 'tenant');
            }
            
            if ($agreement->isFullySigned() && 
                (isset($newValues['signed_by_owner']) || isset($newValues['signed_by_tenant']))) {
                $this->auditService->logFullySigned($agreement);
            }
            
            if (isset($newValues['renewal_reminder_sent']) && $newValues['renewal_reminder_sent']) {
                $this->auditService->logRenewalReminder($agreement);
            }
            
            if (isset($newValues['signature_workflow_started_at']) && $newValues['signature_workflow_started_at']) {
                $this->auditService->logSignatureWorkflow($agreement, 'started');
            }
            
            if (isset($newValues['signature_workflow_completed_at']) && $newValues['signature_workflow_completed_at']) {
                $this->auditService->logSignatureWorkflow($agreement, 'completed');
            }
            
            if (isset($newValues['signature_workflow_cancelled_at']) && $newValues['signature_workflow_cancelled_at']) {
                $this->auditService->logSignatureWorkflow($agreement, 'cancelled', [
                    'cancelled_by' => $agreement->signature_workflow_cancelled_by,
                    'cancellation_reason' => $agreement->signature_workflow_cancellation_reason,
                ]);
            }
        }
    }

    /**
     * Handle the Agreement "deleted" event.
     */
    public function deleted(Agreement $agreement): void
    {
        $this->auditService->logAction(
            $agreement,
            'deleted',
            $agreement->toArray(),
            null,
            [
                'deleted_by' => Auth::id(),
                'deletion_reason' => 'Agreement deleted',
            ]
        );
    }

    /**
     * Handle the Agreement "restored" event.
     */
    public function restored(Agreement $agreement): void
    {
        $this->auditService->logAction(
            $agreement,
            'restored',
            null,
            $agreement->toArray(),
            [
                'restored_by' => Auth::id(),
            ]
        );
    }
} 