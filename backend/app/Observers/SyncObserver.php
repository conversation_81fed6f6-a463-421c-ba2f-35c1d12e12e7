<?php

namespace App\Observers;

use Illuminate\Database\Eloquent\Model;
use App\Events\Sync\SocietyUpdated;
use App\Events\Sync\UnitUpdated;
use App\Events\Sync\TenantUpdated;
use App\Events\Sync\AgreementUpdated;
use App\Events\Sync\PaymentUpdated;
use App\Events\Sync\EntityDeleted;

class SyncObserver
{
    /**
     * Handle the model "created" event.
     */
    public function created(Model $model): void
    {
        if ($this->shouldSync($model)) {
            $this->dispatchSyncEvent($model, 'created');
        }
    }

    /**
     * Handle the model "updated" event.
     */
    public function updated(Model $model): void
    {
        if ($this->shouldSync($model)) {
            $this->dispatchSyncEvent($model, 'updated');
        }
    }

    /**
     * Handle the model "deleted" event.
     */
    public function deleted(Model $model): void
    {
        if ($this->shouldSync($model)) {
            $this->dispatchDeletionEvent($model);
        }
    }

    /**
     * Determine if the model should be synchronized.
     */
    protected function shouldSync(Model $model): bool
    {
        // Check if sync is enabled
        if (!config('sync.enabled', true)) {
            return false;
        }

        // Check if this model type is configured for sync
        $entityType = $this->getEntityType($model);
        $entityConfig = config("sync.entities.{$entityType}");
        
        if (!$entityConfig) {
            return false;
        }

        // Check if the model has a shouldSync method and use it
        if (method_exists($model, 'shouldSync')) {
            return $model->shouldSync();
        }

        return true;
    }

    /**
     * Dispatch the appropriate sync event.
     */
    protected function dispatchSyncEvent(Model $model, string $operation): void
    {
        $entityType = $this->getEntityType($model);
        $eventClass = $this->getEventClass($entityType);
        
        if (!$eventClass) {
            return;
        }

        // Get model data with relationships if needed
        $modelData = $this->getModelDataForSync($model, $entityType);
        
        // Generate correlation ID for tracking
        $correlationId = $this->generateCorrelationId($model, $operation);
        
        // Dispatch the event
        event(new $eventClass($modelData, $operation, $correlationId));
    }

    /**
     * Dispatch entity deletion event.
     */
    protected function dispatchDeletionEvent(Model $model): void
    {
        $entityType = $this->getEntityType($model);
        $societyId = $this->extractSocietyId($model);
        $correlationId = $this->generateCorrelationId($model, 'deleted');
        
        event(new EntityDeleted($entityType, $model->id, $societyId, $correlationId));
    }

    /**
     * Get the entity type for the model.
     */
    protected function getEntityType(Model $model): string
    {
        $className = class_basename($model);
        
        // Handle special cases
        $typeMap = [
            'RentPayment' => 'RentPayment',
        ];
        
        return $typeMap[$className] ?? $className;
    }

    /**
     * Get the event class for the entity type.
     */
    protected function getEventClass(string $entityType): ?string
    {
        $eventMap = [
            'Society' => SocietyUpdated::class,
            'Unit' => UnitUpdated::class,
            'Tenant' => TenantUpdated::class,
            'Agreement' => AgreementUpdated::class,
            'RentPayment' => PaymentUpdated::class,
        ];
        
        return $eventMap[$entityType] ?? null;
    }

    /**
     * Get model data for synchronization.
     */
    protected function getModelDataForSync(Model $model, string $entityType): array
    {
        $entityConfig = config("sync.entities.{$entityType}", []);
        $relationships = $entityConfig['relationships'] ?? [];
        
        // Load required relationships
        if (!empty($relationships)) {
            $model->load($relationships);
        }
        
        // Convert to array and include relationship data
        $data = $model->toArray();
        
        // Add society_id if not present but can be derived
        if (!isset($data['society_id'])) {
            $societyId = $this->extractSocietyId($model);
            if ($societyId) {
                $data['society_id'] = $societyId;
            }
        }
        
        return $data;
    }

    /**
     * Extract society ID from model for tenant isolation.
     */
    protected function extractSocietyId(Model $model): ?int
    {
        // Direct society_id field
        if (isset($model->society_id)) {
            return $model->society_id;
        }
        
        // For Society model, use its own ID
        if ($model instanceof \App\Models\Society) {
            return $model->id;
        }
        
        // For Unit model, use society_id
        if ($model instanceof \App\Models\Unit) {
            return $model->society_id;
        }
        
        // For models with unit relationship
        if (isset($model->unit_id) && method_exists($model, 'unit')) {
            $unit = $model->unit;
            return $unit?->society_id;
        }
        
        // For models with society relationship
        if (method_exists($model, 'society')) {
            $society = $model->society;
            return $society?->id;
        }
        
        return null;
    }

    /**
     * Generate correlation ID for tracking related events.
     */
    protected function generateCorrelationId(Model $model, string $operation): string
    {
        $entityType = $this->getEntityType($model);
        return "sync_{$entityType}_{$operation}_{$model->id}_" . uniqid() . '_' . time();
    }

    /**
     * Check if we're in a bulk operation to prevent queue flooding.
     */
    protected function isBulkOperation(): bool
    {
        // Check if we're in a database transaction with many operations
        // This is a simple heuristic - you might want to implement more sophisticated detection
        return app()->bound('sync.bulk_operation') && app('sync.bulk_operation');
    }

    /**
     * Handle the model "restored" event.
     */
    public function restored(Model $model): void
    {
        if ($this->shouldSync($model)) {
            $this->dispatchSyncEvent($model, 'restored');
        }
    }

    /**
     * Handle the model "force deleted" event.
     */
    public function forceDeleted(Model $model): void
    {
        if ($this->shouldSync($model)) {
            $this->dispatchDeletionEvent($model);
        }
    }
}
