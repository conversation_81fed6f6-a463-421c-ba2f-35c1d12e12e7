<?php

namespace App\Mail;

use App\Models\Agreement;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Mail\Mailable;
use Illuminate\Mail\Mailables\Content;
use Illuminate\Mail\Mailables\Envelope;
use Illuminate\Queue\SerializesModels;

class AgreementRenewalReminder extends Mailable
{
    use Queueable, SerializesModels;

    public $agreement;
    public $recipientType;
    public $daysUntilExpiry;

    /**
     * Create a new message instance.
     */
    public function __construct(Agreement $agreement, string $recipientType)
    {
        $this->agreement = $agreement;
        $this->recipientType = $recipientType;
        $this->daysUntilExpiry = $agreement->end_date->diffInDays(now());
    }

    /**
     * Get the message envelope.
     */
    public function envelope(): Envelope
    {
        $subject = match($this->recipientType) {
            'tenant' => 'Agreement Renewal Reminder - Action Required',
            'owner' => 'Agreement Expiring Soon - Tenant Renewal Notice',
            'admin' => 'Agreement Renewal Alert - Administrative Notice',
            default => 'Agreement Renewal Reminder'
        };

        return new Envelope(
            subject: $subject,
        );
    }

    /**
     * Get the message content definition.
     */
    public function content(): Content
    {
        return new Content(
            view: 'emails.agreement-renewal-reminder',
            with: [
                'agreement' => $this->agreement,
                'recipientType' => $this->recipientType,
                'daysUntilExpiry' => $this->daysUntilExpiry,
                'tenantName' => $this->agreement->tenant->name ?? 'Tenant',
                'unitNumber' => $this->agreement->unit->unit_number ?? 'Unknown',
                'endDate' => $this->agreement->end_date->format('F j, Y'),
                'renewalUrl' => config('app.url') . '/agreements/' . $this->agreement->id . '/renew',
            ],
        );
    }

    /**
     * Get the attachments for the message.
     *
     * @return array<int, \Illuminate\Mail\Mailables\Attachment>
     */
    public function attachments(): array
    {
        return [];
    }
} 