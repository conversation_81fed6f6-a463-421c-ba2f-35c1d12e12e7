<?php

namespace App\Mcp;

use PhpMcp\Server\Attributes\McpTool;
use PhpMcp\Server\Attributes\McpResource;
use PhpMcp\Server\Attributes\McpPrompt;

class TmsTools
{
    /**
     * Get TMS system information
     */
    #[McpTool(name: 'get_system_info', description: 'Get TMS system information')]
    public function getSystemInfo(): array
    {
        return [
            'name' => 'Tenant Management System',
            'version' => '1.0.0',
            'description' => 'Housing Society Tenant Management System',
            'status' => 'active',
            'timestamp' => now()->toISOString(),
        ];
    }

    /**
     * Health check for the TMS system
     */
    #[McpTool(name: 'health_check', description: 'Perform system health check')]
    public function healthCheck(): array
    {
        return [
            'success' => true,
            'data' => [
                'status' => 'healthy',
                'database' => 'connected',
                'cache' => 'operational',
                'timestamp' => now()->toISOString(),
            ]
        ];
    }

    /**
     * Get tenant information
     */
    #[McpTool(name: 'get_tenant_info', description: 'Get tenant information by ID')]
    public function getTenantInfo(string $tenantId): array
    {
        return [
            'tenant_id' => $tenantId,
            'name' => 'Sample Tenant',
            'unit' => 'A-101',
            'status' => 'active',
            'move_in_date' => '2024-01-15',
            'contact' => [
                'email' => '<EMAIL>',
                'phone' => '+**********',
            ],
        ];
    }

    /**
     * List all units in the society
     */
    #[McpTool(name: 'list_units', description: 'List all units in the society')]
    public function listUnits(): array
    {
        return [
            'units' => [
                ['id' => 'A-101', 'status' => 'occupied', 'tenant_id' => 'T001'],
                ['id' => 'A-102', 'status' => 'vacant', 'tenant_id' => null],
                ['id' => 'B-201', 'status' => 'occupied', 'tenant_id' => 'T002'],
                ['id' => 'B-202', 'status' => 'maintenance', 'tenant_id' => null],
            ],
            'total_units' => 4,
            'occupied' => 2,
            'vacant' => 1,
            'maintenance' => 1,
        ];
    }

    /**
     * Get system configuration resource
     */
    #[McpResource(uri: 'config://system', mimeType: 'application/json', description: 'System configuration')]
    public function getSystemConfig(): array
    {
        return [
            'app_name' => config('app.name'),
            'app_env' => config('app.env'),
            'app_debug' => config('app.debug'),
            'app_url' => config('app.url'),
            'timezone' => config('app.timezone'),
        ];
    }

    /**
     * Generate a welcome prompt for new tenants
     */
    #[McpPrompt(name: 'tenant_welcome', description: 'Generate a welcome message for new tenants')]
    public function generateTenantWelcome(string $tenantName, string $unitNumber): array
    {
        return [
            [
                'role' => 'system',
                'content' => "You are a helpful assistant for the Tenant Management System."
            ],
            [
                'role' => 'user',
                'content' => "Generate a welcome message for a new tenant named {$tenantName} in unit {$unitNumber}. Include information about the onboarding process and what documents they need to provide."
            ]
        ];
    }

    /**
     * Get comprehensive system dashboard data
     */
    #[McpTool(name: 'get_system_dashboard', description: 'Get comprehensive system dashboard with all key metrics')]
    public function getSystemDashboard(): array
    {
        try {
            $dashboard = [
                'units' => [
                    'total' => \App\Models\Unit::active()->count(),
                    'occupied' => \App\Models\Unit::where('status', 'occupied')->count(),
                    'vacant' => \App\Models\Unit::where('status', 'vacant')->count(),
                    'to_let' => \App\Models\Unit::where('status', 'to-let')->count(),
                    'occupancy_rate' => \App\Models\Unit::active()->count() > 0
                        ? round((\App\Models\Unit::where('status', 'occupied')->count() / \App\Models\Unit::active()->count()) * 100, 2)
                        : 0
                ],
                'tenants' => [
                    'total' => \App\Models\Tenant::count(),
                    'active' => \App\Models\Tenant::where('status', 'active')->count(),
                    'kyc_pending' => \App\Models\Tenant::where('kyc_status', 'pending')->count(),
                    'kyc_verified' => \App\Models\Tenant::where('kyc_status', 'verified')->count(),
                ],
                'agreements' => [
                    'total' => \App\Models\Agreement::count(),
                    'active' => \App\Models\Agreement::where('status', 'active')->count(),
                    'expiring_soon' => \App\Models\Agreement::where('status', 'active')
                        ->where('end_date', '<=', now()->addDays(30))
                        ->where('end_date', '>=', now())
                        ->count(),
                    'expired' => \App\Models\Agreement::where('status', 'expired')->count(),
                ],
                'payments' => [
                    'this_month' => \App\Models\RentPayment::whereBetween('payment_date', [now()->startOfMonth(), now()->endOfMonth()])->count(),
                    'this_month_amount' => \App\Models\RentPayment::whereBetween('payment_date', [now()->startOfMonth(), now()->endOfMonth()])->sum('total_amount'),
                    'overdue' => \App\Models\RentPayment::where('due_date', '<', now())->where('status', '!=', 'paid')->count(),
                    'overdue_amount' => \App\Models\RentPayment::where('due_date', '<', now())->where('status', '!=', 'paid')->sum('total_amount'),
                ],
                'enquiries' => [
                    'total' => \App\Models\Enquiry::count(),
                    'new' => \App\Models\Enquiry::where('status', 'new')->count(),
                    'this_month' => \App\Models\Enquiry::whereBetween('created_at', [now()->startOfMonth(), now()->endOfMonth()])->count(),
                    'conversion_rate' => \App\Models\Enquiry::count() > 0
                        ? round((\App\Models\Enquiry::where('status', 'converted')->count() / \App\Models\Enquiry::count()) * 100, 2)
                        : 0
                ],
                'recent_activity' => [
                    'recent_payments' => \App\Models\RentPayment::with(['tenant.user:id,name', 'unit:id,unit_number'])
                        ->latest()->limit(5)->get(),
                    'recent_enquiries' => \App\Models\Enquiry::latest()->limit(5)->get(),
                    'recent_unit_changes' => \App\Models\UnitStatusHistory::with(['unit:id,unit_number', 'changedBy:id,name'])
                        ->latest()->limit(5)->get(),
                ]
            ];

            return [
                'success' => true,
                'data' => $dashboard,
                'metadata' => [
                    'generated_at' => now()->toISOString(),
                    'generated_via' => 'MCP System Tool'
                ]
            ];
        } catch (\Exception $e) {
            return [
                'success' => false,
                'message' => 'Error generating system dashboard',
                'error' => $e->getMessage()
            ];
        }
    }

    /**
     * Get system performance metrics
     */
    #[McpTool(name: 'get_system_performance', description: 'Get system performance metrics and health status')]
    public function getSystemPerformance(): array
    {
        try {
            $performance = [
                'database' => [
                    'status' => 'connected',
                    'total_records' => [
                        'units' => \App\Models\Unit::count(),
                        'tenants' => \App\Models\Tenant::count(),
                        'agreements' => \App\Models\Agreement::count(),
                        'payments' => \App\Models\RentPayment::count(),
                        'enquiries' => \App\Models\Enquiry::count(),
                    ],
                    'recent_activity' => [
                        'last_24h_payments' => \App\Models\RentPayment::where('created_at', '>=', now()->subDay())->count(),
                        'last_24h_enquiries' => \App\Models\Enquiry::where('created_at', '>=', now()->subDay())->count(),
                        'last_24h_status_changes' => \App\Models\UnitStatusHistory::where('changed_at', '>=', now()->subDay())->count(),
                    ]
                ],
                'system_health' => [
                    'uptime' => 'Available',
                    'last_backup' => 'Not configured via MCP',
                    'storage_usage' => 'Not available via MCP',
                    'memory_usage' => 'Not available via MCP',
                ],
                'api_usage' => [
                    'total_mcp_calls' => 'Not tracked',
                    'successful_operations' => 'Not tracked',
                    'failed_operations' => 'Not tracked',
                ]
            ];

            return [
                'success' => true,
                'data' => $performance,
                'metadata' => [
                    'generated_at' => now()->toISOString(),
                    'note' => 'Some metrics require additional monitoring setup'
                ]
            ];
        } catch (\Exception $e) {
            return [
                'success' => false,
                'message' => 'Error retrieving system performance',
                'error' => $e->getMessage()
            ];
        }
    }

    /**
     * Execute database maintenance tasks
     */
    #[McpTool(name: 'run_maintenance_tasks', description: 'Execute routine database maintenance and cleanup tasks')]
    public function runMaintenanceTasks(): array
    {
        try {
            $results = [];

            // Clean up old audit logs (older than 1 year)
            $oldAuditLogs = \App\Models\AgreementAuditLog::where('created_at', '<', now()->subYear())->count();
            if ($oldAuditLogs > 0) {
                \App\Models\AgreementAuditLog::where('created_at', '<', now()->subYear())->delete();
                $results['audit_logs_cleaned'] = $oldAuditLogs;
            }

            // Update expired agreements
            $expiredAgreements = \App\Models\Agreement::where('status', 'active')
                ->where('end_date', '<', now())
                ->update(['status' => 'expired']);
            $results['agreements_expired'] = $expiredAgreements;

            // Mark overdue payments
            $overduePayments = \App\Models\RentPayment::where('status', 'pending')
                ->where('due_date', '<', now())
                ->update(['status' => 'overdue']);
            $results['payments_marked_overdue'] = $overduePayments;

            // Close old enquiries (older than 6 months with no activity)
            $oldEnquiries = \App\Models\Enquiry::whereIn('status', ['new', 'contacted'])
                ->where('updated_at', '<', now()->subMonths(6))
                ->update(['status' => 'closed']);
            $results['enquiries_auto_closed'] = $oldEnquiries;

            return [
                'success' => true,
                'message' => 'Maintenance tasks completed successfully',
                'data' => $results,
                'metadata' => [
                    'executed_at' => now()->toISOString(),
                    'executed_via' => 'MCP System Tool'
                ]
            ];
        } catch (\Exception $e) {
            return [
                'success' => false,
                'message' => 'Error executing maintenance tasks',
                'error' => $e->getMessage()
            ];
        }
    }
}