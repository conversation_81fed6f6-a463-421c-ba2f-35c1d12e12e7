<?php

namespace App\Mcp;

use App\Models\Enquiry;
use App\Models\Unit;
use PhpMcp\Server\Attributes\McpTool;
use PhpMcp\Server\Attributes\McpResource;
use PhpMcp\Server\Attributes\McpPrompt;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Validator;

class EnquiryTool
{
    /**
     * Get comprehensive enquiry information
     */
    #[McpTool(name: 'get_enquiry_info', description: 'Get comprehensive enquiry information by ID with relationships')]
    public function getEnquiryInfo(string $enquiryId): array
    {
        try {
            $enquiry = Enquiry::with([
                'unit:id,unit_number,block,floor,type,market_rent',
                'assignedTo:id,name,email'
            ])->find($enquiryId);

            if (!$enquiry) {
                return [
                    'success' => false,
                    'message' => 'Enquiry not found',
                    'enquiry_id' => $enquiryId
                ];
            }

            return [
                'success' => true,
                'data' => [
                    'enquiry' => $enquiry->toArray(),
                    'status_label' => $this->getStatusLabel($enquiry->status),
                    'priority_label' => $this->getPriorityLabel($enquiry->priority),
                    'days_since_enquiry' => $enquiry->created_at->diffInDays(now()),
                    'is_active' => in_array($enquiry->status, ['new', 'contacted', 'interested']),
                    'requires_followup' => $enquiry->status === 'contacted' && $enquiry->updated_at->diffInDays(now()) > 3,
                ],
                'metadata' => [
                    'retrieved_at' => now()->toISOString(),
                    'relationships_loaded' => ['unit', 'assignedTo']
                ]
            ];
        } catch (\Exception $e) {
            return [
                'success' => false,
                'message' => 'Error retrieving enquiry information',
                'error' => $e->getMessage(),
                'enquiry_id' => $enquiryId
            ];
        }
    }

    /**
     * List enquiries with filtering and pagination
     */
    #[McpTool(name: 'list_enquiries', description: 'List enquiries with advanced filtering, search, and pagination')]
    public function listEnquiries(
        ?string $status = null,
        ?string $priority = null,
        ?int $unitId = null,
        ?int $assignedTo = null,
        ?string $source = null,
        ?string $search = null,
        string $sortBy = 'created_at',
        string $sortOrder = 'desc',
        int $page = 1,
        int $perPage = 15
    ): array {
        try {
            $query = Enquiry::with([
                'unit:id,unit_number,block,floor,type',
                'assignedTo:id,name'
            ]);

            // Apply filters
            if ($status) $query->where('status', $status);
            if ($priority) $query->where('priority', $priority);
            if ($unitId) $query->where('unit_id', $unitId);
            if ($assignedTo) $query->where('assigned_to', $assignedTo);
            if ($source) $query->where('source', $source);

            // Search functionality
            if ($search) {
                $query->where(function ($q) use ($search) {
                    $q->where('name', 'like', "%{$search}%")
                      ->orWhere('email', 'like', "%{$search}%")
                      ->orWhere('phone', 'like', "%{$search}%")
                      ->orWhere('message', 'like', "%{$search}%")
                      ->orWhereHas('unit', function($unitQuery) use ($search) {
                          $unitQuery->where('unit_number', 'like', "%{$search}%");
                      });
                });
            }

            // Sorting
            $query->orderBy($sortBy, $sortOrder);

            // Pagination
            $enquiries = $query->paginate($perPage, ['*'], 'page', $page);

            return [
                'success' => true,
                'data' => $enquiries->items(),
                'pagination' => [
                    'current_page' => $enquiries->currentPage(),
                    'last_page' => $enquiries->lastPage(),
                    'per_page' => $enquiries->perPage(),
                    'total' => $enquiries->total(),
                    'from' => $enquiries->firstItem(),
                    'to' => $enquiries->lastItem(),
                ],
                'filters_applied' => array_filter([
                    'status' => $status,
                    'priority' => $priority,
                    'unit_id' => $unitId,
                    'assigned_to' => $assignedTo,
                    'source' => $source,
                    'search' => $search,
                ]),
                'metadata' => [
                    'available_statuses' => ['new', 'contacted', 'interested', 'not_interested', 'converted', 'closed'],
                    'available_priorities' => ['low', 'medium', 'high', 'urgent'],
                    'available_sources' => ['website', 'phone', 'walk_in', 'referral', 'social_media', 'advertisement'],
                    'retrieved_at' => now()->toISOString(),
                ]
            ];
        } catch (\Exception $e) {
            return [
                'success' => false,
                'message' => 'Error listing enquiries',
                'error' => $e->getMessage()
            ];
        }
    }

    /**
     * Create a new enquiry
     */
    #[McpTool(name: 'create_enquiry', description: 'Create a new property enquiry with validation')]
    public function createEnquiry(array $enquiryData): array
    {
        try {
            // Validate input data
            $validator = Validator::make($enquiryData, [
                'name' => 'required|string|max:255',
                'email' => 'required|email|max:255',
                'phone' => 'required|string|max:20',
                'unit_id' => 'nullable|exists:units,id',
                'property_type' => 'nullable|string|max:100',
                'budget_min' => 'nullable|numeric|min:0',
                'budget_max' => 'nullable|numeric|min:0',
                'preferred_location' => 'nullable|string|max:255',
                'bedrooms' => 'nullable|integer|min:0',
                'move_in_date' => 'nullable|date',
                'message' => 'required|string',
                'source' => 'required|in:website,phone,walk_in,referral,social_media,advertisement',
                'priority' => 'nullable|in:low,medium,high,urgent',
                'assigned_to' => 'nullable|exists:users,id',
            ]);

            if ($validator->fails()) {
                return [
                    'success' => false,
                    'message' => 'Validation failed',
                    'errors' => $validator->errors()->toArray()
                ];
            }

            DB::beginTransaction();

            // Generate enquiry number
            $enquiryNumber = 'ENQ-' . date('Y') . '-' . str_pad(Enquiry::count() + 1, 6, '0', STR_PAD_LEFT);

            // Create enquiry
            $enquiry = Enquiry::create([
                'enquiry_number' => $enquiryNumber,
                'name' => $enquiryData['name'],
                'email' => $enquiryData['email'],
                'phone' => $enquiryData['phone'],
                'unit_id' => $enquiryData['unit_id'] ?? null,
                'property_type' => $enquiryData['property_type'] ?? null,
                'budget_min' => $enquiryData['budget_min'] ?? null,
                'budget_max' => $enquiryData['budget_max'] ?? null,
                'preferred_location' => $enquiryData['preferred_location'] ?? null,
                'bedrooms' => $enquiryData['bedrooms'] ?? null,
                'move_in_date' => $enquiryData['move_in_date'] ?? null,
                'message' => $enquiryData['message'],
                'source' => $enquiryData['source'],
                'priority' => $enquiryData['priority'] ?? 'medium',
                'status' => 'new',
                'assigned_to' => $enquiryData['assigned_to'] ?? null,
                'created_by' => Auth::id() ?? 1,
            ]);

            DB::commit();

            return [
                'success' => true,
                'message' => 'Enquiry created successfully',
                'data' => $enquiry->load(['unit', 'assignedTo'])->toArray(),
                'metadata' => [
                    'created_at' => now()->toISOString(),
                    'created_via' => 'MCP Tool',
                    'enquiry_number' => $enquiryNumber
                ]
            ];
        } catch (\Exception $e) {
            DB::rollBack();
            return [
                'success' => false,
                'message' => 'Failed to create enquiry',
                'error' => $e->getMessage()
            ];
        }
    }

    /**
     * Update enquiry status
     */
    #[McpTool(name: 'update_enquiry_status', description: 'Update enquiry status with validation')]
    public function updateEnquiryStatus(string $enquiryId, string $newStatus, ?string $notes = null): array
    {
        try {
            $enquiry = Enquiry::find($enquiryId);
            
            if (!$enquiry) {
                return [
                    'success' => false,
                    'message' => 'Enquiry not found',
                    'enquiry_id' => $enquiryId
                ];
            }

            $validStatuses = ['new', 'contacted', 'interested', 'not_interested', 'converted', 'closed'];
            
            if (!in_array($newStatus, $validStatuses)) {
                return [
                    'success' => false,
                    'message' => 'Invalid status',
                    'provided_status' => $newStatus,
                    'valid_statuses' => $validStatuses
                ];
            }

            $oldStatus = $enquiry->status;
            
            $enquiry->update([
                'status' => $newStatus,
                'status_updated_at' => now(),
                'status_updated_by' => Auth::id() ?? 1,
                'notes' => $notes ? ($enquiry->notes ? $enquiry->notes . "\n\n" . $notes : $notes) : $enquiry->notes,
            ]);

            return [
                'success' => true,
                'message' => 'Enquiry status updated successfully',
                'data' => [
                    'enquiry' => $enquiry->fresh(['unit', 'assignedTo'])->toArray(),
                    'status_change' => [
                        'from' => $oldStatus,
                        'to' => $newStatus,
                        'notes' => $notes,
                        'updated_at' => now()->toISOString(),
                        'updated_by' => Auth::id() ?? 1
                    ]
                ]
            ];
        } catch (\Exception $e) {
            return [
                'success' => false,
                'message' => 'Error updating enquiry status',
                'error' => $e->getMessage(),
                'enquiry_id' => $enquiryId
            ];
        }
    }

    /**
     * Get enquiry statistics
     */
    #[McpTool(name: 'get_enquiry_statistics', description: 'Get comprehensive enquiry statistics and analytics')]
    public function getEnquiryStatistics(?string $period = 'month'): array
    {
        try {
            $dateRange = $this->getDateRange($period);
            
            $stats = [
                'total_enquiries' => Enquiry::whereBetween('created_at', $dateRange)->count(),
                'by_status' => Enquiry::whereBetween('created_at', $dateRange)
                    ->select('status', DB::raw('count(*) as count'))
                    ->groupBy('status')
                    ->pluck('count', 'status'),
                'by_priority' => Enquiry::whereBetween('created_at', $dateRange)
                    ->select('priority', DB::raw('count(*) as count'))
                    ->groupBy('priority')
                    ->pluck('count', 'priority'),
                'by_source' => Enquiry::whereBetween('created_at', $dateRange)
                    ->select('source', DB::raw('count(*) as count'))
                    ->groupBy('source')
                    ->pluck('count', 'source'),
                'conversion_rate' => [
                    'total' => Enquiry::whereBetween('created_at', $dateRange)->count(),
                    'converted' => Enquiry::whereBetween('created_at', $dateRange)->where('status', 'converted')->count(),
                    'percentage' => Enquiry::whereBetween('created_at', $dateRange)->count() > 0 
                        ? round((Enquiry::whereBetween('created_at', $dateRange)->where('status', 'converted')->count() / Enquiry::whereBetween('created_at', $dateRange)->count()) * 100, 2)
                        : 0
                ],
                'pending_followup' => Enquiry::where('status', 'contacted')
                    ->where('updated_at', '<', now()->subDays(3))
                    ->count(),
                'recent_enquiries' => Enquiry::with(['unit:id,unit_number', 'assignedTo:id,name'])
                    ->latest()
                    ->limit(10)
                    ->get(),
            ];

            return [
                'success' => true,
                'data' => $stats,
                'metadata' => [
                    'period' => $period,
                    'date_range' => $dateRange,
                    'generated_at' => now()->toISOString(),
                ]
            ];
        } catch (\Exception $e) {
            return [
                'success' => false,
                'message' => 'Error generating enquiry statistics',
                'error' => $e->getMessage()
            ];
        }
    }

    /**
     * Get available units for enquiries
     */
    #[McpTool(name: 'get_available_units_for_enquiry', description: 'Get available units that match enquiry criteria')]
    public function getAvailableUnitsForEnquiry(?string $propertyType = null, ?int $bedrooms = null, ?float $maxBudget = null): array
    {
        try {
            $query = Unit::available()
                ->with(['owner:id,name'])
                ->select(['id', 'unit_number', 'block', 'floor', 'type', 'bedrooms', 'bathrooms', 
                         'area_sqft', 'market_rent', 'security_deposit', 'amenities']);

            // Apply filters based on enquiry criteria
            if ($propertyType) {
                $query->where('type', $propertyType);
            }
            
            if ($bedrooms) {
                $query->where('bedrooms', $bedrooms);
            }
            
            if ($maxBudget) {
                $query->where('market_rent', '<=', $maxBudget);
            }

            $units = $query->orderBy('market_rent')->get();

            return [
                'success' => true,
                'data' => $units->toArray(),
                'summary' => [
                    'total_available' => $units->count(),
                    'filters_applied' => array_filter([
                        'property_type' => $propertyType,
                        'bedrooms' => $bedrooms,
                        'max_budget' => $maxBudget,
                    ]),
                    'price_range' => [
                        'min' => $units->min('market_rent'),
                        'max' => $units->max('market_rent'),
                        'average' => $units->avg('market_rent'),
                    ]
                ],
                'metadata' => [
                    'retrieved_at' => now()->toISOString(),
                ]
            ];
        } catch (\Exception $e) {
            return [
                'success' => false,
                'message' => 'Error retrieving available units',
                'error' => $e->getMessage()
            ];
        }
    }

    private function getStatusLabel(string $status): string
    {
        return match ($status) {
            'new' => 'New',
            'contacted' => 'Contacted',
            'interested' => 'Interested',
            'not_interested' => 'Not Interested',
            'converted' => 'Converted',
            'closed' => 'Closed',
            default => 'Unknown',
        };
    }

    private function getPriorityLabel(string $priority): string
    {
        return match ($priority) {
            'low' => 'Low',
            'medium' => 'Medium',
            'high' => 'High',
            'urgent' => 'Urgent',
            default => 'Medium',
        };
    }

    private function getDateRange(string $period): array
    {
        return match ($period) {
            'week' => [now()->startOfWeek(), now()->endOfWeek()],
            'month' => [now()->startOfMonth(), now()->endOfMonth()],
            'quarter' => [now()->startOfQuarter(), now()->endOfQuarter()],
            'year' => [now()->startOfYear(), now()->endOfYear()],
            default => [now()->startOfMonth(), now()->endOfMonth()],
        };
    }
}
