<?php

namespace App\Mcp;

use App\Models\Agreement;
use App\Models\Tenant;
use App\Models\Unit;
use App\Models\AgreementAuditLog;
use PhpMcp\Server\Attributes\McpTool;
use PhpMcp\Server\Attributes\McpResource;
use PhpMcp\Server\Attributes\McpPrompt;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Validator;

class AgreementTool
{
    /**
     * Get comprehensive agreement information
     */
    #[McpTool(name: 'get_agreement_info', description: 'Get comprehensive agreement information by ID with relationships')]
    public function getAgreementInfo(string $agreementId): array
    {
        try {
            $agreement = Agreement::with([
                'tenant.user:id,name,email,phone',
                'unit:id,unit_number,block,floor,type',
                'createdBy:id,name',
                'auditLogs' => function($query) {
                    $query->with('user:id,name')->latest()->limit(10);
                }
            ])->find($agreementId);

            if (!$agreement) {
                return [
                    'success' => false,
                    'message' => 'Agreement not found',
                    'agreement_id' => $agreementId
                ];
            }

            return [
                'success' => true,
                'data' => [
                    'agreement' => $agreement->toArray(),
                    'status_label' => $this->getStatusLabel($agreement->status),
                    'days_until_expiry' => $agreement->end_date ? $agreement->end_date->diffInDays(now()) : null,
                    'is_active' => $agreement->status === 'active',
                    'is_expired' => $agreement->end_date && $agreement->end_date->isPast(),
                    'is_expiring_soon' => $agreement->end_date && $agreement->end_date->diffInDays(now()) <= 30,
                ],
                'metadata' => [
                    'retrieved_at' => now()->toISOString(),
                    'relationships_loaded' => ['tenant', 'unit', 'createdBy', 'auditLogs']
                ]
            ];
        } catch (\Exception $e) {
            return [
                'success' => false,
                'message' => 'Error retrieving agreement information',
                'error' => $e->getMessage(),
                'agreement_id' => $agreementId
            ];
        }
    }

    /**
     * List agreements with filtering and pagination
     */
    #[McpTool(name: 'list_agreements', description: 'List agreements with advanced filtering, search, and pagination')]
    public function listAgreements(
        ?string $status = null,
        ?int $tenantId = null,
        ?int $unitId = null,
        ?bool $expiringSoon = null,
        ?string $search = null,
        string $sortBy = 'created_at',
        string $sortOrder = 'desc',
        int $page = 1,
        int $perPage = 15
    ): array {
        try {
            $query = Agreement::with([
                'tenant.user:id,name,email',
                'unit:id,unit_number,block,floor'
            ]);

            // Apply filters
            if ($status) $query->where('status', $status);
            if ($tenantId) $query->where('tenant_id', $tenantId);
            if ($unitId) $query->where('unit_id', $unitId);
            
            if ($expiringSoon) {
                $query->where('end_date', '<=', now()->addDays(30))
                      ->where('end_date', '>=', now());
            }

            // Search functionality
            if ($search) {
                $query->where(function ($q) use ($search) {
                    $q->where('agreement_number', 'like', "%{$search}%")
                      ->orWhere('title', 'like', "%{$search}%")
                      ->orWhereHas('tenant.user', function($userQuery) use ($search) {
                          $userQuery->where('name', 'like', "%{$search}%")
                                   ->orWhere('email', 'like', "%{$search}%");
                      })
                      ->orWhereHas('unit', function($unitQuery) use ($search) {
                          $unitQuery->where('unit_number', 'like', "%{$search}%");
                      });
                });
            }

            // Sorting
            $query->orderBy($sortBy, $sortOrder);

            // Pagination
            $agreements = $query->paginate($perPage, ['*'], 'page', $page);

            return [
                'success' => true,
                'data' => $agreements->items(),
                'pagination' => [
                    'current_page' => $agreements->currentPage(),
                    'last_page' => $agreements->lastPage(),
                    'per_page' => $agreements->perPage(),
                    'total' => $agreements->total(),
                    'from' => $agreements->firstItem(),
                    'to' => $agreements->lastItem(),
                ],
                'filters_applied' => array_filter([
                    'status' => $status,
                    'tenant_id' => $tenantId,
                    'unit_id' => $unitId,
                    'expiring_soon' => $expiringSoon,
                    'search' => $search,
                ]),
                'metadata' => [
                    'available_statuses' => ['draft', 'active', 'expired', 'terminated', 'renewed'],
                    'retrieved_at' => now()->toISOString(),
                ]
            ];
        } catch (\Exception $e) {
            return [
                'success' => false,
                'message' => 'Error listing agreements',
                'error' => $e->getMessage()
            ];
        }
    }

    /**
     * Create a new agreement
     */
    #[McpTool(name: 'create_agreement', description: 'Create a new rental agreement with validation')]
    public function createAgreement(array $agreementData): array
    {
        try {
            // Validate input data
            $validator = Validator::make($agreementData, [
                'tenant_id' => 'required|exists:tenants,id',
                'unit_id' => 'required|exists:units,id',
                'start_date' => 'required|date',
                'end_date' => 'required|date|after_or_equal:start_date',
                'rent_amount' => 'required|numeric|min:0',
                'security_deposit' => 'nullable|numeric|min:0',
                'status' => 'required|string',
            ]);

            if ($validator->fails()) {
                return [
                    'success' => false,
                    'message' => 'Validation failed',
                    'errors' => $validator->errors()->toArray()
                ];
            }

            DB::beginTransaction();

            // Create agreement
            $agreement = Agreement::create($agreementData);

            DB::commit();

            return [
                'success' => true,
                'data' => ['id' => $agreement->id],
                'metadata' => [
                    'created_at' => now()->toISOString(),
                    'created_via' => 'MCP Tool'
                ]
            ];
        } catch (\Exception $e) {
            DB::rollBack();
            return [
                'success' => false,
                'message' => 'Failed to create agreement',
                'error' => $e->getMessage()
            ];
        }
    }

    /**
     * Update agreement status
     */
    #[McpTool(name: 'update_agreement_status', description: 'Update agreement status with validation and audit logging')]
    public function updateAgreementStatus(string $agreementId, string $newStatus): array
    {
        try {
            $agreement = Agreement::find($agreementId);
            
            if (!$agreement) {
                return [
                    'success' => false,
                    'message' => 'Agreement not found',
                    'agreement_id' => $agreementId
                ];
            }

            $agreement->status = $newStatus;
            $agreement->save();

            return [
                'success' => true,
                'data' => ['id' => $agreement->id, 'status' => $agreement->status],
            ];
        } catch (\Exception $e) {
            return [
                'success' => false,
                'message' => 'Failed to update agreement status',
                'error' => $e->getMessage()
            ];
        }
    }

    /**
     * Get agreements expiring soon
     */
    #[McpTool(name: 'get_expiring_agreements', description: 'Get agreements expiring within specified days')]
    public function getExpiringAgreements(int $days = 30): array
    {
        try {
            $agreements = Agreement::with(['tenant.user:id,name,email', 'unit:id,unit_number,block'])
                ->where('status', 'active')
                ->where('end_date', '<=', now()->addDays($days))
                ->where('end_date', '>=', now())
                ->orderBy('end_date', 'asc')
                ->get();

            return [
                'success' => true,
                'data' => $agreements->toArray(),
                'summary' => [
                    'total_expiring' => $agreements->count(),
                    'days_filter' => $days,
                    'earliest_expiry' => $agreements->first()?->end_date,
                    'latest_expiry' => $agreements->last()?->end_date,
                ],
                'metadata' => [
                    'retrieved_at' => now()->toISOString(),
                    'filter_period' => "Next {$days} days"
                ]
            ];
        } catch (\Exception $e) {
            return [
                'success' => false,
                'message' => 'Error retrieving expiring agreements',
                'error' => $e->getMessage()
            ];
        }
    }

    private function getStatusLabel(string $status): string
    {
        return match ($status) {
            'draft' => 'Draft',
            'active' => 'Active',
            'expired' => 'Expired',
            'terminated' => 'Terminated',
            'renewed' => 'Renewed',
            default => 'Unknown',
        };
    }
}
