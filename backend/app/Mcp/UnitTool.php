<?php

namespace App\Mcp;

use App\Models\Unit;
use App\Models\Tenant;
use App\Models\User;
use App\Models\UnitStatusHistory;
use PhpMcp\Server\Attributes\McpTool;
use PhpMcp\Server\Attributes\McpResource;
use PhpMcp\Server\Attributes\McpPrompt;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Validator;

class UnitTool
{
    /**
     * Get comprehensive unit information with relationships
     */
    #[McpTool(name: 'get_unit_info', description: 'Get comprehensive unit information by ID with relationships')]
    public function getUnitInfo(string $unitId): array
    {
        try {
            $unit = Unit::with([
                'owner:id,name,email,phone',
                'currentTenant:id,name,email,phone,move_in_date',
                'statusChangedBy:id,name',
                'statusHistory' => function($query) {
                    $query->with('changedBy:id,name')->latest('changed_at')->limit(5);
                },
                'agreements' => function($query) {
                    $query->latest()->limit(3);
                },
                'enquiries' => function($query) {
                    $query->latest()->limit(3);
                }
            ])->find($unitId);

            if (!$unit) {
                return [
                    'success' => false,
                    'message' => 'Unit not found',
                    'unit_id' => $unitId
                ];
            }

            return [
                'success' => true,
                'data' => [
                    'unit' => $unit->toArray(),
                    'display_name' => $unit->display_name,
                    'full_address' => $unit->full_address,
                    'status_label' => $unit->status_label,
                    'status_color' => $unit->status_color,
                    'is_available' => $unit->isAvailable(),
                    'is_occupied' => $unit->isOccupied(),
                    'is_eligible_for_noc_charge' => $unit->isEligibleForNonOccupancyCharge(),
                ],
                'metadata' => [
                    'retrieved_at' => now()->toISOString(),
                    'relationships_loaded' => ['owner', 'currentTenant', 'statusHistory', 'agreements', 'enquiries']
                ]
            ];
        } catch (\Exception $e) {
            return [
                'success' => false,
                'message' => 'Error retrieving unit information',
                'error' => $e->getMessage(),
                'unit_id' => $unitId
            ];
        }
    }

    /**
     * List all units with advanced filtering and pagination
     */
    #[McpTool(name: 'list_units', description: 'List all units with advanced filtering, search, and pagination')]
    public function listUnits(
        ?string $status = null,
        ?string $block = null,
        ?string $floor = null,
        ?string $type = null,
        ?string $search = null,
        ?bool $available = null,
        ?int $ownerId = null,
        string $sortBy = 'unit_number',
        string $sortOrder = 'asc',
        int $page = 1,
        int $perPage = 15
    ): array {
        try {
            $query = Unit::with(['owner:id,name', 'currentTenant:id,name'])
                ->active();

            // Apply filters
            if ($status) $query->where('status', $status);
            if ($block) $query->where('block', $block);
            if ($floor) $query->where('floor', $floor);
            if ($type) $query->where('type', $type);
            if ($ownerId) $query->where('owner_id', $ownerId);
            if ($available) $query->available();

            // Search functionality
            if ($search) {
                $query->where(function ($q) use ($search) {
                    $q->where('unit_number', 'like', "%{$search}%")
                      ->orWhere('block', 'like', "%{$search}%")
                      ->orWhere('floor', 'like', "%{$search}%")
                      ->orWhere('description', 'like', "%{$search}%");
                });
            }

            // Sorting
            $query->orderBy($sortBy, $sortOrder);

            // Pagination
            $units = $query->paginate($perPage, ['*'], 'page', $page);

            return [
                'success' => true,
                'data' => $units->items(),
                'pagination' => [
                    'current_page' => $units->currentPage(),
                    'last_page' => $units->lastPage(),
                    'per_page' => $units->perPage(),
                    'total' => $units->total(),
                    'from' => $units->firstItem(),
                    'to' => $units->lastItem(),
                ],
                'filters_applied' => array_filter([
                    'status' => $status,
                    'block' => $block,
                    'floor' => $floor,
                    'type' => $type,
                    'search' => $search,
                    'available' => $available,
                    'owner_id' => $ownerId,
                ]),
                'sorting' => [
                    'sort_by' => $sortBy,
                    'sort_order' => $sortOrder,
                ],
                'metadata' => [
                    'available_statuses' => Unit::getStatuses(),
                    'available_types' => Unit::getTypes(),
                    'retrieved_at' => now()->toISOString(),
                ]
            ];
        } catch (\Exception $e) {
            return [
                'success' => false,
                'message' => 'Error listing units',
                'error' => $e->getMessage()
            ];
        }
    }

    /**
     * Create a new unit with validation
     */
    #[McpTool(name: 'create_unit', description: 'Create a new unit with comprehensive validation')]
    public function createUnit(array $unitData): array
    {
        try {
            // Validate input data
            $validator = Validator::make($unitData, [
                'unit_number' => 'required|string|max:255|unique:units',
                'block' => 'nullable|string|max:255',
                'floor' => 'nullable|string|max:255',
                'wing' => 'nullable|string|max:255',
                'type' => 'required|in:' . implode(',', Unit::getTypes()),
                'bedrooms' => 'nullable|integer|min:0',
                'bathrooms' => 'nullable|integer|min:0',
                'area_sqft' => 'nullable|numeric|min:0',
                'carpet_area' => 'nullable|numeric|min:0',
                'built_up_area' => 'nullable|numeric|min:0',
                'status' => 'required|in:' . implode(',', Unit::getStatuses()),
                'owner_id' => 'nullable|exists:users,id',
                'market_rent' => 'nullable|numeric|min:0',
                'security_deposit' => 'nullable|numeric|min:0',
                'maintenance_charge' => 'nullable|numeric|min:0',
                'available_from' => 'nullable|date',
                'description' => 'nullable|string',
                'amenities' => 'nullable|array',
                'preferences' => 'nullable|array',
                'notes' => 'nullable|string',
            ]);

            if ($validator->fails()) {
                return [
                    'success' => false,
                    'message' => 'Validation failed',
                    'errors' => $validator->errors()->toArray()
                ];
            }

            DB::beginTransaction();

            $unit = Unit::create($unitData);

            // Create initial status history
            UnitStatusHistory::create([
                'unit_id' => $unit->id,
                'previous_status' => null,
                'new_status' => $unit->status,
                'changed_by' => Auth::id() ?? 1, // Default to admin if no auth
                'reason' => 'Unit created via MCP',
                'changed_at' => now(),
                'change_type' => UnitStatusHistory::CHANGE_TYPE_SYSTEM ?? 'system',
            ]);

            DB::commit();

            return [
                'success' => true,
                'message' => 'Unit created successfully',
                'data' => $unit->load(['owner', 'currentTenant'])->toArray(),
                'metadata' => [
                    'created_at' => now()->toISOString(),
                    'created_via' => 'MCP Tool'
                ]
            ];
        } catch (\Exception $e) {
            DB::rollBack();
            return [
                'success' => false,
                'message' => 'Failed to create unit',
                'error' => $e->getMessage()
            ];
        }
    }

    /**
     * Update unit status with validation and history tracking
     */
    #[McpTool(name: 'update_unit_status', description: 'Update unit status with validation and automatic history tracking')]
    public function updateUnitStatus(string $unitId, string $newStatus, ?string $reason = null): array
    {
        try {
            $unit = Unit::find($unitId);
            if (!$unit) {
                return [
                    'success' => false,
                    'message' => 'Unit not found',
                    'unit_id' => $unitId
                ];
            }
            if (!in_array($newStatus, Unit::getStatuses())) {
                return [
                    'success' => false,
                    'message' => 'Invalid status',
                    'provided_status' => $newStatus,
                    'valid_statuses' => Unit::getStatuses()
                ];
            }
            $oldStatus = $unit->status;
            if ($oldStatus === $newStatus) {
                return [
                    'success' => true,
                    'data' => ['id' => $unit->id, 'status' => $unit->status],
                ];
            }
            $success = $unit->updateStatus($newStatus, $reason, Auth::id() ?? 1);
            if (!$success) {
                return [
                    'success' => false,
                    'message' => 'Invalid status transition',
                    'current_status' => $oldStatus,
                    'requested_status' => $newStatus
                ];
            }
            return [
                'success' => true,
                'data' => ['id' => $unit->id, 'status' => $newStatus],
            ];
        } catch (\Exception $e) {
            return [
                'success' => false,
                'message' => 'Error updating unit status',
                'error' => $e->getMessage(),
                'unit_id' => $unitId
            ];
        }
    }

    /**
     * Get unit statistics and analytics
     */
    #[McpTool(name: 'get_unit_statistics', description: 'Get comprehensive unit statistics and analytics')]
    public function getUnitStatistics(): array
    {
        try {
            $stats = [
                'total_units' => Unit::active()->count(),
                'by_status' => Unit::active()
                    ->select('status', DB::raw('count(*) as count'))
                    ->groupBy('status')
                    ->pluck('count', 'status'),
                'by_type' => Unit::active()
                    ->select('type', DB::raw('count(*) as count'))
                    ->groupBy('type')
                    ->pluck('count', 'type'),
                'available_units' => Unit::available()->count(),
                'rented_units' => Unit::rented()->count(),
                'recent_changes' => UnitStatusHistory::with(['unit:id,unit_number', 'changedBy:id,name'])
                    ->whereHas('unit')
                    ->where('changed_at', '>=', now()->subDays(7))
                    ->orderBy('changed_at', 'desc')
                    ->limit(10)
                    ->get(),
                'occupancy_rate' => [
                    'occupied' => Unit::where('status', Unit::STATUS_OCCUPIED)->count(),
                    'total' => Unit::active()->count(),
                    'percentage' => Unit::active()->count() > 0
                        ? round((Unit::where('status', Unit::STATUS_OCCUPIED)->count() / Unit::active()->count()) * 100, 2)
                        : 0
                ]
            ];

            return [
                'success' => true,
                'data' => $stats,
                'metadata' => [
                    'generated_at' => now()->toISOString(),
                    'period' => 'Current snapshot with 7-day recent changes'
                ]
            ];
        } catch (\Exception $e) {
            return [
                'success' => false,
                'message' => 'Error generating unit statistics',
                'error' => $e->getMessage()
            ];
        }
    }

    /**
     * Get available units for enquiry
     */
    #[McpTool(name: 'get_available_units_for_enquiry', description: 'List available units for new tenant enquiry')]
    public function getAvailableUnitsForEnquiry(): array
    {
        try {
            $units = Unit::where('status', Unit::STATUS_AVAILABLE)->get(['id', 'unit_number', 'block', 'floor', 'type']);
            return [
                'success' => true,
                'data' => $units->map(fn($u) => ['id' => $u->id, 'unit_number' => $u->unit_number])->values()->toArray()
            ];
        } catch (\Exception $e) {
            return [
                'success' => false,
                'message' => 'Error fetching available units',
                'error' => $e->getMessage()
            ];
        }
    }

    /**
     * Create a new tenant record with validation
     */
    #[McpTool(name: 'create_tenant', description: 'Create a new tenant record with comprehensive validation')]
    public function createTenant(array $tenantData): array
    {
        try {
            // Validate input data
            $validator = Validator::make($tenantData, [
                'name' => 'required|string|max:255',
                'email' => 'required|email|unique:tenants,email',
                'phone' => 'required|string|max:20',
                'address' => 'nullable|string',
                'move_in_date' => 'nullable|date',
                'unit_id' => 'nullable|exists:units,id',
                'status' => 'nullable|string',
                'notes' => 'nullable|string',
            ]);

            if ($validator->fails()) {
                return [
                    'success' => false,
                    'message' => 'Validation failed',
                    'errors' => $validator->errors()->toArray()
                ];
            }

            DB::beginTransaction();

            $tenant = Tenant::create($tenantData);

            DB::commit();

            return [
                'success' => true,
                'data' => ['id' => $tenant->id],
                'metadata' => [
                    'created_at' => now()->toISOString(),
                    'created_via' => 'MCP Tool'
                ]
            ];
        } catch (\Exception $e) {
            DB::rollBack();
            return [
                'success' => false,
                'message' => 'Failed to create tenant',
                'error' => $e->getMessage()
            ];
        }
    }
}
