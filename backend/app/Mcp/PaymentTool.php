<?php

namespace App\Mcp;

use App\Models\RentPayment;
use App\Models\RentReceipt;
use App\Models\Tenant;
use App\Models\Unit;
use PhpMcp\Server\Attributes\McpTool;
use PhpMcp\Server\Attributes\McpResource;
use PhpMcp\Server\Attributes\McpPrompt;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Validator;

class PaymentTool
{
    /**
     * Get comprehensive payment information
     */
    #[McpTool(name: 'get_payment_info', description: 'Get comprehensive payment information by ID with relationships')]
    public function getPaymentInfo(string $paymentId): array
    {
        try {
            $payment = RentPayment::with([
                'tenant.user:id,name,email,phone',
                'unit:id,unit_number,block,floor',
                'receipts',
                'createdBy:id,name'
            ])->find($paymentId);

            if (!$payment) {
                return [
                    'success' => false,
                    'message' => 'Payment not found',
                    'payment_id' => $paymentId
                ];
            }

            return [
                'success' => true,
                'data' => [
                    'payment' => $payment->toArray(),
                    'status_label' => $this->getStatusLabel($payment->status),
                    'is_overdue' => $payment->due_date && $payment->due_date->isPast() && $payment->status !== 'paid',
                    'days_overdue' => $payment->due_date && $payment->due_date->isPast() ? now()->diffInDays($payment->due_date) : 0,
                    'has_receipt' => $payment->receipts->isNotEmpty(),
                ],
                'metadata' => [
                    'retrieved_at' => now()->toISOString(),
                    'relationships_loaded' => ['tenant', 'unit', 'receipts', 'createdBy']
                ]
            ];
        } catch (\Exception $e) {
            return [
                'success' => false,
                'message' => 'Error retrieving payment information',
                'error' => $e->getMessage(),
                'payment_id' => $paymentId
            ];
        }
    }

    /**
     * List payments with filtering and pagination
     */
    #[McpTool(name: 'list_payments', description: 'List payments with advanced filtering, search, and pagination')]
    public function listPayments(
        ?string $status = null,
        ?int $tenantId = null,
        ?int $unitId = null,
        ?string $paymentMethod = null,
        ?bool $overdue = null,
        ?string $dateFrom = null,
        ?string $dateTo = null,
        ?string $search = null,
        string $sortBy = 'created_at',
        string $sortOrder = 'desc',
        int $page = 1,
        int $perPage = 15
    ): array {
        try {
            $query = RentPayment::with([
                'tenant.user:id,name,email',
                'unit:id,unit_number,block,floor'
            ]);

            // Apply filters
            if ($status) $query->where('status', $status);
            if ($tenantId) $query->where('tenant_id', $tenantId);
            if ($unitId) $query->where('unit_id', $unitId);
            if ($paymentMethod) $query->where('payment_method', $paymentMethod);
            
            if ($overdue) {
                $query->where('due_date', '<', now())
                      ->where('status', '!=', 'paid');
            }

            if ($dateFrom) {
                $query->where('payment_date', '>=', $dateFrom);
            }
            
            if ($dateTo) {
                $query->where('payment_date', '<=', $dateTo);
            }

            // Search functionality
            if ($search) {
                $query->where(function ($q) use ($search) {
                    $q->where('reference_number', 'like', "%{$search}%")
                      ->orWhere('transaction_id', 'like', "%{$search}%")
                      ->orWhereHas('tenant.user', function($userQuery) use ($search) {
                          $userQuery->where('name', 'like', "%{$search}%")
                                   ->orWhere('email', 'like', "%{$search}%");
                      })
                      ->orWhereHas('unit', function($unitQuery) use ($search) {
                          $unitQuery->where('unit_number', 'like', "%{$search}%");
                      });
                });
            }

            // Sorting
            $query->orderBy($sortBy, $sortOrder);

            // Pagination
            $payments = $query->paginate($perPage, ['*'], 'page', $page);

            return [
                'success' => true,
                'data' => $payments->items(),
                'pagination' => [
                    'current_page' => $payments->currentPage(),
                    'last_page' => $payments->lastPage(),
                    'per_page' => $payments->perPage(),
                    'total' => $payments->total(),
                    'from' => $payments->firstItem(),
                    'to' => $payments->lastItem(),
                ],
                'filters_applied' => array_filter([
                    'status' => $status,
                    'tenant_id' => $tenantId,
                    'unit_id' => $unitId,
                    'payment_method' => $paymentMethod,
                    'overdue' => $overdue,
                    'date_from' => $dateFrom,
                    'date_to' => $dateTo,
                    'search' => $search,
                ]),
                'metadata' => [
                    'available_statuses' => ['pending', 'paid', 'failed', 'refunded', 'cancelled'],
                    'available_methods' => ['cash', 'bank_transfer', 'upi', 'cheque', 'online'],
                    'retrieved_at' => now()->toISOString(),
                ]
            ];
        } catch (\Exception $e) {
            return [
                'success' => false,
                'message' => 'Error listing payments',
                'error' => $e->getMessage()
            ];
        }
    }

    /**
     * Record a new payment
     */
    #[McpTool(name: 'record_payment', description: 'Record a new rent payment with validation')]
    public function recordPayment(array $paymentData): array
    {
        try {
            // Validate input data
            $validator = Validator::make($paymentData, [
                'tenant_id' => 'required|exists:tenants,id',
                'unit_id' => 'required|exists:units,id',
                'amount' => 'required|numeric|min:0',
                'payment_method' => 'required|in:cash,bank_transfer,upi,cheque,online',
                'payment_date' => 'required|date',
                'due_date' => 'nullable|date',
                'period_from' => 'required|date',
                'period_to' => 'required|date|after_or_equal:period_from',
                'transaction_id' => 'nullable|string|max:255',
                'notes' => 'nullable|string',
                'late_fee' => 'nullable|numeric|min:0',
                'maintenance_charge' => 'nullable|numeric|min:0',
                'other_charges' => 'nullable|numeric|min:0',
            ]);

            if ($validator->fails()) {
                return [
                    'success' => false,
                    'message' => 'Validation failed',
                    'errors' => $validator->errors()->toArray()
                ];
            }

            // Verify tenant and unit relationship
            $tenant = Tenant::find($paymentData['tenant_id']);
            if ($tenant->current_unit_id != $paymentData['unit_id']) {
                return [
                    'success' => false,
                    'message' => 'Tenant is not assigned to the specified unit',
                    'tenant_unit_id' => $tenant->current_unit_id,
                    'provided_unit_id' => $paymentData['unit_id']
                ];
            }

            DB::beginTransaction();

            // Generate reference number
            $referenceNumber = 'PAY-' . date('Y') . '-' . str_pad(RentPayment::count() + 1, 8, '0', STR_PAD_LEFT);

            // Calculate total amount
            $totalAmount = $paymentData['amount'] + 
                          ($paymentData['late_fee'] ?? 0) + 
                          ($paymentData['maintenance_charge'] ?? 0) + 
                          ($paymentData['other_charges'] ?? 0);

            // Create payment record
            $payment = RentPayment::create([
                'reference_number' => $referenceNumber,
                'tenant_id' => $paymentData['tenant_id'],
                'unit_id' => $paymentData['unit_id'],
                'amount' => $paymentData['amount'],
                'late_fee' => $paymentData['late_fee'] ?? 0,
                'maintenance_charge' => $paymentData['maintenance_charge'] ?? 0,
                'other_charges' => $paymentData['other_charges'] ?? 0,
                'total_amount' => $totalAmount,
                'payment_method' => $paymentData['payment_method'],
                'payment_date' => $paymentData['payment_date'],
                'due_date' => $paymentData['due_date'] ?? now()->addDays(30),
                'period_from' => $paymentData['period_from'],
                'period_to' => $paymentData['period_to'],
                'transaction_id' => $paymentData['transaction_id'] ?? null,
                'notes' => $paymentData['notes'] ?? null,
                'status' => 'paid',
                'created_by' => Auth::id() ?? 1,
                'payment_received_at' => now(),
            ]);

            // Generate receipt
            $receipt = RentReceipt::create([
                'payment_id' => $payment->id,
                'receipt_number' => 'REC-' . date('Y') . '-' . str_pad(RentReceipt::count() + 1, 8, '0', STR_PAD_LEFT),
                'tenant_id' => $payment->tenant_id,
                'unit_id' => $payment->unit_id,
                'amount' => $payment->total_amount,
                'issued_date' => now(),
                'issued_by' => Auth::id() ?? 1,
                'status' => 'issued',
            ]);

            DB::commit();

            return [
                'success' => true,
                'message' => 'Payment recorded successfully',
                'data' => [
                    'payment' => $payment->load(['tenant.user', 'unit'])->toArray(),
                    'receipt' => $receipt->toArray(),
                ],
                'metadata' => [
                    'created_at' => now()->toISOString(),
                    'created_via' => 'MCP Tool',
                    'reference_number' => $referenceNumber,
                    'receipt_number' => $receipt->receipt_number
                ]
            ];
        } catch (\Exception $e) {
            DB::rollBack();
            return [
                'success' => false,
                'message' => 'Failed to record payment',
                'error' => $e->getMessage()
            ];
        }
    }

    /**
     * Get payment statistics
     */
    #[McpTool(name: 'get_payment_statistics', description: 'Get comprehensive payment statistics and analytics')]
    public function getPaymentStatistics(?string $period = 'month'): array
    {
        try {
            $dateRange = $this->getDateRange($period);
            
            $stats = [
                'total_payments' => RentPayment::whereBetween('payment_date', $dateRange)->count(),
                'total_amount' => RentPayment::whereBetween('payment_date', $dateRange)->sum('total_amount'),
                'by_status' => RentPayment::whereBetween('payment_date', $dateRange)
                    ->select('status', DB::raw('count(*) as count'), DB::raw('sum(total_amount) as amount'))
                    ->groupBy('status')
                    ->get(),
                'by_method' => RentPayment::whereBetween('payment_date', $dateRange)
                    ->select('payment_method', DB::raw('count(*) as count'), DB::raw('sum(total_amount) as amount'))
                    ->groupBy('payment_method')
                    ->get(),
                'overdue_payments' => RentPayment::where('due_date', '<', now())
                    ->where('status', '!=', 'paid')
                    ->count(),
                'overdue_amount' => RentPayment::where('due_date', '<', now())
                    ->where('status', '!=', 'paid')
                    ->sum('total_amount'),
                'recent_payments' => RentPayment::with(['tenant.user:id,name', 'unit:id,unit_number'])
                    ->latest()
                    ->limit(10)
                    ->get(),
            ];

            return [
                'success' => true,
                'data' => $stats,
                'metadata' => [
                    'period' => $period,
                    'date_range' => $dateRange,
                    'generated_at' => now()->toISOString(),
                ]
            ];
        } catch (\Exception $e) {
            return [
                'success' => false,
                'message' => 'Error generating payment statistics',
                'error' => $e->getMessage()
            ];
        }
    }

    /**
     * Get overdue payments
     */
    #[McpTool(name: 'get_overdue_payments', description: 'Get all overdue payments with tenant and unit details')]
    public function getOverduePayments(): array
    {
        try {
            $overduePayments = RentPayment::with([
                'tenant.user:id,name,email,phone',
                'unit:id,unit_number,block,floor'
            ])
            ->where('due_date', '<', now())
            ->where('status', '!=', 'paid')
            ->orderBy('due_date', 'asc')
            ->get();

            $totalOverdueAmount = $overduePayments->sum('total_amount');

            return [
                'success' => true,
                'data' => $overduePayments->toArray(),
                'summary' => [
                    'total_overdue_payments' => $overduePayments->count(),
                    'total_overdue_amount' => $totalOverdueAmount,
                    'oldest_overdue' => $overduePayments->first()?->due_date,
                    'average_overdue_amount' => $overduePayments->count() > 0 ? $totalOverdueAmount / $overduePayments->count() : 0,
                ],
                'metadata' => [
                    'retrieved_at' => now()->toISOString(),
                    'cutoff_date' => now()->toDateString()
                ]
            ];
        } catch (\Exception $e) {
            return [
                'success' => false,
                'message' => 'Error retrieving overdue payments',
                'error' => $e->getMessage()
            ];
        }
    }

    private function getStatusLabel(string $status): string
    {
        return match ($status) {
            'pending' => 'Pending',
            'paid' => 'Paid',
            'failed' => 'Failed',
            'refunded' => 'Refunded',
            'cancelled' => 'Cancelled',
            default => 'Unknown',
        };
    }

    private function getDateRange(string $period): array
    {
        return match ($period) {
            'week' => [now()->startOfWeek(), now()->endOfWeek()],
            'month' => [now()->startOfMonth(), now()->endOfMonth()],
            'quarter' => [now()->startOfQuarter(), now()->endOfQuarter()],
            'year' => [now()->startOfYear(), now()->endOfYear()],
            default => [now()->startOfMonth(), now()->endOfMonth()],
        };
    }
}
