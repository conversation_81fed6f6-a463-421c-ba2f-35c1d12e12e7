<?php

namespace App\Mcp;

use PhpMcp\Server\Attributes\McpTool;
use PhpMcp\Server\Attributes\McpResource;
use PhpMcp\Server\Attributes\McpPrompt;

/**
 * MCP Tool Registry for Tenant Management System
 * 
 * This class provides a centralized registry of all available MCP tools
 * and their capabilities for AI agent automation.
 */
class McpToolRegistry
{
    /**
     * Get all available MCP tools with their descriptions and capabilities
     */
    #[McpTool(name: 'list_all_mcp_tools', description: 'Get comprehensive list of all available MCP tools and their capabilities')]
    public function listAllMcpTools(): array
    {
        return [
            'success' => true,
            'data' => [
                'system_tools' => [
                    'class' => 'TmsTools',
                    'tools' => [
                        'get_system_info' => 'Get basic TMS system information',
                        'health_check' => 'Perform system health check',
                        'get_tenant_info' => 'Get tenant information by ID (legacy)',
                        'list_units' => 'List all units in the society (legacy)',
                        'get_system_dashboard' => 'Get comprehensive system dashboard with all key metrics',
                        'get_system_performance' => 'Get system performance metrics and health status',
                        'run_maintenance_tasks' => 'Execute routine database maintenance and cleanup tasks',
                    ],
                    'resources' => [
                        'config://system' => 'System configuration resource',
                    ],
                    'prompts' => [
                        'tenant_welcome' => 'Generate welcome message for new tenants',
                    ]
                ],
                'unit_management' => [
                    'class' => 'UnitTool',
                    'tools' => [
                        'get_unit_info' => 'Get comprehensive unit information by ID with relationships',
                        'list_units' => 'List all units with advanced filtering, search, and pagination',
                        'create_unit' => 'Create a new unit with comprehensive validation',
                        'update_unit_status' => 'Update unit status with validation and automatic history tracking',
                        'get_unit_statistics' => 'Get comprehensive unit statistics and analytics',
                    ],
                    'capabilities' => [
                        'CRUD operations for units',
                        'Status management with history tracking',
                        'Advanced filtering and search',
                        'Statistics and analytics',
                        'Relationship management (owner, tenant, agreements)',
                    ]
                ],
                'tenant_management' => [
                    'class' => 'TenantTool',
                    'tools' => [
                        'get_tenant_info' => 'Get comprehensive tenant information by ID with relationships',
                        'list_tenants' => 'List all tenants with advanced filtering, search, and pagination',
                        'create_tenant' => 'Create a new tenant with user account and validation',
                        'update_tenant_kyc' => 'Update tenant KYC status with validation',
                    ],
                    'capabilities' => [
                        'CRUD operations for tenants',
                        'User account management',
                        'KYC status management',
                        'Emergency contact management',
                        'Advanced filtering and search',
                    ]
                ],
                'agreement_management' => [
                    'class' => 'AgreementTool',
                    'tools' => [
                        'get_agreement_info' => 'Get comprehensive agreement information by ID with relationships',
                        'list_agreements' => 'List agreements with advanced filtering, search, and pagination',
                        'create_agreement' => 'Create a new rental agreement with validation',
                        'update_agreement_status' => 'Update agreement status with validation and audit logging',
                        'get_expiring_agreements' => 'Get agreements expiring within specified days',
                    ],
                    'capabilities' => [
                        'CRUD operations for agreements',
                        'Status management with audit trails',
                        'Expiry tracking and notifications',
                        'Advanced filtering and search',
                        'Tenant-unit relationship validation',
                    ]
                ],
                'payment_management' => [
                    'class' => 'PaymentTool',
                    'tools' => [
                        'get_payment_info' => 'Get comprehensive payment information by ID with relationships',
                        'list_payments' => 'List payments with advanced filtering, search, and pagination',
                        'record_payment' => 'Record a new rent payment with validation',
                        'get_payment_statistics' => 'Get comprehensive payment statistics and analytics',
                        'get_overdue_payments' => 'Get all overdue payments with tenant and unit details',
                    ],
                    'capabilities' => [
                        'Payment recording and tracking',
                        'Receipt generation',
                        'Overdue payment management',
                        'Statistics and analytics',
                        'Multiple payment methods support',
                    ]
                ],
                'enquiry_management' => [
                    'class' => 'EnquiryTool',
                    'tools' => [
                        'get_enquiry_info' => 'Get comprehensive enquiry information by ID with relationships',
                        'list_enquiries' => 'List enquiries with advanced filtering, search, and pagination',
                        'create_enquiry' => 'Create a new property enquiry with validation',
                        'update_enquiry_status' => 'Update enquiry status with validation',
                        'get_enquiry_statistics' => 'Get comprehensive enquiry statistics and analytics',
                        'get_available_units_for_enquiry' => 'Get available units that match enquiry criteria',
                    ],
                    'capabilities' => [
                        'Lead management and tracking',
                        'Status workflow management',
                        'Unit matching and recommendations',
                        'Conversion tracking',
                        'Source attribution',
                    ]
                ]
            ],
            'metadata' => [
                'total_tools' => 25,
                'total_classes' => 6,
                'generated_at' => now()->toISOString(),
                'version' => '1.0.0'
            ]
        ];
    }

    /**
     * Get MCP tool usage recommendations for AI agents
     */
    #[McpTool(name: 'get_tool_recommendations', description: 'Get intelligent tool usage recommendations for AI agents')]
    public function getToolRecommendations(string $task_description): array
    {
        $recommendations = [];
        $task_lower = strtolower($task_description);

        // Unit-related tasks
        if (str_contains($task_lower, 'unit') || str_contains($task_lower, 'property')) {
            $recommendations[] = [
                'category' => 'Unit Management',
                'primary_tools' => ['get_unit_info', 'list_units', 'update_unit_status'],
                'use_cases' => ['Unit status updates', 'Property searches', 'Occupancy management']
            ];
        }

        // Tenant-related tasks
        if (str_contains($task_lower, 'tenant') || str_contains($task_lower, 'resident')) {
            $recommendations[] = [
                'category' => 'Tenant Management',
                'primary_tools' => ['get_tenant_info', 'list_tenants', 'create_tenant', 'update_tenant_kyc'],
                'use_cases' => ['Tenant onboarding', 'KYC verification', 'Profile management']
            ];
        }

        // Agreement-related tasks
        if (str_contains($task_lower, 'agreement') || str_contains($task_lower, 'contract') || str_contains($task_lower, 'lease')) {
            $recommendations[] = [
                'category' => 'Agreement Management',
                'primary_tools' => ['get_agreement_info', 'create_agreement', 'get_expiring_agreements'],
                'use_cases' => ['Contract creation', 'Renewal management', 'Expiry tracking']
            ];
        }

        // Payment-related tasks
        if (str_contains($task_lower, 'payment') || str_contains($task_lower, 'rent') || str_contains($task_lower, 'receipt')) {
            $recommendations[] = [
                'category' => 'Payment Management',
                'primary_tools' => ['record_payment', 'get_payment_statistics', 'get_overdue_payments'],
                'use_cases' => ['Payment processing', 'Financial reporting', 'Overdue management']
            ];
        }

        // Enquiry-related tasks
        if (str_contains($task_lower, 'enquiry') || str_contains($task_lower, 'inquiry') || str_contains($task_lower, 'lead')) {
            $recommendations[] = [
                'category' => 'Enquiry Management',
                'primary_tools' => ['create_enquiry', 'update_enquiry_status', 'get_available_units_for_enquiry'],
                'use_cases' => ['Lead capture', 'Follow-up management', 'Unit recommendations']
            ];
        }

        // Analytics and reporting tasks
        if (str_contains($task_lower, 'report') || str_contains($task_lower, 'analytics') || str_contains($task_lower, 'statistics')) {
            $recommendations[] = [
                'category' => 'Analytics & Reporting',
                'primary_tools' => ['get_system_dashboard', 'get_unit_statistics', 'get_payment_statistics', 'get_enquiry_statistics'],
                'use_cases' => ['Dashboard generation', 'Performance analysis', 'Trend reporting']
            ];
        }

        // System maintenance tasks
        if (str_contains($task_lower, 'maintenance') || str_contains($task_lower, 'cleanup') || str_contains($task_lower, 'health')) {
            $recommendations[] = [
                'category' => 'System Management',
                'primary_tools' => ['health_check', 'get_system_performance', 'run_maintenance_tasks'],
                'use_cases' => ['System monitoring', 'Performance optimization', 'Data cleanup']
            ];
        }

        return [
            'success' => true,
            'task_description' => $task_description,
            'recommendations' => $recommendations,
            'general_workflow' => [
                '1. Start with list/get tools to understand current state',
                '2. Use create/update tools to make changes',
                '3. Use statistics tools to verify results',
                '4. Use system tools for monitoring and maintenance'
            ],
            'metadata' => [
                'generated_at' => now()->toISOString(),
                'recommendation_count' => count($recommendations)
            ]
        ];
    }

    /**
     * Get MCP tool workflow examples
     */
    #[McpTool(name: 'get_workflow_examples', description: 'Get common workflow examples using MCP tools')]
    public function getWorkflowExamples(): array
    {
        return [
            'success' => true,
            'workflows' => [
                'tenant_onboarding' => [
                    'description' => 'Complete tenant onboarding process',
                    'steps' => [
                        '1. create_tenant - Create tenant with basic information',
                        '2. get_available_units_for_enquiry - Find suitable units',
                        '3. create_agreement - Create rental agreement',
                        '4. update_unit_status - Mark unit as occupied',
                        '5. update_agreement_status - Activate agreement'
                    ],
                    'tools_used' => ['create_tenant', 'get_available_units_for_enquiry', 'create_agreement', 'update_unit_status', 'update_agreement_status']
                ],
                'monthly_reporting' => [
                    'description' => 'Generate comprehensive monthly reports',
                    'steps' => [
                        '1. get_system_dashboard - Get overall system metrics',
                        '2. get_unit_statistics - Get unit occupancy data',
                        '3. get_payment_statistics - Get payment collection data',
                        '4. get_enquiry_statistics - Get lead generation data',
                        '5. get_overdue_payments - Get outstanding payments'
                    ],
                    'tools_used' => ['get_system_dashboard', 'get_unit_statistics', 'get_payment_statistics', 'get_enquiry_statistics', 'get_overdue_payments']
                ],
                'payment_processing' => [
                    'description' => 'Process rent payments and generate receipts',
                    'steps' => [
                        '1. get_tenant_info - Verify tenant details',
                        '2. get_unit_info - Verify unit details',
                        '3. record_payment - Record the payment',
                        '4. get_payment_info - Verify payment was recorded',
                        '5. get_payment_statistics - Update payment metrics'
                    ],
                    'tools_used' => ['get_tenant_info', 'get_unit_info', 'record_payment', 'get_payment_info', 'get_payment_statistics']
                ],
                'enquiry_management' => [
                    'description' => 'Manage property enquiries from lead to conversion',
                    'steps' => [
                        '1. create_enquiry - Capture new enquiry',
                        '2. get_available_units_for_enquiry - Find matching units',
                        '3. update_enquiry_status - Mark as contacted',
                        '4. update_enquiry_status - Mark as interested/converted',
                        '5. get_enquiry_statistics - Track conversion metrics'
                    ],
                    'tools_used' => ['create_enquiry', 'get_available_units_for_enquiry', 'update_enquiry_status', 'get_enquiry_statistics']
                ],
                'system_maintenance' => [
                    'description' => 'Routine system maintenance and cleanup',
                    'steps' => [
                        '1. health_check - Verify system health',
                        '2. get_system_performance - Check performance metrics',
                        '3. run_maintenance_tasks - Execute cleanup tasks',
                        '4. get_expiring_agreements - Check upcoming renewals',
                        '5. get_overdue_payments - Identify collection issues'
                    ],
                    'tools_used' => ['health_check', 'get_system_performance', 'run_maintenance_tasks', 'get_expiring_agreements', 'get_overdue_payments']
                ]
            ],
            'metadata' => [
                'total_workflows' => 5,
                'generated_at' => now()->toISOString()
            ]
        ];
    }
}
