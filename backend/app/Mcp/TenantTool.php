<?php

namespace App\Mcp;

use App\Models\Tenant;
use App\Models\Unit;
use App\Models\User;
use App\Models\EmergencyContact;
use App\Models\TenantHistory;
use PhpMcp\Server\Attributes\McpTool;
use PhpMcp\Server\Attributes\McpResource;
use PhpMcp\Server\Attributes\McpPrompt;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Facades\Hash;

class TenantTool
{
    /**
     * Get comprehensive tenant information with relationships
     */
    #[McpTool(name: 'get_tenant_info', description: 'Get comprehensive tenant information by ID with relationships')]
    public function getTenantInfo(string $tenantId): array
    {
        try {
            $tenant = Tenant::with([
                'user:id,name,email,phone,email_verified_at',
                'currentUnit:id,unit_number,block,floor,type,market_rent',
                'emergencyContacts',
                'agreements' => function($query) {
                    $query->latest()->limit(3);
                },
                'history' => function($query) {
                    $query->latest()->limit(5);
                }
            ])->find($tenantId);

            if (!$tenant) {
                return [
                    'success' => false,
                    'message' => 'Tenant not found',
                    'tenant_id' => $tenantId
                ];
            }

            return [
                'success' => true,
                'data' => [
                    'tenant' => $tenant->toArray(),
                    'kyc_status_label' => $this->getKycStatusLabel($tenant->kyc_status),
                    'status_label' => $this->getStatusLabel($tenant->status),
                    'is_active' => $tenant->status === Tenant::STATUS_ACTIVE,
                    'days_since_move_in' => $tenant->move_in_date ? now()->diffInDays($tenant->move_in_date) : null,
                    'agreement_days_remaining' => $tenant->agreement_end_date ? $tenant->agreement_end_date->diffInDays(now()) : null,
                ],
                'metadata' => [
                    'retrieved_at' => now()->toISOString(),
                    'relationships_loaded' => ['user', 'currentUnit', 'emergencyContacts', 'agreements', 'history']
                ]
            ];
        } catch (\Exception $e) {
            return [
                'success' => false,
                'message' => 'Error retrieving tenant information',
                'error' => $e->getMessage(),
                'tenant_id' => $tenantId
            ];
        }
    }

    /**
     * List all tenants with advanced filtering and pagination
     */
    #[McpTool(name: 'list_tenants', description: 'List all tenants with advanced filtering, search, and pagination')]
    public function listTenants(
        ?string $status = null,
        ?string $kycStatus = null,
        ?int $unitId = null,
        ?string $search = null,
        string $sortBy = 'created_at',
        string $sortOrder = 'desc',
        int $page = 1,
        int $perPage = 15
    ): array {
        try {
            $query = Tenant::with(['user:id,name,email', 'currentUnit:id,unit_number,block'])
                ->whereNotNull('user_id');

            // Apply filters
            if ($status) $query->where('status', $status);
            if ($kycStatus) $query->where('kyc_status', $kycStatus);
            if ($unitId) $query->where('current_unit_id', $unitId);

            // Search functionality
            if ($search) {
                $query->where(function ($q) use ($search) {
                    $q->whereHas('user', function($userQuery) use ($search) {
                        $userQuery->where('name', 'like', "%{$search}%")
                                 ->orWhere('email', 'like', "%{$search}%")
                                 ->orWhere('phone', 'like', "%{$search}%");
                    })
                    ->orWhere('tenant_id', 'like', "%{$search}%")
                    ->orWhere('occupation', 'like', "%{$search}%")
                    ->orWhere('company', 'like', "%{$search}%");
                });
            }

            // Sorting
            if ($sortBy === 'name') {
                $query->join('users', 'tenants.user_id', '=', 'users.id')
                      ->orderBy('users.name', $sortOrder)
                      ->select('tenants.*');
            } else {
                $query->orderBy($sortBy, $sortOrder);
            }

            // Pagination
            $tenants = $query->paginate($perPage, ['*'], 'page', $page);

            return [
                'success' => true,
                'data' => $tenants->items(),
                'pagination' => [
                    'current_page' => $tenants->currentPage(),
                    'last_page' => $tenants->lastPage(),
                    'per_page' => $tenants->perPage(),
                    'total' => $tenants->total(),
                    'from' => $tenants->firstItem(),
                    'to' => $tenants->lastItem(),
                ],
                'filters_applied' => array_filter([
                    'status' => $status,
                    'kyc_status' => $kycStatus,
                    'unit_id' => $unitId,
                    'search' => $search,
                ]),
                'sorting' => [
                    'sort_by' => $sortBy,
                    'sort_order' => $sortOrder,
                ],
                'metadata' => [
                    'available_statuses' => [
                        Tenant::STATUS_ACTIVE,
                        Tenant::STATUS_INACTIVE,
                        Tenant::STATUS_TERMINATED,
                        Tenant::STATUS_NOTICE_PERIOD
                    ],
                    'available_kyc_statuses' => [
                        Tenant::KYC_PENDING,
                        Tenant::KYC_SUBMITTED,
                        Tenant::KYC_VERIFIED,
                        Tenant::KYC_REJECTED
                    ],
                    'retrieved_at' => now()->toISOString(),
                ]
            ];
        } catch (\Exception $e) {
            return [
                'success' => false,
                'message' => 'Error listing tenants',
                'error' => $e->getMessage()
            ];
        }
    }

    /**
     * Create a new tenant with user account
     */
    #[McpTool(name: 'create_tenant', description: 'Create a new tenant with user account and validation')]
    public function createTenant(array $tenantData): array
    {
        try {
            // Validate input data
            $validator = Validator::make($tenantData, [
                'name' => 'required|string|max:255',
                'email' => 'required|email|unique:users,email',
                'phone' => 'required|string|max:20',
                'password' => 'nullable|string|min:8',
                'date_of_birth' => 'nullable|date',
                'gender' => 'nullable|string|in:male,female,other',
                'occupation' => 'nullable|string|max:255',
                'company' => 'nullable|string|max:255',
                'current_address' => 'nullable|string',
                'permanent_address' => 'nullable|string',
                'id_type' => 'nullable|string|max:100',
                'id_number' => 'nullable|string|max:100',
                'emergency_contact_name' => 'nullable|string|max:255',
                'emergency_contact_phone' => 'nullable|string|max:20',
                'emergency_contact_relation' => 'nullable|string|max:100',
            ]);

            if ($validator->fails()) {
                return [
                    'success' => false,
                    'message' => 'Validation failed',
                    'errors' => $validator->errors()->toArray()
                ];
            }

            DB::beginTransaction();

            // Create user account
            $user = User::create([
                'name' => $tenantData['name'],
                'email' => $tenantData['email'],
                'phone' => $tenantData['phone'] ?? null,
                'password' => Hash::make($tenantData['password'] ?? 'temporary123'),
                'role' => 'tenant',
                'is_active' => true,
            ]);

            // Create tenant record
            $tenant = Tenant::create([
                'user_id' => $user->id,
                'tenant_id' => 'T' . str_pad($user->id, 6, '0', STR_PAD_LEFT),
                'date_of_birth' => $tenantData['date_of_birth'] ?? null,
                'gender' => $tenantData['gender'] ?? null,
                'occupation' => $tenantData['occupation'] ?? null,
                'company' => $tenantData['company'] ?? null,
                'current_address' => $tenantData['current_address'] ?? null,
                'permanent_address' => $tenantData['permanent_address'] ?? null,
                'id_type' => $tenantData['id_type'] ?? null,
                'id_number' => $tenantData['id_number'] ?? null,
                'status' => Tenant::STATUS_ACTIVE,
                'kyc_status' => Tenant::KYC_PENDING,
                'onboarding_completed' => false,
            ]);

            // Create emergency contact if provided
            if (!empty($tenantData['emergency_contact_name'])) {
                EmergencyContact::create([
                    'tenant_id' => $tenant->id,
                    'name' => $tenantData['emergency_contact_name'],
                    'phone' => $tenantData['emergency_contact_phone'],
                    'relationship' => $tenantData['emergency_contact_relation'] ?? 'Other',
                    'is_primary' => true,
                    'is_active' => true,
                ]);
            }

            DB::commit();

            return [
                'success' => true,
                'message' => 'Tenant created successfully',
                'data' => $tenant->load(['user', 'emergencyContacts'])->toArray(),
                'metadata' => [
                    'created_at' => now()->toISOString(),
                    'created_via' => 'MCP Tool',
                    'default_password' => empty($tenantData['password']) ? 'temporary123' : 'custom_provided'
                ]
            ];
        } catch (\Exception $e) {
            DB::rollBack();
            return [
                'success' => false,
                'message' => 'Failed to create tenant',
                'error' => $e->getMessage()
            ];
        }
    }

    /**
     * Update tenant KYC status
     */
    #[McpTool(name: 'update_tenant_kyc', description: 'Update tenant KYC status with validation')]
    public function updateTenantKyc(string $tenantId, string $kycStatus, ?string $reason = null): array
    {
        try {
            $tenant = Tenant::find($tenantId);
            
            if (!$tenant) {
                return [
                    'success' => false,
                    'message' => 'Tenant not found',
                    'tenant_id' => $tenantId
                ];
            }

            $validStatuses = [Tenant::KYC_PENDING, Tenant::KYC_SUBMITTED, Tenant::KYC_VERIFIED, Tenant::KYC_REJECTED];
            
            if (!in_array($kycStatus, $validStatuses)) {
                return [
                    'success' => false,
                    'message' => 'Invalid KYC status',
                    'provided_status' => $kycStatus,
                    'valid_statuses' => $validStatuses
                ];
            }

            $oldStatus = $tenant->kyc_status;
            
            $tenant->update([
                'kyc_status' => $kycStatus,
                'kyc_verified_at' => $kycStatus === Tenant::KYC_VERIFIED ? now() : null,
                'kyc_verified_by' => $kycStatus === Tenant::KYC_VERIFIED ? Auth::id() : null,
                'kyc_rejection_reason' => $kycStatus === Tenant::KYC_REJECTED ? $reason : null,
            ]);

            return [
                'success' => true,
                'message' => 'Tenant KYC status updated successfully',
                'data' => [
                    'tenant' => $tenant->fresh(['user'])->toArray(),
                    'kyc_change' => [
                        'from' => $oldStatus,
                        'to' => $kycStatus,
                        'reason' => $reason,
                        'updated_at' => now()->toISOString(),
                        'updated_by' => Auth::id()
                    ]
                ]
            ];
        } catch (\Exception $e) {
            return [
                'success' => false,
                'message' => 'Error updating tenant KYC status',
                'error' => $e->getMessage(),
                'tenant_id' => $tenantId
            ];
        }
    }

    private function getKycStatusLabel(string $status): string
    {
        return match ($status) {
            Tenant::KYC_PENDING => 'Pending',
            Tenant::KYC_SUBMITTED => 'Submitted',
            Tenant::KYC_VERIFIED => 'Verified',
            Tenant::KYC_REJECTED => 'Rejected',
            default => 'Unknown',
        };
    }

    private function getStatusLabel(string $status): string
    {
        return match ($status) {
            Tenant::STATUS_ACTIVE => 'Active',
            Tenant::STATUS_INACTIVE => 'Inactive',
            Tenant::STATUS_TERMINATED => 'Terminated',
            Tenant::STATUS_NOTICE_PERIOD => 'Notice Period',
            default => 'Unknown',
        };
    }
}
