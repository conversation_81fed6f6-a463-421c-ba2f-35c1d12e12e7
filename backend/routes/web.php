<?php

use Illuminate\Support\Facades\Route;
use Illuminate\Http\Request;
use App\Http\Controllers\McpController;

Route::get('/', function () {
    return view('welcome');
});

// MCP HTTP Routes
Route::prefix('mcp')->group(function () {
    Route::get('tools', [McpController::class, 'tools']);
    Route::post('execute', [McpController::class, 'execute']);
    Route::get('health', [McpController::class, 'health']);
    Route::get('dashboard', [McpController::class, 'dashboard']);
    Route::get('tools/{toolName}', [McpController::class, 'toolInfo']);

    // Direct tool endpoints for automation scripts
    Route::post('{toolName}', function($toolName, Request $request) {
        $controller = new \App\Http\Controllers\McpController();
        return $controller->execute($request, $toolName);
    });
});
