<?php

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Route;
use App\Http\Controllers\UnitController;
use App\Http\Controllers\TenantController;
use App\Http\Controllers\TenantOnboardingController;
use App\Http\Controllers\DocumentController;
use App\Http\Controllers\KycDocumentController;
use App\Http\Controllers\UserController;
use App\Http\Controllers\AuthController;
use App\Http\Controllers\NotificationController;
use App\Http\Controllers\PropertyListingController;
use App\Http\Controllers\Admin\SyncController;
use App\Http\Controllers\McpController;

/*
|--------------------------------------------------------------------------
| API Routes
|--------------------------------------------------------------------------
|
| Here is where you can register API routes for your application. These
| routes are loaded by the RouteServiceProvider and all of them will
| be assigned to the "api" middleware group. Make something great!
|
*/

Route::middleware('auth:sanctum')->get('/user', function (Request $request) {
    return $request->user();
});

/*
|--------------------------------------------------------------------------
| TMS API Routes
|--------------------------------------------------------------------------
|
| Housing Society Tenant Management System API endpoints
|
*/

/*
|--------------------------------------------------------------------------
| Authentication Routes
|--------------------------------------------------------------------------
|
| Public authentication endpoints
|
*/
Route::prefix('v1/auth')->middleware('throttle:auth')->group(function () {
    Route::post('login', [AuthController::class, 'login'])->middleware('throttle:login');
    Route::post('register', [AuthController::class, 'register']);
    Route::post('forgot-password', [AuthController::class, 'forgotPassword'])->middleware('throttle:password-reset');
    Route::post('verify-email', [AuthController::class, 'verifyEmail']);
    
    // Protected auth routes
    Route::middleware('auth:sanctum')->group(function () {
        Route::get('user', [AuthController::class, 'user']);
        Route::post('logout', [AuthController::class, 'logout']);
        Route::post('logout-all', [AuthController::class, 'logoutAll']);
        Route::post('refresh', [AuthController::class, 'refresh']);
        Route::post('change-password', [AuthController::class, 'changePassword']);
    });
});

// Temporary: Units, Tenants, and Agreements routes without authentication for development
Route::prefix('v1')->middleware(['throttle:api'])->group(function () {
    // Unit Management Routes (temporarily without auth for development)
    Route::apiResource('units', UnitController::class);

    // Additional Unit endpoints
    Route::patch('units/{unit}/status', [UnitController::class, 'updateStatus']);
    Route::get('units/{unit}/status-history', [UnitController::class, 'statusHistory']);
    Route::get('units/statistics/overview', [UnitController::class, 'statistics']);
    Route::patch('units/bulk/status', [UnitController::class, 'bulkUpdateStatus']);
    Route::post('units/{unit}/assign-tenant', [UnitController::class, 'assignTenant']);
    Route::post('units/{unit}/unassign-tenant', [UnitController::class, 'unassignTenant']);
    Route::get('units/available/list', [UnitController::class, 'getAvailableUnits']);

    // Tenant Management Routes (temporarily without auth for development)
    Route::apiResource('tenants', TenantController::class);

    // Additional Tenant endpoints
    Route::post('tenants/{tenant}/kyc/submit', [TenantController::class, 'submitKyc']);
    Route::patch('tenants/{tenant}/kyc/verify', [TenantController::class, 'verifyKyc']);
    Route::patch('tenants/{tenant}/terminate', [TenantController::class, 'terminate']);
    Route::patch('tenants/{tenant}/notice-period', [TenantController::class, 'startNoticePeriod']);
    Route::get('tenants/statistics/overview', [TenantController::class, 'statistics']);

    // Agreement Statistics endpoint (must be before resource routes)
    Route::get('agreements/statistics', [\App\Http\Controllers\AgreementController::class, 'statistics']);
    Route::get('agreements/expiring', [\App\Http\Controllers\AgreementController::class, 'expiringAgreements']);

    // Agreement Management Routes (temporarily without auth for development)
    Route::apiResource('agreements', \App\Http\Controllers\AgreementController::class);
    Route::get('agreements/verify-signature/{signatureHash}', [\App\Http\Controllers\AgreementController::class, 'verifySignature']);
    Route::get('agreements/{agreement}/download', [\App\Http\Controllers\AgreementController::class, 'download']);
    Route::get('agreements/{agreement}/pdf', [\App\Http\Controllers\AgreementController::class, 'downloadPdf']);
    Route::post('agreements/{agreement}/send-email', [\App\Http\Controllers\AgreementController::class, 'sendEmail']);
    Route::post('agreements/{agreement}/renew', [\App\Http\Controllers\AgreementController::class, 'renew']);
    Route::post('agreements/{agreement}/extend', [\App\Http\Controllers\AgreementController::class, 'extend']);
    Route::post('agreements/{agreement}/activate', [\App\Http\Controllers\AgreementController::class, 'activate']);
    Route::post('agreements/{agreement}/terminate', [\App\Http\Controllers\AgreementController::class, 'terminate']);
    Route::post('agreements/{agreement}/sign', [\App\Http\Controllers\AgreementController::class, 'sign']);
    Route::get('agreements/{agreement}/signature-status', [\App\Http\Controllers\AgreementController::class, 'getSignatureStatus']);

    // NOC Application Statistics Endpoint (must be before resource routes)
    Route::get('noc/applications/statistics', [\App\Http\Controllers\NocApplicationController::class, 'statistics']);

    // NOC Application Management Routes (temporarily without auth for development)
    Route::apiResource('noc/applications', \App\Http\Controllers\NocApplicationController::class);

    // Property Listing Management Routes (temporarily without auth for development)
    Route::prefix('property-listings')->group(function () {
        Route::get('/', [\App\Http\Controllers\PropertyListingController::class, 'index']);
        Route::post('/', [\App\Http\Controllers\PropertyListingController::class, 'store']);
        Route::get('/{listing}', [\App\Http\Controllers\PropertyListingController::class, 'show']);
        Route::put('/{listing}', [\App\Http\Controllers\PropertyListingController::class, 'update']);
        Route::delete('/{listing}', [\App\Http\Controllers\PropertyListingController::class, 'destroy']);
        Route::post('/{listing}/sync', [\App\Http\Controllers\PropertyListingController::class, 'syncToPortals']);
        Route::post('/{listing}/remove-from-portals', [\App\Http\Controllers\PropertyListingController::class, 'removeFromPortals']);
        Route::get('/{listing}/leads', [\App\Http\Controllers\PropertyListingController::class, 'leads']);
    });

    // Property Listing Analytics Routes (temporarily without auth for development)
    Route::get('property-listings-analytics', [\App\Http\Controllers\PropertyListingController::class, 'analytics']);

    // Available Units for Listing Routes (temporarily without auth for development)
    Route::get('available-units', [\App\Http\Controllers\PropertyListingController::class, 'availableUnits']);

    // Document Management Routes (temporarily without auth for development)
    Route::apiResource('documents', DocumentController::class);
    Route::get('documents/statistics/overview', [DocumentController::class, 'statistics']);
    Route::get('documents/pending-kyc', [DocumentController::class, 'listPendingKycDocuments']);
    Route::get('documents/{document}/download', [DocumentController::class, 'download']);
    Route::patch('documents/{document}/verify', [DocumentController::class, 'verify']);
    Route::post('documents/{document}/versions', [DocumentController::class, 'createVersion']);
    Route::get('documents/by-entity', [DocumentController::class, 'byEntity']);
    Route::patch('documents/bulk/verify', [DocumentController::class, 'bulkVerify']);

    // Billing and Payment Routes (temporarily without auth for development)
    // Billing Rules Routes
    Route::get('billing/rules', [\App\Http\Controllers\BillingController::class, 'listBillingRules']);
    Route::post('billing/rules', [\App\Http\Controllers\BillingController::class, 'createBillingRule']);
    Route::get('billing/rules/{billingRule}', [\App\Http\Controllers\BillingController::class, 'showBillingRule']);
    Route::put('billing/rules/{billingRule}', [\App\Http\Controllers\BillingController::class, 'updateBillingRule']);
    Route::delete('billing/rules/{billingRule}', [\App\Http\Controllers\BillingController::class, 'deleteBillingRule']);

    // Billing Records Routes
    Route::get('billing/records', [\App\Http\Controllers\BillingController::class, 'listBillingRecords']);
    Route::post('billing/records', [\App\Http\Controllers\BillingController::class, 'createBillingRecord']);
    Route::get('billing/records/{recordId}', [\App\Http\Controllers\BillingController::class, 'getBillingRecord']);
    Route::put('billing/records/{recordId}', [\App\Http\Controllers\BillingController::class, 'updateBillingRecord']);

    // Payment CRUD Routes
    Route::get('payments', [\App\Http\Controllers\PaymentController::class, 'index']);
    Route::post('payments', [\App\Http\Controllers\PaymentController::class, 'store']);
    Route::get('payments/{payment}', [\App\Http\Controllers\PaymentController::class, 'show']);
    Route::put('payments/{payment}', [\App\Http\Controllers\PaymentController::class, 'update']);
    Route::delete('payments/{payment}', [\App\Http\Controllers\PaymentController::class, 'destroy']);
    Route::get('payments/statistics/overview', [\App\Http\Controllers\PaymentController::class, 'statistics']);
});

Route::prefix('v1')->middleware(['auth:sanctum', 'throttle:api'])->group(function () {
    // User Management Routes
    Route::apiResource('users', UserController::class)->middleware('role:admin');

    // Additional User endpoints
    Route::patch('users/{user}/status', [UserController::class, 'updateStatus'])->middleware('role:admin');
    Route::patch('users/{user}/verify', [UserController::class, 'verify'])->middleware('role:admin');
    Route::patch('users/{user}/change-password', [UserController::class, 'changePassword'])->middleware('role:admin');
    Route::get('users/statistics/overview', [UserController::class, 'statistics'])->middleware('role:admin');
    Route::patch('users/bulk/status', [UserController::class, 'bulkUpdateStatus'])->middleware('role:admin');
    
    // Tenant Management Routes (moved to non-authenticated section for development)
    Route::get('tenants/agreements/expiring', [TenantController::class, 'expiringAgreements'])->middleware('permission:tenants:read');
    Route::patch('tenants/bulk/status', [TenantController::class, 'bulkUpdateStatus'])->middleware('role:admin');
    
    // Tenant Onboarding Routes
    Route::prefix('onboarding')->middleware('role:tenant')->group(function () {
        Route::get('progress', [TenantOnboardingController::class, 'getProgress']);
        Route::post('personal-details', [TenantOnboardingController::class, 'startOnboarding']);
        Route::post('family-details', [TenantOnboardingController::class, 'saveFamilyDetails']);
        Route::post('employment-details', [TenantOnboardingController::class, 'saveEmploymentDetails']);
        Route::post('references', [TenantOnboardingController::class, 'saveReferences']);
        Route::post('emergency-contacts', [TenantOnboardingController::class, 'saveEmergencyContacts']);
        Route::post('preferences', [TenantOnboardingController::class, 'savePreferences']);
        Route::post('complete', [TenantOnboardingController::class, 'completeOnboarding']);
        Route::get('available-units', [TenantOnboardingController::class, 'getAvailableUnits']);
        Route::post('request-unit', [TenantOnboardingController::class, 'requestUnitAssignment']);
    });
    
    // Admin Onboarding Progress Tracking Routes
    Route::prefix('admin/onboarding')->middleware('role:admin,manager')->group(function () {
        Route::get('dashboard', [TenantOnboardingController::class, 'getAdminDashboard']);
        Route::get('tenants/{tenant}/progress', [TenantOnboardingController::class, 'getTenantProgress']);
        Route::post('reminders', [TenantOnboardingController::class, 'sendReminders']);
    });
    
    // KYC Document Management Routes
    Route::prefix('kyc')->middleware('role:tenant')->group(function () {
        Route::get('requirements', [KycDocumentController::class, 'getRequirements']);
        Route::get('documents', [KycDocumentController::class, 'getDocuments']);
        Route::post('upload', [KycDocumentController::class, 'uploadDocument']);
        Route::delete('documents/{document}', [KycDocumentController::class, 'deleteDocument']);
        Route::post('submit', [KycDocumentController::class, 'submitForReview']);
    });
    
    // Document Management Routes (moved to unauthenticated section for development)
    // Route::apiResource('documents', DocumentController::class)->middleware('permission:documents:read');

    // Additional Document endpoints (moved to unauthenticated section for development)
    // Route::get('documents/pending-kyc', [DocumentController::class, 'listPendingKycDocuments'])->middleware('role:admin,owner');
    // Route::get('documents/{document}/download', [DocumentController::class, 'download'])->middleware('permission:documents:read');
    // Route::patch('documents/{document}/verify', [DocumentController::class, 'verify'])->middleware('role:admin,owner');
    // Route::post('documents/{document}/versions', [DocumentController::class, 'createVersion'])->middleware('permission:documents:create');
    // Route::get('documents/statistics/overview', [DocumentController::class, 'statistics'])->middleware('permission:documents:read');
    // Route::get('documents/by-entity', [DocumentController::class, 'byEntity'])->middleware('permission:documents:read');
    // Route::patch('documents/bulk/verify', [DocumentController::class, 'bulkVerify'])->middleware('role:admin,owner');
    
    // Agreement Template Management Routes
    Route::apiResource('agreements/templates', \App\Http\Controllers\AgreementTemplateController::class)
        ->middleware('permission:agreements:read');

    // Agreement Management Routes (moved to non-authenticated section for development)
    // Route::get('agreements/expiring', [\App\Http\Controllers\AgreementController::class, 'expiringAgreements'])
    //     ->middleware('permission:agreements:read');

    // Route::get('agreements/statistics', [\App\Http\Controllers\AgreementController::class, 'statistics'])
    //     ->middleware('permission:agreements:read');

    // Route::get('agreements/verify-signature/{signatureHash}', [\App\Http\Controllers\AgreementController::class, 'verifySignature']);

    // Agreement Resource Routes (moved to non-authenticated section for development)
    // Route::apiResource('agreements', \App\Http\Controllers\AgreementController::class)
    //     ->middleware('permission:agreements:read');
    // Agreement routes moved to non-authenticated section for development
    // Route::get('agreements/{agreement}/download', [\App\Http\Controllers\AgreementController::class, 'download'])
    //     ->middleware('permission:agreements:read');
    // Route::get('agreements/{agreement}/pdf', [\App\Http\Controllers\AgreementController::class, 'downloadPdf'])
    //     ->middleware('permission:agreements:read');
    // Route::post('agreements/{agreement}/send-email', [\App\Http\Controllers\AgreementController::class, 'sendEmail'])
    //     ->middleware('permission:agreements:read');
    // Route::post('agreements/{agreement}/renew', [\App\Http\Controllers\AgreementController::class, 'renew'])
    //     ->middleware('permission:agreements:create');
    // Route::post('agreements/{agreement}/extend', [\App\Http\Controllers\AgreementController::class, 'extend'])
    //     ->middleware('permission:agreements:update');
    // Route::post('agreements/{agreement}/activate', [\App\Http\Controllers\AgreementController::class, 'activate'])
    //     ->middleware('permission:agreements:update');
    // Route::post('agreements/{agreement}/terminate', [\App\Http\Controllers\AgreementController::class, 'terminate'])
    //     ->middleware('permission:agreements:update');

    // Digital Signature Routes (moved to non-authenticated section for development)
    // Route::post('agreements/{agreement}/sign', [\App\Http\Controllers\AgreementController::class, 'sign'])
    //     ->middleware('permission:agreements:update');

    // Route::get('agreements/{agreement}/signature-status', [\App\Http\Controllers\AgreementController::class, 'getSignatureStatus'])
    //     ->middleware('permission:agreements:read');

    // Signed Agreement Management Routes
    Route::post('agreements/{agreement}/upload-signed', [\App\Http\Controllers\AgreementController::class, 'uploadSignedAgreement'])
        ->middleware('permission:agreements:update');
    Route::get('agreements/{agreement}/download-signed', [\App\Http\Controllers\AgreementController::class, 'downloadSignedAgreement'])
        ->middleware('permission:agreements:read');
    Route::delete('agreements/{agreement}/delete-signed', [\App\Http\Controllers\AgreementController::class, 'deleteSignedAgreement'])
        ->middleware('permission:agreements:update');

    // Digital Signature Workflow Routes
    Route::post('agreements/{agreement}/initiate-digital-signature', [\App\Http\Controllers\AgreementController::class, 'initiateDigitalSignature'])
        ->middleware('permission:agreements:update');
    Route::get('agreements/{agreement}/digital-signature-status', [\App\Http\Controllers\AgreementController::class, 'getDigitalSignatureStatus'])
        ->middleware('permission:agreements:read');
    Route::post('agreements/{agreement}/cancel-digital-signature', [\App\Http\Controllers\AgreementController::class, 'cancelDigitalSignature'])
        ->middleware('permission:agreements:update');
    Route::post('agreements/{agreement}/resend-signature-invitations', [\App\Http\Controllers\AgreementController::class, 'resendSignatureInvitations'])
        ->middleware('permission:agreements:update');
    Route::get('agreements/signature-reminders', [\App\Http\Controllers\AgreementController::class, 'getSignatureReminders'])
        ->middleware('permission:agreements:read');
    Route::post('agreements/process-external-signature/{signatureRequestId}', [\App\Http\Controllers\AgreementController::class, 'processExternalSignature'])
        ->middleware('permission:agreements:update');
    
    // Notification Management Routes
    Route::get('notifications', [NotificationController::class, 'index']);
    Route::patch('notifications/{id}/read', [NotificationController::class, 'markAsRead']);
    Route::patch('notifications/mark-all-read', [NotificationController::class, 'markAllAsRead']);
    Route::get('notifications/statistics', [NotificationController::class, 'statistics']);
    Route::post('notifications/test', [NotificationController::class, 'testNotification']);
    
    // Notification Preferences Routes
    Route::prefix('notification-preferences')->group(function () {
        Route::get('/', [\App\Http\Controllers\NotificationPreferenceController::class, 'index']);
        Route::post('/', [\App\Http\Controllers\NotificationPreferenceController::class, 'store']);
        Route::put('{preference}', [\App\Http\Controllers\NotificationPreferenceController::class, 'update']);
        Route::delete('{preference}', [\App\Http\Controllers\NotificationPreferenceController::class, 'destroy']);
        Route::patch('bulk-update', [\App\Http\Controllers\NotificationPreferenceController::class, 'bulkUpdate']);
        Route::get('options', [\App\Http\Controllers\NotificationPreferenceController::class, 'getOptions']);
        Route::get('defaults', [\App\Http\Controllers\NotificationPreferenceController::class, 'getDefaults']);
        Route::post('reset-to-defaults', [\App\Http\Controllers\NotificationPreferenceController::class, 'resetToDefaults']);
        Route::get('statistics', [\App\Http\Controllers\NotificationPreferenceController::class, 'statistics']);
    });
    
    // Notification Templates Routes
    Route::prefix('notification-templates')->middleware('role:admin,manager')->group(function () {
        Route::get('/', [\App\Http\Controllers\NotificationTemplateController::class, 'index']);
        Route::post('/', [\App\Http\Controllers\NotificationTemplateController::class, 'store']);
        Route::get('{template}', [\App\Http\Controllers\NotificationTemplateController::class, 'show']);
        Route::put('{template}', [\App\Http\Controllers\NotificationTemplateController::class, 'update']);
        Route::delete('{template}', [\App\Http\Controllers\NotificationTemplateController::class, 'destroy']);
        Route::post('{template}/preview', [\App\Http\Controllers\NotificationTemplateController::class, 'preview']);
        Route::get('options', [\App\Http\Controllers\NotificationTemplateController::class, 'getOptions']);
        Route::get('category/{category}', [\App\Http\Controllers\NotificationTemplateController::class, 'getByCategory']);
        Route::get('type/{type}', [\App\Http\Controllers\NotificationTemplateController::class, 'getByType']);
        Route::post('{template}/duplicate', [\App\Http\Controllers\NotificationTemplateController::class, 'duplicate']);
        Route::get('statistics', [\App\Http\Controllers\NotificationTemplateController::class, 'statistics']);
    });
    
    // NOC Template Management Routes (moved to authenticated section)
    // Route::get('noc/types', [\App\Http\Controllers\NocTemplateController::class, 'getTypes']);
    // Route::get('noc/form-config', [\App\Http\Controllers\NocTemplateController::class, 'getFormConfig']);
    // Route::apiResource('noc/templates', \App\Http\Controllers\NocTemplateController::class)
    //     ->middleware('role:admin,manager');
    // Route::get('noc/templates/{template}/preview', [\App\Http\Controllers\NocTemplateController::class, 'preview'])
    //     ->middleware('role:admin,manager');

    // NOC Application Management Routes (moved to unauthenticated section for development)

    // NOC Document Management Routes (commented out for development)
    // Route::get('noc/documents/requirements', [\App\Http\Controllers\NocDocumentController::class, 'getRequirements']);
    // Route::post('noc/documents/upload', [\App\Http\Controllers\NocDocumentController::class, 'uploadDocument'])
    //     ->middleware('role:admin,manager,owner,tenant');
    // Route::get('noc/applications/{application}/documents', [\App\Http\Controllers\NocDocumentController::class, 'getDocuments'])
    //     ->middleware('role:admin,manager,owner,tenant');
    // Route::delete('noc/applications/{application}/documents/{document}', [\App\Http\Controllers\NocDocumentController::class, 'deleteDocument'])
    //     ->middleware('role:admin,manager,owner,tenant');

    // NOC Application Status Transition Routes (commented out for development)
    // Route::post('noc/applications/{id}/submit', [\App\Http\Controllers\NocApplicationController::class, 'submit'])
    //     ->middleware('role:tenant,owner');
    // Route::post('noc/applications/{id}/review', [\App\Http\Controllers\NocApplicationController::class, 'review'])
    //     ->middleware('role:admin,manager');
    // Route::post('noc/applications/{id}/approve', [\App\Http\Controllers\NocApplicationController::class, 'approve'])
    //     ->middleware('role:admin,manager');
    // Route::post('noc/applications/{id}/reject', [\App\Http\Controllers\NocApplicationController::class, 'reject'])
    //     ->middleware('role:admin,manager');
    // Route::post('noc/applications/{id}/cancel', [\App\Http\Controllers\NocApplicationController::class, 'cancel'])
    //     ->middleware('role:tenant,owner');

    // NOC Application Status History Endpoint (commented out for development)
    // Route::get('noc/applications/{id}/status-history', [\App\Http\Controllers\NocApplicationController::class, 'statusHistory'])
    //     ->middleware('role:admin,manager,owner,tenant');
    
    // Enquiry Management Routes
    Route::get('enquiries/available-units', [\App\Http\Controllers\EnquiryController::class, 'availableUnits'])
        ->middleware('role:admin,manager,owner,tenant');
    Route::get('enquiries/statistics/overview', [\App\Http\Controllers\EnquiryController::class, 'statistics'])
        ->middleware('role:admin,manager,owner');
    
    Route::apiResource('enquiries', \App\Http\Controllers\EnquiryController::class)
        ->middleware('role:admin,manager,owner,tenant');
    
    // Additional Enquiry endpoints
    Route::post('enquiries/{id}/contacted', [\App\Http\Controllers\EnquiryController::class, 'markAsContacted'])
        ->middleware('role:admin,manager,owner');
    Route::post('enquiries/{id}/interested', [\App\Http\Controllers\EnquiryController::class, 'markAsInterested'])
        ->middleware('role:admin,manager,owner');
    Route::post('enquiries/{id}/not-interested', [\App\Http\Controllers\EnquiryController::class, 'markAsNotInterested'])
        ->middleware('role:admin,manager,owner');
    Route::post('enquiries/{id}/close', [\App\Http\Controllers\EnquiryController::class, 'close'])
        ->middleware('role:admin,manager,owner');
    Route::post('enquiries/{id}/communication', [\App\Http\Controllers\EnquiryController::class, 'addCommunication'])
        ->middleware('role:admin,manager,owner,tenant');
    
    // Emergency Contact Management Routes
    Route::prefix('emergency-contacts')->group(function () {
        Route::get('relationships', [\App\Http\Controllers\EmergencyContactController::class, 'relationships']);
        Route::get('statistics', [\App\Http\Controllers\EmergencyContactController::class, 'statistics'])
            ->middleware('role:admin,manager,owner,tenant');
        
        Route::get('/', [\App\Http\Controllers\EmergencyContactController::class, 'index'])
            ->middleware('role:admin,manager,owner,tenant');
        Route::post('/', [\App\Http\Controllers\EmergencyContactController::class, 'store'])
            ->middleware('role:admin,manager,owner,tenant');
        Route::get('{id}', [\App\Http\Controllers\EmergencyContactController::class, 'show'])
            ->middleware('role:admin,manager,owner,tenant');
        Route::put('{id}', [\App\Http\Controllers\EmergencyContactController::class, 'update'])
            ->middleware('role:admin,manager,owner,tenant');
        Route::delete('{id}', [\App\Http\Controllers\EmergencyContactController::class, 'destroy'])
            ->middleware('role:admin,manager,owner,tenant');
        Route::patch('{id}/priority', [\App\Http\Controllers\EmergencyContactController::class, 'updatePriority'])
            ->middleware('role:admin,manager,owner,tenant');
        Route::patch('{id}/toggle-status', [\App\Http\Controllers\EmergencyContactController::class, 'toggleStatus'])
            ->middleware('role:admin,manager,owner,tenant');
    });

    // Tenant-specific Emergency Contact Routes
    Route::prefix('tenants/{tenantId}/emergency-contacts')->group(function () {
        Route::get('/', [\App\Http\Controllers\EmergencyContactController::class, 'index'])
            ->middleware('role:admin,manager,owner');
        Route::post('/', [\App\Http\Controllers\EmergencyContactController::class, 'store'])
            ->middleware('role:admin,manager,owner');
    });
    
    // Tenant History Management Routes
    Route::prefix('tenants/{tenant}/history')->group(function () {
        Route::get('/', [\App\Http\Controllers\TenantHistoryController::class, 'getTenantHistory'])
            ->middleware('role:admin,manager,owner,tenant');
        Route::get('timeline', [\App\Http\Controllers\TenantHistoryController::class, 'getTenantTimeline'])
            ->middleware('role:admin,manager,owner,tenant');
        Route::get('statistics', [\App\Http\Controllers\TenantHistoryController::class, 'getTenantStatistics'])
            ->middleware('role:admin,manager,owner,tenant');
    });
    
    // System-wide History Management Routes
    Route::prefix('history')->group(function () {
        Route::get('activity-types', [\App\Http\Controllers\TenantHistoryController::class, 'getActivityTypes'])
            ->middleware('role:admin,manager');
        Route::get('system-statistics', [\App\Http\Controllers\TenantHistoryController::class, 'getSystemStatistics'])
            ->middleware('role:admin,manager');
    });
    
    // Tenant Profile Management Routes
    Route::prefix('tenant/profile')->middleware('role:tenant')->group(function () {
        Route::get('/', [\App\Http\Controllers\TenantProfileController::class, 'getProfile']);
        Route::patch('personal-info', [\App\Http\Controllers\TenantProfileController::class, 'updatePersonalInfo']);
        Route::patch('family-details', [\App\Http\Controllers\TenantProfileController::class, 'updateFamilyDetails']);
        Route::patch('preferences', [\App\Http\Controllers\TenantProfileController::class, 'updatePreferences']);
        Route::patch('references', [\App\Http\Controllers\TenantProfileController::class, 'updateReferences']);
        Route::get('completion', [\App\Http\Controllers\TenantProfileController::class, 'getCompletionStatus']);
    });
    
    // Public Profile Management Routes (accessible by admins and tenants)
    Route::prefix('profile')->middleware('role:admin,manager,tenant')->group(function () {
        Route::get('requirements', [\App\Http\Controllers\TenantProfileController::class, 'getRequirements']);
    });
    
    // Admin Profile Management Routes
    Route::prefix('profiles')->middleware('role:admin,manager')->group(function () {
        Route::get('statistics', [\App\Http\Controllers\TenantProfileController::class, 'getProfileStatistics']);
    });
    
    // Future endpoints for other models
    // Route::apiResource('agreements', AgreementController::class);
    // Route::apiResource('noc-requests', NOCController::class);
    // Route::apiResource('enquiries', EnquiryController::class);

    // Billing Calculation Routes (legacy external service routes)
    Route::get('billing/rules/by-type', [\App\Http\Controllers\BillingController::class, 'getBillingRules']);
    Route::get('units/{unit}/charges', [\App\Http\Controllers\BillingController::class, 'calculateUnitCharges']);
    Route::get('units/{unit}/all-charges', [\App\Http\Controllers\BillingController::class, 'getAllCharges']);
    Route::get('noc-applications/{nocApplication}/charges', [\App\Http\Controllers\BillingController::class, 'calculateNocCharges']);
    Route::get('units/{unit}/billing-history', [\App\Http\Controllers\BillingController::class, 'getBillingHistory']);
    Route::get('billing/statistics', [\App\Http\Controllers\BillingController::class, 'getBillingStatistics']);
    Route::get('billing/service-status', [\App\Http\Controllers\BillingController::class, 'getServiceStatus']);
    Route::post('billing/test-connection', [\App\Http\Controllers\BillingController::class, 'testConnection']);

    // Unit and Tenant Payment Routes
    Route::get('units/{unit}/payments', [\App\Http\Controllers\PaymentController::class, 'getUnitPayments'])
        ->middleware('permission:payments:read');
    Route::get('units/{unit}/payments/statistics', [\App\Http\Controllers\PaymentController::class, 'getUnitPaymentStats'])
        ->middleware('permission:payments:read');
    Route::get('tenants/{tenant}/payments', [\App\Http\Controllers\PaymentController::class, 'getTenantPayments'])
        ->middleware('permission:payments:read');
    Route::get('tenants/{tenant}/payments/statistics', [\App\Http\Controllers\PaymentController::class, 'getTenantPaymentStats'])
        ->middleware('permission:payments:read');

    // Receipt Management Routes
    Route::prefix('receipts')->middleware('permission:receipts:read')->group(function () {
        Route::get('/', [\App\Http\Controllers\ReceiptController::class, 'listReceipts']);
        Route::get('statistics', [\App\Http\Controllers\ReceiptController::class, 'getReceiptStats']);
        Route::get('analytics', [\App\Http\Controllers\ReceiptController::class, 'getReceiptAnalytics']);
        Route::get('{receipt}', [\App\Http\Controllers\ReceiptController::class, 'getReceipt']);
        Route::get('{receipt}/download', [\App\Http\Controllers\ReceiptController::class, 'downloadReceipt']);
        Route::post('{receipt}/regenerate', [\App\Http\Controllers\ReceiptController::class, 'regenerateReceipt'])
            ->middleware('permission:receipts:update');
        Route::post('{receipt}/deliver', [\App\Http\Controllers\ReceiptController::class, 'deliverReceipt'])
            ->middleware('permission:receipts:update');
        Route::delete('{receipt}', [\App\Http\Controllers\ReceiptController::class, 'deleteReceipt'])
            ->middleware('permission:receipts:delete');
    });

    // Receipt Generation Routes
    Route::post('payments/{payment}/receipts', [\App\Http\Controllers\ReceiptController::class, 'generateReceipt'])
        ->middleware('permission:receipts:create');

    // Public Receipt Verification Route (no auth required)
    Route::get('receipts/verify/{code}', [\App\Http\Controllers\ReceiptController::class, 'verifyReceipt'])
        ->name('receipts.verify');

    // Reports and Analytics routes moved outside auth group for testing

    // Renewal Reminder Routes
    Route::get('/agreements/renewal/statistics', [\App\Http\Controllers\AgreementController::class, 'getRenewalStatistics']);
    Route::get('/agreements/renewal/expiring', [\App\Http\Controllers\AgreementController::class, 'getExpiringAgreements']);
    Route::get('/agreements/renewal/needing-reminders', [\App\Http\Controllers\AgreementController::class, 'getAgreementsNeedingReminders']);
    Route::post('/agreements/renewal/send-reminders', [\App\Http\Controllers\AgreementController::class, 'sendRenewalReminders']);
    Route::post('/agreements/renewal/reset-flags', [\App\Http\Controllers\AgreementController::class, 'resetReminderFlags']);

    // Audit Log Routes
    Route::get('/agreements/{agreementId}/audit-logs', [\App\Http\Controllers\AgreementController::class, 'getAuditLogs']);
    Route::get('/agreements/audit/statistics', [\App\Http\Controllers\AgreementController::class, 'getAuditStatistics']);
    Route::get('/agreements/{agreementId}/audit/statistics', [\App\Http\Controllers\AgreementController::class, 'getAuditStatistics']);
    Route::get('/agreements/audit/recent-activity', [\App\Http\Controllers\AgreementController::class, 'getRecentAuditActivity']);
    Route::get('/agreements/dashboard/status', [\App\Http\Controllers\AgreementController::class, 'getStatusDashboard']);

    /*
    |--------------------------------------------------------------------------
    | Property Listing Routes (Task 13)
    |--------------------------------------------------------------------------
    |
    | Routes for managing property listings and portal integrations
    |
    */
    
    // Property Listing Management Routes (moved to unauthenticated section for development)
    // Route::prefix('property-listings')->group(function () {
    //     Route::get('/', [\App\Http\Controllers\PropertyListingController::class, 'index'])
    //         ->middleware('permission:listings:read');
    //     Route::post('/', [\App\Http\Controllers\PropertyListingController::class, 'store'])
    //         ->middleware('permission:listings:create');
    //     Route::get('/{listing}', [\App\Http\Controllers\PropertyListingController::class, 'show'])
    //         ->middleware('permission:listings:read');
    //     Route::put('/{listing}', [\App\Http\Controllers\PropertyListingController::class, 'update'])
    //         ->middleware('permission:listings:update');
    //     Route::delete('/{listing}', [\App\Http\Controllers\PropertyListingController::class, 'destroy'])
    //         ->middleware('permission:listings:delete');
    //
    //     // Portal Sync Routes
    //     Route::post('/{listing}/sync', [\App\Http\Controllers\PropertyListingController::class, 'syncToPortals'])
    //         ->middleware('permission:listings:sync');
    //     Route::post('/{listing}/remove-from-portals', [\App\Http\Controllers\PropertyListingController::class, 'removeFromPortals'])
    //         ->middleware('permission:listings:sync');
    //     Route::post('/bulk-sync', [\App\Http\Controllers\PropertyListingController::class, 'bulkSync'])
    //         ->middleware('permission:listings:sync');
    //
    //     // Lead Management Routes
    //     Route::get('/{listing}/leads', [\App\Http\Controllers\PropertyListingController::class, 'leads'])
    //         ->middleware('permission:leads:read');
    //     Route::put('/leads/{lead}/status', [\App\Http\Controllers\PropertyListingController::class, 'updateLeadStatus'])
    //         ->middleware('permission:leads:update');

    //     // Property Listing Leads Routes
    //     Route::get('/{listing}/leads', [\App\Http\Controllers\PropertyListingController::class, 'leads'])
    //         ->middleware('permission:listings:read');
    // });

    // // Property Listing Analytics Routes
    // Route::get('/property-listings-analytics', [\App\Http\Controllers\PropertyListingController::class, 'analytics'])
    //     ->middleware('permission:listings:read');

    // // Available Units for Listing Routes
    // Route::get('/available-units', [\App\Http\Controllers\PropertyListingController::class, 'availableUnits'])
    //     ->middleware('permission:units:read');

    // Active Portals Routes
    Route::get('/listing-portals', [\App\Http\Controllers\PropertyListingController::class, 'portals'])
        ->middleware('permission:listings:read');
});

/*
|--------------------------------------------------------------------------
| Temporary Unauthenticated Routes for Testing
|--------------------------------------------------------------------------
|
| These routes are temporarily without authentication for development/testing
|
*/
Route::prefix('v1')->middleware(['throttle:api'])->group(function () {
    // Reports Management Routes (temporarily without auth for testing)
    Route::prefix('reports')->group(function () {
        Route::get('dashboard', [\App\Http\Controllers\ReportController::class, 'dashboard']);
        Route::get('statistics', [\App\Http\Controllers\ReportController::class, 'getReportStatistics']);
        Route::get('financial', [\App\Http\Controllers\ReportController::class, 'financialReports']);
        Route::get('occupancy', [\App\Http\Controllers\ReportController::class, 'occupancyReports']);
        Route::get('tenant', [\App\Http\Controllers\ReportController::class, 'tenantReports']);
        Route::post('export', [\App\Http\Controllers\ReportController::class, 'exportReport']);
    });

    // Financial Analytics Routes (temporarily without auth for testing)
    Route::prefix('analytics')->group(function () {
        Route::get('metrics', [\App\Http\Controllers\FinancialAnalyticsController::class, 'getMetrics']);
        Route::get('revenue', [\App\Http\Controllers\FinancialAnalyticsController::class, 'getRevenueAnalytics']);
        Route::get('payment-methods', [\App\Http\Controllers\FinancialAnalyticsController::class, 'getPaymentMethods']);
        Route::get('outstanding-dues', [\App\Http\Controllers\FinancialAnalyticsController::class, 'getOutstandingDues']);
    });
});

/*
|--------------------------------------------------------------------------
| Public Property Listing Routes
|--------------------------------------------------------------------------
|
| Public routes for portal webhooks (no authentication required)
|
*/
Route::prefix('v1/public')->group(function () {
    // Portal Webhook Routes
    Route::post('/webhooks/portals/{portalName}', [\App\Http\Controllers\PropertyListingController::class, 'webhook'])
        ->middleware('throttle:webhooks');
});

/*
|--------------------------------------------------------------------------
| Sync Management Routes (Admin Only)
|--------------------------------------------------------------------------
|
| Real-time sync management endpoints for admin dashboard
|
*/
Route::prefix('v1/admin/sync')->middleware(['auth:sanctum', 'role:admin'])->group(function () {
    // Dashboard and statistics
    Route::get('dashboard', [SyncController::class, 'dashboard']);
    Route::get('config', [SyncController::class, 'config']);

    // Sync audit logs
    Route::get('logs', [SyncController::class, 'logs']);
    Route::get('logs/{id}', [SyncController::class, 'logDetails']);

    // Retry operations
    Route::post('retry-failed', [SyncController::class, 'retryFailed']);
    Route::post('logs/{id}/retry', [SyncController::class, 'retryLog']);
});

/*
|--------------------------------------------------------------------------
| MCP Routes
|--------------------------------------------------------------------------
|
| MCP routes for automation scripts and AI agents.
| These routes are available without CSRF protection.
|
*/

// MCP HTTP Routes (without CSRF protection)
Route::prefix('mcp')->group(function () {
    Route::get('tools', [McpController::class, 'tools']);
    Route::post('execute', [McpController::class, 'execute']);
    Route::get('health', [McpController::class, 'health']);
    Route::get('dashboard', [McpController::class, 'dashboard']);
    Route::get('tools/{toolName}', [McpController::class, 'toolInfo']);

    // Direct tool endpoints for automation scripts
    Route::post('{toolName}', function($toolName, Request $request) {
        $controller = new McpController();
        return $controller->execute($request, $toolName);
    });
});