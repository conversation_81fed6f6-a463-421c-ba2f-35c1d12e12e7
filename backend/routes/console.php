<?php

use Illuminate\Foundation\Inspiring;
use Illuminate\Support\Facades\Artisan;
use Illuminate\Support\Facades\Schedule;

Artisan::command('inspire', function () {
    $this->comment(Inspiring::quote());
})->purpose('Display an inspiring quote');

// Schedule the ExpireAgreements command to run daily at 2 AM
Schedule::command('agreements:expire')
    ->dailyAt('02:00')
    ->name('expire-agreements')
    ->description('Automatically expire agreements that have passed their end date')
    ->withoutOverlapping()
    ->onOneServer();

// Schedule the PaymentsSync command to run daily at 3 AM
Schedule::command('payments:sync')
    ->dailyAt('03:00')
    ->name('sync-payments')
    ->description('Sync rent payments from external OneApp service')
    ->withoutOverlapping()
    ->onOneServer();

// Schedule the Receipt Generation command to run daily at 4 AM
Schedule::command('receipts:schedule')
    ->dailyAt('04:00')
    ->name('generate-receipts')
    ->description('Automatically generate receipts for completed payments')
    ->withoutOverlapping()
    ->onOneServer();

// Property Listing Commands (Task 13)
// Schedule property listing sync to run daily at 5 AM
Schedule::command('listings:sync --active-only')
    ->dailyAt('05:00')
    ->name('sync-property-listings')
    ->description('Sync active property listings to external portals')
    ->withoutOverlapping()
    ->onOneServer();

// Schedule lead processing to run every hour
Schedule::command('leads:process --auto-qualify --send-notifications')
    ->hourly()
    ->name('process-property-leads')
    ->description('Process property leads and send notifications')
    ->withoutOverlapping()
    ->onOneServer();

// Schedule old lead cleanup to run weekly on Sunday at 6 AM
Schedule::command('leads:process --cleanup-old')
    ->weeklyOn(0, '06:00')
    ->name('cleanup-old-leads')
    ->description('Clean up old rejected/spam leads')
    ->withoutOverlapping()
    ->onOneServer();
