<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('users', function (Blueprint $table) {
            // Role and user type
            $table->enum('role', ['admin', 'owner', 'tenant', 'staff'])->default('tenant');
            $table->enum('user_type', ['society_admin', 'owner', 'tenant', 'staff'])->default('tenant');
            
            // Personal information
            $table->string('first_name')->nullable();
            $table->string('last_name')->nullable();
            $table->string('phone', 15)->nullable();
            $table->string('alternate_phone', 15)->nullable();
            $table->date('date_of_birth')->nullable();
            $table->enum('gender', ['male', 'female', 'other'])->nullable();
            
            // Address information
            $table->text('address')->nullable();
            $table->string('city', 100)->nullable();
            $table->string('state', 100)->nullable();
            $table->string('country', 100)->default('India');
            $table->string('pincode', 10)->nullable();
            
            // Emergency contact
            $table->json('emergency_contact')->nullable(); // {name, phone, relationship}
            
            // Profile and preferences
            $table->string('profile_photo')->nullable();
            $table->json('preferences')->nullable(); // User preferences
            
            // Status and verification
            $table->enum('status', ['active', 'inactive', 'suspended', 'pending_verification'])->default('pending_verification');
            $table->boolean('is_verified')->default(false);
            $table->timestamp('verified_at')->nullable();
            $table->unsignedBigInteger('verified_by')->nullable();
            
            // Permissions and access
            $table->json('permissions')->nullable(); // Custom permissions
            $table->timestamp('last_login_at')->nullable();
            $table->string('last_login_ip')->nullable();
            
            // Soft deletes
            $table->softDeletes();
            
            // Indexes
            $table->index('role');
            $table->index('user_type');
            $table->index('status');
            $table->index('phone');
            $table->index(['first_name', 'last_name']);
            
            // Foreign keys
            $table->foreign('verified_by')->references('id')->on('users')->onDelete('set null');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('users', function (Blueprint $table) {
            $table->dropSoftDeletes();
            $table->dropForeign(['verified_by']);
            $table->dropIndex(['role']);
            $table->dropIndex(['user_type']);
            $table->dropIndex(['status']);
            $table->dropIndex(['phone']);
            $table->dropIndex(['first_name', 'last_name']);
            
            $table->dropColumn([
                'role', 'user_type', 'first_name', 'last_name', 'phone', 'alternate_phone',
                'date_of_birth', 'gender', 'address', 'city', 'state', 'country', 'pincode',
                'emergency_contact', 'profile_photo', 'preferences', 'status', 'is_verified',
                'verified_at', 'verified_by', 'permissions', 'last_login_at', 'last_login_ip'
            ]);
        });
    }
};
