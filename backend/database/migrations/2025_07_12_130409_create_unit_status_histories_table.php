<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('unit_status_histories', function (Blueprint $table) {
            $table->id();
            
            // Unit reference
            $table->foreignId('unit_id')->constrained()->onDelete('cascade');
            
            // Status change details
            $table->enum('previous_status', ['occupied', 'vacant', 'to-let', 'rented'])->nullable();
            $table->enum('new_status', ['occupied', 'vacant', 'to-let', 'rented']);
            
            // Change tracking
            $table->foreignId('changed_by')->constrained('users')->onDelete('cascade');
            $table->text('reason')->nullable();
            $table->timestamp('changed_at');
            
            // Additional context
            $table->string('change_type')->default('manual'); // manual, automatic, system
            $table->json('metadata')->nullable(); // for additional context
            
            $table->timestamps();
            
            // Indexes
            $table->index(['unit_id', 'changed_at']);
            $table->index(['changed_by', 'changed_at']);
            $table->index(['new_status', 'changed_at']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('unit_status_histories');
    }
};
