<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('notification_preferences', function (Blueprint $table) {
            $table->id();
            $table->foreignId('user_id')->constrained()->onDelete('cascade');
            $table->string('notification_type'); // system, tenant, unit, enquiry, noc, etc.
            $table->string('channel'); // email, sms, push, in_app
            $table->boolean('enabled')->default(true);
            $table->json('settings')->nullable(); // Channel-specific settings
            $table->string('frequency')->default('immediate'); // immediate, daily, weekly
            $table->time('quiet_hours_start')->nullable(); // Quiet hours start
            $table->time('quiet_hours_end')->nullable(); // Quiet hours end
            $table->json('metadata')->nullable(); // Additional preferences
            $table->timestamps();
            
            // Unique constraint to prevent duplicate preferences
            $table->unique(['user_id', 'notification_type', 'channel']);
            
            // Indexes for performance
            $table->index(['user_id', 'enabled']);
            $table->index(['notification_type', 'channel']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('notification_preferences');
    }
};
