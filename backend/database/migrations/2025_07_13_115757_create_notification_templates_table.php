<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('notification_templates', function (Blueprint $table) {
            $table->id();
            $table->string('name')->unique();
            $table->string('type'); // email, sms, push, in_app
            $table->string('category'); // system, tenant, unit, enquiry, noc, etc.
            $table->string('title');
            $table->text('content');
            $table->json('variables')->nullable(); // Available variables for template
            $table->json('channels')->nullable(); // Supported channels for this template
            $table->boolean('is_active')->default(true);
            $table->string('version')->default('1.0');
            $table->json('metadata')->nullable(); // Additional template metadata
            $table->timestamps();
            
            // Indexes for performance
            $table->index(['type', 'category']);
            $table->index(['is_active']);
            $table->index(['name']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('notification_templates');
    }
};
