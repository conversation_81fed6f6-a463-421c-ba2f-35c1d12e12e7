<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('noc_templates', function (Blueprint $table) {
            $table->id();
            $table->string('name');
            $table->text('description')->nullable();
            $table->enum('noc_type', ['rental', 'residence', 'vehicle', 'renovation', 'transfer']);
            $table->text('template_content'); // HTML template content
            $table->json('required_fields')->nullable(); // Required form fields
            $table->json('document_requirements')->nullable(); // Required documents
            $table->json('placeholders')->nullable(); // Template placeholders
            $table->boolean('is_active')->default(true);
            $table->unsignedBigInteger('created_by');
            $table->foreign('created_by')->references('id')->on('users')->onDelete('cascade');
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('noc_templates');
    }
}; 