<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('property_listings', function (Blueprint $table) {
            $table->id();
            $table->foreignId('unit_id')->constrained('units')->onDelete('cascade');
            $table->string('listing_title');
            $table->text('description');
            $table->decimal('rent_amount', 10, 2);
            $table->decimal('deposit_amount', 10, 2)->nullable();
            $table->date('available_from');
            $table->enum('listing_status', ['draft', 'pending_approval', 'approved', 'published', 'expired', 'inactive'])->default('draft');
            $table->json('portal_mappings')->nullable(); // Maps to different portal IDs
            $table->json('media_urls')->nullable(); // Array of media URLs
            $table->json('amenities')->nullable(); // Array of amenities
            $table->json('preferences')->nullable(); // Tenant preferences
            $table->foreignId('created_by')->constrained('users')->onDelete('cascade');
            $table->foreignId('approved_by')->nullable()->constrained('users')->onDelete('set null');
            $table->timestamp('approved_at')->nullable();
            $table->text('rejection_reason')->nullable();
            $table->timestamp('published_at')->nullable();
            $table->timestamp('expires_at')->nullable();
            $table->boolean('is_featured')->default(false);
            $table->boolean('is_urgent')->default(false);
            $table->integer('view_count')->default(0);
            $table->integer('inquiry_count')->default(0);
            $table->json('metadata')->nullable(); // Additional portal-specific data
            $table->timestamps();
            $table->softDeletes();

            // Indexes
            $table->index(['unit_id', 'listing_status']);
            $table->index(['listing_status', 'published_at']);
            $table->index(['created_by', 'listing_status']);
            $table->index(['available_from', 'listing_status']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('property_listings');
    }
};
