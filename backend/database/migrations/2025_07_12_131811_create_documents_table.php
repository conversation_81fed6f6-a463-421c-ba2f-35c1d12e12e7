<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('documents', function (Blueprint $table) {
            $table->id();
            
            // Document identification
            $table->string('document_number', 50)->unique();
            $table->string('title', 200);
            $table->text('description')->nullable();
            
            // Document type and category
            $table->enum('type', [
                'id_proof', 'address_proof', 'income_proof', 'photo', 'agreement',
                'noc', 'police_verification', 'background_check', 'reference_letter',
                'bank_statement', 'salary_slip', 'other'
            ]);
            $table->string('category', 50)->nullable(); // Additional categorization
            
            // Related entity (polymorphic)
            $table->unsignedBigInteger('documentable_id');
            $table->string('documentable_type'); // User, Tenant, Unit, etc.
            
            // File information
            $table->string('file_name', 255);
            $table->string('file_path', 500);
            $table->string('file_type', 50); // pdf, jpg, png, etc.
            $table->unsignedBigInteger('file_size'); // in bytes
            $table->string('mime_type', 100);
            $table->string('file_hash', 64)->nullable(); // For duplicate detection
            
            // Document metadata
            $table->json('metadata')->nullable(); // Additional document metadata
            $table->date('document_date')->nullable(); // Date on the document
            $table->date('expiry_date')->nullable(); // For documents with expiry
            $table->string('issuing_authority', 200)->nullable();
            $table->string('document_id_number', 100)->nullable(); // ID number on the document
            
            // Verification and approval
            $table->enum('status', ['pending', 'verified', 'rejected', 'expired'])->default('pending');
            $table->unsignedBigInteger('uploaded_by');
            $table->unsignedBigInteger('verified_by')->nullable();
            $table->timestamp('verified_at')->nullable();
            $table->text('verification_notes')->nullable();
            $table->text('rejection_reason')->nullable();
            
            // Access control
            $table->enum('visibility', ['public', 'private', 'admin_only'])->default('private');
            $table->boolean('is_required')->default(false);
            $table->boolean('is_sensitive')->default(false);
            
            // Version control
            $table->unsignedBigInteger('parent_document_id')->nullable(); // For document versions
            $table->integer('version')->default(1);
            $table->boolean('is_current_version')->default(true);
            
            // Timestamps
            $table->timestamps();
            $table->softDeletes();
            
            // Foreign keys
            $table->foreign('uploaded_by')->references('id')->on('users')->onDelete('cascade');
            $table->foreign('verified_by')->references('id')->on('users')->onDelete('set null');
            $table->foreign('parent_document_id')->references('id')->on('documents')->onDelete('cascade');
            
            // Indexes
            $table->index('document_number');
            $table->index('type');
            $table->index('status');
            $table->index(['documentable_id', 'documentable_type']);
            $table->index('uploaded_by');
            $table->index('verified_by');
            $table->index('expiry_date');
            $table->index('file_hash');
            $table->index(['type', 'status']);
            $table->index(['documentable_type', 'documentable_id', 'type']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('documents');
    }
};
