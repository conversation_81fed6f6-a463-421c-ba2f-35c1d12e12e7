<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('agreements', function (Blueprint $table) {
            $table->unsignedBigInteger('renewed_from')->nullable()->after('id');
            $table->foreign('renewed_from')->references('id')->on('agreements')->onDelete('set null');
            $table->json('history')->nullable()->after('metadata');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('agreements', function (Blueprint $table) {
            $table->dropForeign(['renewed_from']);
            $table->dropColumn(['renewed_from', 'history']);
        });
    }
};
