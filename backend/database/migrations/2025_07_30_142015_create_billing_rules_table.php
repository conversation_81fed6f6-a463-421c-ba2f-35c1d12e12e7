<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('billing_rules', function (Blueprint $table) {
            $table->id();
            $table->string('name');
            $table->enum('type', ['maintenance', 'utilities', 'parking', 'non_occupancy', 'noc_charges']);
            $table->enum('calculation_method', ['per_sqft', 'fixed', 'percentage', 'per_unit']);
            $table->decimal('amount', 10, 2);
            $table->text('description')->nullable();
            $table->boolean('is_active')->default(true);
            $table->enum('applies_to', ['all_units', 'specific_units', 'tenant_type']);
            $table->enum('frequency', ['monthly', 'quarterly', 'yearly', 'one_time']);
            $table->integer('due_day')->default(5);
            $table->decimal('late_fee_amount', 8, 2)->default(0);
            $table->integer('late_fee_days')->default(7);
            $table->foreignId('created_by')->constrained('users')->onDelete('cascade');
            $table->json('metadata')->nullable();
            $table->timestamps();
            $table->softDeletes();

            // Indexes
            $table->index(['type', 'is_active']);
            $table->index(['applies_to', 'is_active']);
            $table->index('frequency');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('billing_rules');
    }
};
