<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('agreements', function (Blueprint $table) {
            $table->string('signed_file_path')->nullable()->after('file_path');
            $table->json('signature_data')->nullable()->after('signed_file_path');
            $table->string('signature_hash')->nullable()->after('signature_data');
            $table->timestamp('signed_at')->nullable()->after('signature_hash');
            $table->boolean('signed_by_owner')->default(false)->after('signed_at');
            $table->boolean('signed_by_tenant')->default(false)->after('signed_by_owner');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('agreements', function (Blueprint $table) {
            $table->dropColumn([
                'signed_file_path',
                'signature_data',
                'signature_hash',
                'signed_at',
                'signed_by_owner',
                'signed_by_tenant'
            ]);
        });
    }
};
