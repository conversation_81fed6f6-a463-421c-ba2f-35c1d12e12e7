<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('agreements', function (Blueprint $table) {
            // Renewal reminder fields
            $table->boolean('renewal_reminder_sent')->default(false)->after('signature_workflow_cancellation_reason');
            $table->timestamp('renewal_reminder_sent_at')->nullable()->after('renewal_reminder_sent');
            $table->json('renewal_reminder_history')->nullable()->after('renewal_reminder_sent_at');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('agreements', function (Blueprint $table) {
            $table->dropColumn([
                'renewal_reminder_sent',
                'renewal_reminder_sent_at',
                'renewal_reminder_history'
            ]);
        });
    }
}; 