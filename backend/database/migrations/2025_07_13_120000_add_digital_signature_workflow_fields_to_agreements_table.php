<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('agreements', function (Blueprint $table) {
            // Digital signature workflow fields
            $table->string('signature_request_id')->nullable()->after('signed_by_tenant');
            $table->timestamp('signature_workflow_started_at')->nullable()->after('signature_request_id');
            $table->timestamp('signature_workflow_completed_at')->nullable()->after('signature_workflow_started_at');
            $table->timestamp('signature_workflow_cancelled_at')->nullable()->after('signature_workflow_completed_at');
            $table->unsignedBigInteger('signature_workflow_cancelled_by')->nullable()->after('signature_workflow_cancelled_at');
            $table->text('signature_workflow_cancellation_reason')->nullable()->after('signature_workflow_cancelled_by');
            
            // Add foreign key for cancelled by
            $table->foreign('signature_workflow_cancelled_by')->references('id')->on('users')->onDelete('set null');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('agreements', function (Blueprint $table) {
            $table->dropForeign(['signature_workflow_cancelled_by']);
            $table->dropColumn([
                'signature_request_id',
                'signature_workflow_started_at',
                'signature_workflow_completed_at',
                'signature_workflow_cancelled_at',
                'signature_workflow_cancelled_by',
                'signature_workflow_cancellation_reason'
            ]);
        });
    }
}; 