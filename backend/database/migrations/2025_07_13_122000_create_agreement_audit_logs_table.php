<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('agreement_audit_logs', function (Blueprint $table) {
            $table->id();
            $table->unsignedBigInteger('agreement_id');
            $table->unsignedBigInteger('user_id')->nullable(); // User who performed the action
            $table->string('action'); // created, updated, signed, renewed, terminated, etc.
            $table->string('entity_type')->default('agreement'); // agreement, signature, renewal, etc.
            $table->string('entity_id')->nullable(); // ID of the related entity if applicable
            $table->json('old_values')->nullable(); // Previous values before change
            $table->json('new_values')->nullable(); // New values after change
            $table->json('metadata')->nullable(); // Additional context information
            $table->string('ip_address')->nullable(); // IP address of the user
            $table->string('user_agent')->nullable(); // User agent string
            $table->timestamp('performed_at'); // When the action was performed
            $table->timestamps();

            $table->foreign('agreement_id')->references('id')->on('agreements')->onDelete('cascade');
            $table->foreign('user_id')->references('id')->on('users')->onDelete('set null');
            
            $table->index(['agreement_id', 'performed_at']);
            $table->index(['user_id', 'performed_at']);
            $table->index(['action', 'performed_at']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('agreement_audit_logs');
    }
}; 