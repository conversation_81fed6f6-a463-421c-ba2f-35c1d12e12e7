<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('noc_applications', function (Blueprint $table) {
            $table->id();
            $table->unsignedBigInteger('applicant_id'); // FK to users/tenants
            $table->unsignedBigInteger('unit_id'); // FK to units
            $table->enum('noc_type', ['rental', 'residence', 'vehicle', 'renovation', 'transfer']);
            $table->text('purpose');
            $table->enum('status', ['draft', 'submitted', 'under_review', 'approved', 'rejected', 'cancelled'])->default('draft');
            $table->json('documents')->nullable(); // Uploaded files
            $table->text('remarks')->nullable(); // Admin/manager notes
            $table->json('history')->nullable(); // Audit trail
            $table->date('start_date')->nullable();
            $table->date('end_date')->nullable();
            $table->timestamps();

            $table->foreign('applicant_id')->references('id')->on('users')->onDelete('cascade');
            $table->foreign('unit_id')->references('id')->on('units')->onDelete('cascade');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('noc_applications');
    }
};
