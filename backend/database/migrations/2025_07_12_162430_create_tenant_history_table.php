<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('tenant_history', function (Blueprint $table) {
            $table->id();
            $table->foreignId('tenant_id')->constrained()->onDelete('cascade');
            $table->foreignId('user_id')->nullable()->constrained()->onDelete('set null'); // Who performed the action
            $table->string('activity_type', 100); // onboarding, document_upload, status_change, profile_update, etc.
            $table->string('activity_description', 500); // Human readable description
            $table->json('metadata')->nullable(); // Additional data about the activity
            $table->json('old_values')->nullable(); // Previous values for updates
            $table->json('new_values')->nullable(); // New values for updates
            $table->string('ip_address', 45)->nullable(); // IP address of the user
            $table->string('user_agent')->nullable(); // Browser/device information
            $table->enum('severity', ['low', 'medium', 'high'])->default('medium'); // Importance level
            $table->timestamp('performed_at'); // When the activity occurred
            $table->timestamps();
            
            // Indexes for performance
            $table->index(['tenant_id', 'performed_at']);
            $table->index(['activity_type', 'performed_at']);
            $table->index(['user_id', 'performed_at']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('tenant_history');
    }
};
