<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('rent_receipts', function (Blueprint $table) {
            $table->id();
            $table->foreignId('payment_id')->constrained('rent_payments')->onDelete('cascade');
            $table->string('receipt_number')->unique();
            $table->timestamp('generated_at');
            $table->string('recipient_email');
            $table->enum('delivery_status', ['pending', 'sent', 'delivered', 'failed'])->default('pending');
            $table->string('pdf_path')->nullable();
            $table->json('metadata')->nullable();
            $table->text('delivery_notes')->nullable();
            $table->timestamp('delivered_at')->nullable();
            $table->string('verification_code')->unique()->nullable();
            $table->string('qr_code_path')->nullable();
            $table->timestamps();
            
            $table->index(['payment_id']);
            $table->index(['receipt_number']);
            $table->index(['delivery_status']);
            $table->index(['generated_at']);
            $table->index(['recipient_email']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('rent_receipts');
    }
};
