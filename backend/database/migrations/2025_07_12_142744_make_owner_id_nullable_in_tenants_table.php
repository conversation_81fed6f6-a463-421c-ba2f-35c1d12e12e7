<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('tenants', function (Blueprint $table) {
            // Drop the foreign key constraint first
            $table->dropForeign(['owner_id']);
            
            // Modify the column to be nullable
            $table->unsignedBigInteger('owner_id')->nullable()->change();
            
            // Re-add the foreign key constraint
            $table->foreign('owner_id')->references('id')->on('users')->onDelete('set null');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('tenants', function (Blueprint $table) {
            // Drop the foreign key constraint
            $table->dropForeign(['owner_id']);
            
            // Make the column not nullable again
            $table->unsignedBigInteger('owner_id')->nullable(false)->change();
            
            // Re-add the foreign key constraint
            $table->foreign('owner_id')->references('id')->on('users')->onDelete('cascade');
        });
    }
};
