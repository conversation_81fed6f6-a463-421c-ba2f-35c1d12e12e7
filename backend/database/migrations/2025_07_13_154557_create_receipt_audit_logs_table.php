<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('receipt_audit_logs', function (Blueprint $table) {
            $table->id();
            $table->foreignId('receipt_id')->constrained('rent_receipts')->onDelete('cascade');
            $table->foreignId('user_id')->nullable()->constrained('users')->onDelete('set null');
            $table->string('event'); // e.g., created, updated, delivered, failed, downloaded, verified
            $table->json('details')->nullable();
            $table->timestamp('created_at')->useCurrent();

            $table->index(['receipt_id']);
            $table->index(['event']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('receipt_audit_logs');
    }
}; 