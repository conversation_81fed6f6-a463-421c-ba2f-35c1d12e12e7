<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('enquiries', function (Blueprint $table) {
            $table->id();
            $table->foreignId('unit_id')->constrained()->onDelete('cascade');
            $table->foreignId('enquirer_id')->constrained('users')->onDelete('cascade');
            $table->foreignId('owner_id')->constrained('users')->onDelete('cascade');
            $table->string('enquiry_type')->default('rental'); // rental, purchase, visit, other
            $table->string('status')->default('new'); // new, contacted, interested, not_interested, closed
            $table->string('priority')->default('medium'); // low, medium, high, urgent
            $table->text('message');
            $table->json('enquirer_details')->nullable(); // Store enquirer contact details
            $table->json('communication_history')->nullable(); // Store communication history
            $table->json('response_data')->nullable(); // Store response details
            $table->timestamp('contacted_at')->nullable();
            $table->timestamp('responded_at')->nullable();
            $table->timestamp('closed_at')->nullable();
            $table->text('notes')->nullable(); // Admin/owner notes
            $table->json('metadata')->nullable(); // Additional data
            $table->timestamps();
            
            // Indexes for better performance
            $table->index(['unit_id', 'status']);
            $table->index(['enquirer_id', 'status']);
            $table->index(['owner_id', 'status']);
            $table->index(['enquiry_type', 'status']);
            $table->index(['priority', 'status']);
            $table->index('created_at');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('enquiries');
    }
};
