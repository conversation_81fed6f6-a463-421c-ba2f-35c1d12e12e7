<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('units', function (Blueprint $table) {
            $table->id();
            
            // Unit identification
            $table->string('unit_number')->unique();
            $table->string('block')->nullable();
            $table->string('floor')->nullable();
            $table->string('wing')->nullable();
            $table->string('type')->default('residential'); // residential, commercial, parking
            
            // Unit specifications
            $table->integer('bedrooms')->nullable();
            $table->integer('bathrooms')->nullable();
            $table->decimal('area_sqft', 8, 2)->nullable();
            $table->decimal('carpet_area', 8, 2)->nullable();
            $table->decimal('built_up_area', 8, 2)->nullable();
            
            // Status management - Four-state system as per PRD
            $table->enum('status', ['occupied', 'vacant', 'to-let', 'rented'])
                  ->default('vacant')
                  ->comment('Unit occupancy status');
            
            // Ownership and tenancy
            $table->foreignId('owner_id')->nullable()->constrained('users')->onDelete('set null');
            $table->foreignId('current_tenant_id')->nullable()->constrained('users')->onDelete('set null');
            $table->boolean('is_sublet')->default(false);
            $table->boolean('is_owner_occupied')->default(false);
            
            // Financial information
            $table->decimal('market_rent', 10, 2)->nullable();
            $table->decimal('security_deposit', 10, 2)->nullable();
            $table->decimal('maintenance_charge', 8, 2)->nullable();
            
            // Status history and tracking
            $table->timestamp('last_status_change')->nullable();
            $table->foreignId('status_changed_by')->nullable()->constrained('users')->onDelete('set null');
            $table->text('status_change_reason')->nullable();
            
            // Availability and rental information
            $table->date('available_from')->nullable();
            $table->text('description')->nullable();
            $table->json('amenities')->nullable(); // parking, balcony, etc.
            $table->json('preferences')->nullable(); // family only, no pets, etc.
            
            // Document and media
            $table->json('photos')->nullable();
            $table->json('documents')->nullable();
            
            // Metadata
            $table->boolean('is_active')->default(true);
            $table->text('notes')->nullable();
            $table->json('metadata')->nullable(); // for extensibility
            
            $table->timestamps();
            
            // Indexes for performance
            $table->index(['status', 'is_active']);
            $table->index(['owner_id', 'status']);
            $table->index(['block', 'floor', 'unit_number']);
            $table->index('last_status_change');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('units');
    }
};
