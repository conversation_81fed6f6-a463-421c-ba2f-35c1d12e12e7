<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('listing_portals', function (Blueprint $table) {
            $table->id();
            $table->string('name'); // Portal name (e.g., 99acres, MagicBricks)
            $table->string('display_name');
            $table->string('slug')->unique(); // URL-friendly name
            $table->text('description')->nullable();
            $table->string('base_url'); // Portal API base URL
            $table->string('api_key')->nullable();
            $table->string('api_secret')->nullable();
            $table->string('username')->nullable();
            $table->string('password')->nullable();
            $table->json('api_config')->nullable(); // Portal-specific API configuration
            $table->json('field_mapping')->nullable(); // Field mapping for portal requirements
            $table->json('media_requirements')->nullable(); // Media requirements per portal
            $table->boolean('is_active')->default(true);
            $table->boolean('requires_approval')->default(false);
            $table->integer('rate_limit_per_hour')->default(100);
            $table->integer('rate_limit_per_day')->default(1000);
            $table->timestamp('last_sync_at')->nullable();
            $table->json('sync_status')->nullable(); // Sync status and error logs
            $table->json('metadata')->nullable(); // Additional portal-specific data
            $table->timestamps();
            $table->softDeletes();

            // Indexes
            $table->index(['slug', 'is_active']);
            $table->index(['is_active', 'last_sync_at']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('listing_portals');
    }
};
