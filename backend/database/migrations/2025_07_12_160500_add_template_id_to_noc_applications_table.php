<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('noc_applications', function (Blueprint $table) {
            $table->unsignedBigInteger('template_id')->nullable()->after('noc_type');
            $table->json('form_data')->nullable()->after('purpose'); // Store form field values
            
            $table->foreign('template_id')->references('id')->on('noc_templates')->onDelete('set null');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('noc_applications', function (Blueprint $table) {
            $table->dropForeign(['template_id']);
            $table->dropColumn(['template_id', 'form_data']);
        });
    }
}; 