<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('property_leads', function (Blueprint $table) {
            $table->id();
            $table->foreignId('listing_id')->constrained('property_listings')->onDelete('cascade');
            $table->foreignId('portal_id')->constrained('listing_portals')->onDelete('cascade');
            $table->string('portal_lead_id')->nullable(); // Lead ID from the portal
            $table->string('enquirer_name');
            $table->string('enquirer_email');
            $table->string('enquirer_phone')->nullable();
            $table->text('message')->nullable();
            $table->enum('lead_status', ['new', 'contacted', 'interested', 'not_interested', 'converted', 'lost'])->default('new');
            $table->enum('priority', ['low', 'medium', 'high', 'urgent'])->default('medium');
            $table->json('enquirer_details')->nullable(); // Additional enquirer information
            $table->json('communication_history')->nullable(); // Communication history
            $table->json('response_data')->nullable(); // Response tracking data
            $table->timestamp('contacted_at')->nullable();
            $table->timestamp('responded_at')->nullable();
            $table->timestamp('converted_at')->nullable();
            $table->foreignId('assigned_to')->nullable()->constrained('users')->onDelete('set null');
            $table->text('notes')->nullable();
            $table->json('metadata')->nullable(); // Additional portal-specific data
            $table->timestamps();
            $table->softDeletes();

            // Indexes
            $table->index(['listing_id', 'lead_status']);
            $table->index(['portal_id', 'portal_lead_id']);
            $table->index(['lead_status', 'created_at']);
            $table->index(['assigned_to', 'lead_status']);
            $table->index(['enquirer_email', 'listing_id']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('property_leads');
    }
};
