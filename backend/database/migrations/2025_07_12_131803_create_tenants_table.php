<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('tenants', function (Blueprint $table) {
            $table->id();
            
            // Basic tenant information
            $table->unsignedBigInteger('user_id');
            $table->string('tenant_code', 20)->unique();
            $table->unsignedBigInteger('unit_id');
            $table->unsignedBigInteger('owner_id');
            
            // Tenant details
            $table->string('occupation', 100)->nullable();
            $table->string('company_name', 200)->nullable();
            $table->string('company_address')->nullable();
            $table->integer('family_members')->default(1);
            $table->json('family_details')->nullable(); // Array of family member details
            
            // Financial information
            $table->decimal('monthly_income', 10, 2)->nullable();
            $table->decimal('security_deposit', 10, 2)->nullable();
            $table->decimal('advance_amount', 10, 2)->nullable();
            
            // Agreement details
            $table->date('move_in_date')->nullable();
            $table->date('move_out_date')->nullable();
            $table->date('agreement_start_date')->nullable();
            $table->date('agreement_end_date')->nullable();
            $table->string('agreement_type', 50)->default('leave_and_license');
            
            // KYC and verification
            $table->enum('kyc_status', ['pending', 'submitted', 'verified', 'rejected'])->default('pending');
            $table->json('kyc_documents')->nullable(); // Document IDs and status
            $table->timestamp('kyc_submitted_at')->nullable();
            $table->timestamp('kyc_verified_at')->nullable();
            $table->unsignedBigInteger('kyc_verified_by')->nullable();
            $table->text('kyc_rejection_reason')->nullable();
            
            // Verification details
            $table->boolean('background_check_done')->default(false);
            $table->enum('background_check_status', ['pending', 'passed', 'failed'])->nullable();
            $table->timestamp('background_check_date')->nullable();
            $table->text('background_check_notes')->nullable();
            
            // Police verification
            $table->boolean('police_verification_required')->default(false);
            $table->enum('police_verification_status', ['pending', 'submitted', 'verified', 'rejected'])->nullable();
            $table->timestamp('police_verification_date')->nullable();
            $table->text('police_verification_notes')->nullable();
            
            // References
            $table->json('references')->nullable(); // Array of references with contact details
            
            // Tenant status and behavior
            $table->enum('status', ['active', 'inactive', 'terminated', 'notice_period'])->default('active');
            $table->enum('behavior_rating', ['excellent', 'good', 'average', 'poor'])->nullable();
            $table->text('behavior_notes')->nullable();
            
            // Notice and termination
            $table->date('notice_given_date')->nullable();
            $table->integer('notice_period_days')->nullable();
            $table->text('termination_reason')->nullable();
            $table->timestamp('terminated_at')->nullable();
            $table->unsignedBigInteger('terminated_by')->nullable();
            
            // Preferences and notes
            $table->json('preferences')->nullable(); // Tenant preferences
            $table->text('special_requirements')->nullable();
            $table->text('admin_notes')->nullable();
            
            // Timestamps
            $table->timestamps();
            $table->softDeletes();
            
            // Foreign keys
            $table->foreign('user_id')->references('id')->on('users')->onDelete('cascade');
            $table->foreign('unit_id')->references('id')->on('units')->onDelete('cascade');
            $table->foreign('owner_id')->references('id')->on('users')->onDelete('cascade');
            $table->foreign('kyc_verified_by')->references('id')->on('users')->onDelete('set null');
            $table->foreign('terminated_by')->references('id')->on('users')->onDelete('set null');
            
            // Indexes
            $table->index('tenant_code');
            $table->index('unit_id');
            $table->index('owner_id');
            $table->index('status');
            $table->index('kyc_status');
            $table->index('agreement_start_date');
            $table->index('agreement_end_date');
            $table->index('move_in_date');
            $table->index('move_out_date');
            $table->index(['status', 'kyc_status']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('tenants');
    }
};
