<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('tenants', function (Blueprint $table) {
            // Drop the foreign key constraint first
            $table->dropForeign(['unit_id']);
            
            // Modify the column to be nullable
            $table->unsignedBigInteger('unit_id')->nullable()->change();
            
            // Re-add the foreign key constraint
            $table->foreign('unit_id')->references('id')->on('units')->onDelete('set null');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('tenants', function (Blueprint $table) {
            // Drop the foreign key constraint
            $table->dropForeign(['unit_id']);
            
            // Make the column not nullable again
            $table->unsignedBigInteger('unit_id')->nullable(false)->change();
            
            // Re-add the foreign key constraint
            $table->foreign('unit_id')->references('id')->on('units')->onDelete('cascade');
        });
    }
};
