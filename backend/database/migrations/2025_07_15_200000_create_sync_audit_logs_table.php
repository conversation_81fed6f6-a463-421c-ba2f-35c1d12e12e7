<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('sync_audit_logs', function (Blueprint $table) {
            $table->id();
            $table->string('entity_type'); // 'Society', 'Unit', 'Tenant', etc.
            $table->unsignedBigInteger('entity_id')->nullable();
            $table->string('operation'); // 'created', 'updated', 'deleted'
            $table->string('source_system'); // 'tms', 'onesociety'
            $table->string('target_system')->nullable(); // 'tms', 'onesociety'
            $table->json('payload');
            $table->json('original_payload')->nullable(); // For conflict resolution
            $table->enum('status', ['pending', 'processing', 'success', 'failed', 'skipped', 'conflict']);
            $table->text('error_message')->nullable();
            $table->json('error_details')->nullable();
            $table->string('sync_version')->nullable(); // For optimistic locking
            $table->string('correlation_id')->nullable(); // For tracking related events
            $table->string('batch_id')->nullable(); // For batch processing
            $table->integer('retry_count')->default(0);
            $table->timestamp('last_retry_at')->nullable();
            $table->timestamp('processed_at')->nullable();
            $table->timestamp('scheduled_at')->nullable();
            $table->decimal('processing_time_ms', 10, 2)->nullable();
            $table->unsignedBigInteger('memory_usage_bytes')->nullable();
            $table->json('metadata')->nullable(); // Additional context data
            $table->timestamps();

            // Indexes for performance
            $table->index(['entity_type', 'entity_id']);
            $table->index(['status', 'processed_at']);
            $table->index(['source_system', 'created_at']);
            $table->index(['operation', 'entity_type']);
            $table->index(['correlation_id']);
            $table->index(['batch_id']);
            $table->index(['retry_count', 'status']);
            $table->index(['scheduled_at', 'status']);
            
            // Composite indexes for common queries
            $table->index(['entity_type', 'status', 'processed_at']);
            $table->index(['source_system', 'status', 'created_at']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('sync_audit_logs');
    }
};
