<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use App\Models\Notification;
use App\Models\User;

class NotificationSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Get users to assign notifications to
        $users = User::all();
        
        if ($users->isEmpty()) {
            $this->command->error('No users found. Please run UserSeeder first.');
            return;
        }

        // Create sample notifications for each user
        foreach ($users as $user) {
            // Payment reminder notification
            Notification::create([
                'user_id' => $user->id,
                'type' => 'payment_reminder',
                'channel' => 'email',
                'title' => 'Rent Payment Reminder',
                'message' => 'Your monthly rent payment is due in 3 days. Please make the payment to avoid late fees.',
                'data' => [
                    'amount' => 25000,
                    'due_date' => now()->addDays(3)->toDateString(),
                    'unit_number' => 'A-101',
                    'payment_method' => 'bank_transfer',
                ],
                'status' => 'sent',
                'priority' => 'high',
                'is_scheduled' => false,
                'sent_at' => now(),
                'read_at' => null,
            ]);

            // Maintenance request notification
            Notification::create([
                'user_id' => $user->id,
                'type' => 'maintenance_request',
                'channel' => 'in_app',
                'title' => 'New Maintenance Request',
                'message' => 'A new maintenance request has been submitted for your unit. Our team will contact you soon.',
                'data' => [
                    'request_id' => rand(1000, 9999),
                    'issue_type' => 'plumbing',
                    'priority' => 'medium',
                    'unit_number' => 'A-101',
                ],
                'status' => 'sent',
                'priority' => 'medium',
                'is_scheduled' => false,
                'sent_at' => now()->subHours(2),
                'read_at' => null,
            ]);

            // Agreement expiry warning
            Notification::create([
                'user_id' => $user->id,
                'type' => 'agreement_expiry',
                'channel' => 'email',
                'title' => 'Lease Agreement Expiring Soon',
                'message' => 'Your lease agreement will expire in 30 days. Please contact us to discuss renewal options.',
                'data' => [
                    'agreement_id' => rand(100, 999),
                    'expiry_date' => now()->addDays(30)->toDateString(),
                    'unit_number' => 'A-101',
                    'renewal_deadline' => now()->addDays(15)->toDateString(),
                ],
                'status' => 'sent',
                'priority' => 'high',
                'is_scheduled' => false,
                'sent_at' => now()->subDays(1),
                'read_at' => now()->subHours(12), // This one is read
            ]);

            // Payment received confirmation
            Notification::create([
                'user_id' => $user->id,
                'type' => 'payment_received',
                'channel' => 'push',
                'title' => 'Payment Received',
                'message' => 'Thank you! Your payment of ₹25,000 has been received and processed successfully.',
                'data' => [
                    'amount' => 25000,
                    'payment_id' => 'PAY-' . rand(10000, 99999),
                    'payment_method' => 'online_transfer',
                    'unit_number' => 'A-101',
                    'receipt_number' => 'RCP-' . rand(1000, 9999),
                ],
                'status' => 'delivered',
                'priority' => 'low',
                'is_scheduled' => false,
                'sent_at' => now()->subDays(2),
                'delivered_at' => now()->subDays(2),
                'read_at' => now()->subDays(1), // This one is read
            ]);

            // System maintenance notification
            Notification::create([
                'user_id' => $user->id,
                'type' => 'system_maintenance',
                'channel' => 'in_app',
                'title' => 'Scheduled System Maintenance',
                'message' => 'The tenant management system will be under maintenance tonight from 2:00 AM to 6:00 AM.',
                'data' => [
                    'maintenance_start' => now()->addDays(1)->setHour(2)->setMinute(0)->toISOString(),
                    'maintenance_end' => now()->addDays(1)->setHour(6)->setMinute(0)->toISOString(),
                    'affected_services' => ['payment_portal', 'maintenance_requests'],
                ],
                'status' => 'sent',
                'priority' => 'medium',
                'is_scheduled' => false,
                'sent_at' => now()->subHours(6),
                'read_at' => null,
            ]);

            // NOC application update
            Notification::create([
                'user_id' => $user->id,
                'type' => 'noc_update',
                'channel' => 'email',
                'title' => 'NOC Application Approved',
                'message' => 'Your No Objection Certificate application has been approved. You can download the certificate from your dashboard.',
                'data' => [
                    'application_id' => 'NOC-' . rand(1000, 9999),
                    'noc_type' => 'rental',
                    'approval_date' => now()->subDays(1)->toDateString(),
                    'certificate_url' => '/downloads/noc-certificate.pdf',
                ],
                'status' => 'sent',
                'priority' => 'medium',
                'is_scheduled' => false,
                'sent_at' => now()->subDays(1),
                'read_at' => null,
            ]);

            // New enquiry notification (for owners)
            if ($user->role === 'owner') {
                Notification::create([
                    'user_id' => $user->id,
                    'type' => 'new_enquiry',
                    'channel' => 'email',
                    'title' => 'New Rental Enquiry',
                    'message' => 'You have received a new rental enquiry for your property. The prospective tenant is interested in viewing the unit.',
                    'data' => [
                        'enquiry_id' => 'ENQ-' . rand(1000, 9999),
                        'enquirer_name' => 'John Doe',
                        'enquirer_phone' => '+91 98765 43210',
                        'unit_number' => 'A-101',
                        'preferred_move_date' => now()->addDays(15)->toDateString(),
                    ],
                    'status' => 'sent',
                    'priority' => 'medium',
                    'is_scheduled' => false,
                    'sent_at' => now()->subHours(4),
                    'read_at' => null,
                ]);
            }
        }

        $this->command->info('Sample notifications created successfully!');
    }
}
