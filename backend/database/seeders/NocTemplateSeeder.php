<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use App\Models\NocTemplate;
use App\Models\User;

class NocTemplateSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Get the first admin user for created_by
        $admin = User::where('role', 'admin')->first();

        if (!$admin) {
            $this->command->error('No admin user found. Please create an admin user first.');
            return;
        }

        $templates = [
            [
                'name' => 'Standard Rental NOC',
                'description' => 'Standard No Objection Certificate for property rental',
                'noc_type' => 'rental',
                'template_content' => $this->getRentalTemplate(),
                'required_fields' => NocTemplate::getDefaultRequiredFields('rental'),
                'document_requirements' => NocTemplate::getDefaultDocumentRequirements('rental'),
                'placeholders' => [
                    'applicant_name', 'unit_number', 'tenant_name', 'rental_amount', 
                    'lease_duration', 'move_in_date', 'application_date', 'society_name'
                ],
                'is_active' => true,
                'created_by' => $admin->id,
            ],
            [
                'name' => 'Standard Residence NOC',
                'description' => 'Standard No Objection Certificate for property residence',
                'noc_type' => 'residence',
                'template_content' => $this->getResidenceTemplate(),
                'required_fields' => NocTemplate::getDefaultRequiredFields('residence'),
                'document_requirements' => NocTemplate::getDefaultDocumentRequirements('residence'),
                'placeholders' => [
                    'applicant_name', 'unit_number', 'contact_number', 
                    'email', 'application_date', 'society_name'
                ],
                'is_active' => true,
                'created_by' => $admin->id,
            ],
            [
                'name' => 'Vehicle Parking NOC',
                'description' => 'No Objection Certificate for vehicle parking',
                'noc_type' => 'vehicle',
                'template_content' => $this->getVehicleTemplate(),
                'required_fields' => NocTemplate::getDefaultRequiredFields('vehicle'),
                'document_requirements' => NocTemplate::getDefaultDocumentRequirements('vehicle'),
                'placeholders' => [
                    'applicant_name', 'unit_number', 'vehicle_type', 'vehicle_number',
                    'parking_slot', 'application_date', 'society_name'
                ],
                'is_active' => true,
                'created_by' => $admin->id,
            ],
            [
                'name' => 'Renovation NOC',
                'description' => 'No Objection Certificate for property renovation',
                'noc_type' => 'renovation',
                'template_content' => $this->getRenovationTemplate(),
                'required_fields' => NocTemplate::getDefaultRequiredFields('renovation'),
                'document_requirements' => NocTemplate::getDefaultDocumentRequirements('renovation'),
                'placeholders' => [
                    'applicant_name', 'unit_number', 'renovation_type', 'start_date',
                    'end_date', 'contractor_name', 'application_date', 'society_name'
                ],
                'is_active' => true,
                'created_by' => $admin->id,
            ],
            [
                'name' => 'Property Transfer NOC',
                'description' => 'No Objection Certificate for property transfer',
                'noc_type' => 'transfer',
                'template_content' => $this->getTransferTemplate(),
                'required_fields' => NocTemplate::getDefaultRequiredFields('transfer'),
                'document_requirements' => NocTemplate::getDefaultDocumentRequirements('transfer'),
                'placeholders' => [
                    'applicant_name', 'unit_number', 'new_owner_name',
                    'transfer_date', 'application_date', 'society_name'
                ],
                'is_active' => true,
                'created_by' => $admin->id,
            ],
        ];

        foreach ($templates as $template) {
            NocTemplate::create($template);
        }

        $this->command->info('NOC templates seeded successfully!');
    }

    private function getRentalTemplate(): string
    {
        return '
<!DOCTYPE html>
<html>
<head>
    <title>Rental NOC</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 40px; line-height: 1.6; }
        .header { text-align: center; margin-bottom: 30px; }
        .content { margin: 20px 0; }
        .signature { margin-top: 50px; }
        .footer { margin-top: 30px; font-size: 12px; color: #666; }
    </style>
</head>
<body>
    <div class="header">
        <h2>NO OBJECTION CERTIFICATE</h2>
        <h3>For Property Rental</h3>
        <p><strong>{society_name}</strong></p>
    </div>
    
    <div class="content">
        <p><strong>Date:</strong> {application_date}</p>
        <p><strong>To Whom It May Concern,</strong></p>
        
        <p>This is to certify that we have no objection to <strong>{applicant_name}</strong>, 
        owner of Unit <strong>{unit_number}</strong> in our society, renting out the said property 
        to <strong>{tenant_name}</strong>.</p>
        
        <p><strong>Rental Details:</strong></p>
        <ul>
            <li>Monthly Rental Amount: {rental_amount}</li>
            <li>Lease Duration: {lease_duration}</li>
            <li>Expected Move-in Date: {move_in_date}</li>
        </ul>
        
        <p>The tenant is required to follow all society rules and regulations. 
        The owner will be responsible for any violations by the tenant.</p>
        
        <p>This NOC is valid for the duration of the lease agreement.</p>
    </div>
    
    <div class="signature">
        <p>Sincerely,</p>
        <br><br>
        <p>_________________________</p>
        <p>Society Management Committee</p>
        <p>{society_name}</p>
    </div>
    
    <div class="footer">
        <p>This is a computer-generated document and does not require a physical signature.</p>
    </div>
</body>
</html>';
    }

    private function getResidenceTemplate(): string
    {
        return '
<!DOCTYPE html>
<html>
<head>
    <title>Residence NOC</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 40px; line-height: 1.6; }
        .header { text-align: center; margin-bottom: 30px; }
        .content { margin: 20px 0; }
        .signature { margin-top: 50px; }
        .footer { margin-top: 30px; font-size: 12px; color: #666; }
    </style>
</head>
<body>
    <div class="header">
        <h2>NO OBJECTION CERTIFICATE</h2>
        <h3>For Residence</h3>
        <p><strong>{society_name}</strong></p>
    </div>
    
    <div class="content">
        <p><strong>Date:</strong> {application_date}</p>
        <p><strong>To Whom It May Concern,</strong></p>
        
        <p>This is to certify that we have no objection to <strong>{applicant_name}</strong> 
        residing in Unit <strong>{unit_number}</strong> in our society premises.</p>
        
        <p><strong>Resident Details:</strong></p>
        <ul>
            <li>Name: {applicant_name}</li>
            <li>Unit Number: {unit_number}</li>
            <li>Contact: {contact_number}</li>
            <li>Email: {email}</li>
        </ul>
        
        <p>The resident is expected to follow all society rules, regulations, and by-laws. 
        Any violation may result in the revocation of this NOC.</p>
        
        <p>This certificate is issued for official purposes and is valid until further notice.</p>
    </div>
    
    <div class="signature">
        <p>Sincerely,</p>
        <br><br>
        <p>_________________________</p>
        <p>Society Management Committee</p>
        <p>{society_name}</p>
    </div>
    
    <div class="footer">
        <p>This is a computer-generated document and does not require a physical signature.</p>
    </div>
</body>
</html>';
    }

    private function getVehicleTemplate(): string
    {
        return '
<!DOCTYPE html>
<html>
<head>
    <title>Vehicle Parking NOC</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 40px; line-height: 1.6; }
        .header { text-align: center; margin-bottom: 30px; }
        .content { margin: 20px 0; }
        .signature { margin-top: 50px; }
        .footer { margin-top: 30px; font-size: 12px; color: #666; }
    </style>
</head>
<body>
    <div class="header">
        <h2>NO OBJECTION CERTIFICATE</h2>
        <h3>For Vehicle Parking</h3>
        <p><strong>{society_name}</strong></p>
    </div>
    
    <div class="content">
        <p><strong>Date:</strong> {application_date}</p>
        <p><strong>To Whom It May Concern,</strong></p>
        
        <p>This is to certify that we have no objection to <strong>{applicant_name}</strong>, 
        resident of Unit <strong>{unit_number}</strong>, parking their vehicle in our society premises.</p>
        
        <p><strong>Vehicle Details:</strong></p>
        <ul>
            <li>Vehicle Type: {vehicle_type}</li>
            <li>Registration Number: {vehicle_number}</li>
            <li>Assigned Parking Slot: {parking_slot}</li>
            <li>Owner: {applicant_name}</li>
        </ul>
        
        <p>The vehicle owner must:</p>
        <ul>
            <li>Follow all parking rules and regulations</li>
            <li>Maintain valid insurance and registration</li>
            <li>Park only in the designated slot</li>
            <li>Ensure the vehicle does not cause any inconvenience to other residents</li>
        </ul>
        
        <p>This NOC is valid as long as the resident maintains their residency in the society.</p>
    </div>
    
    <div class="signature">
        <p>Sincerely,</p>
        <br><br>
        <p>_________________________</p>
        <p>Society Management Committee</p>
        <p>{society_name}</p>
    </div>
    
    <div class="footer">
        <p>This is a computer-generated document and does not require a physical signature.</p>
    </div>
</body>
</html>';
    }

    private function getRenovationTemplate(): string
    {
        return '
<!DOCTYPE html>
<html>
<head>
    <title>Renovation NOC</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 40px; line-height: 1.6; }
        .header { text-align: center; margin-bottom: 30px; }
        .content { margin: 20px 0; }
        .signature { margin-top: 50px; }
        .footer { margin-top: 30px; font-size: 12px; color: #666; }
    </style>
</head>
<body>
    <div class="header">
        <h2>NO OBJECTION CERTIFICATE</h2>
        <h3>For Property Renovation</h3>
        <p><strong>{society_name}</strong></p>
    </div>
    
    <div class="content">
        <p><strong>Date:</strong> {application_date}</p>
        <p><strong>To Whom It May Concern,</strong></p>
        
        <p>This is to certify that we have no objection to <strong>{applicant_name}</strong>, 
        owner of Unit <strong>{unit_number}</strong>, carrying out renovation work in their property.</p>
        
        <p><strong>Renovation Details:</strong></p>
        <ul>
            <li>Type of Renovation: {renovation_type}</li>
            <li>Expected Start Date: {start_date}</li>
            <li>Expected Completion Date: {end_date}</li>
            <li>Contractor: {contractor_name}</li>
        </ul>
        
        <p><strong>Conditions:</strong></p>
        <ul>
            <li>Work should be carried out only during permitted hours (9 AM to 6 PM on weekdays)</li>
            <li>No structural changes without prior approval</li>
            <li>All safety measures must be followed</li>
            <li>Debris and construction material should be properly disposed of</li>
            <li>Any damage to common areas will be the owner\'s responsibility</li>
        </ul>
        
        <p>This NOC is valid for the specified renovation period only.</p>
    </div>
    
    <div class="signature">
        <p>Sincerely,</p>
        <br><br>
        <p>_________________________</p>
        <p>Society Management Committee</p>
        <p>{society_name}</p>
    </div>
    
    <div class="footer">
        <p>This is a computer-generated document and does not require a physical signature.</p>
    </div>
</body>
</html>';
    }

    private function getTransferTemplate(): string
    {
        return '
<!DOCTYPE html>
<html>
<head>
    <title>Property Transfer NOC</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 40px; line-height: 1.6; }
        .header { text-align: center; margin-bottom: 30px; }
        .content { margin: 20px 0; }
        .signature { margin-top: 50px; }
        .footer { margin-top: 30px; font-size: 12px; color: #666; }
    </style>
</head>
<body>
    <div class="header">
        <h2>NO OBJECTION CERTIFICATE</h2>
        <h3>For Property Transfer</h3>
        <p><strong>{society_name}</strong></p>
    </div>
    
    <div class="content">
        <p><strong>Date:</strong> {application_date}</p>
        <p><strong>To Whom It May Concern,</strong></p>
        
        <p>This is to certify that we have no objection to the transfer of ownership 
        of Unit <strong>{unit_number}</strong> from <strong>{applicant_name}</strong> 
        to <strong>{new_owner_name}</strong>.</p>
        
        <p><strong>Transfer Details:</strong></p>
        <ul>
            <li>Property: Unit {unit_number}</li>
            <li>Current Owner: {applicant_name}</li>
            <li>New Owner: {new_owner_name}</li>
            <li>Transfer Date: {transfer_date}</li>
        </ul>
        
        <p><strong>Conditions:</strong></p>
        <ul>
            <li>All society dues must be cleared before transfer</li>
            <li>New owner must agree to follow society rules and regulations</li>
            <li>Transfer formalities must be completed as per society by-laws</li>
            <li>Required documents must be submitted to the society office</li>
        </ul>
        
        <p>This NOC is issued subject to the completion of all legal and financial formalities.</p>
    </div>
    
    <div class="signature">
        <p>Sincerely,</p>
        <br><br>
        <p>_________________________</p>
        <p>Society Management Committee</p>
        <p>{society_name}</p>
    </div>
    
    <div class="footer">
        <p>This is a computer-generated document and does not require a physical signature.</p>
    </div>
</body>
</html>';
    }
} 