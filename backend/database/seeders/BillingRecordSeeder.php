<?php

namespace Database\Seeders;

use App\Models\BillingRecord;
use App\Models\BillingRule;
use App\Models\Unit;
use App\Models\User;
use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;

class BillingRecordSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $adminUser = User::where('role', 'admin')->first();
        $units = Unit::with('currentTenant')->take(10)->get();
        $billingRules = BillingRule::all();

        if ($units->isEmpty() || $billingRules->isEmpty()) {
            $this->command->warn('No units or billing rules found. Please run UnitSeeder and BillingRuleSeeder first.');
            return;
        }

        foreach ($units as $unit) {
            // Create 3-5 billing records per unit for different months
            $recordCount = rand(3, 5);

            for ($i = 0; $i < $recordCount; $i++) {
                $billingRule = $billingRules->random();
                $periodStart = now()->subMonths($i + 1)->startOfMonth();
                $periodEnd = $periodStart->copy()->endOfMonth();
                $dueDate = $periodEnd->copy()->addDays(5);

                // Calculate amount based on billing rule
                $amount = $this->calculateAmount($billingRule, $unit);

                // Determine status based on due date
                $status = $this->determineStatus($dueDate);

                BillingRecord::create([
                    'unit_id' => $unit->id,
                    'tenant_id' => $unit->current_tenant_id,
                    'billing_rule_id' => $billingRule->id,
                    'charge_type' => $billingRule->type,
                    'amount' => $amount,
                    'period_start' => $periodStart,
                    'period_end' => $periodEnd,
                    'due_date' => $dueDate,
                    'status' => $status,
                    'breakdown' => $this->generateBreakdown($billingRule->type, $amount),
                    'notes' => $this->generateNotes($billingRule->type),
                    'paid_date' => $status === 'paid' ? $dueDate->copy()->addDays(rand(1, 10)) : null,
                    'payment_method' => $status === 'paid' ? $this->getRandomPaymentMethod() : null,
                    'transaction_id' => $status === 'paid' ? 'TXN' . rand(10000000, 99999999) : null,
                    'created_by' => $adminUser->id,
                    'metadata' => [
                        'auto_generated' => true,
                        'billing_rule_name' => $billingRule->name,
                    ],
                ]);
            }
        }

        // Create some additional random billing records
        // BillingRecord::factory()->count(20)->create([
        //     'created_by' => $adminUser->id,
        // ]);
    }

    private function calculateAmount(BillingRule $billingRule, Unit $unit): float
    {
        return match ($billingRule->calculation_method) {
            'per_sqft' => $billingRule->amount * ($unit->area ?? 1000),
            'fixed' => $billingRule->amount,
            'percentage' => ($unit->market_rent ?? 25000) * ($billingRule->amount / 100),
            'per_unit' => $billingRule->amount,
            default => $billingRule->amount,
        };
    }

    private function determineStatus($dueDate): string
    {
        $now = now();

        if ($dueDate->isFuture()) {
            return 'pending';
        } elseif ($dueDate->diffInDays($now) <= 30) {
            return collect(['paid', 'pending', 'overdue'])->random();
        } else {
            return collect(['paid', 'overdue'])->random();
        }
    }

    private function generateBreakdown(string $chargeType, float $amount): array
    {
        return match ($chargeType) {
            'maintenance' => [
                'base_amount' => round($amount * 0.8, 2),
                'service_charge' => round($amount * 0.15, 2),
                'tax' => round($amount * 0.05, 2),
            ],
            'utilities' => [
                'electricity' => round($amount * 0.6, 2),
                'water' => round($amount * 0.3, 2),
                'gas' => round($amount * 0.1, 2),
            ],
            'parking' => [
                'monthly_fee' => $amount,
            ],
            'non_occupancy' => [
                'base_charge' => round($amount * 0.9, 2),
                'penalty' => round($amount * 0.1, 2),
            ],
            'noc_charges' => [
                'processing_fee' => round($amount * 0.7, 2),
                'documentation' => round($amount * 0.3, 2),
            ],
            default => [
                'amount' => $amount,
            ],
        };
    }

    private function generateNotes(string $chargeType): ?string
    {
        $notes = [
            'maintenance' => 'Monthly maintenance charges for common area upkeep',
            'utilities' => 'Utility charges for electricity, water, and gas consumption',
            'parking' => 'Parking space rental charges',
            'non_occupancy' => 'Non-occupancy charges as per society rules',
            'noc_charges' => 'NOC processing fee for application',
        ];

        return $notes[$chargeType] ?? null;
    }

    private function getRandomPaymentMethod(): string
    {
        return collect(['bank_transfer', 'upi', 'cash', 'cheque', 'online'])->random();
    }
}
