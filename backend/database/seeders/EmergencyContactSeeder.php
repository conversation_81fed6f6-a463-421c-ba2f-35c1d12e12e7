<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use App\Models\EmergencyContact;
use App\Models\Tenant;
use App\Models\User;

class EmergencyContactSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $tenants = Tenant::with('user')->get();
        
        if ($tenants->isEmpty()) {
            $this->command->info('No tenants found. Please run TenantSeeder first.');
            return;
        }

        $admin = User::where('role', 'admin')->first();
        $relationships = EmergencyContact::getAllRelationships();

        foreach ($tenants as $tenant) {
            // Create 2-3 emergency contacts per tenant
            $contactCount = rand(2, 3);
            
            for ($i = 1; $i <= $contactCount; $i++) {
                $relationship = $relationships[array_rand($relationships)];
                $name = $this->generateName($relationship, $tenant->user->first_name);
                
                EmergencyContact::create([
                    'tenant_id' => $tenant->id,
                    'name' => $name,
                    'relationship' => $relationship,
                    'primary_phone' => $this->generatePhoneNumber(),
                    'secondary_phone' => rand(0, 1) ? $this->generatePhoneNumber() : null,
                    'email' => strtolower(str_replace(' ', '.', $name)) . '@example.com',
                    'address' => $this->generateAddress(),
                    'city' => $this->getRandomCity(),
                    'state' => 'Maharashtra',
                    'country' => 'India',
                    'pincode' => $this->generatePincode(),
                    'priority' => $i,
                    'is_local' => $i === 1 ? true : (bool)rand(0, 1), // Primary contact is usually local
                    'is_active' => true,
                    'notes' => $i === 1 ? 'Primary emergency contact' : null,
                    'created_by' => $admin?->id,
                    'updated_by' => $admin?->id,
                ]);
            }
        }

        $this->command->info('Emergency contacts seeded successfully!');
    }

    /**
     * Generate a name based on relationship
     */
    private function generateName(string $relationship, string $tenantName): string
    {
        $firstNames = [
            'parent' => ['Rajesh', 'Sunita', 'Ramesh', 'Priya', 'Suresh', 'Kavita'],
            'spouse' => ['Amit', 'Sneha', 'Rohit', 'Pooja', 'Vikash', 'Neha'],
            'sibling' => ['Arjun', 'Riya', 'Karan', 'Isha', 'Varun', 'Diya'],
            'child' => ['Aarav', 'Ananya', 'Aryan', 'Ira', 'Vihaan', 'Saanvi'],
            'relative' => ['Mohan', 'Geeta', 'Prakash', 'Meera', 'Anil', 'Sita'],
            'friend' => ['Rahul', 'Shweta', 'Deepak', 'Nisha', 'Sanjay', 'Ritu'],
            'colleague' => ['Manoj', 'Preeti', 'Ajay', 'Swati', 'Vinod', 'Rekha'],
            'neighbor' => ['Kishore', 'Usha', 'Mahesh', 'Lata', 'Ravi', 'Manju'],
            'other' => ['Sachin', 'Archana', 'Nitin', 'Vandana', 'Ashok', 'Seema'],
        ];

        $lastNames = ['Sharma', 'Patel', 'Singh', 'Kumar', 'Gupta', 'Jain', 'Agarwal', 'Verma', 'Mehta', 'Shah'];
        
        $firstName = $firstNames[$relationship][array_rand($firstNames[$relationship])];
        $lastName = $lastNames[array_rand($lastNames)];
        
        return $firstName . ' ' . $lastName;
    }

    /**
     * Generate a random Indian phone number
     */
    private function generatePhoneNumber(): string
    {
        $prefixes = ['98', '97', '96', '95', '94', '93', '92', '91', '90', '89'];
        $prefix = $prefixes[array_rand($prefixes)];
        $number = $prefix . str_pad(rand(10000000, 99999999), 8, '0', STR_PAD_LEFT);
        return $number;
    }

    /**
     * Generate a random address
     */
    private function generateAddress(): string
    {
        $areas = [
            'Bandra West', 'Andheri East', 'Powai', 'Worli', 'Lower Parel',
            'Thane West', 'Ghatkopar', 'Malad West', 'Borivali East', 'Kandivali West'
        ];
        
        $buildingNames = [
            'Sunrise Apartments', 'Green Valley', 'Royal Palace', 'Ocean View',
            'Golden Heights', 'Silver Oaks', 'Paradise Tower', 'Crystal Gardens'
        ];
        
        $building = $buildingNames[array_rand($buildingNames)];
        $area = $areas[array_rand($areas)];
        $flatNo = rand(101, 2505);
        
        return "Flat {$flatNo}, {$building}, {$area}";
    }

    /**
     * Get a random city
     */
    private function getRandomCity(): string
    {
        $cities = ['Mumbai', 'Pune', 'Nagpur', 'Nashik', 'Aurangabad', 'Solapur', 'Thane', 'Navi Mumbai'];
        return $cities[array_rand($cities)];
    }

    /**
     * Generate a random pincode
     */
    private function generatePincode(): string
    {
        return '4' . str_pad(rand(10000, 99999), 5, '0', STR_PAD_LEFT);
    }
} 