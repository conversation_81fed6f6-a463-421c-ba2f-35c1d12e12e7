<?php

namespace Database\Seeders;

use App\Models\User;
// use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;

class DatabaseSeeder extends Seeder
{
    /**
     * Seed the application's database.
     */
    public function run(): void
    {
        // Seed in order of dependencies
        $this->call([
            UserSeeder::class,      // First - creates users (owners, tenants, admins)
            UnitSeeder::class,      // Second - creates units with owners
            TenantSeeder::class,    // Third - creates tenant records and assigns to units
        ]);
    }
}
