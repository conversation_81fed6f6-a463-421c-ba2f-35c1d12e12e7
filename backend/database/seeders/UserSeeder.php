<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use App\Models\User;
use Illuminate\Support\Facades\Hash;

class UserSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Create system admin
        User::create([
            'name' => 'System Admin',
            'first_name' => 'System',
            'last_name' => 'Admin',
            'email' => '<EMAIL>',
            'email_verified_at' => now(),
            'password' => Hash::make('admin123'),
            'role' => User::ROLE_ADMIN,
            'user_type' => User::TYPE_SOCIETY_ADMIN,
            'phone' => '+91-9876543210',
            'status' => User::STATUS_ACTIVE,
            'is_verified' => true,
            'verified_at' => now(),
        ]);

        // Create society admin
        User::create([
            'name' => 'Society Admin',
            'first_name' => 'Society',
            'last_name' => 'Admin',
            'email' => '<EMAIL>',
            'email_verified_at' => now(),
            'password' => Hash::make('society123'),
            'role' => User::ROLE_ADMIN,
            'user_type' => User::TYPE_SOCIETY_ADMIN,
            'phone' => '+91-9876543211',
            'status' => User::STATUS_ACTIVE,
            'is_verified' => true,
            'verified_at' => now(),
        ]);

        // Create sample owners/members
        $owners = [
            ['first_name' => 'Rajesh', 'last_name' => 'Kumar', 'email' => '<EMAIL>', 'phone' => '+91-9876543212'],
            ['first_name' => 'Priya', 'last_name' => 'Sharma', 'email' => '<EMAIL>', 'phone' => '+91-9876543213'],
            ['first_name' => 'Amit', 'last_name' => 'Singh', 'email' => '<EMAIL>', 'phone' => '+91-9876543214'],
            ['first_name' => 'Sunita', 'last_name' => 'Patel', 'email' => '<EMAIL>', 'phone' => '+91-9876543215'],
            ['first_name' => 'Vikram', 'last_name' => 'Gupta', 'email' => '<EMAIL>', 'phone' => '+91-9876543216'],
            ['first_name' => 'Kavita', 'last_name' => 'Reddy', 'email' => '<EMAIL>', 'phone' => '+91-9876543217'],
            ['first_name' => 'Manoj', 'last_name' => 'Agarwal', 'email' => '<EMAIL>', 'phone' => '+91-9876543218'],
            ['first_name' => 'Deepa', 'last_name' => 'Joshi', 'email' => '<EMAIL>', 'phone' => '+91-9876543219'],
            ['first_name' => 'Ravi', 'last_name' => 'Mehta', 'email' => '<EMAIL>', 'phone' => '+91-9876543220'],
            ['first_name' => 'Sita', 'last_name' => 'Verma', 'email' => '<EMAIL>', 'phone' => '+91-9876543221'],
        ];

        foreach ($owners as $index => $owner) {
            User::create([
                'name' => $owner['first_name'] . ' ' . $owner['last_name'],
                'first_name' => $owner['first_name'],
                'last_name' => $owner['last_name'],
                'email' => $owner['email'],
                'email_verified_at' => now(),
                'password' => Hash::make('password123'),
                'role' => User::ROLE_OWNER,
                'user_type' => User::TYPE_OWNER,
                'phone' => $owner['phone'],
                'address' => 'Address ' . ($index + 1) . ', Sample City',
                'city' => 'Mumbai',
                'state' => 'Maharashtra',
                'country' => 'India',
                'pincode' => '400001',
                'status' => User::STATUS_ACTIVE,
                'is_verified' => true,
                'verified_at' => now(),
                'emergency_contact' => [
                    'name' => 'Emergency Contact ' . ($index + 1),
                    'phone' => '+91-9876543' . str_pad(300 + $index, 3, '0', STR_PAD_LEFT),
                    'relationship' => 'Spouse'
                ],
            ]);
        }

        // Create sample tenants
        $tenants = [
            ['first_name' => 'Arjun', 'last_name' => 'Malhotra', 'email' => '<EMAIL>', 'phone' => '+91-9876543222'],
            ['first_name' => 'Neha', 'last_name' => 'Kapoor', 'email' => '<EMAIL>', 'phone' => '+91-9876543223'],
            ['first_name' => 'Rohit', 'last_name' => 'Nair', 'email' => '<EMAIL>', 'phone' => '+91-9876543224'],
            ['first_name' => 'Anita', 'last_name' => 'Bansal', 'email' => '<EMAIL>', 'phone' => '+91-9876543225'],
            ['first_name' => 'Suresh', 'last_name' => 'Iyer', 'email' => '<EMAIL>', 'phone' => '+91-9876543226'],
            ['first_name' => 'Pooja', 'last_name' => 'Saxena', 'email' => '<EMAIL>', 'phone' => '+91-9876543227'],
            ['first_name' => 'Kiran', 'last_name' => 'Rao', 'email' => '<EMAIL>', 'phone' => '+91-9876543228'],
            ['first_name' => 'Manish', 'last_name' => 'Jain', 'email' => '<EMAIL>', 'phone' => '+91-9876543229'],
        ];

        foreach ($tenants as $index => $tenant) {
            User::create([
                'name' => $tenant['first_name'] . ' ' . $tenant['last_name'],
                'first_name' => $tenant['first_name'],
                'last_name' => $tenant['last_name'],
                'email' => $tenant['email'],
                'email_verified_at' => now(),
                'password' => Hash::make('tenant123'),
                'role' => User::ROLE_TENANT,
                'user_type' => User::TYPE_TENANT,
                'phone' => $tenant['phone'],
                'address' => 'Tenant Address ' . ($index + 1) . ', Sample City',
                'city' => 'Mumbai',
                'state' => 'Maharashtra',
                'country' => 'India',
                'pincode' => '400002',
                'status' => User::STATUS_ACTIVE,
                'is_verified' => true,
                'verified_at' => now(),
                'emergency_contact' => [
                    'name' => 'Emergency Contact ' . ($index + 1),
                    'phone' => '+91-9876543' . str_pad(400 + $index, 3, '0', STR_PAD_LEFT),
                    'relationship' => 'Family'
                ],
            ]);
        }

        $this->command->info('Created ' . User::count() . ' users');
    }
}
