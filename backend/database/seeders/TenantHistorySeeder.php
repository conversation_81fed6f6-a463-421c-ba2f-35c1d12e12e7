<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use App\Models\TenantHistory;
use App\Models\Tenant;
use App\Models\User;
use Carbon\Carbon;

class TenantHistorySeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Get all tenants and users
        $tenants = Tenant::all();
        $users = User::all();

        if ($tenants->isEmpty() || $users->isEmpty()) {
            $this->command->warn('No tenants or users found. Please run TenantSeeder and UserSeeder first.');
            return;
        }

        $this->command->info('Creating tenant history records...');

        foreach ($tenants as $tenant) {
            $this->createTenantHistory($tenant, $users);
        }

        $this->command->info('Tenant history records created successfully!');
    }

    private function createTenantHistory(Tenant $tenant, $users)
    {
        $adminUser = $users->where('role', 'admin')->first();
        $tenantUser = $users->find($tenant->user_id);

        // Create a timeline of activities for this tenant
        $activities = [
            [
                'activity_type' => 'tenant_created',
                'description' => "Tenant account created with code {$tenant->tenant_code}",
                'severity' => 'high',
                'user_id' => $adminUser?->id,
                'performed_at' => $tenant->created_at,
                'metadata' => [
                    'tenant_code' => $tenant->tenant_code,
                    'user_id' => $tenant->user_id,
                ],
            ],
            [
                'activity_type' => 'onboarding_started',
                'description' => 'Tenant onboarding process started',
                'severity' => 'medium',
                'user_id' => $tenantUser?->id,
                'performed_at' => $tenant->created_at->addMinutes(5),
            ],
            [
                'activity_type' => 'personal_details_updated',
                'description' => 'Personal details updated',
                'severity' => 'medium',
                'user_id' => $tenantUser?->id,
                'performed_at' => $tenant->created_at->addMinutes(15),
                'old_values' => ['occupation' => null, 'company_name' => null],
                'new_values' => ['occupation' => $tenant->occupation, 'company_name' => $tenant->company_name],
            ],
            [
                'activity_type' => 'family_details_updated',
                'description' => 'Family details updated',
                'severity' => 'medium',
                'user_id' => $tenantUser?->id,
                'performed_at' => $tenant->created_at->addMinutes(25),
                'metadata' => [
                    'family_members' => $tenant->family_members,
                ],
            ],
        ];

        // Add emergency contact activities if tenant has emergency contacts
        if ($tenant->emergencyContacts->count() > 0) {
            foreach ($tenant->emergencyContacts as $index => $contact) {
                $activities[] = [
                    'activity_type' => 'emergency_contact_added',
                    'description' => "Emergency contact added: {$contact->name} ({$contact->relationship})",
                    'severity' => 'medium',
                    'user_id' => $tenantUser?->id,
                    'performed_at' => $tenant->created_at->addMinutes(30 + ($index * 5)),
                    'metadata' => [
                        'contact_id' => $contact->id,
                        'name' => $contact->name,
                        'relationship' => $contact->relationship,
                        'phone' => $contact->primary_phone,
                    ],
                ];
            }
        }

        // Add document upload activities
        $documentTypes = ['aadhar_card', 'pan_card', 'passport', 'bank_statement', 'salary_slip'];
        foreach (array_slice($documentTypes, 0, rand(2, 4)) as $index => $docType) {
            $activities[] = [
                'activity_type' => 'document_uploaded',
                'description' => "Document uploaded: " . ucwords(str_replace('_', ' ', $docType)),
                'severity' => 'medium',
                'user_id' => $tenantUser?->id,
                'performed_at' => $tenant->created_at->addHours(1 + $index),
                'metadata' => [
                    'document_type' => $docType,
                    'file_name' => "{$docType}_{$tenant->tenant_code}.pdf",
                ],
            ];
        }

        // Add KYC status changes
        if ($tenant->kyc_status !== 'pending') {
            $activities[] = [
                'activity_type' => 'kyc_status_changed',
                'description' => "KYC status changed from pending to submitted",
                'severity' => 'high',
                'user_id' => $tenantUser?->id,
                'performed_at' => $tenant->created_at->addHours(3),
                'old_values' => ['kyc_status' => 'pending'],
                'new_values' => ['kyc_status' => 'submitted'],
            ];

            if ($tenant->kyc_status === 'verified') {
                $activities[] = [
                    'activity_type' => 'kyc_status_changed',
                    'description' => "KYC status changed from submitted to verified",
                    'severity' => 'high',
                    'user_id' => $adminUser?->id,
                    'performed_at' => $tenant->created_at->addHours(24),
                    'old_values' => ['kyc_status' => 'submitted'],
                    'new_values' => ['kyc_status' => 'verified'],
                ];

                $activities[] = [
                    'activity_type' => 'document_verified',
                    'description' => "Documents verified by admin",
                    'severity' => 'high',
                    'user_id' => $adminUser?->id,
                    'performed_at' => $tenant->created_at->addHours(24)->addMinutes(5),
                    'metadata' => [
                        'verified_by' => $adminUser?->name,
                        'verification_date' => $tenant->created_at->addHours(24)->toDateString(),
                    ],
                ];
            }
        }

        // Add unit assignment if tenant has a unit
        if ($tenant->unit_id) {
            $activities[] = [
                'activity_type' => 'unit_assigned',
                'description' => "Unit assigned: Unit #{$tenant->unit_id}",
                'severity' => 'high',
                'user_id' => $adminUser?->id,
                'performed_at' => $tenant->created_at->addDays(1),
                'metadata' => [
                    'unit_id' => $tenant->unit_id,
                    'assigned_by' => $adminUser?->name,
                ],
            ];
        }

        // Add some recent login activities
        $recentDays = rand(1, 7);
        for ($i = 0; $i < $recentDays; $i++) {
            $loginTime = Carbon::now()->subDays($i)->setTime(rand(8, 20), rand(0, 59));
            $activities[] = [
                'activity_type' => 'login_activity',
                'description' => 'Tenant logged in',
                'severity' => 'low',
                'user_id' => $tenantUser?->id,
                'performed_at' => $loginTime,
                'ip_address' => $this->generateRandomIP(),
                'user_agent' => $this->generateRandomUserAgent(),
            ];

            // Sometimes add logout activity
            if (rand(0, 1)) {
                $activities[] = [
                    'activity_type' => 'logout_activity',
                    'description' => 'Tenant logged out',
                    'severity' => 'low',
                    'user_id' => $tenantUser?->id,
                    'performed_at' => $loginTime->addHours(rand(1, 8)),
                ];
            }
        }

        // Add some profile updates
        if (rand(0, 1)) {
            $activities[] = [
                'activity_type' => 'profile_updated',
                'description' => 'Tenant profile updated',
                'severity' => 'medium',
                'user_id' => $tenantUser?->id,
                'performed_at' => Carbon::now()->subDays(rand(1, 15)),
                'old_values' => ['preferences' => null],
                'new_values' => ['preferences' => ['notifications' => true, 'newsletter' => false]],
            ];
        }

        // Add some admin actions
        if (rand(0, 1)) {
            $adminActions = [
                'Profile review completed',
                'Background check initiated',
                'Reference verification completed',
                'Document re-verification requested',
            ];

            $activities[] = [
                'activity_type' => 'admin_action',
                'description' => 'Admin action performed: ' . $adminActions[array_rand($adminActions)],
                'severity' => 'high',
                'user_id' => $adminUser?->id,
                'performed_at' => Carbon::now()->subDays(rand(1, 10)),
                'metadata' => [
                    'action_type' => 'review',
                    'notes' => 'Routine verification process completed successfully.',
                ],
            ];
        }

        // Create all activities
        foreach ($activities as $activity) {
            TenantHistory::create([
                'tenant_id' => $tenant->id,
                'user_id' => $activity['user_id'],
                'activity_type' => $activity['activity_type'],
                'activity_description' => $activity['description'],
                'severity' => $activity['severity'],
                'performed_at' => $activity['performed_at'],
                'metadata' => $activity['metadata'] ?? null,
                'old_values' => $activity['old_values'] ?? null,
                'new_values' => $activity['new_values'] ?? null,
                'ip_address' => $activity['ip_address'] ?? null,
                'user_agent' => $activity['user_agent'] ?? null,
            ]);
        }
    }

    private function generateRandomIP(): string
    {
        return rand(1, 255) . '.' . rand(1, 255) . '.' . rand(1, 255) . '.' . rand(1, 255);
    }

    private function generateRandomUserAgent(): string
    {
        $userAgents = [
            'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
            'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
            'Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
            'Mozilla/5.0 (iPhone; CPU iPhone OS 14_6 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/14.1.1 Mobile/15E148 Safari/604.1',
            'Mozilla/5.0 (iPad; CPU OS 14_6 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/14.1.1 Mobile/15E148 Safari/604.1',
        ];

        return $userAgents[array_rand($userAgents)];
    }
}
