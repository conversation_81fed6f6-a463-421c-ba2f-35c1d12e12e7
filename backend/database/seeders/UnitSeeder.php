<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use App\Models\Unit;
use App\Models\UnitStatusHistory;
use App\Models\User;

class UnitSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Create sample units for a housing society
        $blocks = ['A', 'B', 'C', 'D'];
        $floors = ['1', '2', '3', '4', '5'];
        $statuses = ['occupied', 'vacant', 'to-let', 'rented'];
        
        $unitNumber = 1;
        
        foreach ($blocks as $block) {
            foreach ($floors as $floor) {
                // Create 2-4 units per floor
                $unitsPerFloor = rand(2, 4);
                
                for ($i = 1; $i <= $unitsPerFloor; $i++) {
                    $unitCode = $block . $floor . sprintf('%02d', $i);
                    $status = $statuses[array_rand($statuses)];
                    
                    $unit = Unit::create([
                        'unit_number' => $unitCode,
                        'block' => $block,
                        'floor' => $floor,
                        'wing' => rand(0, 1) ? 'East' : 'West',
                        'type' => 'residential',
                        'bedrooms' => rand(1, 4),
                        'bathrooms' => rand(1, 3),
                        'area_sqft' => rand(800, 2000),
                        'carpet_area' => rand(600, 1500),
                        'built_up_area' => rand(700, 1800),
                        'status' => $status,
                        'is_sublet' => rand(0, 1) ? true : false,
                        'is_owner_occupied' => $status === 'occupied',
                        'market_rent' => rand(15000, 50000),
                        'security_deposit' => rand(30000, 100000),
                        'maintenance_charge' => rand(2000, 8000),
                        'available_from' => $status === 'to-let' ? now()->addDays(rand(1, 30)) : null,
                        'description' => $this->generateUnitDescription(),
                        'amenities' => $this->generateAmenities(),
                        'preferences' => $this->generatePreferences(),
                        'is_active' => true,
                        'notes' => rand(0, 1) ? 'Sample unit for testing' : null,
                    ]);
                    
                    // Create initial status history
                    UnitStatusHistory::create([
                        'unit_id' => $unit->id,
                        'previous_status' => null,
                        'new_status' => $status,
                        'changed_by' => 1, // Assuming admin user has ID 1
                        'reason' => 'Initial unit creation',
                        'changed_at' => now()->subDays(rand(1, 30)),
                        'change_type' => UnitStatusHistory::CHANGE_TYPE_SYSTEM,
                    ]);
                    
                    // Create some status history for demonstration
                    if (rand(0, 1)) {
                        $previousStatus = $statuses[array_rand($statuses)];
                        if ($previousStatus !== $status) {
                            UnitStatusHistory::create([
                                'unit_id' => $unit->id,
                                'previous_status' => $previousStatus,
                                'new_status' => $status,
                                'changed_by' => 1,
                                'reason' => $this->generateStatusChangeReason($previousStatus, $status),
                                'changed_at' => now()->subDays(rand(1, 15)),
                                'change_type' => UnitStatusHistory::CHANGE_TYPE_MANUAL,
                            ]);
                        }
                    }
                }
            }
        }
        
        // Create some parking units
        for ($i = 1; $i <= 20; $i++) {
            $parkingNumber = 'P' . sprintf('%03d', $i);
            
            Unit::create([
                'unit_number' => $parkingNumber,
                'block' => 'Parking',
                'floor' => 'Ground',
                'type' => 'parking',
                'status' => rand(0, 1) ? 'occupied' : 'vacant',
                'market_rent' => rand(2000, 5000),
                'security_deposit' => rand(5000, 15000),
                'maintenance_charge' => rand(500, 1500),
                'description' => 'Covered parking space',
                'is_active' => true,
            ]);
        }
        
        // Create some commercial units
        for ($i = 1; $i <= 5; $i++) {
            $shopNumber = 'S' . sprintf('%02d', $i);
            
            Unit::create([
                'unit_number' => $shopNumber,
                'block' => 'Commercial',
                'floor' => 'Ground',
                'type' => 'commercial',
                'area_sqft' => rand(200, 800),
                'built_up_area' => rand(250, 900),
                'status' => ['vacant', 'to-let', 'rented'][array_rand(['vacant', 'to-let', 'rented'])],
                'market_rent' => rand(10000, 30000),
                'security_deposit' => rand(20000, 60000),
                'maintenance_charge' => rand(1000, 5000),
                'description' => 'Commercial shop space',
                'amenities' => ['separate_entrance', 'street_facing', 'parking_available'],
                'is_active' => true,
            ]);
        }
        
        $this->command->info('Created ' . Unit::count() . ' units with status history');
    }
    
    /**
     * Generate unit description
     */
    private function generateUnitDescription(): string
    {
        $descriptions = [
            'Spacious apartment with good ventilation and natural light',
            'Well-maintained unit with modern amenities',
            'Comfortable living space with excellent connectivity',
            'Prime location unit with all basic facilities',
            'Newly renovated apartment with contemporary fittings',
            'Family-friendly unit in a peaceful environment',
            'Bright and airy apartment with cross ventilation',
            'Premium unit with high-quality finishes',
        ];
        
        return $descriptions[array_rand($descriptions)];
    }
    
    /**
     * Generate amenities array
     */
    private function generateAmenities(): array
    {
        $allAmenities = [
            'parking',
            'balcony',
            'elevator_access',
            'security_system',
            'power_backup',
            'water_supply',
            'internet_ready',
            'gym_access',
            'swimming_pool',
            'garden_view',
            'playground',
            'club_house',
        ];
        
        // Select random 3-6 amenities
        $selectedAmenities = array_rand($allAmenities, rand(3, 6));
        if (!is_array($selectedAmenities)) {
            $selectedAmenities = [$selectedAmenities];
        }
        
        return array_map(function($index) use ($allAmenities) {
            return $allAmenities[$index];
        }, $selectedAmenities);
    }
    
    /**
     * Generate preferences array
     */
    private function generatePreferences(): array
    {
        $preferences = [];
        
        if (rand(0, 1)) {
            $preferences[] = 'family_only';
        }
        
        if (rand(0, 1)) {
            $preferences[] = 'no_pets';
        }
        
        if (rand(0, 1)) {
            $preferences[] = 'vegetarian_only';
        }
        
        if (rand(0, 1)) {
            $preferences[] = 'no_smoking';
        }
        
        return $preferences;
    }
    
    /**
     * Generate status change reason
     */
    private function generateStatusChangeReason(string $from, string $to): string
    {
        $reasons = [
            'occupied' => [
                'Owner moved in',
                'Tenant agreement completed',
                'Family relocated to the unit',
            ],
            'vacant' => [
                'Tenant moved out',
                'Owner vacated the property',
                'Agreement expired',
                'Unit renovation completed',
            ],
            'to-let' => [
                'Owner decided to rent out',
                'Unit available for rental',
                'Looking for suitable tenant',
            ],
            'rented' => [
                'New tenant moved in',
                'Rental agreement signed',
                'Tenant onboarding completed',
            ],
        ];
        
        $toReasons = $reasons[$to] ?? ['Status updated'];
        return $toReasons[array_rand($toReasons)];
    }
}
