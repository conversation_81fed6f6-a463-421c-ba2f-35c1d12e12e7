<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use App\Models\ListingPortal;

class ListingPortalSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $portals = [
            [
                'name' => '99acres',
                'display_name' => '99acres',
                'slug' => '99acres',
                'description' => 'One of India\'s leading property portals for buying, selling, and renting properties',
                'base_url' => 'https://api.99acres.com',
                'api_key' => null,
                'api_secret' => null,
                'username' => null,
                'password' => null,
                'api_config' => [
                    'endpoints' => [
                        'listings' => '/v1/listings',
                        'leads' => '/v1/leads',
                        'media' => '/v1/media',
                    ],
                    'rate_limits' => [
                        'requests_per_minute' => 60,
                        'requests_per_hour' => 1000,
                    ],
                ],
                'field_mapping' => [
                    'title' => 'property_title',
                    'description' => 'property_description',
                    'rent_amount' => 'rent_amount',
                    'deposit_amount' => 'security_deposit',
                    'bedrooms' => 'bedrooms',
                    'bathrooms' => 'bathrooms',
                    'area_sqft' => 'area_sqft',
                    'furnishing' => 'furnishing_status',
                    'available_from' => 'possession_date',
                ],
                'media_requirements' => [
                    'images' => [
                        'min_count' => 3,
                        'max_count' => 20,
                        'max_size_mb' => 5,
                        'formats' => ['jpg', 'jpeg', 'png'],
                        'dimensions' => [
                            'min_width' => 800,
                            'min_height' => 600,
                        ],
                    ],
                    'videos' => [
                        'max_count' => 3,
                        'max_size_mb' => 50,
                        'formats' => ['mp4', 'avi'],
                        'max_duration_seconds' => 300,
                    ],
                ],
                'is_active' => true,
                'requires_approval' => false,
                'rate_limit_per_hour' => 1000,
                'rate_limit_per_day' => 10000,
                'metadata' => [
                    'logo_url' => 'https://www.99acres.com/favicon.ico',
                    'website_url' => 'https://www.99acres.com',
                    'contact_email' => '<EMAIL>',
                ],
            ],
            [
                'name' => 'MagicBricks',
                'display_name' => 'MagicBricks',
                'slug' => 'magicbricks',
                'description' => 'India\'s leading real estate portal for property search and listings',
                'base_url' => 'https://api.magicbricks.com',
                'api_key' => null,
                'api_secret' => null,
                'username' => null,
                'password' => null,
                'api_config' => [
                    'endpoints' => [
                        'listings' => '/api/v1/properties',
                        'leads' => '/api/v1/enquiries',
                        'media' => '/api/v1/media',
                    ],
                    'rate_limits' => [
                        'requests_per_minute' => 50,
                        'requests_per_hour' => 800,
                    ],
                ],
                'field_mapping' => [
                    'title' => 'property_name',
                    'description' => 'property_description',
                    'rent_amount' => 'monthly_rent',
                    'deposit_amount' => 'deposit_amount',
                    'bedrooms' => 'bedrooms',
                    'bathrooms' => 'bathrooms',
                    'area_sqft' => 'carpet_area',
                    'furnishing' => 'furnishing',
                    'available_from' => 'available_from',
                ],
                'media_requirements' => [
                    'images' => [
                        'min_count' => 5,
                        'max_count' => 25,
                        'max_size_mb' => 3,
                        'formats' => ['jpg', 'jpeg', 'png'],
                        'dimensions' => [
                            'min_width' => 1024,
                            'min_height' => 768,
                        ],
                    ],
                    'videos' => [
                        'max_count' => 5,
                        'max_size_mb' => 100,
                        'formats' => ['mp4', 'mov'],
                        'max_duration_seconds' => 600,
                    ],
                ],
                'is_active' => true,
                'requires_approval' => true,
                'rate_limit_per_hour' => 800,
                'rate_limit_per_day' => 8000,
                'metadata' => [
                    'logo_url' => 'https://www.magicbricks.com/favicon.ico',
                    'website_url' => 'https://www.magicbricks.com',
                    'contact_email' => '<EMAIL>',
                ],
            ],
            [
                'name' => 'Housing.com',
                'display_name' => 'Housing.com',
                'slug' => 'housing',
                'description' => 'Modern property portal with advanced search and listing features',
                'base_url' => 'https://api.housing.com',
                'api_key' => null,
                'api_secret' => null,
                'username' => null,
                'password' => null,
                'api_config' => [
                    'endpoints' => [
                        'listings' => '/v2/properties',
                        'leads' => '/v2/enquiries',
                        'media' => '/v2/media',
                    ],
                    'rate_limits' => [
                        'requests_per_minute' => 40,
                        'requests_per_hour' => 600,
                    ],
                ],
                'field_mapping' => [
                    'title' => 'property_title',
                    'description' => 'property_details',
                    'rent_amount' => 'rent',
                    'deposit_amount' => 'deposit',
                    'bedrooms' => 'bedrooms',
                    'bathrooms' => 'bathrooms',
                    'area_sqft' => 'area',
                    'furnishing' => 'furnishing_type',
                    'available_from' => 'available_date',
                ],
                'media_requirements' => [
                    'images' => [
                        'min_count' => 4,
                        'max_count' => 30,
                        'max_size_mb' => 4,
                        'formats' => ['jpg', 'jpeg', 'png', 'webp'],
                        'dimensions' => [
                            'min_width' => 1200,
                            'min_height' => 800,
                        ],
                    ],
                    'videos' => [
                        'max_count' => 10,
                        'max_size_mb' => 200,
                        'formats' => ['mp4', 'webm'],
                        'max_duration_seconds' => 900,
                    ],
                ],
                'is_active' => true,
                'requires_approval' => false,
                'rate_limit_per_hour' => 600,
                'rate_limit_per_day' => 6000,
                'metadata' => [
                    'logo_url' => 'https://www.housing.com/favicon.ico',
                    'website_url' => 'https://www.housing.com',
                    'contact_email' => '<EMAIL>',
                ],
            ],
            [
                'name' => 'OLX Properties',
                'display_name' => 'OLX Properties',
                'slug' => 'olx-properties',
                'description' => 'OLX\'s dedicated property section for buying, selling, and renting properties',
                'base_url' => 'https://api.olx.in/properties',
                'api_key' => null,
                'api_secret' => null,
                'username' => null,
                'password' => null,
                'api_config' => [
                    'endpoints' => [
                        'listings' => '/v1/ads',
                        'leads' => '/v1/messages',
                        'media' => '/v1/media',
                    ],
                    'rate_limits' => [
                        'requests_per_minute' => 30,
                        'requests_per_hour' => 500,
                    ],
                ],
                'field_mapping' => [
                    'title' => 'ad_title',
                    'description' => 'ad_description',
                    'rent_amount' => 'price',
                    'deposit_amount' => 'deposit',
                    'bedrooms' => 'bedrooms',
                    'bathrooms' => 'bathrooms',
                    'area_sqft' => 'area',
                    'furnishing' => 'furnishing',
                    'available_from' => 'available_from',
                ],
                'media_requirements' => [
                    'images' => [
                        'min_count' => 1,
                        'max_count' => 15,
                        'max_size_mb' => 2,
                        'formats' => ['jpg', 'jpeg', 'png'],
                        'dimensions' => [
                            'min_width' => 800,
                            'min_height' => 600,
                        ],
                    ],
                    'videos' => [
                        'max_count' => 3,
                        'max_size_mb' => 50,
                        'formats' => ['mp4'],
                        'max_duration_seconds' => 180,
                    ],
                ],
                'is_active' => true,
                'requires_approval' => false,
                'rate_limit_per_hour' => 500,
                'rate_limit_per_day' => 5000,
                'metadata' => [
                    'logo_url' => 'https://www.olx.in/favicon.ico',
                    'website_url' => 'https://www.olx.in/properties',
                    'contact_email' => '<EMAIL>',
                ],
            ],
            [
                'name' => 'PropTiger',
                'display_name' => 'PropTiger',
                'slug' => 'proptiger',
                'description' => 'Comprehensive property portal with detailed listings and market insights',
                'base_url' => 'https://api.proptiger.com',
                'api_key' => null,
                'api_secret' => null,
                'username' => null,
                'password' => null,
                'api_config' => [
                    'endpoints' => [
                        'listings' => '/api/v1/properties',
                        'leads' => '/api/v1/enquiries',
                        'media' => '/api/v1/media',
                    ],
                    'rate_limits' => [
                        'requests_per_minute' => 35,
                        'requests_per_hour' => 700,
                    ],
                ],
                'field_mapping' => [
                    'title' => 'property_name',
                    'description' => 'property_description',
                    'rent_amount' => 'monthly_rent',
                    'deposit_amount' => 'security_deposit',
                    'bedrooms' => 'bedrooms',
                    'bathrooms' => 'bathrooms',
                    'area_sqft' => 'carpet_area',
                    'furnishing' => 'furnishing_status',
                    'available_from' => 'possession_date',
                ],
                'media_requirements' => [
                    'images' => [
                        'min_count' => 3,
                        'max_count' => 20,
                        'max_size_mb' => 3,
                        'formats' => ['jpg', 'jpeg', 'png'],
                        'dimensions' => [
                            'min_width' => 800,
                            'min_height' => 600,
                        ],
                    ],
                    'videos' => [
                        'max_count' => 5,
                        'max_size_mb' => 75,
                        'formats' => ['mp4'],
                        'max_duration_seconds' => 300,
                    ],
                ],
                'is_active' => true,
                'requires_approval' => true,
                'rate_limit_per_hour' => 700,
                'rate_limit_per_day' => 7000,
                'metadata' => [
                    'logo_url' => 'https://www.proptiger.com/favicon.ico',
                    'website_url' => 'https://www.proptiger.com',
                    'contact_email' => '<EMAIL>',
                ],
            ],
        ];

        foreach ($portals as $portalData) {
            ListingPortal::updateOrCreate(
                ['slug' => $portalData['slug']],
                $portalData
            );
        }

        $this->command->info('Listing portals seeded successfully!');
    }
}
