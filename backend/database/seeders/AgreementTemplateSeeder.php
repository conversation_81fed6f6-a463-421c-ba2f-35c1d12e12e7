<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use App\Models\AgreementTemplate;
use App\Models\User;
use Illuminate\Support\Facades\Storage;

class AgreementTemplateSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Get the first admin user or create one
        $adminUser = User::where('role', 'admin')->first() ?? User::first();

        if (!$adminUser) {
            $this->command->error('No users found. Please run UserSeeder first.');
            return;
        }

        $templates = [
            [
                'name' => 'Standard Leave & Licence Agreement',
                'description' => 'Standard rental agreement template for residential properties',
                'template_content' => $this->getStandardTemplate(),
                'placeholders' => [
                    'tenant_name',
                    'tenant_address',
                    'tenant_phone',
                    'tenant_email',
                    'owner_name',
                    'owner_address',
                    'owner_phone',
                    'unit_number',
                    'unit_type',
                    'society_name',
                    'society_address',
                    'rent_amount',
                    'security_deposit',
                    'start_date',
                    'end_date',
                    'agreement_date',
                    'witness_name',
                    'witness_address'
                ]
            ],
            [
                'name' => 'Commercial Leave & Licence Agreement',
                'description' => 'Commercial property rental agreement template',
                'template_content' => $this->getCommercialTemplate(),
                'placeholders' => [
                    'tenant_name',
                    'tenant_company',
                    'tenant_address',
                    'tenant_phone',
                    'tenant_email',
                    'owner_name',
                    'owner_address',
                    'owner_phone',
                    'unit_number',
                    'unit_type',
                    'society_name',
                    'society_address',
                    'rent_amount',
                    'security_deposit',
                    'start_date',
                    'end_date',
                    'agreement_date',
                    'business_type',
                    'witness_name',
                    'witness_address'
                ]
            ],
            [
                'name' => 'Short-term Leave & Licence Agreement',
                'description' => 'Short-term rental agreement template (3-6 months)',
                'template_content' => $this->getShortTermTemplate(),
                'placeholders' => [
                    'tenant_name',
                    'tenant_address',
                    'tenant_phone',
                    'tenant_email',
                    'owner_name',
                    'owner_address',
                    'owner_phone',
                    'unit_number',
                    'unit_type',
                    'society_name',
                    'society_address',
                    'rent_amount',
                    'security_deposit',
                    'start_date',
                    'end_date',
                    'agreement_date',
                    'witness_name',
                    'witness_address'
                ]
            ]
        ];

        foreach ($templates as $templateData) {
            // Create HTML file for the template
            $fileName = 'agreements/templates/' . strtolower(str_replace(' ', '_', $templateData['name'])) . '.html';
            Storage::put($fileName, $templateData['template_content']);

            AgreementTemplate::create([
                'name' => $templateData['name'],
                'description' => $templateData['description'],
                'template_file' => $fileName,
                'placeholders' => $templateData['placeholders'],
                'created_by' => $adminUser->id,
            ]);
        }

        $this->command->info('Agreement templates seeded successfully!');
    }

    private function getStandardTemplate(): string
    {
        return '<!DOCTYPE html>
<html>
<head>
    <title>Leave & Licence Agreement</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 40px; line-height: 1.6; }
        .header { text-align: center; margin-bottom: 30px; border-bottom: 2px solid #333; padding-bottom: 20px; }
        .content { margin: 20px 0; }
        .section { margin: 25px 0; }
        .section h3 { color: #333; border-bottom: 1px solid #ccc; padding-bottom: 5px; }
        .signature { margin-top: 50px; }
        .footer { margin-top: 30px; font-size: 12px; color: #666; }
        .highlight { background-color: #f0f0f0; padding: 10px; margin: 10px 0; }
        table { width: 100%; border-collapse: collapse; margin: 10px 0; }
        table, th, td { border: 1px solid #ddd; }
        th, td { padding: 8px; text-align: left; }
        th { background-color: #f2f2f2; }
    </style>
</head>
<body>
    <div class="header">
        <h1>LEAVE & LICENCE AGREEMENT</h1>
        <h2>For Residential Property</h2>
        <p><strong>{society_name}</strong></p>
        <p>{society_address}</p>
    </div>
    
    <div class="content">
        <div class="section">
            <h3>1. PARTIES</h3>
            <p><strong>LICENSOR (Owner):</strong></p>
            <p>Name: {owner_name}<br>
            Address: {owner_address}<br>
            Phone: {owner_phone}</p>
            
            <p><strong>LICENSEE (Tenant):</strong></p>
            <p>Name: {tenant_name}<br>
            Address: {tenant_address}<br>
            Phone: {tenant_phone}<br>
            Email: {tenant_email}</p>
        </div>
        
        <div class="section">
            <h3>2. PROPERTY DETAILS</h3>
            <table>
                <tr><th>Property Type</th><td>{unit_type}</td></tr>
                <tr><th>Unit Number</th><td>{unit_number}</td></tr>
                <tr><th>Society</th><td>{society_name}</td></tr>
                <tr><th>Address</th><td>{society_address}</td></tr>
            </table>
        </div>
        
        <div class="section">
            <h3>3. TERMS & CONDITIONS</h3>
            <div class="highlight">
                <p><strong>Licence Period:</strong> From {start_date} to {end_date}</p>
                <p><strong>Monthly Rent:</strong> ₹{rent_amount}</p>
                <p><strong>Security Deposit:</strong> ₹{security_deposit}</p>
            </div>
            
            <h4>3.1 Payment Terms</h4>
            <ul>
                <li>Rent is payable in advance on or before the 5th day of each month</li>
                <li>Late payment will attract a penalty of ₹500 per day</li>
                <li>Security deposit is refundable at the end of the licence period</li>
            </ul>
            
            <h4>3.2 Use of Property</h4>
            <ul>
                <li>The property shall be used only for residential purposes</li>
                <li>No commercial activities are permitted</li>
                <li>No structural modifications without written consent</li>
                <li>Maintenance of the property is the responsibility of the licensee</li>
            </ul>
            
            <h4>3.3 Society Rules</h4>
            <ul>
                <li>Licensee must follow all society rules and regulations</li>
                <li>No pets without prior written permission</li>
                <li>No loud music or disturbance to neighbors</li>
                <li>Proper disposal of waste and garbage</li>
            </ul>
        </div>
        
        <div class="section">
            <h3>4. TERMINATION</h3>
            <ul>
                <li>Either party can terminate with 30 days written notice</li>
                <li>Immediate termination for breach of terms</li>
                <li>Property must be vacated in the same condition as received</li>
                <li>Security deposit will be refunded after property inspection</li>
            </ul>
        </div>
        
        <div class="section">
            <h3>5. UTILITIES & MAINTENANCE</h3>
            <ul>
                <li>Licensee is responsible for utility payments (electricity, water, gas)</li>
                <li>Regular maintenance and cleaning is licensee\'s responsibility</li>
                <li>Major repairs will be handled by the licensor</li>
                <li>Licensee must report any damages immediately</li>
            </ul>
        </div>
    </div>
    
    <div class="signature">
        <p><strong>Agreement Date:</strong> {agreement_date}</p>
        <br><br>
        <table style="width: 100%; border: none;">
            <tr>
                <td style="border: none; width: 50%;">
                    <p>_________________________</p>
                    <p><strong>LICENSOR (Owner)</strong></p>
                    <p>Name: {owner_name}</p>
                </td>
                <td style="border: none; width: 50%;">
                    <p>_________________________</p>
                    <p><strong>LICENSEE (Tenant)</strong></p>
                    <p>Name: {tenant_name}</p>
                </td>
            </tr>
        </table>
        
        <br><br>
        <p><strong>WITNESS:</strong></p>
        <p>Name: {witness_name}<br>
        Address: {witness_address}</p>
        <p>_________________________</p>
    </div>
    
    <div class="footer">
        <p>This agreement is executed in duplicate, both parties retaining one copy each.</p>
        <p>Generated on: {agreement_date}</p>
    </div>
</body>
</html>';
    }

    private function getCommercialTemplate(): string
    {
        return '<!DOCTYPE html>
<html>
<head>
    <title>Commercial Leave & Licence Agreement</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 40px; line-height: 1.6; }
        .header { text-align: center; margin-bottom: 30px; border-bottom: 2px solid #333; padding-bottom: 20px; }
        .content { margin: 20px 0; }
        .section { margin: 25px 0; }
        .section h3 { color: #333; border-bottom: 1px solid #ccc; padding-bottom: 5px; }
        .signature { margin-top: 50px; }
        .footer { margin-top: 30px; font-size: 12px; color: #666; }
        .highlight { background-color: #f0f0f0; padding: 10px; margin: 10px 0; }
        table { width: 100%; border-collapse: collapse; margin: 10px 0; }
        table, th, td { border: 1px solid #ddd; }
        th, td { padding: 8px; text-align: left; }
        th { background-color: #f2f2f2; }
    </style>
</head>
<body>
    <div class="header">
        <h1>COMMERCIAL LEAVE & LICENCE AGREEMENT</h1>
        <h2>For Commercial Property</h2>
        <p><strong>{society_name}</strong></p>
        <p>{society_address}</p>
    </div>
    
    <div class="content">
        <div class="section">
            <h3>1. PARTIES</h3>
            <p><strong>LICENSOR (Owner):</strong></p>
            <p>Name: {owner_name}<br>
            Address: {owner_address}<br>
            Phone: {owner_phone}</p>
            
            <p><strong>LICENSEE (Business):</strong></p>
            <p>Company: {tenant_company}<br>
            Contact Person: {tenant_name}<br>
            Address: {tenant_address}<br>
            Phone: {tenant_phone}<br>
            Email: {tenant_email}</p>
        </div>
        
        <div class="section">
            <h3>2. PROPERTY DETAILS</h3>
            <table>
                <tr><th>Property Type</th><td>{unit_type}</td></tr>
                <tr><th>Unit Number</th><td>{unit_number}</td></tr>
                <tr><th>Society</th><td>{society_name}</td></tr>
                <tr><th>Address</th><td>{society_address}</td></tr>
                <tr><th>Business Type</th><td>{business_type}</td></tr>
            </table>
        </div>
        
        <div class="section">
            <h3>3. COMMERCIAL TERMS</h3>
            <div class="highlight">
                <p><strong>Licence Period:</strong> From {start_date} to {end_date}</p>
                <p><strong>Monthly Rent:</strong> ₹{rent_amount}</p>
                <p><strong>Security Deposit:</strong> ₹{security_deposit}</p>
            </div>
            
            <h4>3.1 Business Operations</h4>
            <ul>
                <li>Property shall be used only for {business_type} business</li>
                <li>No change in business type without written consent</li>
                <li>Compliance with all local business regulations required</li>
                <li>Business hours: 9:00 AM to 8:00 PM (unless otherwise specified)</li>
            </ul>
            
            <h4>3.2 Commercial Regulations</h4>
            <ul>
                <li>All necessary business licenses must be obtained</li>
                <li>Compliance with fire safety regulations</li>
                <li>Proper waste disposal for commercial activities</li>
                <li>No hazardous materials without prior approval</li>
            </ul>
            
            <h4>3.3 Payment Terms</h4>
            <ul>
                <li>Rent payable in advance on or before the 1st of each month</li>
                <li>Late payment penalty: ₹1000 per day</li>
                <li>Security deposit refundable after property inspection</li>
                <li>All utility payments are licensee\'s responsibility</li>
            </ul>
        </div>
        
        <div class="section">
            <h3>4. MAINTENANCE & REPAIRS</h3>
            <ul>
                <li>Licensee responsible for interior maintenance</li>
                <li>Structural repairs handled by licensor</li>
                <li>Regular cleaning and pest control required</li>
                <li>Immediate reporting of any damages</li>
            </ul>
        </div>
        
        <div class="section">
            <h3>5. TERMINATION</h3>
            <ul>
                <li>60 days written notice required for termination</li>
                <li>Immediate termination for regulatory violations</li>
                <li>Property must be restored to original condition</li>
                <li>All business equipment must be removed</li>
            </ul>
        </div>
    </div>
    
    <div class="signature">
        <p><strong>Agreement Date:</strong> {agreement_date}</p>
        <br><br>
        <table style="width: 100%; border: none;">
            <tr>
                <td style="border: none; width: 50%;">
                    <p>_________________________</p>
                    <p><strong>LICENSOR (Owner)</strong></p>
                    <p>Name: {owner_name}</p>
                </td>
                <td style="border: none; width: 50%;">
                    <p>_________________________</p>
                    <p><strong>LICENSEE (Business)</strong></p>
                    <p>Company: {tenant_company}</p>
                    <p>Authorized Signatory: {tenant_name}</p>
                </td>
            </tr>
        </table>
        
        <br><br>
        <p><strong>WITNESS:</strong></p>
        <p>Name: {witness_name}<br>
        Address: {witness_address}</p>
        <p>_________________________</p>
    </div>
    
    <div class="footer">
        <p>This agreement is executed in duplicate, both parties retaining one copy each.</p>
        <p>Generated on: {agreement_date}</p>
    </div>
</body>
</html>';
    }

    private function getShortTermTemplate(): string
    {
        return '<!DOCTYPE html>
<html>
<head>
    <title>Short-term Leave & Licence Agreement</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 40px; line-height: 1.6; }
        .header { text-align: center; margin-bottom: 30px; border-bottom: 2px solid #333; padding-bottom: 20px; }
        .content { margin: 20px 0; }
        .section { margin: 25px 0; }
        .section h3 { color: #333; border-bottom: 1px solid #ccc; padding-bottom: 5px; }
        .signature { margin-top: 50px; }
        .footer { margin-top: 30px; font-size: 12px; color: #666; }
        .highlight { background-color: #f0f0f0; padding: 10px; margin: 10px 0; }
        table { width: 100%; border-collapse: collapse; margin: 10px 0; }
        table, th, td { border: 1px solid #ddd; }
        th, td { padding: 8px; text-align: left; }
        th { background-color: #f2f2f2; }
    </style>
</head>
<body>
    <div class="header">
        <h1>SHORT-TERM LEAVE & LICENCE AGREEMENT</h1>
        <h2>For Temporary Residential Use</h2>
        <p><strong>{society_name}</strong></p>
        <p>{society_address}</p>
    </div>
    
    <div class="content">
        <div class="section">
            <h3>1. PARTIES</h3>
            <p><strong>LICENSOR (Owner):</strong></p>
            <p>Name: {owner_name}<br>
            Address: {owner_address}<br>
            Phone: {owner_phone}</p>
            
            <p><strong>LICENSEE (Tenant):</strong></p>
            <p>Name: {tenant_name}<br>
            Address: {tenant_address}<br>
            Phone: {tenant_phone}<br>
            Email: {tenant_email}</p>
        </div>
        
        <div class="section">
            <h3>2. PROPERTY DETAILS</h3>
            <table>
                <tr><th>Property Type</th><td>{unit_type}</td></tr>
                <tr><th>Unit Number</th><td>{unit_number}</td></tr>
                <tr><th>Society</th><td>{society_name}</td></tr>
                <tr><th>Address</th><td>{society_address}</td></tr>
            </table>
        </div>
        
        <div class="section">
            <h3>3. SHORT-TERM TERMS</h3>
            <div class="highlight">
                <p><strong>Licence Period:</strong> From {start_date} to {end_date}</p>
                <p><strong>Monthly Rent:</strong> ₹{rent_amount}</p>
                <p><strong>Security Deposit:</strong> ₹{security_deposit}</p>
                <p><strong>Note:</strong> This is a short-term agreement for temporary use</p>
            </div>
            
            <h4>3.1 Payment Terms</h4>
            <ul>
                <li>Rent is payable in advance for the entire period</li>
                <li>Security deposit is required before move-in</li>
                <li>No monthly payments - full payment upfront</li>
                <li>Refund of unused days if early termination</li>
            </ul>
            
            <h4>3.2 Use Restrictions</h4>
            <ul>
                <li>Property for residential use only</li>
                <li>No commercial activities permitted</li>
                <li>No pets without prior written consent</li>
                <li>Maximum occupancy: 4 persons</li>
                <li>No subletting or assignment</li>
            </ul>
            
            <h4>3.3 Maintenance</h4>
            <ul>
                <li>Licensee responsible for daily cleaning</li>
                <li>No structural modifications</li>
                <li>Report any damages immediately</li>
                <li>Property must be returned in same condition</li>
            </ul>
        </div>
        
        <div class="section">
            <h3>4. EARLY TERMINATION</h3>
            <ul>
                <li>7 days written notice required for early termination</li>
                <li>Pro-rated refund for unused days</li>
                <li>Security deposit refunded after inspection</li>
                <li>Immediate termination for rule violations</li>
            </ul>
        </div>
        
        <div class="section">
            <h3>5. UTILITIES</h3>
            <ul>
                <li>Licensee responsible for all utility payments</li>
                <li>Meter readings to be taken at start and end</li>
                <li>Final utility bills must be settled before departure</li>
                <li>No outstanding bills at time of termination</li>
            </ul>
        </div>
    </div>
    
    <div class="signature">
        <p><strong>Agreement Date:</strong> {agreement_date}</p>
        <br><br>
        <table style="width: 100%; border: none;">
            <tr>
                <td style="border: none; width: 50%;">
                    <p>_________________________</p>
                    <p><strong>LICENSOR (Owner)</strong></p>
                    <p>Name: {owner_name}</p>
                </td>
                <td style="border: none; width: 50%;">
                    <p>_________________________</p>
                    <p><strong>LICENSEE (Tenant)</strong></p>
                    <p>Name: {tenant_name}</p>
                </td>
            </tr>
        </table>
        
        <br><br>
        <p><strong>WITNESS:</strong></p>
        <p>Name: {witness_name}<br>
        Address: {witness_address}</p>
        <p>_________________________</p>
    </div>
    
    <div class="footer">
        <p>This is a short-term agreement valid only for the specified period.</p>
        <p>Generated on: {agreement_date}</p>
    </div>
</body>
</html>';
    }
} 