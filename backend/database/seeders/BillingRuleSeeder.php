<?php

namespace Database\Seeders;

use App\Models\BillingRule;
use App\Models\User;
use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;

class BillingRuleSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $adminUser = User::where('role', 'admin')->first();

        if (!$adminUser) {
            $adminUser = User::factory()->create(['role' => 'admin']);
        }

        $billingRules = [
            [
                'name' => 'Monthly Maintenance Charges',
                'type' => 'maintenance',
                'calculation_method' => 'per_sqft',
                'amount' => 5.50,
                'description' => 'Monthly maintenance charges calculated per square foot of unit area',
                'is_active' => true,
                'applies_to' => 'all_units',
                'frequency' => 'monthly',
                'due_day' => 5,
                'late_fee_amount' => 100,
                'late_fee_days' => 7,
                'created_by' => $adminUser->id,
                'metadata' => [],
            ],
            [
                'name' => 'Parking Charges',
                'type' => 'parking',
                'calculation_method' => 'fixed',
                'amount' => 500,
                'description' => 'Monthly parking charges for assigned parking spots',
                'is_active' => true,
                'applies_to' => 'specific_units',
                'frequency' => 'monthly',
                'due_day' => 5,
                'late_fee_amount' => 50,
                'late_fee_days' => 7,
                'created_by' => $adminUser->id,
                'metadata' => [
                    'specific_unit_ids' => [11, 12, 13, 14, 15]
                ],
            ],
            [
                'name' => 'Utility Charges',
                'type' => 'utilities',
                'calculation_method' => 'per_unit',
                'amount' => 1500,
                'description' => 'Monthly utility charges including electricity, water, and gas',
                'is_active' => true,
                'applies_to' => 'all_units',
                'frequency' => 'monthly',
                'due_day' => 10,
                'late_fee_amount' => 75,
                'late_fee_days' => 5,
                'created_by' => $adminUser->id,
                'metadata' => [],
            ],
            [
                'name' => 'Non-Occupancy Charges',
                'type' => 'non_occupancy',
                'calculation_method' => 'percentage',
                'amount' => 15,
                'description' => 'Non-occupancy charges for units that are rented out or vacant',
                'is_active' => true,
                'applies_to' => 'all_units',
                'frequency' => 'monthly',
                'due_day' => 1,
                'late_fee_amount' => 200,
                'late_fee_days' => 10,
                'created_by' => $adminUser->id,
                'metadata' => [],
            ],
            [
                'name' => 'NOC Processing Fee',
                'type' => 'noc_charges',
                'calculation_method' => 'fixed',
                'amount' => 250,
                'description' => 'Processing fee for No Objection Certificate applications',
                'is_active' => true,
                'applies_to' => 'all_units',
                'frequency' => 'one_time',
                'due_day' => 1,
                'late_fee_amount' => 25,
                'late_fee_days' => 3,
                'created_by' => $adminUser->id,
                'metadata' => [],
            ],
        ];

        foreach ($billingRules as $ruleData) {
            BillingRule::create($ruleData);
        }

        // Create some additional random billing rules
        BillingRule::factory()->count(5)->create([
            'created_by' => $adminUser->id,
        ]);
    }
}
