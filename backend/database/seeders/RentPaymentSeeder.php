<?php

namespace Database\Seeders;

use App\Models\RentPayment;
use App\Models\Unit;
use App\Models\User;
use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;

class RentPaymentSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $units = Unit::with('currentTenant')->whereNotNull('current_tenant_id')->take(10)->get();

        if ($units->isEmpty()) {
            $this->command->warn('No units with tenants found. Please run UnitSeeder and TenantSeeder first.');
            return;
        }

        foreach ($units as $unit) {
            // Create 6-12 payment records per unit for different months
            $paymentCount = rand(6, 12);

            for ($i = 0; $i < $paymentCount; $i++) {
                $paymentDate = now()->subMonths($i)->subDays(rand(1, 28));
                $amount = $unit->market_rent ?? rand(15000, 35000);

                // Add some variation to the amount
                $amount += rand(-2000, 2000);

                $status = $this->determineStatus($paymentDate);
                $paymentMethod = $this->getRandomPaymentMethod();

                RentPayment::create([
                    'unit_id' => $unit->id,
                    'tenant_id' => $unit->current_tenant_id,
                    'amount' => $amount,
                    'payment_date' => $paymentDate,
                    'payment_method' => $paymentMethod,
                    'external_reference' => $status === 'completed' ? 'REF' . rand(100000, 999999) : null,
                    'status' => $status,
                    'metadata' => [
                        'rent_month' => $paymentDate->format('Y-m'),
                        'late_fee' => rand(0, 1) ? rand(100, 500) : 0,
                    ],
                    'notes' => $this->generateNotes($status, $paymentMethod),
                    'reconciled_at' => $status === 'completed' ? $paymentDate->copy()->addDays(rand(1, 3)) : null,
                ]);
            }
        }

        // Create some additional random payment records
        // RentPayment::factory()->count(30)->create();
    }

    private function determineStatus($paymentDate): string
    {
        $now = now();
        $daysDiff = $paymentDate->diffInDays($now);

        if ($daysDiff > 60) {
            return collect(['completed', 'completed', 'completed', 'failed'])->random();
        } elseif ($daysDiff > 30) {
            return collect(['completed', 'completed', 'pending', 'failed'])->random();
        } else {
            return collect(['completed', 'pending', 'failed', 'cancelled'])->random();
        }
    }

    private function getRandomPaymentMethod(): string
    {
        return collect([
            'bank_transfer',
            'upi',
            'cash',
            'cheque',
            'online',
            'neft',
            'rtgs',
            'imps'
        ])->random();
    }

    private function generateNotes(string $status, string $paymentMethod): ?string
    {
        $notes = [
            'completed' => [
                "Payment received via {$paymentMethod}",
                "Monthly rent payment - {$paymentMethod}",
                "Rent payment processed successfully",
                "Payment cleared - {$paymentMethod}",
            ],
            'pending' => [
                "Payment initiated via {$paymentMethod}",
                "Awaiting payment confirmation",
                "Payment in process",
                "Pending verification",
            ],
            'failed' => [
                "Payment failed - insufficient funds",
                "Transaction declined by bank",
                "Payment gateway error",
                "Failed payment - retry required",
            ],
            'cancelled' => [
                "Payment cancelled by tenant",
                "Transaction cancelled",
                "Payment request cancelled",
                "Cancelled due to dispute",
            ],
        ];

        return collect($notes[$status] ?? [])->random();
    }
}
