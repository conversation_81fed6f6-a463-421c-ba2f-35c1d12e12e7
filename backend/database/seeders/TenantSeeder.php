<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use App\Models\Tenant;
use App\Models\User;
use App\Models\Unit;
use Carbon\Carbon;

class TenantSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Get tenant users (role = tenant)
        $tenantUsers = User::where('role', User::ROLE_TENANT)->get();
        
        // Get available units (status = rented or to-let)
        $availableUnits = Unit::whereIn('status', ['rented', 'to-let'])->with('owner')->get();
        
        if ($tenantUsers->isEmpty() || $availableUnits->isEmpty()) {
            $this->command->warn('No tenant users or available units found. Please seed users and units first.');
            return;
        }

        $kycStatuses = [
            Tenant::KYC_PENDING,
            Tenant::KYC_SUBMITTED,
            Tenant::KYC_VERIFIED,
            Tenant::KYC_REJECTED,
        ];

        $behaviorRatings = [
            Tenant::BEHAVIOR_EXCELLENT,
            Tenant::BEHAVIOR_GOOD,
            Tenant::BEHAVIOR_AVERAGE,
            Tenant::BEHAVIOR_POOR,
        ];

        $occupations = [
            'Software Engineer',
            'Marketing Manager',
            'Financial Analyst',
            'Teacher',
            'Doctor',
            'Architect',
            'Consultant',
            'Sales Executive',
        ];

        $companies = [
            'Tech Solutions Pvt Ltd',
            'Global Marketing Inc',
            'Financial Services Co',
            'Education Institute',
            'Healthcare Center',
            'Design Studio',
            'Business Consulting',
            'Sales Corporation',
        ];

        $tenantCount = 0;
        foreach ($tenantUsers as $index => $tenantUser) {
            if ($tenantCount >= count($availableUnits)) {
                break;
            }

            $unit = $availableUnits[$tenantCount];
            $kycStatus = $kycStatuses[array_rand($kycStatuses)];
            $behaviorRating = $behaviorRatings[array_rand($behaviorRatings)];
            $occupation = $occupations[array_rand($occupations)];
            $company = $companies[array_rand($companies)];

            // Create tenant record
            $tenant = Tenant::create([
                'user_id' => $tenantUser->id,
                'unit_id' => $unit->id,
                'owner_id' => $unit->owner_id,
                'occupation' => $occupation,
                'company_name' => $company,
                'company_address' => 'Company Address ' . ($index + 1) . ', Business District, Mumbai',
                'family_members' => rand(1, 5),
                'family_details' => [
                    [
                        'name' => 'Family Member 1',
                        'relationship' => 'Spouse',
                        'age' => rand(25, 45),
                    ],
                    [
                        'name' => 'Family Member 2',
                        'relationship' => 'Child',
                        'age' => rand(5, 18),
                    ],
                ],
                'monthly_income' => rand(50000, 200000),
                'security_deposit' => rand(50000, 150000),
                'advance_amount' => rand(10000, 50000),
                'move_in_date' => Carbon::now()->subDays(rand(30, 365)),
                'agreement_start_date' => Carbon::now()->subDays(rand(30, 365)),
                'agreement_end_date' => Carbon::now()->addDays(rand(365, 730)),
                'agreement_type' => 'leave_and_license',
                'kyc_status' => $kycStatus,
                'kyc_submitted_at' => $kycStatus !== Tenant::KYC_PENDING ? Carbon::now()->subDays(rand(1, 30)) : null,
                'kyc_verified_at' => $kycStatus === Tenant::KYC_VERIFIED ? Carbon::now()->subDays(rand(1, 15)) : null,
                'kyc_verified_by' => $kycStatus === Tenant::KYC_VERIFIED ? 1 : null, // System admin
                'kyc_rejection_reason' => $kycStatus === Tenant::KYC_REJECTED ? 'Incomplete documents' : null,
                'background_check_done' => rand(0, 1),
                'background_check_status' => rand(0, 1) ? Tenant::BACKGROUND_CHECK_PASSED : Tenant::BACKGROUND_CHECK_PENDING,
                'background_check_date' => Carbon::now()->subDays(rand(1, 60)),
                'background_check_notes' => 'Background check completed successfully',
                'police_verification_required' => rand(0, 1),
                'police_verification_status' => rand(0, 1) ? Tenant::POLICE_VERIFICATION_VERIFIED : Tenant::POLICE_VERIFICATION_PENDING,
                'police_verification_date' => Carbon::now()->subDays(rand(1, 45)),
                'police_verification_notes' => 'Police verification completed',
                'references' => [
                    [
                        'name' => 'Reference 1',
                        'phone' => '+91-9876543' . str_pad(rand(100, 999), 3, '0', STR_PAD_LEFT),
                        'relationship' => 'Previous Landlord',
                        'address' => 'Reference Address 1',
                    ],
                    [
                        'name' => 'Reference 2',
                        'phone' => '+91-9876543' . str_pad(rand(100, 999), 3, '0', STR_PAD_LEFT),
                        'relationship' => 'Colleague',
                        'address' => 'Reference Address 2',
                    ],
                ],
                'status' => Tenant::STATUS_ACTIVE,
                'behavior_rating' => $behaviorRating,
                'behavior_notes' => 'Tenant behavior is ' . $behaviorRating,
                'preferences' => [
                    'communication_method' => 'email',
                    'maintenance_time' => 'morning',
                    'contact_preference' => 'phone',
                ],
                'special_requirements' => 'No special requirements',
                'admin_notes' => 'Sample tenant data created by seeder',
            ]);

            // Update unit status to rented and set current tenant
            $unit->update([
                'status' => 'rented',
                'current_tenant_id' => $tenantUser->id,
            ]);

            $tenantCount++;
        }

        $this->command->info('Created ' . $tenantCount . ' tenant records');
    }
}
