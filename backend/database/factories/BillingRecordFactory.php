<?php

namespace Database\Factories;

use App\Models\BillingRecord;
use App\Models\BillingRule;
use App\Models\Unit;
use App\Models\User;
use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\BillingRecord>
 */
class BillingRecordFactory extends Factory
{
    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        $chargeTypes = ['maintenance', 'utilities', 'parking', 'non_occupancy', 'noc_charges'];
        $statuses = ['pending', 'paid', 'overdue', 'cancelled'];
        $paymentMethods = ['cash', 'bank_transfer', 'upi', 'cheque', 'online'];

        $chargeType = $this->faker->randomElement($chargeTypes);
        $status = $this->faker->randomElement($statuses);

        // Generate period dates
        $periodStart = $this->faker->dateTimeBetween('-6 months', 'now');
        $periodEnd = (clone $periodStart)->modify('+1 month');
        $dueDate = (clone $periodEnd)->modify('+5 days');

        // Set amount based on charge type
        $amount = match ($chargeType) {
            'maintenance' => $this->faker->numberBetween(2000, 8000),
            'utilities' => $this->faker->numberBetween(500, 3000),
            'parking' => $this->faker->numberBetween(300, 1000),
            'non_occupancy' => $this->faker->numberBetween(3000, 10000),
            'noc_charges' => $this->faker->numberBetween(100, 500),
            default => $this->faker->numberBetween(500, 2000),
        };

        return [
            'unit_id' => Unit::factory(),
            'tenant_id' => User::factory(),
            'billing_rule_id' => BillingRule::factory(),
            'charge_type' => $chargeType,
            'amount' => $amount,
            'period_start' => $periodStart,
            'period_end' => $periodEnd,
            'due_date' => $dueDate,
            'status' => $status,
            'breakdown' => $this->generateBreakdown($chargeType, $amount),
            'notes' => $this->faker->optional()->sentence(),
            'paid_date' => $status === 'paid' ? $this->faker->dateTimeBetween($dueDate, 'now') : null,
            'payment_method' => $status === 'paid' ? $this->faker->randomElement($paymentMethods) : null,
            'transaction_id' => $status === 'paid' ? 'TXN' . $this->faker->unique()->numerify('########') : null,
            'created_by' => User::factory(),
            'metadata' => [
                'auto_generated' => $this->faker->boolean(),
                'late_fee_applied' => $this->faker->boolean(20),
            ],
        ];
    }

    /**
     * Generate breakdown based on charge type.
     */
    private function generateBreakdown(string $chargeType, float $amount): array
    {
        return match ($chargeType) {
            'maintenance' => [
                'base_amount' => $amount * 0.8,
                'service_charge' => $amount * 0.15,
                'tax' => $amount * 0.05,
            ],
            'utilities' => [
                'electricity' => $amount * 0.6,
                'water' => $amount * 0.3,
                'gas' => $amount * 0.1,
            ],
            'parking' => [
                'monthly_fee' => $amount,
            ],
            'non_occupancy' => [
                'base_charge' => $amount * 0.9,
                'penalty' => $amount * 0.1,
            ],
            'noc_charges' => [
                'processing_fee' => $amount * 0.7,
                'documentation' => $amount * 0.3,
            ],
            default => [
                'amount' => $amount,
            ],
        };
    }

    /**
     * Indicate that the billing record is pending.
     */
    public function pending(): static
    {
        return $this->state(fn (array $attributes) => [
            'status' => 'pending',
            'paid_date' => null,
            'payment_method' => null,
            'transaction_id' => null,
        ]);
    }

    /**
     * Indicate that the billing record is paid.
     */
    public function paid(): static
    {
        return $this->state(fn (array $attributes) => [
            'status' => 'paid',
            'paid_date' => $this->faker->dateTimeBetween('-1 month', 'now'),
            'payment_method' => $this->faker->randomElement(['cash', 'bank_transfer', 'upi', 'cheque', 'online']),
            'transaction_id' => 'TXN' . $this->faker->unique()->numerify('########'),
        ]);
    }

    /**
     * Indicate that the billing record is overdue.
     */
    public function overdue(): static
    {
        return $this->state(fn (array $attributes) => [
            'status' => 'overdue',
            'due_date' => $this->faker->dateTimeBetween('-2 months', '-1 week'),
            'paid_date' => null,
            'payment_method' => null,
            'transaction_id' => null,
        ]);
    }
}
