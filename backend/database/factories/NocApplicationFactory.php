<?php

namespace Database\Factories;

use App\Models\NocApplication;
use App\Models\User;
use App\Models\Unit;
use App\Models\NocTemplate;
use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\NocApplication>
 */
class NocApplicationFactory extends Factory
{
    protected $model = NocApplication::class;

    public function definition(): array
    {
        $nocTypes = ['rental', 'residence', 'vehicle', 'renovation', 'transfer'];
        $nocType = $this->faker->randomElement($nocTypes);
        $startDate = $this->faker->dateTimeBetween('-1 month', '+1 month');
        $endDate = (clone $startDate)->modify('+6 months');

        // Create user first
        $user = User::factory()->create(['role' => 'tenant']);
        
        // Create unit
        $unit = Unit::factory()->create();
        
        // Create template
        $template = NocTemplate::factory()->create(['noc_type' => $nocType]);

        return [
            'applicant_id' => $user->id,
            'unit_id' => $unit->id,
            'template_id' => $template->id,
            'noc_type' => $nocType,
            'purpose' => $this->faker->sentence(6),
            'form_data' => [
                'field1' => $this->faker->word(),
                'field2' => $this->faker->word(),
            ],
            'status' => 'draft',
            'documents' => [],
            'history' => [
                ['action' => 'created', 'by' => $user->id, 'at' => now()]
            ],
            'remarks' => null,
            'start_date' => $startDate,
            'end_date' => $endDate,
        ];
    }
} 