<?php

namespace Database\Factories;

use App\Models\Notification;
use App\Models\User;
use App\Models\NotificationTemplate;
use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\Notification>
 */
class NotificationFactory extends Factory
{
    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        return [
            'user_id' => User::factory(),
            'type' => $this->faker->randomElement(Notification::getTypes()),
            'channel' => $this->faker->randomElement(Notification::getChannels()),
            'title' => $this->faker->sentence(),
            'message' => $this->faker->paragraph(),
            'data' => [
                'action' => $this->faker->word(),
                'entity_id' => $this->faker->numberBetween(1, 100),
                'entity_type' => $this->faker->word(),
            ],
            'status' => $this->faker->randomElement(Notification::getStatuses()),
            'priority' => $this->faker->randomElement(Notification::getPriorities()),
            'is_scheduled' => false,
            'scheduled_at' => null,
            'sent_at' => null,
            'delivered_at' => null,
            'read_at' => null,
            'delivery_log' => [],
            'metadata' => [],
        ];
    }

    /**
     * Indicate that the notification is pending.
     */
    public function pending(): static
    {
        return $this->state(fn (array $attributes) => [
            'status' => Notification::STATUS_PENDING,
            'sent_at' => null,
            'delivered_at' => null,
        ]);
    }

    /**
     * Indicate that the notification is sent.
     */
    public function sent(): static
    {
        return $this->state(fn (array $attributes) => [
            'status' => Notification::STATUS_SENT,
            'sent_at' => $this->faker->dateTimeBetween('-1 hour', 'now'),
            'delivered_at' => null,
        ]);
    }

    /**
     * Indicate that the notification is delivered.
     */
    public function delivered(): static
    {
        return $this->state(fn (array $attributes) => [
            'status' => Notification::STATUS_DELIVERED,
            'sent_at' => $this->faker->dateTimeBetween('-1 hour', '-30 minutes'),
            'delivered_at' => $this->faker->dateTimeBetween('-30 minutes', 'now'),
        ]);
    }

    /**
     * Indicate that the notification is failed.
     */
    public function failed(): static
    {
        return $this->state(fn (array $attributes) => [
            'status' => Notification::STATUS_FAILED,
            'sent_at' => null,
            'delivered_at' => null,
            'delivery_log' => [
                [
                    'status' => Notification::STATUS_FAILED,
                    'error' => 'Provider unavailable',
                    'timestamp' => now()->toISOString(),
                ]
            ],
        ]);
    }

    /**
     * Indicate that the notification is read.
     */
    public function read(): static
    {
        return $this->state(fn (array $attributes) => [
            'read_at' => $this->faker->dateTimeBetween('-1 hour', 'now'),
        ]);
    }

    /**
     * Indicate that the notification is unread.
     */
    public function unread(): static
    {
        return $this->state(fn (array $attributes) => [
            'read_at' => null,
        ]);
    }

    /**
     * Indicate that the notification is scheduled.
     */
    public function scheduled(): static
    {
        return $this->state(fn (array $attributes) => [
            'is_scheduled' => true,
            'scheduled_at' => $this->faker->dateTimeBetween('now', '+1 week'),
            'status' => Notification::STATUS_PENDING,
        ]);
    }

    /**
     * Indicate that the notification is high priority.
     */
    public function highPriority(): static
    {
        return $this->state(fn (array $attributes) => [
            'priority' => Notification::PRIORITY_HIGH,
        ]);
    }

    /**
     * Indicate that the notification is urgent priority.
     */
    public function urgentPriority(): static
    {
        return $this->state(fn (array $attributes) => [
            'priority' => Notification::PRIORITY_URGENT,
        ]);
    }

    /**
     * Indicate that the notification is an email.
     */
    public function email(): static
    {
        return $this->state(fn (array $attributes) => [
            'type' => Notification::TYPE_EMAIL,
            'channel' => Notification::CHANNEL_EMAIL,
        ]);
    }

    /**
     * Indicate that the notification is an SMS.
     */
    public function sms(): static
    {
        return $this->state(fn (array $attributes) => [
            'type' => Notification::TYPE_SMS,
            'channel' => Notification::CHANNEL_SMS,
        ]);
    }

    /**
     * Indicate that the notification is a push notification.
     */
    public function push(): static
    {
        return $this->state(fn (array $attributes) => [
            'type' => Notification::TYPE_PUSH,
            'channel' => Notification::CHANNEL_PUSH,
        ]);
    }

    /**
     * Indicate that the notification is an in-app notification.
     */
    public function inApp(): static
    {
        return $this->state(fn (array $attributes) => [
            'type' => Notification::TYPE_IN_APP,
            'channel' => Notification::CHANNEL_IN_APP,
        ]);
    }

    /**
     * Indicate that the notification has a template.
     */
    public function withTemplate(): static
    {
        return $this->state(fn (array $attributes) => [
            'template_id' => NotificationTemplate::factory(),
        ]);
    }

    /**
     * Indicate that the notification is for system events.
     */
    public function system(): static
    {
        return $this->state(fn (array $attributes) => [
            'title' => 'System Notification',
            'message' => 'A system event has occurred.',
            'data' => [
                'event_type' => 'system_maintenance',
                'scheduled_time' => now()->addHours(2)->toISOString(),
            ],
        ]);
    }

    /**
     * Indicate that the notification is for tenant events.
     */
    public function tenant(): static
    {
        return $this->state(fn (array $attributes) => [
            'title' => 'Tenant Update',
            'message' => 'Your tenant information has been updated.',
            'data' => [
                'tenant_id' => $this->faker->numberBetween(1, 100),
                'update_type' => 'profile_update',
            ],
        ]);
    }

    /**
     * Indicate that the notification is for unit events.
     */
    public function unit(): static
    {
        return $this->state(fn (array $attributes) => [
            'title' => 'Unit Status Change',
            'message' => 'The status of your unit has changed.',
            'data' => [
                'unit_id' => $this->faker->numberBetween(1, 100),
                'unit_number' => $this->faker->bothify('A-###'),
                'previous_status' => 'available',
                'new_status' => 'occupied',
            ],
        ]);
    }

    /**
     * Indicate that the notification is for enquiry events.
     */
    public function enquiry(): static
    {
        return $this->state(fn (array $attributes) => [
            'title' => 'New Enquiry',
            'message' => 'A new enquiry has been received for your unit.',
            'data' => [
                'enquiry_id' => $this->faker->numberBetween(1, 100),
                'unit_id' => $this->faker->numberBetween(1, 100),
                'enquirer_name' => $this->faker->name(),
                'enquiry_type' => 'rental',
            ],
        ]);
    }

    /**
     * Indicate that the notification is for NOC events.
     */
    public function noc(): static
    {
        return $this->state(fn (array $attributes) => [
            'title' => 'NOC Application Update',
            'message' => 'Your NOC application status has been updated.',
            'data' => [
                'noc_id' => $this->faker->numberBetween(1, 100),
                'noc_type' => 'rental',
                'previous_status' => 'submitted',
                'new_status' => 'approved',
            ],
        ]);
    }

    /**
     * Indicate that the notification is for agreement events.
     */
    public function agreement(): static
    {
        return $this->state(fn (array $attributes) => [
            'title' => 'Agreement Update',
            'message' => 'Your rental agreement has been updated.',
            'data' => [
                'agreement_id' => $this->faker->numberBetween(1, 100),
                'agreement_number' => $this->faker->bothify('AGR-####-###'),
                'action' => 'renewal_reminder',
                'due_date' => now()->addDays(30)->toISOString(),
            ],
        ]);
    }
} 