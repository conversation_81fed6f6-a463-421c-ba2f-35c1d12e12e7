<?php

namespace Database\Factories;

use App\Models\Document;
use App\Models\User;
use Illuminate\Database\Eloquent\Factories\Factory;

class DocumentFactory extends Factory
{
    protected $model = Document::class;

    public function definition(): array
    {
        $types = [
            Document::TYPE_ID_PROOF,
            Document::TYPE_ADDRESS_PROOF,
            Document::TYPE_INCOME_PROOF,
            Document::TYPE_PHOTO,
            Document::TYPE_AGREEMENT,
            Document::TYPE_NOC,
            Document::TYPE_POLICE_VERIFICATION,
            Document::TYPE_BACKGROUND_CHECK,
            Document::TYPE_REFERENCE_LETTER,
            Document::TYPE_BANK_STATEMENT,
            Document::TYPE_SALARY_SLIP,
            Document::TYPE_OTHER,
        ];

        $statuses = [
            Document::STATUS_PENDING,
            Document::STATUS_VERIFIED,
            Document::STATUS_REJECTED,
            Document::STATUS_EXPIRED,
        ];

        $visibilities = [
            Document::VISIBILITY_PUBLIC,
            Document::VISIBILITY_PRIVATE,
            Document::VISIBILITY_ADMIN_ONLY,
        ];

        $fileTypes = ['pdf', 'jpg', 'png', 'doc', 'docx'];
        $mimeTypes = [
            'application/pdf',
            'image/jpeg',
            'image/png',
            'application/msword',
            'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
        ];

        return [
            'title' => $this->faker->sentence(3),
            'description' => $this->faker->optional()->paragraph(),
            'type' => $this->faker->randomElement($types),
            'category' => $this->faker->optional()->word(),
            'documentable_id' => $this->faker->numberBetween(1, 100),
            'documentable_type' => $this->faker->randomElement(['App\\Models\\User', 'App\\Models\\Tenant', 'App\\Models\\Unit']),
            'file_name' => $this->faker->uuid . '.' . $this->faker->randomElement($fileTypes),
            'file_path' => 'documents/' . $this->faker->uuid . '.' . $this->faker->randomElement($fileTypes),
            'file_type' => $this->faker->randomElement($fileTypes),
            'file_size' => $this->faker->numberBetween(1000, 10000000),
            'mime_type' => $this->faker->randomElement($mimeTypes),
            'file_hash' => $this->faker->optional()->sha1(),
            'metadata' => $this->faker->optional()->randomElements(['api', 'web', 'mobile'], $this->faker->numberBetween(0, 3)),
            'document_date' => $this->faker->optional()->dateTimeBetween('-5 years', 'now'),
            'expiry_date' => $this->faker->optional()->dateTimeBetween('now', '+5 years'),
            'issuing_authority' => $this->faker->optional()->company(),
            'document_id_number' => $this->faker->optional()->bothify('DOC-####-####'),
            'status' => $this->faker->randomElement($statuses),
            'uploaded_by' => null, // Set to null to avoid foreign key issues
            'verified_by' => null, // Set to null to avoid foreign key issues
            'verified_at' => $this->faker->optional()->dateTimeBetween('-1 year', 'now'),
            'verification_notes' => $this->faker->optional()->sentence(),
            'rejection_reason' => $this->faker->optional()->sentence(),
            'visibility' => $this->faker->randomElement($visibilities),
            'is_required' => $this->faker->boolean(),
            'is_sensitive' => $this->faker->boolean(),
            'parent_document_id' => $this->faker->optional()->numberBetween(1, 100),
            'version' => $this->faker->numberBetween(1, 10),
            'is_current_version' => $this->faker->boolean(),
            'document_number' => 'DOC' . $this->faker->numberBetween(100000000, 999999999),
        ];
    }
} 