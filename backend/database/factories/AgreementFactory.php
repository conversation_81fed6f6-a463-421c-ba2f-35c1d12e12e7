<?php

namespace Database\Factories;

use App\Models\Agreement;
use App\Models\AgreementTemplate;
use App\Models\Tenant;
use App\Models\Unit;
use App\Models\User;
use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\Agreement>
 */
class AgreementFactory extends Factory
{
    /**
     * The name of the factory's corresponding model.
     *
     * @var string
     */
    protected $model = Agreement::class;

    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        return [
            'tenant_id' => Tenant::factory(),
            'unit_id' => Unit::factory(),
            'template_id' => AgreementTemplate::factory(),
            'file_path' => 'agreements/generated/' . fake()->uuid() . '.pdf',
            'status' => fake()->randomElement(['draft', 'active', 'expired', 'terminated']),
            'start_date' => fake()->dateTimeBetween('-1 year', 'now'),
            'end_date' => fake()->dateTimeBetween('now', '+1 year'),
            'created_by' => User::factory(),
            'metadata' => [
                'tenant_name' => fake()->name(),
                'owner_name' => fake()->name(),
                'unit_number' => fake()->regexify('[A-Z][0-9]{3}'),
                'rent_amount' => fake()->numberBetween(5000, 50000),
                'security_deposit' => fake()->numberBetween(10000, 100000),
            ],
            'renewed_from' => null,
            'history' => [],
            'signed_file_path' => null,
            'signature_data' => null,
            'signature_hash' => null,
            'signed_at' => null,
            'signed_by_owner' => false,
            'signed_by_tenant' => false,
        ];
    }

    /**
     * Indicate that the agreement is draft.
     */
    public function draft(): static
    {
        return $this->state(fn (array $attributes) => [
            'status' => 'draft',
        ]);
    }

    /**
     * Indicate that the agreement is active.
     */
    public function active(): static
    {
        return $this->state(fn (array $attributes) => [
            'status' => 'active',
            'start_date' => fake()->dateTimeBetween('-6 months', 'now'),
            'end_date' => fake()->dateTimeBetween('now', '+6 months'),
        ]);
    }

    /**
     * Indicate that the agreement is expired.
     */
    public function expired(): static
    {
        return $this->state(fn (array $attributes) => [
            'status' => 'active',
            'start_date' => fake()->dateTimeBetween('-1 year', '-6 months'),
            'end_date' => fake()->dateTimeBetween('-6 months', '-1 day'),
        ]);
    }

    /**
     * Indicate that the agreement is terminated.
     */
    public function terminated(): static
    {
        return $this->state(fn (array $attributes) => [
            'status' => 'terminated',
        ]);
    }

    /**
     * Indicate that the agreement is signed by owner.
     */
    public function signedByOwner(): static
    {
        return $this->state(fn (array $attributes) => [
            'signed_by_owner' => true,
            'signed_at' => fake()->dateTimeBetween('-1 month', 'now'),
            'signature_data' => [
                'signer_type' => 'owner',
                'signer_name' => fake()->name(),
                'signature_date' => fake()->date(),
            ],
            'signature_hash' => fake()->sha1(),
        ]);
    }

    /**
     * Indicate that the agreement is signed by tenant.
     */
    public function signedByTenant(): static
    {
        return $this->state(fn (array $attributes) => [
            'signed_by_tenant' => true,
            'signed_at' => fake()->dateTimeBetween('-1 month', 'now'),
            'signature_data' => [
                'signer_type' => 'tenant',
                'signer_name' => fake()->name(),
                'signature_date' => fake()->date(),
            ],
            'signature_hash' => fake()->sha1(),
        ]);
    }

    /**
     * Indicate that the agreement is fully signed.
     */
    public function fullySigned(): static
    {
        return $this->state(fn (array $attributes) => [
            'signed_by_owner' => true,
            'signed_by_tenant' => true,
            'signed_at' => fake()->dateTimeBetween('-1 month', 'now'),
            'signature_data' => [
                'owner_name' => fake()->name(),
                'tenant_name' => fake()->name(),
                'signature_date' => fake()->date(),
            ],
            'signature_hash' => fake()->sha1(),
        ]);
    }

    /**
     * Indicate that the agreement is expiring soon.
     */
    public function expiringSoon(): static
    {
        return $this->state(fn (array $attributes) => [
            'status' => 'active',
            'start_date' => fake()->dateTimeBetween('-11 months', '-10 months'),
            'end_date' => fake()->dateTimeBetween('now', '+30 days'),
        ]);
    }
}
