<?php

namespace Database\Factories;

use App\Models\Tenant;
use App\Models\User;
use App\Models\Unit;
use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\Tenant>
 */
class TenantFactory extends Factory
{
    /**
     * The name of the factory's corresponding model.
     *
     * @var string
     */
    protected $model = Tenant::class;

    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        return [
            // Foreign keys
            'user_id' => User::factory()->state(['role' => 'tenant']),
            'unit_id' => Unit::factory(),
            'owner_id' => User::factory()->state(['role' => 'owner']),

            // Identifiers
            'tenant_code' => strtoupper(fake()->bothify('TNT-#####')),

            // Tenant details
            'occupation' => fake()->optional()->jobTitle(),
            'company_name' => fake()->optional()->company(),
            'company_address' => fake()->optional()->address(),
            'family_members' => fake()->numberBetween(1, 6),
            'family_details' => [
                [
                    'name' => fake()->name(),
                    'age' => fake()->numberBetween(1, 80),
                    'relationship' => fake()->randomElement(['spouse', 'child', 'parent', 'other'])
                ]
            ],

            // Financial information
            'monthly_income' => fake()->optional()->randomFloat(2, 20000, 200000),
            'security_deposit' => fake()->optional()->randomFloat(2, 10000, 100000),
            'advance_amount' => fake()->optional()->randomFloat(2, 5000, 50000),

            // Agreement details
            'move_in_date' => fake()->optional()->date(),
            'move_out_date' => fake()->optional()->date(),
            'agreement_start_date' => fake()->optional()->date(),
            'agreement_end_date' => fake()->optional()->date(),
            'agreement_type' => fake()->randomElement(['leave_and_license', 'commercial', 'short_term']),

            // KYC and verification
            'kyc_status' => fake()->randomElement(['pending', 'submitted', 'verified', 'rejected']),
            'kyc_documents' => [
                [
                    'type' => 'aadhaar',
                    'number' => fake()->numerify('####-####-####'),
                    'status' => fake()->randomElement(['pending', 'verified', 'rejected'])
                ]
            ],
            'kyc_submitted_at' => fake()->optional()->dateTimeThisYear(),
            'kyc_verified_at' => fake()->optional()->dateTimeThisYear(),
            'kyc_verified_by' => User::factory()->state(['role' => 'admin']),
            'kyc_rejection_reason' => fake()->optional()->sentence(),

            // Verification details
            'background_check_done' => fake()->boolean(),
            'background_check_status' => fake()->optional()->randomElement(['pending', 'passed', 'failed']),
            'background_check_date' => fake()->optional()->dateTimeThisYear(),
            'background_check_notes' => fake()->optional()->sentence(),

            // Police verification
            'police_verification_required' => fake()->boolean(),
            'police_verification_status' => fake()->optional()->randomElement(['pending', 'submitted', 'verified', 'rejected']),
            'police_verification_date' => fake()->optional()->dateTimeThisYear(),
            'police_verification_notes' => fake()->optional()->sentence(),

            // References
            'references' => [
                [
                    'name' => fake()->name(),
                    'relationship' => fake()->randomElement(['friend', 'colleague', 'landlord']),
                    'phone' => fake()->phoneNumber(),
                ]
            ],

            // Tenant status and behavior
            'status' => fake()->randomElement(['active', 'inactive', 'terminated', 'notice_period']),
            'behavior_rating' => fake()->optional()->randomElement(['excellent', 'good', 'average', 'poor']),
            'behavior_notes' => fake()->optional()->sentence(),

            // Notice and termination
            'notice_given_date' => fake()->optional()->date(),
            'notice_period_days' => fake()->optional()->numberBetween(15, 90),
            'termination_reason' => fake()->optional()->sentence(),
            'terminated_at' => fake()->optional()->dateTimeThisYear(),
            'terminated_by' => User::factory()->state(['role' => 'admin']),

            // Preferences and notes
            'preferences' => [
                'quiet_hours' => fake()->boolean(),
                'parking_required' => fake()->boolean(),
                'maintenance_notifications' => fake()->boolean(),
                'newsletter_subscription' => fake()->boolean(),
            ],
            'special_requirements' => fake()->optional()->sentence(),
            'admin_notes' => fake()->optional()->sentence(),
        ];
    }

    /**
     * Indicate that the tenant is active.
     */
    public function active(): static
    {
        return $this->state(fn (array $attributes) => [
            'status' => 'active',
        ]);
    }

    /**
     * Indicate that the tenant is inactive.
     */
    public function inactive(): static
    {
        return $this->state(fn (array $attributes) => [
            'status' => 'inactive',
        ]);
    }

    /**
     * Indicate that the tenant is pending.
     */
    public function pending(): static
    {
        return $this->state(fn (array $attributes) => [
            'status' => 'pending',
        ]);
    }

    /**
     * Indicate that the tenant has completed onboarding.
     */
    public function onboardingCompleted(): static
    {
        return $this->state(fn (array $attributes) => [
            'onboarding_completed' => true,
            'onboarding_completed_at' => fake()->dateTimeBetween('-1 month', 'now'),
        ]);
    }

    /**
     * Indicate that the tenant has not completed onboarding.
     */
    public function onboardingIncomplete(): static
    {
        return $this->state(fn (array $attributes) => [
            'onboarding_completed' => false,
            'onboarding_completed_at' => null,
        ]);
    }

    /**
     * Indicate that the tenant has family.
     */
    public function withFamily(): static
    {
        return $this->state(fn (array $attributes) => [
            'family_details' => [
                [
                    'name' => fake()->name(),
                    'age' => fake()->numberBetween(1, 80),
                    'relationship' => fake()->randomElement(['spouse', 'child', 'parent', 'other'])
                ]
            ],
        ]);
    }

    /**
     * Indicate that the tenant has no family.
     */
    public function withoutFamily(): static
    {
        return $this->state(fn (array $attributes) => [
            'family_details' => [],
        ]);
    }
}
