<?php

namespace Database\Factories;

use App\Models\Agreement;
use App\Models\AgreementAuditLog;
use App\Models\User;
use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\AgreementAuditLog>
 */
class AgreementAuditLogFactory extends Factory
{
    /**
     * The name of the factory's corresponding model.
     *
     * @var string
     */
    protected $model = AgreementAuditLog::class;

    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        return [
            'agreement_id' => Agreement::factory(),
            'user_id' => User::factory(),
            'action' => fake()->randomElement([
                'created', 'updated', 'signed_by_tenant', 'signed_by_owner', 
                'fully_signed', 'status_changed', 'reminder_sent',
                'signature_workflow_started', 'signature_workflow_completed',
                'document_uploaded', 'document_deleted'
            ]),
            'entity_type' => 'agreement',
            'entity_id' => fake()->numberBetween(1, 1000),
            'old_values' => fake()->randomElement([
                null,
                ['status' => 'draft'],
                ['signed_by_tenant' => false],
                ['signed_by_owner' => false],
            ]),
            'new_values' => fake()->randomElement([
                ['status' => 'active'],
                ['signed_by_tenant' => true],
                ['signed_by_owner' => true],
                ['renewal_reminder_sent' => true],
            ]),
            'metadata' => [
                'agreement_status' => fake()->randomElement(['draft', 'active', 'expired']),
                'tenant_id' => fake()->numberBetween(1, 100),
                'unit_id' => fake()->numberBetween(1, 100),
            ],
            'ip_address' => fake()->ipv4(),
            'user_agent' => fake()->userAgent(),
            'performed_at' => fake()->dateTimeBetween('-1 month', 'now'),
        ];
    }

    /**
     * Indicate that the audit log is for agreement creation.
     */
    public function created(): static
    {
        return $this->state(fn (array $attributes) => [
            'action' => 'created',
            'old_values' => null,
            'new_values' => [
                'status' => 'draft',
                'tenant_id' => fake()->numberBetween(1, 100),
                'unit_id' => fake()->numberBetween(1, 100),
            ],
        ]);
    }

    /**
     * Indicate that the audit log is for agreement signing.
     */
    public function signed(): static
    {
        return $this->state(fn (array $attributes) => [
            'action' => fake()->randomElement(['signed_by_tenant', 'signed_by_owner']),
            'old_values' => ['signed_by_tenant' => false, 'signed_by_owner' => false],
            'new_values' => ['signed_by_tenant' => true, 'signed_by_owner' => false],
        ]);
    }

    /**
     * Indicate that the audit log is for status change.
     */
    public function statusChanged(): static
    {
        return $this->state(fn (array $attributes) => [
            'action' => 'status_changed',
            'old_values' => ['status' => 'draft'],
            'new_values' => ['status' => 'active'],
            'metadata' => array_merge($attributes['metadata'] ?? [], [
                'status_transition' => 'draft -> active',
            ]),
        ]);
    }

    /**
     * Indicate that the audit log is for reminder sent.
     */
    public function reminderSent(): static
    {
        return $this->state(fn (array $attributes) => [
            'action' => 'reminder_sent',
            'old_values' => null,
            'new_values' => [
                'reminder_sent_at' => now(),
                'days_until_expiry' => fake()->numberBetween(1, 30),
            ],
            'metadata' => array_merge($attributes['metadata'] ?? [], [
                'reminder_type' => 'renewal',
            ]),
        ]);
    }

    /**
     * Indicate that the audit log is recent.
     */
    public function recent(): static
    {
        return $this->state(fn (array $attributes) => [
            'performed_at' => fake()->dateTimeBetween('-7 days', 'now'),
        ]);
    }

    /**
     * Indicate that the audit log is for a specific user.
     */
    public function byUser(User $user): static
    {
        return $this->state(fn (array $attributes) => [
            'user_id' => $user->id,
        ]);
    }

    /**
     * Indicate that the audit log is for a specific agreement.
     */
    public function forAgreement(Agreement $agreement): static
    {
        return $this->state(fn (array $attributes) => [
            'agreement_id' => $agreement->id,
            'metadata' => array_merge($attributes['metadata'] ?? [], [
                'agreement_status' => $agreement->status,
                'tenant_id' => $agreement->tenant_id,
                'unit_id' => $agreement->unit_id,
            ]),
        ]);
    }
} 