<?php

namespace Database\Factories;

use App\Models\NotificationPreference;
use App\Models\User;
use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\NotificationPreference>
 */
class NotificationPreferenceFactory extends Factory
{
    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        return [
            'user_id' => User::factory(),
            'notification_type' => $this->faker->randomElement(NotificationPreference::getTypes()),
            'channel' => $this->faker->randomElement(NotificationPreference::getChannels()),
            'enabled' => $this->faker->boolean(80), // 80% chance of being enabled
            'settings' => [
                'include_attachments' => $this->faker->boolean(),
                'format' => $this->faker->randomElement(['html', 'text']),
            ],
            'frequency' => $this->faker->randomElement(NotificationPreference::getFrequencies()),
            'quiet_hours_start' => $this->faker->optional()->time(),
            'quiet_hours_end' => $this->faker->optional()->time(),
            'metadata' => [
                'created_via' => 'user_preference',
                'last_updated' => now()->toISOString(),
            ],
        ];
    }

    /**
     * Indicate that the preference is enabled.
     */
    public function enabled(): static
    {
        return $this->state(fn (array $attributes) => [
            'enabled' => true,
        ]);
    }

    /**
     * Indicate that the preference is disabled.
     */
    public function disabled(): static
    {
        return $this->state(fn (array $attributes) => [
            'enabled' => false,
        ]);
    }

    /**
     * Indicate that the preference is for system notifications.
     */
    public function system(): static
    {
        return $this->state(fn (array $attributes) => [
            'notification_type' => NotificationPreference::TYPE_SYSTEM,
        ]);
    }

    /**
     * Indicate that the preference is for tenant notifications.
     */
    public function tenant(): static
    {
        return $this->state(fn (array $attributes) => [
            'notification_type' => NotificationPreference::TYPE_TENANT,
        ]);
    }

    /**
     * Indicate that the preference is for unit notifications.
     */
    public function unit(): static
    {
        return $this->state(fn (array $attributes) => [
            'notification_type' => NotificationPreference::TYPE_UNIT,
        ]);
    }

    /**
     * Indicate that the preference is for enquiry notifications.
     */
    public function enquiry(): static
    {
        return $this->state(fn (array $attributes) => [
            'notification_type' => NotificationPreference::TYPE_ENQUIRY,
        ]);
    }

    /**
     * Indicate that the preference is for NOC notifications.
     */
    public function noc(): static
    {
        return $this->state(fn (array $attributes) => [
            'notification_type' => NotificationPreference::TYPE_NOC,
        ]);
    }

    /**
     * Indicate that the preference is for agreement notifications.
     */
    public function agreement(): static
    {
        return $this->state(fn (array $attributes) => [
            'notification_type' => NotificationPreference::TYPE_AGREEMENT,
        ]);
    }

    /**
     * Indicate that the preference is for billing notifications.
     */
    public function billing(): static
    {
        return $this->state(fn (array $attributes) => [
            'notification_type' => NotificationPreference::TYPE_BILLING,
        ]);
    }

    /**
     * Indicate that the preference is for maintenance notifications.
     */
    public function maintenance(): static
    {
        return $this->state(fn (array $attributes) => [
            'notification_type' => NotificationPreference::TYPE_MAINTENANCE,
        ]);
    }

    /**
     * Indicate that the preference is for email channel.
     */
    public function email(): static
    {
        return $this->state(fn (array $attributes) => [
            'channel' => NotificationPreference::CHANNEL_EMAIL,
            'settings' => [
                'include_attachments' => $this->faker->boolean(),
                'format' => $this->faker->randomElement(['html', 'text']),
                'signature' => $this->faker->optional()->sentence(),
            ],
        ]);
    }

    /**
     * Indicate that the preference is for SMS channel.
     */
    public function sms(): static
    {
        return $this->state(fn (array $attributes) => [
            'channel' => NotificationPreference::CHANNEL_SMS,
            'settings' => [
                'include_links' => false,
                'max_length' => 160,
                'sender_id' => 'TMS',
            ],
        ]);
    }

    /**
     * Indicate that the preference is for push channel.
     */
    public function push(): static
    {
        return $this->state(fn (array $attributes) => [
            'channel' => NotificationPreference::CHANNEL_PUSH,
            'settings' => [
                'sound' => true,
                'vibration' => true,
                'badge' => true,
                'priority' => 'high',
            ],
        ]);
    }

    /**
     * Indicate that the preference is for in-app channel.
     */
    public function inApp(): static
    {
        return $this->state(fn (array $attributes) => [
            'channel' => NotificationPreference::CHANNEL_IN_APP,
            'settings' => [
                'show_toast' => true,
                'auto_dismiss' => true,
                'dismiss_after' => 5000,
                'position' => 'top-right',
            ],
        ]);
    }

    /**
     * Indicate that the preference has immediate frequency.
     */
    public function immediate(): static
    {
        return $this->state(fn (array $attributes) => [
            'frequency' => NotificationPreference::FREQUENCY_IMMEDIATE,
        ]);
    }

    /**
     * Indicate that the preference has daily frequency.
     */
    public function daily(): static
    {
        return $this->state(fn (array $attributes) => [
            'frequency' => NotificationPreference::FREQUENCY_DAILY,
        ]);
    }

    /**
     * Indicate that the preference has weekly frequency.
     */
    public function weekly(): static
    {
        return $this->state(fn (array $attributes) => [
            'frequency' => NotificationPreference::FREQUENCY_WEEKLY,
        ]);
    }

    /**
     * Indicate that the preference has quiet hours.
     */
    public function withQuietHours(): static
    {
        return $this->state(fn (array $attributes) => [
            'quiet_hours_start' => '22:00:00',
            'quiet_hours_end' => '08:00:00',
        ]);
    }

    /**
     * Indicate that the preference has custom settings.
     */
    public function withCustomSettings(): static
    {
        return $this->state(fn (array $attributes) => [
            'settings' => [
                'custom_setting_1' => $this->faker->word(),
                'custom_setting_2' => $this->faker->numberBetween(1, 10),
                'custom_setting_3' => $this->faker->boolean(),
            ],
        ]);
    }

    /**
     * Indicate that the preference is for high priority notifications.
     */
    public function highPriority(): static
    {
        return $this->state(fn (array $attributes) => [
            'settings' => array_merge($attributes['settings'] ?? [], [
                'priority' => 'high',
                'urgent_override' => true,
            ]),
        ]);
    }

    /**
     * Indicate that the preference is for low priority notifications.
     */
    public function lowPriority(): static
    {
        return $this->state(fn (array $attributes) => [
            'settings' => array_merge($attributes['settings'] ?? [], [
                'priority' => 'low',
                'batch_notifications' => true,
            ]),
        ]);
    }
} 