<?php

namespace Database\Factories;

use App\Models\BillingRule;
use App\Models\User;
use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\BillingRule>
 */
class BillingRuleFactory extends Factory
{
    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        $types = ['maintenance', 'utilities', 'parking', 'non_occupancy', 'noc_charges'];
        $calculationMethods = ['per_sqft', 'fixed', 'percentage', 'per_unit'];
        $appliesTo = ['all_units', 'specific_units', 'tenant_type'];
        $frequencies = ['monthly', 'quarterly', 'yearly', 'one_time'];

        $type = $this->faker->randomElement($types);
        $calculationMethod = $this->faker->randomElement($calculationMethods);

        // Set realistic amounts based on type and calculation method
        $amount = match ($type) {
            'maintenance' => $calculationMethod === 'per_sqft' ? $this->faker->randomFloat(2, 3, 8) : $this->faker->numberBetween(1000, 5000),
            'utilities' => $calculationMethod === 'per_sqft' ? $this->faker->randomFloat(2, 1, 3) : $this->faker->numberBetween(500, 2000),
            'parking' => $this->faker->numberBetween(300, 1000),
            'non_occupancy' => $calculationMethod === 'percentage' ? $this->faker->numberBetween(10, 25) : $this->faker->numberBetween(2000, 8000),
            'noc_charges' => $this->faker->numberBetween(100, 500),
            default => $this->faker->numberBetween(100, 1000),
        };

        return [
            'name' => $this->faker->words(3, true) . ' ' . ucfirst($type),
            'type' => $type,
            'calculation_method' => $calculationMethod,
            'amount' => $amount,
            'description' => $this->faker->sentence(),
            'is_active' => $this->faker->boolean(80),
            'applies_to' => $this->faker->randomElement($appliesTo),
            'frequency' => $this->faker->randomElement($frequencies),
            'due_day' => $this->faker->numberBetween(1, 28),
            'late_fee_amount' => $this->faker->numberBetween(50, 200),
            'late_fee_days' => $this->faker->numberBetween(3, 15),
            'created_by' => User::factory(),
            'metadata' => $this->getMetadataForAppliesTo($this->faker->randomElement($appliesTo)),
        ];
    }

    /**
     * Generate metadata based on applies_to value.
     */
    private function getMetadataForAppliesTo(string $appliesTo): array
    {
        return match ($appliesTo) {
            'specific_units' => [
                'specific_unit_ids' => $this->faker->randomElements([1, 2, 3, 4, 5], $this->faker->numberBetween(1, 3))
            ],
            'tenant_type' => [
                'tenant_type' => $this->faker->randomElement(['individual', 'family', 'corporate'])
            ],
            default => [],
        };
    }

    /**
     * Indicate that the billing rule is active.
     */
    public function active(): static
    {
        return $this->state(fn (array $attributes) => [
            'is_active' => true,
        ]);
    }

    /**
     * Indicate that the billing rule is inactive.
     */
    public function inactive(): static
    {
        return $this->state(fn (array $attributes) => [
            'is_active' => false,
        ]);
    }

    /**
     * Create a maintenance rule.
     */
    public function maintenance(): static
    {
        return $this->state(fn (array $attributes) => [
            'type' => 'maintenance',
            'name' => 'Monthly Maintenance Charges',
            'calculation_method' => 'per_sqft',
            'amount' => 5.50,
        ]);
    }

    /**
     * Create a parking rule.
     */
    public function parking(): static
    {
        return $this->state(fn (array $attributes) => [
            'type' => 'parking',
            'name' => 'Parking Charges',
            'calculation_method' => 'fixed',
            'amount' => 500,
        ]);
    }
}
