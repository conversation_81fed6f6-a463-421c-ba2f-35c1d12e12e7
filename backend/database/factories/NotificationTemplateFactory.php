<?php

namespace Database\Factories;

use App\Models\NotificationTemplate;
use App\Models\User;
use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\NotificationTemplate>
 */
class NotificationTemplateFactory extends Factory
{
    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        return [
            'name' => $this->faker->unique()->words(3, true),
            'type' => $this->faker->randomElement(NotificationTemplate::getTypes()),
            'category' => $this->faker->randomElement(NotificationTemplate::getCategories()),
            'title' => $this->faker->sentence(),
            'content' => $this->faker->paragraph(),
            'variables' => ['user_name', 'action', 'timestamp'],
            'channels' => ['email', 'in_app'],
            'is_active' => $this->faker->boolean(80), // 80% chance of being active
            'version' => '1.0',
            'metadata' => [
                'created_by' => 'system',
                'last_updated' => now()->toISOString(),
            ],
        ];
    }

    /**
     * Indicate that the template is active.
     */
    public function active(): static
    {
        return $this->state(fn (array $attributes) => [
            'is_active' => true,
        ]);
    }

    /**
     * Indicate that the template is inactive.
     */
    public function inactive(): static
    {
        return $this->state(fn (array $attributes) => [
            'is_active' => false,
        ]);
    }

    /**
     * Indicate that the template is for email type.
     */
    public function email(): static
    {
        return $this->state(fn (array $attributes) => [
            'type' => NotificationTemplate::TYPE_EMAIL,
            'channels' => ['email'],
            'content' => $this->faker->paragraph() . "\n\nBest regards,\nTMS Team",
        ]);
    }

    /**
     * Indicate that the template is for SMS type.
     */
    public function sms(): static
    {
        return $this->state(fn (array $attributes) => [
            'type' => NotificationTemplate::TYPE_SMS,
            'channels' => ['sms'],
            'content' => $this->faker->sentence(10),
            'variables' => ['user_name', 'action'],
        ]);
    }

    /**
     * Indicate that the template is for push type.
     */
    public function push(): static
    {
        return $this->state(fn (array $attributes) => [
            'type' => NotificationTemplate::TYPE_PUSH,
            'channels' => ['push'],
            'title' => $this->faker->sentence(5),
            'content' => $this->faker->sentence(15),
            'variables' => ['user_name', 'action', 'entity_id'],
        ]);
    }

    /**
     * Indicate that the template is for in-app type.
     */
    public function inApp(): static
    {
        return $this->state(fn (array $attributes) => [
            'type' => NotificationTemplate::TYPE_IN_APP,
            'channels' => ['in_app'],
            'title' => $this->faker->sentence(6),
            'content' => $this->faker->paragraph(),
            'variables' => ['user_name', 'action', 'timestamp'],
        ]);
    }

    /**
     * Indicate that the template is for system category.
     */
    public function system(): static
    {
        return $this->state(fn (array $attributes) => [
            'category' => NotificationTemplate::CATEGORY_SYSTEM,
            'title' => 'System Notification',
            'content' => 'A system event has occurred: {{action}}. Please check your dashboard for more details.',
            'variables' => ['action', 'timestamp', 'system_name'],
        ]);
    }

    /**
     * Indicate that the template is for tenant category.
     */
    public function tenant(): static
    {
        return $this->state(fn (array $attributes) => [
            'category' => NotificationTemplate::CATEGORY_TENANT,
            'title' => 'Tenant Update',
            'content' => 'Hello {{user_name}}, your tenant information has been updated. {{action}}',
            'variables' => ['user_name', 'action', 'tenant_id'],
        ]);
    }

    /**
     * Indicate that the template is for unit category.
     */
    public function unit(): static
    {
        return $this->state(fn (array $attributes) => [
            'category' => NotificationTemplate::CATEGORY_UNIT,
            'title' => 'Unit Status Change',
            'content' => 'The status of unit {{unit_number}} has changed from {{previous_status}} to {{new_status}}.',
            'variables' => ['unit_number', 'previous_status', 'new_status', 'unit_id'],
        ]);
    }

    /**
     * Indicate that the template is for enquiry category.
     */
    public function enquiry(): static
    {
        return $this->state(fn (array $attributes) => [
            'category' => NotificationTemplate::CATEGORY_ENQUIRY,
            'title' => 'New Enquiry',
            'content' => 'A new enquiry has been received for unit {{unit_number}} from {{enquirer_name}}.',
            'variables' => ['unit_number', 'enquirer_name', 'enquiry_type', 'enquiry_id'],
        ]);
    }

    /**
     * Indicate that the template is for NOC category.
     */
    public function noc(): static
    {
        return $this->state(fn (array $attributes) => [
            'category' => NotificationTemplate::CATEGORY_NOC,
            'title' => 'NOC Application Update',
            'content' => 'Your NOC application ({{noc_type}}) status has been updated to {{new_status}}.',
            'variables' => ['noc_type', 'previous_status', 'new_status', 'noc_id'],
        ]);
    }

    /**
     * Indicate that the template is for agreement category.
     */
    public function agreement(): static
    {
        return $this->state(fn (array $attributes) => [
            'category' => NotificationTemplate::CATEGORY_AGREEMENT,
            'title' => 'Agreement Update',
            'content' => 'Your rental agreement {{agreement_number}} has been {{action}}. Due date: {{due_date}}.',
            'variables' => ['agreement_number', 'action', 'due_date', 'agreement_id'],
        ]);
    }

    /**
     * Indicate that the template is for billing category.
     */
    public function billing(): static
    {
        return $this->state(fn (array $attributes) => [
            'category' => NotificationTemplate::CATEGORY_BILLING,
            'title' => 'Billing Update',
            'content' => 'A new bill of {{amount}} has been generated for {{bill_type}}. Due date: {{due_date}}.',
            'variables' => ['amount', 'bill_type', 'due_date', 'bill_id'],
        ]);
    }

    /**
     * Indicate that the template is for maintenance category.
     */
    public function maintenance(): static
    {
        return $this->state(fn (array $attributes) => [
            'category' => NotificationTemplate::CATEGORY_MAINTENANCE,
            'title' => 'Maintenance Update',
            'content' => 'Maintenance request for {{issue_type}} has been {{action}}. Priority: {{priority}}.',
            'variables' => ['issue_type', 'action', 'priority', 'request_id'],
        ]);
    }

    /**
     * Indicate that the template has multiple channels.
     */
    public function multiChannel(): static
    {
        return $this->state(fn (array $attributes) => [
            'channels' => ['email', 'sms', 'push', 'in_app'],
        ]);
    }

    /**
     * Indicate that the template has many variables.
     */
    public function withManyVariables(): static
    {
        return $this->state(fn (array $attributes) => [
            'variables' => [
                'user_name', 'user_email', 'action', 'timestamp', 'entity_id', 
                'entity_type', 'status', 'priority', 'amount', 'due_date'
            ],
        ]);
    }

    /**
     * Indicate that the template has custom metadata.
     */
    public function withCustomMetadata(): static
    {
        return $this->state(fn (array $attributes) => [
            'metadata' => [
                'created_by' => 'admin',
                'approved_by' => 'manager',
                'last_updated' => now()->toISOString(),
                'usage_count' => $this->faker->numberBetween(0, 100),
                'tags' => ['important', 'frequent'],
            ],
        ]);
    }

    /**
     * Indicate that the template is for urgent notifications.
     */
    public function urgent(): static
    {
        return $this->state(fn (array $attributes) => [
            'title' => 'URGENT: ' . $this->faker->sentence(),
            'content' => 'URGENT: ' . $this->faker->paragraph(),
            'metadata' => [
                'priority' => 'urgent',
                'requires_acknowledgment' => true,
            ],
        ]);
    }

    /**
     * Indicate that the template is for welcome messages.
     */
    public function welcome(): static
    {
        return $this->state(fn (array $attributes) => [
            'name' => 'welcome_message',
            'title' => 'Welcome to TMS',
            'content' => 'Welcome {{user_name}}! Thank you for joining TMS. We\'re excited to have you on board.',
            'variables' => ['user_name', 'registration_date'],
        ]);
    }

    /**
     * Indicate that the template is for reminder messages.
     */
    public function reminder(): static
    {
        return $this->state(fn (array $attributes) => [
            'name' => 'reminder_message',
            'title' => 'Reminder',
            'content' => 'This is a friendly reminder about {{action}}. Please take action by {{due_date}}.',
            'variables' => ['action', 'due_date', 'entity_type'],
        ]);
    }

    /**
     * Indicate that the template is for confirmation messages.
     */
    public function confirmation(): static
    {
        return $this->state(fn (array $attributes) => [
            'name' => 'confirmation_message',
            'title' => 'Action Confirmed',
            'content' => 'Your {{action}} has been successfully confirmed. Reference ID: {{reference_id}}.',
            'variables' => ['action', 'reference_id', 'timestamp'],
        ]);
    }

    /**
     * Indicate that the template is for error messages.
     */
    public function error(): static
    {
        return $this->state(fn (array $attributes) => [
            'name' => 'error_message',
            'title' => 'Action Failed',
            'content' => 'Sorry, your {{action}} could not be completed. Error: {{error_message}}. Please try again.',
            'variables' => ['action', 'error_message', 'timestamp'],
        ]);
    }
} 