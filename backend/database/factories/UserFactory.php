<?php

namespace Database\Factories;

use App\Models\User;
use Illuminate\Database\Eloquent\Factories\Factory;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Str;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\User>
 */
class UserFactory extends Factory
{
    /**
     * The current password being used by the factory.
     */
    protected static ?string $password;

    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        return [
            'name' => fake()->name(),
            'email' => fake()->unique()->safeEmail(),
            'email_verified_at' => now(),
            'password' => static::$password ??= Hash::make('password'),
            'remember_token' => Str::random(10),
            'role' => User::ROLE_TENANT,
            'user_type' => 'tenant',
            'first_name' => fake()->firstName(),
            'last_name' => fake()->lastName(),
            'phone' => fake()->phoneNumber(),
            'alternate_phone' => fake()->optional()->phoneNumber(),
            'date_of_birth' => fake()->dateTimeBetween('-60 years', '-18 years'),
            'gender' => fake()->randomElement(['male', 'female', 'other']),
            'address' => fake()->address(),
            'city' => fake()->city(),
            'state' => fake()->state(),
            'country' => 'India',
            'pincode' => fake()->postcode(),
            'emergency_contact' => [
                'name' => fake()->name(),
                'phone' => fake()->phoneNumber(),
                'relationship' => fake()->randomElement(['spouse', 'parent', 'sibling', 'friend']),
            ],
            'profile_photo' => null,
            'preferences' => [],
            'status' => User::STATUS_ACTIVE,
            'is_verified' => true,
            'verified_at' => now(),
            'permissions' => [],
            'last_login_at' => fake()->optional()->dateTimeBetween('-1 month', 'now'),
            'last_login_ip' => fake()->optional()->ipv4(),
            'verified_by' => null,
        ];
    }

    /**
     * Indicate that the model's email address should be unverified.
     */
    public function unverified(): static
    {
        return $this->state(fn (array $attributes) => [
            'email_verified_at' => null,
            'is_verified' => false,
            'verified_at' => null,
        ]);
    }

    /**
     * Indicate that the user is an admin.
     */
    public function admin(): static
    {
        return $this->state(fn (array $attributes) => [
            'role' => User::ROLE_ADMIN,
            'user_type' => 'society_admin',
            'is_verified' => true,
            'verified_at' => now(),
            'permissions' => [
                'agreements:read',
                'agreements:create',
                'agreements:update',
                'agreements:delete',
            ],
        ]);
    }

    /**
     * Indicate that the user is an owner.
     */
    public function owner(): static
    {
        return $this->state(fn (array $attributes) => [
            'role' => User::ROLE_OWNER,
            'user_type' => 'owner',
            'is_verified' => true,
            'verified_at' => now(),
            'permissions' => [
                'agreements:read',
                'agreements:create',
                'agreements:update',
                'agreements:delete',
            ],
        ]);
    }

    /**
     * Indicate that the user is a tenant.
     */
    public function tenant(): static
    {
        return $this->state(fn (array $attributes) => [
            'role' => User::ROLE_TENANT,
            'user_type' => 'tenant',
            'is_verified' => true,
            'verified_at' => now(),
        ]);
    }

    /**
     * Indicate that the user is staff.
     */
    public function staff(): static
    {
        return $this->state(fn (array $attributes) => [
            'role' => User::ROLE_STAFF,
            'user_type' => 'staff',
            'is_verified' => true,
            'verified_at' => now(),
        ]);
    }
}
