<?php

namespace Database\Factories;

use App\Models\Unit;
use App\Models\User;
use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\Unit>
 */
class UnitFactory extends Factory
{
    /**
     * The name of the factory's corresponding model.
     *
     * @var string
     */
    protected $model = Unit::class;

    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        return [
            'unit_number' => fake()->regexify('[A-Z][0-9]{3}'),
            'block' => fake()->randomLetter(),
            'floor' => fake()->numberBetween(1, 20),
            'wing' => fake()->optional()->randomLetter(),
            'type' => fake()->randomElement(['residential', 'commercial', 'parking']),
            'bedrooms' => fake()->numberBetween(1, 4),
            'bathrooms' => fake()->numberBetween(1, 3),
            'area_sqft' => fake()->numberBetween(500, 2000),
            'carpet_area' => fake()->numberBetween(400, 1800),
            'built_up_area' => fake()->numberBetween(600, 2200),
            'status' => fake()->randomElement(['occupied', 'vacant', 'to-let', 'rented']),
            'owner_id' => User::factory()->state(['role' => 'owner']),
            'current_tenant_id' => null,
            'is_sublet' => fake()->boolean(20),
            'is_owner_occupied' => fake()->boolean(30),
            'market_rent' => fake()->numberBetween(5000, 50000),
            'security_deposit' => fake()->numberBetween(10000, 100000),
            'maintenance_charge' => fake()->numberBetween(500, 5000),
            'last_status_change' => fake()->dateTimeBetween('-1 month', 'now'),
            'status_changed_by' => User::factory(),
            'status_change_reason' => fake()->optional()->sentence(),
            'available_from' => fake()->optional()->dateTimeBetween('now', '+3 months'),
            'description' => fake()->sentence(),
            'amenities' => fake()->randomElements(['parking', 'garden', 'gym', 'pool', 'security', 'elevator', 'power_backup'], fake()->numberBetween(0, 4)),
            'preferences' => [
                'quiet_hours' => fake()->boolean(),
                'no_pets' => fake()->boolean(),
                'no_smoking' => fake()->boolean(),
            ],
            'photos' => [],
            'documents' => [],
            'is_active' => true,
            'notes' => fake()->optional()->paragraph(),
            'metadata' => [],
        ];
    }

    /**
     * Indicate that the unit is occupied.
     */
    public function occupied(): static
    {
        return $this->state(fn (array $attributes) => [
            'status' => 'occupied',
        ]);
    }

    /**
     * Indicate that the unit is vacant.
     */
    public function vacant(): static
    {
        return $this->state(fn (array $attributes) => [
            'status' => 'vacant',
        ]);
    }

    /**
     * Indicate that the unit is to let.
     */
    public function toLet(): static
    {
        return $this->state(fn (array $attributes) => [
            'status' => 'to-let',
        ]);
    }

    /**
     * Indicate that the unit is rented.
     */
    public function rented(): static
    {
        return $this->state(fn (array $attributes) => [
            'status' => 'rented',
        ]);
    }

    /**
     * Indicate that the unit is residential.
     */
    public function residential(): static
    {
        return $this->state(fn (array $attributes) => [
            'type' => 'residential',
        ]);
    }

    /**
     * Indicate that the unit is commercial.
     */
    public function commercial(): static
    {
        return $this->state(fn (array $attributes) => [
            'type' => 'commercial',
        ]);
    }

    /**
     * Indicate that the unit is a parking space.
     */
    public function parking(): static
    {
        return $this->state(fn (array $attributes) => [
            'type' => 'parking',
        ]);
    }

    /**
     * Indicate that the unit is owner occupied.
     */
    public function ownerOccupied(): static
    {
        return $this->state(fn (array $attributes) => [
            'is_owner_occupied' => true,
            'status' => 'occupied',
        ]);
    }

    /**
     * Indicate that the unit is available for rent.
     */
    public function available(): static
    {
        return $this->state(fn (array $attributes) => [
            'status' => 'to-let',
            'available_from' => fake()->dateTimeBetween('now', '+1 month'),
        ]);
    }
}
