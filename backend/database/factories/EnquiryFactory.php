<?php

namespace Database\Factories;

use App\Models\Enquiry;
use App\Models\Unit;
use App\Models\User;
use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\Enquiry>
 */
class EnquiryFactory extends Factory
{
    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        $enquiryTypes = [Enquiry::TYPE_RENTAL, Enquiry::TYPE_PURCHASE, Enquiry::TYPE_VISIT, Enquiry::TYPE_OTHER];
        $priorities = [Enquiry::PRIORITY_LOW, Enquiry::PRIORITY_MEDIUM, Enquiry::PRIORITY_HIGH, Enquiry::PRIORITY_URGENT];
        $statuses = [Enquiry::STATUS_NEW, Enquiry::STATUS_CONTACTED, Enquiry::STATUS_INTERESTED, Enquiry::STATUS_NOT_INTERESTED, Enquiry::STATUS_CLOSED];

        // Create user first
        $enquirer = User::factory()->create(['role' => 'tenant']);
        
        // Create unit
        $unit = Unit::factory()->create();
        
        // Create owner
        $owner = User::factory()->create(['role' => 'owner']);

        return [
            'unit_id' => $unit->id,
            'enquirer_id' => $enquirer->id,
            'owner_id' => $owner->id,
            'enquiry_type' => $this->faker->randomElement($enquiryTypes),
            'status' => $this->faker->randomElement($statuses),
            'priority' => $this->faker->randomElement($priorities),
            'message' => $this->faker->paragraph(3),
            'enquirer_details' => [
                'name' => $this->faker->name(),
                'email' => $this->faker->email(),
                'phone' => $this->faker->phoneNumber(),
                'occupation' => $this->faker->jobTitle(),
                'family_size' => $this->faker->numberBetween(1, 6),
            ],
            'communication_history' => [
                [
                    'type' => 'enquiry',
                    'message' => $this->faker->paragraph(2),
                    'user_id' => $enquirer->id,
                    'timestamp' => now()->subDays($this->faker->numberBetween(1, 30))->toISOString(),
                ]
            ],
            'response_data' => null,
            'contacted_at' => null,
            'responded_at' => null,
            'closed_at' => null,
            'notes' => null,
            'metadata' => [
                'source' => $this->faker->randomElement(['website', 'phone', 'walk_in', 'referral']),
                'urgency_level' => $this->faker->randomElement(['low', 'medium', 'high']),
            ],
        ];
    }

    /**
     * Indicate that the enquiry is new.
     */
    public function asNew(): static
    {
        return $this->state(fn (array $attributes) => [
            'status' => Enquiry::STATUS_NEW,
            'contacted_at' => null,
            'responded_at' => null,
            'closed_at' => null,
        ]);
    }

    /**
     * Indicate that the enquiry has been contacted.
     */
    public function asContacted(): static
    {
        return $this->state(fn (array $attributes) => [
            'status' => Enquiry::STATUS_CONTACTED,
            'contacted_at' => now()->subDays($this->faker->numberBetween(1, 7)),
            'responded_at' => null,
            'closed_at' => null,
        ]);
    }

    /**
     * Indicate that the enquiry is interested.
     */
    public function asInterested(): static
    {
        return $this->state(fn (array $attributes) => [
            'status' => Enquiry::STATUS_INTERESTED,
            'contacted_at' => now()->subDays($this->faker->numberBetween(1, 7)),
            'responded_at' => now()->subDays($this->faker->numberBetween(1, 3)),
            'closed_at' => null,
        ]);
    }

    /**
     * Indicate that the enquiry is not interested.
     */
    public function asNotInterested(): static
    {
        return $this->state(fn (array $attributes) => [
            'status' => Enquiry::STATUS_NOT_INTERESTED,
            'contacted_at' => now()->subDays($this->faker->numberBetween(1, 7)),
            'responded_at' => now()->subDays($this->faker->numberBetween(1, 3)),
            'closed_at' => null,
        ]);
    }

    /**
     * Indicate that the enquiry is closed.
     */
    public function asClosed(): static
    {
        return $this->state(fn (array $attributes) => [
            'status' => Enquiry::STATUS_CLOSED,
            'contacted_at' => now()->subDays($this->faker->numberBetween(1, 7)),
            'responded_at' => now()->subDays($this->faker->numberBetween(1, 3)),
            'closed_at' => now()->subDays($this->faker->numberBetween(1, 1)),
        ]);
    }

    /**
     * Indicate that the enquiry is urgent.
     */
    public function asUrgent(): static
    {
        return $this->state(fn (array $attributes) => [
            'priority' => Enquiry::PRIORITY_URGENT,
        ]);
    }

    /**
     * Indicate that the enquiry is high priority.
     */
    public function asHighPriority(): static
    {
        return $this->state(fn (array $attributes) => [
            'priority' => Enquiry::PRIORITY_HIGH,
        ]);
    }

    /**
     * Indicate that the enquiry is for rental.
     */
    public function asRental(): static
    {
        return $this->state(fn (array $attributes) => [
            'enquiry_type' => Enquiry::TYPE_RENTAL,
        ]);
    }

    /**
     * Indicate that the enquiry is for purchase.
     */
    public function asPurchase(): static
    {
        return $this->state(fn (array $attributes) => [
            'enquiry_type' => Enquiry::TYPE_PURCHASE,
        ]);
    }
}
