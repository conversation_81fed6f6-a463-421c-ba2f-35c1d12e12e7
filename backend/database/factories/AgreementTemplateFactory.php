<?php

namespace Database\Factories;

use App\Models\AgreementTemplate;
use App\Models\User;
use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\AgreementTemplate>
 */
class AgreementTemplateFactory extends Factory
{
    /**
     * The name of the factory's corresponding model.
     *
     * @var string
     */
    protected $model = AgreementTemplate::class;

    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        return [
            'name' => $this->faker->words(3, true),
            'description' => $this->faker->optional()->sentence(),
            'template_file' => 'templates/' . $this->faker->uuid . '.pdf',
            'placeholders' => [
                'tenant_name',
                'unit_number',
                'agreement_start_date',
                'agreement_end_date',
            ],
            'created_by' => User::factory(),
        ];
    }

    /**
     * Indicate that the template is for residential agreements.
     */
    public function residential(): static
    {
        return $this->state(fn (array $attributes) => [
            'name' => 'Residential Leave & Licence Agreement',
            'description' => 'Standard residential rental agreement template',
            'placeholders' => [
                'tenant_name',
                'tenant_address',
                'tenant_phone',
                'tenant_email',
                'owner_name',
                'owner_address',
                'owner_phone',
                'unit_number',
                'unit_type',
                'society_name',
                'society_address',
                'rent_amount',
                'security_deposit',
                'start_date',
                'end_date',
                'agreement_date',
                'witness_name',
                'witness_address'
            ],
        ]);
    }

    /**
     * Indicate that the template is for commercial agreements.
     */
    public function commercial(): static
    {
        return $this->state(fn (array $attributes) => [
            'name' => 'Commercial Leave & Licence Agreement',
            'description' => 'Commercial property rental agreement template',
            'placeholders' => [
                'tenant_name',
                'tenant_company',
                'tenant_address',
                'tenant_phone',
                'tenant_email',
                'owner_name',
                'owner_address',
                'owner_phone',
                'unit_number',
                'unit_type',
                'society_name',
                'society_address',
                'rent_amount',
                'security_deposit',
                'start_date',
                'end_date',
                'agreement_date',
                'business_type',
                'witness_name',
                'witness_address'
            ],
        ]);
    }

    /**
     * Indicate that the template is for short-term agreements.
     */
    public function shortTerm(): static
    {
        return $this->state(fn (array $attributes) => [
            'name' => 'Short-term Leave & Licence Agreement',
            'description' => 'Short-term rental agreement template (3-6 months)',
            'placeholders' => [
                'tenant_name',
                'tenant_address',
                'tenant_phone',
                'tenant_email',
                'owner_name',
                'owner_address',
                'owner_phone',
                'unit_number',
                'unit_type',
                'society_name',
                'society_address',
                'rent_amount',
                'security_deposit',
                'start_date',
                'end_date',
                'agreement_date',
                'witness_name',
                'witness_address'
            ],
        ]);
    }
}
