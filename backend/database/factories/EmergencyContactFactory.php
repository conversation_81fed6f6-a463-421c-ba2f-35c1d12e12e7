<?php

namespace Database\Factories;

use App\Models\EmergencyContact;
use App\Models\Tenant;
use App\Models\User;
use Illuminate\Database\Eloquent\Factories\Factory;

class EmergencyContactFactory extends Factory
{
    protected $model = EmergencyContact::class;

    public function definition(): array
    {
        $relationships = [
            EmergencyContact::RELATIONSHIP_PARENT,
            EmergencyContact::RELATIONSHIP_SPOUSE,
            EmergencyContact::RELATIONSHIP_SIBLING,
            EmergencyContact::RELATIONSHIP_CHILD,
            EmergencyContact::RELATIONSHIP_RELATIVE,
            EmergencyContact::RELATIONSHIP_FRIEND,
            EmergencyContact::RELATIONSHIP_COLLEAGUE,
            EmergencyContact::REL<PERSON>IONSHIP_NEIGHBOR,
            EmergencyContact::RELATIONSHIP_OTHER,
        ];
        return [
            'tenant_id' => Tenant::factory(),
            'name' => $this->faker->name(),
            'relationship' => $this->faker->randomElement($relationships),
            'primary_phone' => $this->faker->phoneNumber(),
            'secondary_phone' => $this->faker->optional()->phoneNumber(),
            'email' => $this->faker->optional()->safeEmail(),
            'address' => $this->faker->optional()->address(),
            'city' => $this->faker->optional()->city(),
            'state' => $this->faker->optional()->state(),
            'country' => 'India',
            'pincode' => $this->faker->optional()->postcode(),
            'priority' => $this->faker->numberBetween(1, 3),
            'is_local' => $this->faker->boolean(),
            'is_active' => $this->faker->boolean(),
            'notes' => $this->faker->optional()->sentence(),
            'metadata' => $this->faker->optional()->randomElements([
                'preferred_contact_time' => $this->faker->time(),
                'language' => $this->faker->languageCode(),
            ], 1),
            'created_by' => $this->faker->optional()->randomElement([User::factory(), null]),
            'updated_by' => $this->faker->optional()->randomElement([User::factory(), null]),
        ];
    }
} 