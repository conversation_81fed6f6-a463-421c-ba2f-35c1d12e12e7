<?php

namespace Database\Factories;

use App\Models\NocTemplate;
use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\NocTemplate>
 */
class NocTemplateFactory extends Factory
{
    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        $nocTypes = ['rental', 'residence', 'vehicle', 'renovation', 'transfer'];
        $nocType = $this->faker->randomElement($nocTypes);
        
        return [
            'name' => $this->faker->sentence(3),
            'description' => $this->faker->paragraph(),
            'noc_type' => $nocType,
            'template_content' => $this->getTemplateContent($nocType),
            'required_fields' => $this->getRequiredFields($nocType),
            'document_requirements' => $this->getDocumentRequirements($nocType),
            'placeholders' => $this->getPlaceholders($nocType),
            'is_active' => true,
            'created_by' => \App\Models\User::factory()->create(['role' => 'admin'])->id,
        ];
    }

    /**
     * Get template content based on NOC type
     */
    private function getTemplateContent(string $nocType): string
    {
        return match ($nocType) {
            'rental' => '<h1>Rental NOC Application</h1><p>This is a template for rental NOC applications.</p>',
            'residence' => '<h1>Residence NOC Application</h1><p>This is a template for residence NOC applications.</p>',
            'vehicle' => '<h1>Vehicle NOC Application</h1><p>This is a template for vehicle NOC applications.</p>',
            'renovation' => '<h1>Renovation NOC Application</h1><p>This is a template for renovation NOC applications.</p>',
            'transfer' => '<h1>Transfer NOC Application</h1><p>This is a template for transfer NOC applications.</p>',
            default => '<h1>NOC Application</h1><p>This is a general NOC application template.</p>',
        };
    }

    /**
     * Get required fields based on NOC type
     */
    private function getRequiredFields(string $nocType): array
    {
        return match ($nocType) {
            'rental' => [
                'tenant_name' => ['type' => 'text', 'required' => true],
                'rental_period' => ['type' => 'date_range', 'required' => true],
                'rental_amount' => ['type' => 'number', 'required' => true],
            ],
            'residence' => [
                'resident_name' => ['type' => 'text', 'required' => true],
                'residence_period' => ['type' => 'date_range', 'required' => true],
            ],
            'vehicle' => [
                'vehicle_number' => ['type' => 'text', 'required' => true],
                'vehicle_type' => ['type' => 'select', 'required' => true],
                'parking_slot' => ['type' => 'text', 'required' => false],
            ],
            'renovation' => [
                'renovation_type' => ['type' => 'select', 'required' => true],
                'renovation_period' => ['type' => 'date_range', 'required' => true],
                'contractor_details' => ['type' => 'text', 'required' => true],
            ],
            'transfer' => [
                'current_owner' => ['type' => 'text', 'required' => true],
                'new_owner' => ['type' => 'text', 'required' => true],
                'transfer_date' => ['type' => 'date', 'required' => true],
            ],
            default => [
                'purpose' => ['type' => 'text', 'required' => true],
                'period' => ['type' => 'date_range', 'required' => true],
            ],
        };
    }

    /**
     * Get document requirements based on NOC type
     */
    private function getDocumentRequirements(string $nocType): array
    {
        return match ($nocType) {
            'rental' => [
                'rental_agreement' => ['required' => true, 'max_size' => 2048, 'types' => ['pdf']],
                'id_proof' => ['required' => true, 'max_size' => 1024, 'types' => ['pdf', 'jpg', 'jpeg']],
                'police_verification' => ['required' => true, 'max_size' => 1024, 'types' => ['pdf']],
            ],
            'residence' => [
                'id_proof' => ['required' => true, 'max_size' => 1024, 'types' => ['pdf', 'jpg', 'jpeg']],
                'address_proof' => ['required' => true, 'max_size' => 1024, 'types' => ['pdf', 'jpg', 'jpeg']],
            ],
            'vehicle' => [
                'vehicle_registration' => ['required' => true, 'max_size' => 1024, 'types' => ['pdf', 'jpg', 'jpeg']],
                'driving_license' => ['required' => true, 'max_size' => 1024, 'types' => ['pdf', 'jpg', 'jpeg']],
                'insurance' => ['required' => true, 'max_size' => 1024, 'types' => ['pdf']],
            ],
            'renovation' => [
                'renovation_plan' => ['required' => true, 'max_size' => 2048, 'types' => ['pdf']],
                'contractor_license' => ['required' => true, 'max_size' => 1024, 'types' => ['pdf', 'jpg', 'jpeg']],
                'insurance_certificate' => ['required' => true, 'max_size' => 1024, 'types' => ['pdf']],
            ],
            'transfer' => [
                'sale_deed' => ['required' => true, 'max_size' => 2048, 'types' => ['pdf']],
                'noc_from_society' => ['required' => true, 'max_size' => 1024, 'types' => ['pdf']],
                'clearance_certificate' => ['required' => true, 'max_size' => 1024, 'types' => ['pdf']],
            ],
            default => [
                'supporting_documents' => ['required' => false, 'max_size' => 2048, 'types' => ['pdf', 'jpg', 'jpeg', 'png']],
            ],
        };
    }

    /**
     * Get placeholders based on NOC type
     */
    private function getPlaceholders(string $nocType): array
    {
        return match ($nocType) {
            'rental' => [
                '{{tenant_name}}' => 'Name of the tenant',
                '{{rental_period}}' => 'Rental period (start date to end date)',
                '{{rental_amount}}' => 'Monthly rental amount',
            ],
            'residence' => [
                '{{resident_name}}' => 'Name of the resident',
                '{{residence_period}}' => 'Residence period',
            ],
            'vehicle' => [
                '{{vehicle_number}}' => 'Vehicle registration number',
                '{{vehicle_type}}' => 'Type of vehicle',
                '{{parking_slot}}' => 'Assigned parking slot number',
            ],
            'renovation' => [
                '{{renovation_type}}' => 'Type of renovation work',
                '{{renovation_period}}' => 'Renovation period',
                '{{contractor_details}}' => 'Contractor name and contact details',
            ],
            'transfer' => [
                '{{current_owner}}' => 'Current owner name',
                '{{new_owner}}' => 'New owner name',
                '{{transfer_date}}' => 'Date of transfer',
            ],
            default => [
                '{{purpose}}' => 'Purpose of the NOC',
                '{{period}}' => 'Period for which NOC is required',
            ],
        };
    }
} 