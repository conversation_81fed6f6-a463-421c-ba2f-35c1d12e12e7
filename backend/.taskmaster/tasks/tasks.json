[{"id": 11, "title": "Rent Payment Tracking Service", "description": "Implement rent payment tracking service that fetches rent payment information from the external OneApp mobile application payment service, tracks payments per unit and tenant, reconciles payments, and provides reporting and notifications", "status": "pending", "priority": "medium", "dependencies": [], "subtasks": [{"id": null, "title": "Design rent payment data models", "description": "Create database migrations and Eloquent model for rent payments", "details": "Define `rent_payments` table with fields: unit_id, tenant_id, amount, payment_date, payment_method, external_reference, status, metadata; implement `RentPayment` model with relationships to `Unit` and `User` and necessary casts.", "status": "pending", "dependencies": []}, {"id": null, "title": "Implement external payment fetching service", "description": "Integrate with OneApp external payment API", "details": "Create `ExternalPaymentService` to call OneApp payment endpoints using configured credentials, handle pagination, errors, and parse payment records.", "status": "pending", "dependencies": []}, {"id": null, "title": "Implement rent payment reconciliation service", "description": "Match external payments with local records and handle discrepancies", "details": "Develop `RentPaymentService` to fetch latest payments, store new records, update statuses, mark missing or duplicate payments, and log discrepancies.", "status": "pending", "dependencies": []}, {"id": null, "title": "Create scheduled sync command", "description": "Automate periodic fetching of rent payments", "details": "Implement an Artisan command `payments:sync` to call the reconciliation service, and register it in the scheduler to run daily or configurable interval.", "status": "pending", "dependencies": []}, {"id": null, "title": "Build API endpoints for rent payment history", "description": "Expose payment data to unit owners and tenants", "details": "Create `PaymentController` with endpoints: `GET /api/v1/units/{unit}/payments`, `GET /api/v1/tenants/{tenant}/payments` returning paginated payment history with filters.", "status": "pending", "dependencies": []}, {"id": null, "title": "Implement payment notifications", "description": "Notify landlords and tenants of payment events", "details": "Use `NotificationService` to send notifications on new payment, overdue payment, and failed reconciliation; update user preferences for payment notifications.", "status": "pending", "dependencies": []}, {"id": null, "title": "Write comprehensive tests for payment tracker", "description": "Ensure correct functionality of models, services, scheduler, and APIs", "details": "Write unit tests for model and services (`ExternalPaymentService`, `RentPaymentService`), feature tests for API endpoints, and integration tests for scheduled command.", "status": "pending", "dependencies": []}], "tags": ["billing", "payments"], "createdAt": "2025-07-14T00:00:00Z"}, {"id": 12, "title": "Rent Receipt Service", "description": "Implement comprehensive rent receipt generation and management system that automatically creates professional receipts for completed payments, supports multiple formats (PDF, email), provides digital delivery, and maintains receipt history with proper numbering and audit trails", "status": "pending", "priority": "medium", "dependencies": [11], "subtasks": [{"id": null, "title": "Design rent receipt data models", "description": "Create database migrations and models for rent receipts", "details": "Define `rent_receipts` table with fields: payment_id, receipt_number, generated_at, recipient_email, delivery_status, pdf_path, metadata; implement `RentReceipt` model with relationships to `RentPayment`, `Unit`, and `User`; add receipt numbering logic.", "status": "pending", "dependencies": []}, {"id": null, "title": "Create receipt template system", "description": "Design customizable receipt templates for PDF generation", "details": "Create receipt template blade views with housing society branding, payment details, unit information, tenant details, and legal compliance text; support for multiple template layouts and customization options.", "status": "pending", "dependencies": []}, {"id": null, "title": "Implement PDF receipt generation service", "description": "Build service to generate professional PDF receipts", "details": "Create `ReceiptGenerationService` using DomPDF library to generate receipts from templates; include QR codes for verification, digital signatures, watermarks, and proper formatting for printing.", "status": "pending", "dependencies": []}, {"id": null, "title": "Build receipt numbering and audit system", "description": "Implement sequential receipt numbering with audit trails", "details": "Create receipt numbering service with configurable formats (e.g., REC-2024-001), ensure uniqueness, handle sequence gaps, and maintain audit logs for receipt generation, modifications, and access.", "status": "pending", "dependencies": []}, {"id": null, "title": "Implement automatic receipt generation", "description": "Auto-generate receipts when payments are completed", "details": "Integrate with payment completion events to automatically generate receipts; handle bulk receipt generation for multiple payments; implement queued processing for performance.", "status": "pending", "dependencies": []}, {"id": null, "title": "Create receipt delivery system", "description": "Implement multiple delivery channels for receipts", "details": "Build delivery service supporting email delivery with attachments, SMS with download links, in-app notifications, and WhatsApp integration; track delivery status and retry failed deliveries.", "status": "pending", "dependencies": []}, {"id": null, "title": "Build receipt management API endpoints", "description": "Create REST APIs for receipt operations", "details": "Implement `ReceiptController` with endpoints: `GET /api/v1/receipts`, `GET /api/v1/receipts/{id}`, `POST /api/v1/receipts/{id}/resend`, `GET /api/v1/receipts/{id}/download`, `GET /api/v1/payments/{payment}/receipt` with proper authorization and filtering.", "status": "pending", "dependencies": []}, {"id": null, "title": "Implement receipt verification system", "description": "Add QR code and digital verification for receipts", "details": "Generate unique verification codes and QR codes for each receipt; create verification endpoint for authenticity checking; implement digital signatures using housing society's certificate.", "status": "pending", "dependencies": []}, {"id": null, "title": "Create receipt analytics and reporting", "description": "Build reporting system for receipt generation metrics", "details": "Implement analytics for receipt generation rates, delivery success rates, verification attempts; create admin dashboard for receipt management and bulk operations.", "status": "pending", "dependencies": []}, {"id": null, "title": "Write comprehensive tests for receipt service", "description": "Ensure correct functionality of receipt generation and management", "details": "Write unit tests for models and services (`ReceiptGenerationService`, delivery services), feature tests for API endpoints, integration tests for automatic generation, and PDF generation tests.", "status": "pending", "dependencies": []}], "tags": ["receipts", "payments", "documents", "automation"], "createdAt": "2025-07-14T00:00:00Z"}]