<?php

namespace Tests;

use Illuminate\Foundation\Testing\TestCase as BaseTestCase;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Support\Facades\Storage;
use Laravel\Sanctum\Sanctum;
use App\Models\User;

abstract class TestCase extends BaseTestCase
{
    use RefreshDatabase;

    protected function setUp(): void
    {
        parent::setUp();

        // Fake storage for file upload tests
        Storage::fake('public');
        Storage::fake('local');
        Storage::fake('agreements');
        Storage::fake('documents');
    }

    /**
     * Create and authenticate a user with specific role.
     */
    protected function actingAsRole(string $role, array $attributes = []): User
    {
        $user = User::factory()->create(array_merge(['role' => $role], $attributes));
        Sanctum::actingAs($user);
        return $user;
    }

    /**
     * Create an admin user and authenticate.
     */
    protected function actingAsAdmin(array $attributes = []): User
    {
        return $this->actingAsRole('admin', $attributes);
    }

    /**
     * Create an owner user and authenticate.
     */
    protected function actingAsOwner(array $attributes = []): User
    {
        return $this->actingAsRole('owner', $attributes);
    }

    /**
     * Create a tenant user and authenticate.
     */
    protected function actingAsTenant(array $attributes = []): User
    {
        return $this->actingAsRole('tenant', $attributes);
    }

    /**
     * Create a manager user and authenticate.
     */
    protected function actingAsManager(array $attributes = []): User
    {
        return $this->actingAsRole('manager', $attributes);
    }

    /**
     * Assert JSON response structure with success format.
     */
    protected function assertSuccessResponse($response, array $dataStructure = null): void
    {
        $response->assertStatus(200)
            ->assertJsonStructure([
                'success',
                'message',
                'data' => $dataStructure,
            ])
            ->assertJson(['success' => true]);
    }

    /**
     * Assert JSON response structure for paginated data.
     */
    protected function assertPaginatedResponse($response, array $dataStructure = null): void
    {
        $response->assertStatus(200)
            ->assertJsonStructure([
                'success',
                'message',
                'data' => $dataStructure,
                'meta' => [
                    'current_page',
                    'from',
                    'last_page',
                    'per_page',
                    'to',
                    'total',
                ],
            ])
            ->assertJson(['success' => true]);
    }

    /**
     * Assert validation error response.
     */
    protected function assertValidationError($response, array $fields = []): void
    {
        $response->assertStatus(422)
            ->assertJsonStructure([
                'success',
                'message',
                'errors',
            ])
            ->assertJson(['success' => false]);

        if (!empty($fields)) {
            $response->assertJsonValidationErrors($fields);
        }
    }

    /**
     * Assert unauthorized response.
     */
    protected function assertUnauthorized($response): void
    {
        $response->assertStatus(401)
            ->assertJsonStructure([
                'success',
                'message',
            ])
            ->assertJson(['success' => false]);
    }

    /**
     * Assert forbidden response.
     */
    protected function assertForbidden($response): void
    {
        $response->assertStatus(403)
            ->assertJsonStructure([
                'success',
                'message',
            ])
            ->assertJson(['success' => false]);
    }

    /**
     * Assert not found response.
     */
    protected function assertNotFound($response): void
    {
        $response->assertStatus(404)
            ->assertJsonStructure([
                'success',
                'message',
            ])
            ->assertJson(['success' => false]);
    }

    /**
     * Create test file for upload testing.
     */
    protected function createTestFile(string $name = 'test.pdf', string $mimeType = 'application/pdf', int $size = 1024): \Illuminate\Http\UploadedFile
    {
        return \Illuminate\Http\UploadedFile::fake()->create($name, $size, $mimeType);
    }

    /**
     * Create test image for upload testing.
     */
    protected function createTestImage(string $name = 'test.jpg', int $width = 100, int $height = 100): \Illuminate\Http\UploadedFile
    {
        return \Illuminate\Http\UploadedFile::fake()->image($name, $width, $height);
    }
}
