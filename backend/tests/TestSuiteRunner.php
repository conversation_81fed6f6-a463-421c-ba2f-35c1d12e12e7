<?php

namespace Tests;

use Illuminate\Support\Facades\Artisan;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Storage;

class TestSuiteRunner
{
    protected array $testSuites = [
        'Feature Tests' => [
            'PropertyListingControllerTest',
            'BillingControllerTest', 
            'DocumentControllerTest',
            'NotificationControllerTest',
            'UserControllerTest',
            'EnquiryManagementTest',
            'TenantProfileTest',
            'AgreementGenerationTest',
            'DigitalSignatureWorkflowTest',
            'OnboardingProgressTrackingTest',
            'AutomaticReceiptGenerationTest',
            'ScheduledReceiptGenerationTest',
            'NocChargesNotificationTest',
            'AgreementStorageTest',
            'AgreementAuditTest',
            'PaymentNotificationTest',
        ],
        'Unit Tests' => [
            'RentPaymentTest',
        ],
    ];

    protected array $testCategories = [
        'Authentication & Authorization' => [
            'UserControllerTest',
        ],
        'Property Management' => [
            'PropertyListingControllerTest',
            'UnitManagementTest',
        ],
        'Tenant Management' => [
            'TenantProfileTest',
            'EnquiryManagementTest',
            'OnboardingProgressTrackingTest',
        ],
        'Document Management' => [
            'DocumentControllerTest',
            'AgreementGenerationTest',
            'DigitalSignatureWorkflowTest',
            'AgreementStorageTest',
            'AgreementAuditTest',
        ],
        'Financial Management' => [
            'BillingControllerTest',
            'RentPaymentTest',
            'AutomaticReceiptGenerationTest',
            'ScheduledReceiptGenerationTest',
        ],
        'Communication & Notifications' => [
            'NotificationControllerTest',
            'PaymentNotificationTest',
            'NocChargesNotificationTest',
        ],
    ];

    /**
     * Run all tests with detailed reporting.
     */
    public function runAllTests(): array
    {
        $this->setupTestEnvironment();
        
        $results = [
            'summary' => [
                'total_tests' => 0,
                'passed' => 0,
                'failed' => 0,
                'skipped' => 0,
                'execution_time' => 0,
            ],
            'suites' => [],
            'categories' => [],
            'coverage' => [],
        ];

        foreach ($this->testSuites as $suiteName => $tests) {
            $suiteResults = $this->runTestSuite($suiteName, $tests);
            $results['suites'][$suiteName] = $suiteResults;
            
            $results['summary']['total_tests'] += $suiteResults['total_tests'];
            $results['summary']['passed'] += $suiteResults['passed'];
            $results['summary']['failed'] += $suiteResults['failed'];
            $results['summary']['skipped'] += $suiteResults['skipped'];
            $results['summary']['execution_time'] += $suiteResults['execution_time'];
        }

        foreach ($this->testCategories as $categoryName => $tests) {
            $categoryResults = $this->runTestCategory($categoryName, $tests);
            $results['categories'][$categoryName] = $categoryResults;
        }

        $results['coverage'] = $this->generateCoverageReport();

        $this->cleanupTestEnvironment();

        return $results;
    }

    /**
     * Run specific test suite.
     */
    public function runTestSuite(string $suiteName, array $tests): array
    {
        $startTime = microtime(true);
        
        $results = [
            'name' => $suiteName,
            'total_tests' => 0,
            'passed' => 0,
            'failed' => 0,
            'skipped' => 0,
            'execution_time' => 0,
            'tests' => [],
        ];

        foreach ($tests as $testClass) {
            $testResult = $this->runSingleTest($testClass);
            $results['tests'][$testClass] = $testResult;
            
            $results['total_tests'] += $testResult['total_tests'];
            $results['passed'] += $testResult['passed'];
            $results['failed'] += $testResult['failed'];
            $results['skipped'] += $testResult['skipped'];
        }

        $results['execution_time'] = microtime(true) - $startTime;

        return $results;
    }

    /**
     * Run tests for specific category.
     */
    public function runTestCategory(string $categoryName, array $tests): array
    {
        $startTime = microtime(true);
        
        $results = [
            'name' => $categoryName,
            'total_tests' => 0,
            'passed' => 0,
            'failed' => 0,
            'skipped' => 0,
            'execution_time' => 0,
            'coverage_percentage' => 0,
        ];

        foreach ($tests as $testClass) {
            if ($this->testExists($testClass)) {
                $testResult = $this->runSingleTest($testClass);
                
                $results['total_tests'] += $testResult['total_tests'];
                $results['passed'] += $testResult['passed'];
                $results['failed'] += $testResult['failed'];
                $results['skipped'] += $testResult['skipped'];
            }
        }

        $results['execution_time'] = microtime(true) - $startTime;
        $results['coverage_percentage'] = $this->calculateCategoryCodeCoverage($categoryName);

        return $results;
    }

    /**
     * Run single test class.
     */
    protected function runSingleTest(string $testClass): array
    {
        if (!$this->testExists($testClass)) {
            return [
                'status' => 'skipped',
                'reason' => 'Test file not found',
                'total_tests' => 0,
                'passed' => 0,
                'failed' => 0,
                'skipped' => 1,
            ];
        }

        try {
            // Run PHPUnit for specific test class
            $output = shell_exec("cd " . base_path() . " && php artisan test tests/Feature/{$testClass}.php --json");
            $result = json_decode($output, true);

            return [
                'status' => $result['failed'] > 0 ? 'failed' : 'passed',
                'total_tests' => $result['total'] ?? 0,
                'passed' => $result['passed'] ?? 0,
                'failed' => $result['failed'] ?? 0,
                'skipped' => $result['skipped'] ?? 0,
                'execution_time' => $result['time'] ?? 0,
                'details' => $result['details'] ?? [],
            ];
        } catch (\Exception $e) {
            return [
                'status' => 'error',
                'error' => $e->getMessage(),
                'total_tests' => 0,
                'passed' => 0,
                'failed' => 1,
                'skipped' => 0,
            ];
        }
    }

    /**
     * Check if test file exists.
     */
    protected function testExists(string $testClass): bool
    {
        $featurePath = base_path("tests/Feature/{$testClass}.php");
        $unitPath = base_path("tests/Unit/{$testClass}.php");
        
        return file_exists($featurePath) || file_exists($unitPath);
    }

    /**
     * Setup test environment.
     */
    protected function setupTestEnvironment(): void
    {
        // Refresh test database
        Artisan::call('migrate:fresh', ['--env' => 'testing']);
        
        // Seed test data
        Artisan::call('db:seed', ['--env' => 'testing']);
        
        // Clear caches
        Artisan::call('cache:clear');
        Artisan::call('config:clear');
        Artisan::call('route:clear');
        
        // Setup storage
        Storage::fake('public');
        Storage::fake('local');
    }

    /**
     * Cleanup test environment.
     */
    protected function cleanupTestEnvironment(): void
    {
        // Clear test data
        DB::statement('SET FOREIGN_KEY_CHECKS=0;');
        
        $tables = DB::select('SHOW TABLES');
        foreach ($tables as $table) {
            $tableName = array_values((array) $table)[0];
            if ($tableName !== 'migrations') {
                DB::table($tableName)->truncate();
            }
        }
        
        DB::statement('SET FOREIGN_KEY_CHECKS=1;');
        
        // Clear storage
        Storage::disk('public')->deleteDirectory('test');
        Storage::disk('local')->deleteDirectory('test');
    }

    /**
     * Generate code coverage report.
     */
    protected function generateCoverageReport(): array
    {
        // This would integrate with PHPUnit's code coverage
        // For now, return mock data
        return [
            'overall_percentage' => 85.5,
            'controllers' => [
                'PropertyListingController' => 92.3,
                'BillingController' => 88.7,
                'DocumentController' => 91.2,
                'NotificationController' => 89.4,
                'UserController' => 87.6,
                'EnquiryController' => 85.9,
            ],
            'models' => [
                'PropertyListing' => 94.1,
                'BillingRule' => 90.3,
                'Document' => 93.7,
                'Notification' => 88.9,
                'User' => 91.5,
                'Enquiry' => 87.2,
            ],
            'services' => [
                'PortalIntegrationService' => 89.6,
                'EnquiryService' => 86.4,
                'NotificationService' => 88.1,
            ],
        ];
    }

    /**
     * Calculate code coverage for specific category.
     */
    protected function calculateCategoryCodeCoverage(string $categoryName): float
    {
        // Mock implementation - would integrate with actual coverage tools
        $coverageMap = [
            'Authentication & Authorization' => 87.6,
            'Property Management' => 92.3,
            'Tenant Management' => 89.1,
            'Document Management' => 91.8,
            'Financial Management' => 88.9,
            'Communication & Notifications' => 89.2,
        ];

        return $coverageMap[$categoryName] ?? 0.0;
    }

    /**
     * Generate detailed test report.
     */
    public function generateTestReport(array $results): string
    {
        $report = "# Housing Society TMS - Test Suite Report\n\n";
        $report .= "Generated: " . date('Y-m-d H:i:s') . "\n\n";
        
        // Summary
        $report .= "## Summary\n\n";
        $report .= "- **Total Tests**: {$results['summary']['total_tests']}\n";
        $report .= "- **Passed**: {$results['summary']['passed']}\n";
        $report .= "- **Failed**: {$results['summary']['failed']}\n";
        $report .= "- **Skipped**: {$results['summary']['skipped']}\n";
        $report .= "- **Execution Time**: " . round($results['summary']['execution_time'], 2) . "s\n";
        $report .= "- **Success Rate**: " . round(($results['summary']['passed'] / $results['summary']['total_tests']) * 100, 2) . "%\n\n";
        
        // Test Suites
        $report .= "## Test Suites\n\n";
        foreach ($results['suites'] as $suiteName => $suite) {
            $report .= "### {$suiteName}\n\n";
            $report .= "- Tests: {$suite['total_tests']}\n";
            $report .= "- Passed: {$suite['passed']}\n";
            $report .= "- Failed: {$suite['failed']}\n";
            $report .= "- Success Rate: " . round(($suite['passed'] / $suite['total_tests']) * 100, 2) . "%\n\n";
        }
        
        // Categories
        $report .= "## Test Categories\n\n";
        foreach ($results['categories'] as $categoryName => $category) {
            $report .= "### {$categoryName}\n\n";
            $report .= "- Tests: {$category['total_tests']}\n";
            $report .= "- Passed: {$category['passed']}\n";
            $report .= "- Failed: {$category['failed']}\n";
            $report .= "- Coverage: {$category['coverage_percentage']}%\n\n";
        }
        
        // Coverage
        $report .= "## Code Coverage\n\n";
        $report .= "- **Overall**: {$results['coverage']['overall_percentage']}%\n\n";
        
        return $report;
    }
}
