<?php

namespace Tests\Unit;

use App\Models\RentPayment;
use App\Models\Unit;
use App\Models\User;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Tests\TestCase;

class RentPaymentTest extends TestCase
{
    use RefreshDatabase;

    /** @test */
    public function it_can_create_a_rent_payment()
    {
        $unit = Unit::factory()->create();
        $tenant = User::factory()->create(['role' => 'tenant']);

        $payment = RentPayment::create([
            'unit_id' => $unit->id,
            'tenant_id' => $tenant->id,
            'amount' => 2500.00,
            'payment_date' => '2024-01-15',
            'payment_method' => 'bank_transfer',
            'external_reference' => 'EXT123456',
            'status' => 'completed',
            'metadata' => ['source' => 'oneapp'],
        ]);

        $this->assertDatabaseHas('rent_payments', [
            'id' => $payment->id,
            'unit_id' => $unit->id,
            'tenant_id' => $tenant->id,
            'amount' => 2500.00,
            'status' => 'completed',
        ]);

        $this->assertEquals('2500.00', $payment->amount);
        $this->assertEquals('completed', $payment->status);
        $this->assertEquals(['source' => 'oneapp'], $payment->metadata);
    }

    /** @test */
    public function it_casts_amount_to_decimal()
    {
        $unit = Unit::factory()->create();
        $tenant = User::factory()->create(['role' => 'tenant']);

        $payment = RentPayment::create([
            'unit_id' => $unit->id,
            'tenant_id' => $tenant->id,
            'amount' => 2500.50,
            'payment_date' => '2024-01-15',
            'status' => 'pending',
        ]);

        $this->assertInstanceOf(\Decimal\Decimal::class, $payment->amount);
        $this->assertEquals('2500.50', (string) $payment->amount);
    }

    /** @test */
    public function it_casts_payment_date_to_date()
    {
        $unit = Unit::factory()->create();
        $tenant = User::factory()->create(['role' => 'tenant']);

        $payment = RentPayment::create([
            'unit_id' => $unit->id,
            'tenant_id' => $tenant->id,
            'amount' => 2500.00,
            'payment_date' => '2024-01-15',
            'status' => 'pending',
        ]);

        $this->assertInstanceOf(\Carbon\Carbon::class, $payment->payment_date);
        $this->assertEquals('2024-01-15', $payment->payment_date->toDateString());
    }

    /** @test */
    public function it_casts_metadata_to_array()
    {
        $unit = Unit::factory()->create();
        $tenant = User::factory()->create(['role' => 'tenant']);

        $metadata = ['source' => 'oneapp', 'transaction_id' => 'TXN123'];

        $payment = RentPayment::create([
            'unit_id' => $unit->id,
            'tenant_id' => $tenant->id,
            'amount' => 2500.00,
            'payment_date' => '2024-01-15',
            'status' => 'pending',
            'metadata' => $metadata,
        ]);

        $this->assertIsArray($payment->metadata);
        $this->assertEquals($metadata, $payment->metadata);
    }

    /** @test */
    public function it_belongs_to_unit()
    {
        $unit = Unit::factory()->create();
        $tenant = User::factory()->create(['role' => 'tenant']);

        $payment = RentPayment::create([
            'unit_id' => $unit->id,
            'tenant_id' => $tenant->id,
            'amount' => 2500.00,
            'payment_date' => '2024-01-15',
            'status' => 'pending',
        ]);

        $this->assertInstanceOf(Unit::class, $payment->unit);
        $this->assertEquals($unit->id, $payment->unit->id);
    }

    /** @test */
    public function it_belongs_to_tenant()
    {
        $unit = Unit::factory()->create();
        $tenant = User::factory()->create(['role' => 'tenant']);

        $payment = RentPayment::create([
            'unit_id' => $unit->id,
            'tenant_id' => $tenant->id,
            'amount' => 2500.00,
            'payment_date' => '2024-01-15',
            'status' => 'pending',
        ]);

        $this->assertInstanceOf(User::class, $payment->tenant);
        $this->assertEquals($tenant->id, $payment->tenant->id);
    }

    /** @test */
    public function it_can_be_filtered_by_status()
    {
        $unit = Unit::factory()->create();
        $tenant = User::factory()->create(['role' => 'tenant']);

        RentPayment::create([
            'unit_id' => $unit->id,
            'tenant_id' => $tenant->id,
            'amount' => 2500.00,
            'payment_date' => '2024-01-15',
            'status' => 'completed',
        ]);

        RentPayment::create([
            'unit_id' => $unit->id,
            'tenant_id' => $tenant->id,
            'amount' => 2500.00,
            'payment_date' => '2024-01-16',
            'status' => 'pending',
        ]);

        $completedPayments = RentPayment::where('status', 'completed')->get();
        $this->assertCount(1, $completedPayments);
        $this->assertEquals('completed', $completedPayments->first()->status);
    }

    /** @test */
    public function it_can_be_filtered_by_external_reference()
    {
        $unit = Unit::factory()->create();
        $tenant = User::factory()->create(['role' => 'tenant']);

        RentPayment::create([
            'unit_id' => $unit->id,
            'tenant_id' => $tenant->id,
            'amount' => 2500.00,
            'payment_date' => '2024-01-15',
            'external_reference' => 'EXT123456',
            'status' => 'completed',
        ]);

        $payment = RentPayment::where('external_reference', 'EXT123456')->first();
        $this->assertNotNull($payment);
        $this->assertEquals('EXT123456', $payment->external_reference);
    }
}
