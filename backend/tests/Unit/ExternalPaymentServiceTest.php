<?php

namespace Tests\Unit;

use App\Services\ExternalPaymentService;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Support\Facades\Config;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;
use Tests\TestCase;

class ExternalPaymentServiceTest extends TestCase
{
    use RefreshDatabase;

    protected ExternalPaymentService $service;

    protected function setUp(): void
    {
        parent::setUp();
        
        // Mock configuration
        Config::set('services.oneapp.base_url', 'https://api.oneapp.test');
        Config::set('services.oneapp.api_key', 'test-api-key');
        Config::set('services.oneapp.client_id', 'test-client-id');
        Config::set('services.oneapp.client_secret', 'test-client-secret');
        
        $this->service = new ExternalPaymentService();
    }

    /** @test */
    public function it_handles_missing_configuration_gracefully()
    {
        Config::set('services.oneapp.base_url', '');
        Config::set('services.oneapp.api_key', '');
        
        $service = new ExternalPaymentService();
        $result = $service->fetchPayments();
        
        $this->assertIsArray($result);
        $this->assertEmpty($result);
    }

    /** @test */
    public function it_fetches_payments_successfully()
    {
        Http::fake([
            'https://api.oneapp.test/payments*' => Http::response([
                'data' => [
                    [
                        'id' => 1,
                        'unit_id' => 1,
                        'tenant_id' => 1,
                        'amount' => 2500.00,
                        'payment_date' => '2024-01-15',
                        'external_reference' => 'EXT123456',
                        'status' => 'completed',
                    ]
                ],
                'meta' => [
                    'has_more' => false
                ]
            ], 200)
        ]);

        $result = $this->service->fetchPayments();

        $this->assertIsArray($result);
        $this->assertCount(1, $result);
        $this->assertEquals('EXT123456', $result[0]['external_reference']);
    }

    /** @test */
    public function it_handles_api_errors_gracefully()
    {
        Http::fake([
            'https://api.oneapp.test/payments*' => Http::response([
                'error' => 'Unauthorized'
            ], 401)
        ]);

        Log::shouldReceive('error')->once();

        $result = $this->service->fetchPayments();

        $this->assertIsArray($result);
        $this->assertEmpty($result);
    }

    /** @test */
    public function it_handles_pagination()
    {
        Http::fake([
            'https://api.oneapp.test/payments?page=1*' => Http::response([
                'data' => [
                    ['id' => 1, 'external_reference' => 'EXT001'],
                    ['id' => 2, 'external_reference' => 'EXT002'],
                ],
                'meta' => [
                    'has_more' => true
                ]
            ], 200),
            'https://api.oneapp.test/payments?page=2*' => Http::response([
                'data' => [
                    ['id' => 3, 'external_reference' => 'EXT003'],
                ],
                'meta' => [
                    'has_more' => false
                ]
            ], 200)
        ]);

        $result = $this->service->fetchPayments();

        $this->assertIsArray($result);
        $this->assertCount(3, $result);
        $this->assertEquals('EXT001', $result[0]['external_reference']);
        $this->assertEquals('EXT002', $result[1]['external_reference']);
        $this->assertEquals('EXT003', $result[2]['external_reference']);
    }

    /** @test */
    public function it_includes_parameters_in_request()
    {
        Http::fake([
            'https://api.oneapp.test/payments?days=7&page=1*' => Http::response([
                'data' => [],
                'meta' => [
                    'has_more' => false
                ]
            ], 200)
        ]);

        $this->service->fetchPayments(['days' => 7]);

        Http::assertSent(function ($request) {
            return $request->url() === 'https://api.oneapp.test/payments' &&
                   $request['days'] === 7 &&
                   $request['page'] === 1;
        });
    }

    /** @test */
    public function it_sets_proper_authentication_headers()
    {
        Http::fake([
            'https://api.oneapp.test/payments*' => Http::response([
                'data' => [],
                'meta' => [
                    'has_more' => false
                ]
            ], 200)
        ]);

        $this->service->fetchPayments();

        Http::assertSent(function ($request) {
            return $request->hasHeader('Authorization', 'Bearer test-api-key') &&
                   $request->hasHeader('X-Client-Id', 'test-client-id') &&
                   $request->hasHeader('X-Client-Secret', 'test-client-secret') &&
                   $request->hasHeader('Accept', 'application/json');
        });
    }
}
