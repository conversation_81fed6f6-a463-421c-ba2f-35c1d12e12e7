<?php

namespace Tests\Unit;

use App\Models\RentPayment;
use App\Models\Unit;
use App\Models\User;
use App\Notifications\PaymentReceivedNotification;
use App\Services\ExternalPaymentService;
use App\Services\RentPaymentService;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Support\Facades\Notification;
use Tests\TestCase;

class RentPaymentServiceTest extends TestCase
{
    use RefreshDatabase;

    protected RentPaymentService $service;
    protected ExternalPaymentService $externalService;

    protected function setUp(): void
    {
        parent::setUp();
        
        $this->externalService = $this->createMock(ExternalPaymentService::class);
        $this->service = new RentPaymentService($this->externalService);
        
        Notification::fake();
    }

    /** @test */
    public function it_reconciles_payments_successfully()
    {
        $unit = Unit::factory()->create();
        $tenant = User::factory()->create(['role' => 'tenant']);

        $externalPayments = [
            [
                'unit_id' => $unit->id,
                'tenant_id' => $tenant->id,
                'amount' => 2500.00,
                'payment_date' => '2024-01-15',
                'external_reference' => 'EXT123456',
                'status' => 'completed',
            ]
        ];

        $this->externalService
            ->expects($this->once())
            ->method('fetchPayments')
            ->willReturn($externalPayments);

        $result = $this->service->reconcilePayments();

        $this->assertArrayHasKey('reconciled', $result);
        $this->assertArrayHasKey('discrepancies', $result);
        $this->assertCount(1, $result['reconciled']);
        $this->assertCount(0, $result['discrepancies']);

        $this->assertDatabaseHas('rent_payments', [
            'external_reference' => 'EXT123456',
            'status' => 'completed',
        ]);
    }

    /** @test */
    public function it_handles_missing_external_reference()
    {
        $externalPayments = [
            [
                'unit_id' => 1,
                'tenant_id' => 1,
                'amount' => 2500.00,
                'payment_date' => '2024-01-15',
                'status' => 'completed',
                // Missing external_reference
            ]
        ];

        $this->externalService
            ->expects($this->once())
            ->method('fetchPayments')
            ->willReturn($externalPayments);

        $result = $this->service->reconcilePayments();

        $this->assertCount(0, $result['reconciled']);
        $this->assertCount(1, $result['discrepancies']);
        $this->assertStringContainsString('Missing external reference', $result['discrepancies'][0]);
    }

    /** @test */
    public function it_updates_existing_payment()
    {
        $unit = Unit::factory()->create();
        $tenant = User::factory()->create(['role' => 'tenant']);

        $existingPayment = RentPayment::create([
            'unit_id' => $unit->id,
            'tenant_id' => $tenant->id,
            'amount' => 2500.00,
            'payment_date' => '2024-01-15',
            'external_reference' => 'EXT123456',
            'status' => 'pending',
        ]);

        $externalPayments = [
            [
                'unit_id' => $unit->id,
                'tenant_id' => $tenant->id,
                'amount' => 2500.00,
                'payment_date' => '2024-01-15',
                'external_reference' => 'EXT123456',
                'status' => 'completed',
            ]
        ];

        $this->externalService
            ->expects($this->once())
            ->method('fetchPayments')
            ->willReturn($externalPayments);

        $result = $this->service->reconcilePayments();

        $this->assertCount(1, $result['reconciled']);
        $this->assertCount(0, $result['discrepancies']);

        $updatedPayment = RentPayment::where('external_reference', 'EXT123456')->first();
        $this->assertEquals('completed', $updatedPayment->status);
    }

    /** @test */
    public function it_sends_notification_for_new_payment()
    {
        $unit = Unit::factory()->create();
        $tenant = User::factory()->create(['role' => 'tenant']);

        $externalPayments = [
            [
                'unit_id' => $unit->id,
                'tenant_id' => $tenant->id,
                'amount' => 2500.00,
                'payment_date' => '2024-01-15',
                'external_reference' => 'EXT123456',
                'status' => 'completed',
            ]
        ];

        $this->externalService
            ->expects($this->once())
            ->method('fetchPayments')
            ->willReturn($externalPayments);

        $this->service->reconcilePayments();

        Notification::assertSentTo(
            $tenant,
            PaymentReceivedNotification::class
        );
    }

    /** @test */
    public function it_handles_reconciliation_errors()
    {
        $this->externalService
            ->expects($this->once())
            ->method('fetchPayments')
            ->willThrowException(new \Exception('API connection failed'));

        $result = $this->service->reconcilePayments();

        $this->assertCount(0, $result['reconciled']);
        $this->assertCount(1, $result['discrepancies']);
        $this->assertStringContainsString('Reconciliation failed', $result['discrepancies'][0]);
    }

    /** @test */
    public function it_handles_database_errors_gracefully()
    {
        $externalPayments = [
            [
                'unit_id' => 999999, // Non-existent unit
                'tenant_id' => 999999, // Non-existent tenant
                'amount' => 2500.00,
                'payment_date' => '2024-01-15',
                'external_reference' => 'EXT123456',
                'status' => 'completed',
            ]
        ];

        $this->externalService
            ->expects($this->once())
            ->method('fetchPayments')
            ->willReturn($externalPayments);

        $result = $this->service->reconcilePayments();

        $this->assertCount(0, $result['reconciled']);
        $this->assertCount(1, $result['discrepancies']);
        $this->assertStringContainsString('Failed to create payment record', $result['discrepancies'][0]);
    }
}
