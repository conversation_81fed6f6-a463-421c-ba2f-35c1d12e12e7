<?php

namespace Tests\Feature;

use Tests\TestCase;
use App\Models\User;
use App\Models\Tenant;
use App\Models\Unit;
use App\Models\Agreement;
use App\Models\AgreementTemplate;
use App\Services\AgreementRenewalReminderService;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Foundation\Testing\WithFaker;
use Carbon\Carbon;

class AgreementRenewalReminderTest extends TestCase
{
    use RefreshDatabase, WithFaker;

    protected $user;
    protected $tenant;
    protected $unit;
    protected $template;
    protected $service;

    protected function setUp(): void
    {
        parent::setUp();
        
        $this->user = User::factory()->create(['role' => 'admin']);
        $this->tenant = Tenant::factory()->create();
        $this->unit = Unit::factory()->create(['owner_id' => $this->user->id]);
        $this->template = AgreementTemplate::factory()->create();
        $this->service = new AgreementRenewalReminderService();
    }

    /** @test */
    public function it_can_get_agreements_needing_reminders()
    {
        // Create agreement expiring in 30 days
        $agreement = Agreement::factory()->create([
            'tenant_id' => $this->tenant->id,
            'unit_id' => $this->unit->id,
            'template_id' => $this->template->id,
            'created_by' => $this->user->id,
            'status' => 'active',
            'end_date' => now()->addDays(30),
            'renewal_reminder_sent' => false,
        ]);

        $reminders = $this->service->getAgreementsNeedingReminders(30);

        $this->assertCount(1, $reminders);
        $this->assertEquals($agreement->id, $reminders[0]['agreement_id']);
        $this->assertEquals($this->tenant->name, $reminders[0]['tenant_name']);
        $this->assertEquals($this->unit->unit_number, $reminders[0]['unit_number']);
    }

    /** @test */
    public function it_does_not_return_agreements_with_reminders_already_sent()
    {
        // Create agreement with reminder already sent
        $agreement = Agreement::factory()->create([
            'tenant_id' => $this->tenant->id,
            'unit_id' => $this->unit->id,
            'template_id' => $this->template->id,
            'created_by' => $this->user->id,
            'status' => 'active',
            'end_date' => now()->addDays(30),
            'renewal_reminder_sent' => true,
        ]);

        $reminders = $this->service->getAgreementsNeedingReminders(30);

        $this->assertCount(0, $reminders);
    }

    /** @test */
    public function it_does_not_return_inactive_agreements()
    {
        // Create inactive agreement
        $agreement = Agreement::factory()->create([
            'tenant_id' => $this->tenant->id,
            'unit_id' => $this->unit->id,
            'template_id' => $this->template->id,
            'created_by' => $this->user->id,
            'status' => 'expired',
            'end_date' => now()->addDays(30),
            'renewal_reminder_sent' => false,
        ]);

        $reminders = $this->service->getAgreementsNeedingReminders(30);

        $this->assertCount(0, $reminders);
    }

    /** @test */
    public function it_can_get_renewal_statistics()
    {
        // Create agreements with different expiry dates
        Agreement::factory()->create([
            'tenant_id' => $this->tenant->id,
            'unit_id' => $this->unit->id,
            'template_id' => $this->template->id,
            'created_by' => $this->user->id,
            'status' => 'active',
            'end_date' => now()->addDays(5),
            'renewal_reminder_sent' => false,
        ]);

        Agreement::factory()->create([
            'tenant_id' => $this->tenant->id,
            'unit_id' => $this->unit->id,
            'template_id' => $this->template->id,
            'created_by' => $this->user->id,
            'status' => 'active',
            'end_date' => now()->addDays(15),
            'renewal_reminder_sent' => false,
        ]);

        $statistics = $this->service->getRenewalStatistics();

        $this->assertArrayHasKey('total_active_agreements', $statistics);
        $this->assertArrayHasKey('expiring_this_month', $statistics);
        $this->assertArrayHasKey('expiring_this_week', $statistics);
        $this->assertArrayHasKey('pending_reminders', $statistics);
        $this->assertGreaterThanOrEqual(2, $statistics['total_active_agreements']);
    }

    /** @test */
    public function it_can_get_expiring_agreements()
    {
        // Create agreement expiring in 10 days
        $agreement = Agreement::factory()->create([
            'tenant_id' => $this->tenant->id,
            'unit_id' => $this->unit->id,
            'template_id' => $this->template->id,
            'created_by' => $this->user->id,
            'status' => 'active',
            'end_date' => now()->addDays(10),
            'renewal_reminder_sent' => false,
        ]);

        $expiringAgreements = $this->service->getExpiringAgreements(15);

        $this->assertCount(1, $expiringAgreements);
        $this->assertEquals($agreement->id, $expiringAgreements[0]['id']);
        $this->assertEquals($this->tenant->name, $expiringAgreements[0]['tenant_name']);
        $this->assertEquals($this->unit->unit_number, $expiringAgreements[0]['unit_number']);
    }

    /** @test */
    public function it_can_reset_reminder_flags()
    {
        // Create agreement with reminder sent
        $agreement = Agreement::factory()->create([
            'tenant_id' => $this->tenant->id,
            'unit_id' => $this->unit->id,
            'template_id' => $this->template->id,
            'created_by' => $this->user->id,
            'status' => 'active',
            'end_date' => now()->addDays(30),
            'renewal_reminder_sent' => true,
            'renewal_reminder_sent_at' => now(),
        ]);

        $updated = $this->service->resetReminderFlags($agreement->id);

        $this->assertEquals(1, $updated);
        
        $agreement->refresh();
        $this->assertFalse($agreement->renewal_reminder_sent);
        $this->assertNull($agreement->renewal_reminder_sent_at);
    }

    /** @test */
    public function it_can_get_renewal_statistics_via_api()
    {
        $response = $this->actingAs($this->user)
            ->getJson('/api/v1/agreements/renewal/statistics');

        $response->assertStatus(200)
            ->assertJsonStructure([
                'success',
                'data' => [
                    'total_active_agreements',
                    'expiring_this_month',
                    'expiring_this_week',
                    'expiring_tomorrow',
                    'reminders_sent_today',
                    'pending_reminders'
                ]
            ]);
    }

    /** @test */
    public function it_can_get_expiring_agreements_via_api()
    {
        $response = $this->actingAs($this->user)
            ->getJson('/api/v1/agreements/renewal/expiring?days=30');

        $response->assertStatus(200)
            ->assertJsonStructure([
                'success',
                'data' => [
                    '*' => [
                        'id',
                        'tenant_name',
                        'unit_number',
                        'end_date',
                        'days_until_expiry',
                        'renewal_reminder_sent',
                        'renewal_reminder_sent_at',
                        'tenant_email',
                        'owner_email'
                    ]
                ]
            ]);
    }

    /** @test */
    public function it_can_get_agreements_needing_reminders_via_api()
    {
        $response = $this->actingAs($this->user)
            ->getJson('/api/v1/agreements/renewal/needing-reminders?days_advance=30');

        $response->assertStatus(200)
            ->assertJsonStructure([
                'success',
                'data' => [
                    '*' => [
                        'agreement_id',
                        'tenant_name',
                        'unit_number',
                        'end_date',
                        'days_until_expiry',
                        'tenant_email',
                        'owner_email',
                        'creator_email'
                    ]
                ]
            ]);
    }

    /** @test */
    public function it_can_send_renewal_reminders_via_api()
    {
        // Create agreement expiring soon
        $agreement = Agreement::factory()->create([
            'tenant_id' => $this->tenant->id,
            'unit_id' => $this->unit->id,
            'template_id' => $this->template->id,
            'created_by' => $this->user->id,
            'status' => 'active',
            'end_date' => now()->addDays(5),
            'renewal_reminder_sent' => false,
        ]);

        $response = $this->actingAs($this->user)
            ->postJson('/api/v1/agreements/renewal/send-reminders', [
                'days_advance' => 10
            ]);

        $response->assertStatus(200)
            ->assertJsonStructure([
                'success',
                'message',
                'data' => [
                    'total_reminders',
                    'sent_count',
                    'errors'
                ]
            ]);

        $agreement->refresh();
        $this->assertTrue($agreement->renewal_reminder_sent);
        $this->assertNotNull($agreement->renewal_reminder_sent_at);
    }

    /** @test */
    public function it_can_send_reminder_for_specific_agreement_via_api()
    {
        // Create agreement expiring soon
        $agreement = Agreement::factory()->create([
            'tenant_id' => $this->tenant->id,
            'unit_id' => $this->unit->id,
            'template_id' => $this->template->id,
            'created_by' => $this->user->id,
            'status' => 'active',
            'end_date' => now()->addDays(5),
            'renewal_reminder_sent' => false,
        ]);

        $response = $this->actingAs($this->user)
            ->postJson('/api/v1/agreements/renewal/send-reminders', [
                'agreement_id' => $agreement->id
            ]);

        $response->assertStatus(200)
            ->assertJsonStructure([
                'success',
                'message',
                'data' => [
                    'agreement_id',
                    'tenant_name',
                    'unit_number',
                    'end_date',
                    'days_until_expiry',
                    'tenant_email',
                    'owner_email',
                    'creator_email'
                ]
            ]);

        $agreement->refresh();
        $this->assertTrue($agreement->renewal_reminder_sent);
        $this->assertNotNull($agreement->renewal_reminder_sent_at);
    }

    /** @test */
    public function it_can_reset_reminder_flags_via_api()
    {
        // Create agreement with reminder sent
        $agreement = Agreement::factory()->create([
            'tenant_id' => $this->tenant->id,
            'unit_id' => $this->unit->id,
            'template_id' => $this->template->id,
            'created_by' => $this->user->id,
            'status' => 'active',
            'end_date' => now()->addDays(30),
            'renewal_reminder_sent' => true,
            'renewal_reminder_sent_at' => now(),
        ]);

        $response = $this->actingAs($this->user)
            ->postJson('/api/v1/agreements/renewal/reset-flags', [
                'agreement_id' => $agreement->id
            ]);

        $response->assertStatus(200)
            ->assertJsonStructure([
                'success',
                'message',
                'data' => [
                    'updated_count'
                ]
            ]);

        $agreement->refresh();
        $this->assertFalse($agreement->renewal_reminder_sent);
        $this->assertNull($agreement->renewal_reminder_sent_at);
    }

    /** @test */
    public function it_validates_api_requests()
    {
        $response = $this->actingAs($this->user)
            ->postJson('/api/v1/agreements/renewal/send-reminders', [
                'days_advance' => 'invalid'
            ]);

        $response->assertStatus(422);

        $response = $this->actingAs($this->user)
            ->postJson('/api/v1/agreements/renewal/send-reminders', [
                'agreement_id' => 99999
            ]);

        $response->assertStatus(422);
    }
} 