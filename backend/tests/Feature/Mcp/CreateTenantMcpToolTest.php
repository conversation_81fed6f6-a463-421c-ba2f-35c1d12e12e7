<?php

namespace Tests\Feature\Mcp;

use Illuminate\Foundation\Testing\RefreshDatabase;
use Tests\TestCase;
use App\Models\Tenant;
use Illuminate\Support\Facades\Queue;
use Illuminate\Support\Facades\Event;

class CreateTenantMcpToolTest extends TestCase
{
    use RefreshDatabase;

    /** @test */
    public function it_creates_a_tenant_via_mcp_tool_and_fires_sync_event()
    {
        // Fake queue and events for sync
        Queue::fake();
        Event::fake();

        $payload = [
            'tool' => 'UnitTool@create_tenant',
            'data' => [
                'name' => 'Test Tenant',
                'email' => '<EMAIL>',
                'phone' => '1234567890',
                'address' => '123 Test Street',
                'move_in_date' => now()->toDateString(),
            ]
        ];

        $response = $this->postJson('/mcp/execute', $payload);

        $response->assertStatus(200)
            ->assertJson(['success' => true])
            ->assertJsonStructure(['data' => ['id']]);

        $tenantId = $response->json('data.id');
        $this->assertNotNull($tenantId);
        $this->assertDatabaseHas('tenants', [
            'id' => $tenantId,
            'email' => '<EMAIL>',
        ]);

        // Optional: Assert SyncObserver/event fired
        Event::assertDispatched(
            fn ($event) =>
                method_exists($event, 'getEntityType') &&
                $event->getEntityType() === 'Tenant' &&
                $event->operation === 'created'
        );

        // Optional: Assert job was pushed to queue
        Queue::assertPushed(
            fn ($job) =>
                property_exists($job, 'entityData') &&
                $job->entityData['email'] === '<EMAIL>'
        );
    }
}
