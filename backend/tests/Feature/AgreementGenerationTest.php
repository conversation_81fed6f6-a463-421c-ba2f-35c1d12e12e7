<?php

namespace Tests\Feature;

use Tests\TestCase;
use App\Models\User;
use App\Models\Tenant;
use App\Models\Unit;
use App\Models\Agreement;
use App\Models\AgreementTemplate;
use App\Services\DigitalSignatureService;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Foundation\Testing\WithFaker;
use Illuminate\Support\Facades\Storage;
use Laravel\Sanctum\Sanctum;

class AgreementGenerationTest extends TestCase
{
    use RefreshDatabase, WithFaker;

    protected $adminUser;
    protected $ownerUser;
    protected $tenantUser;
    protected $unit;
    protected $tenant;
    protected $template;

    protected function setUp(): void
    {
        parent::setUp();
        
        // Create test users
        $this->adminUser = User::factory()->admin()->create([
            'email' => '<EMAIL>',
            'status' => 'active',
            'email_verified_at' => now(),
        ]);

        $this->ownerUser = User::factory()->owner()->create([
            'email' => '<EMAIL>',
            'status' => 'active',
            'email_verified_at' => now(),
        ]);

        $this->tenantUser = User::factory()->tenant()->create([
            'email' => '<EMAIL>',
            'status' => 'active',
            'email_verified_at' => now(),
        ]);

        // Sanctum actingAs with all agreement permissions
        Sanctum::actingAs($this->adminUser, [
            'agreements:read',
            'agreements:create',
            'agreements:update',
            'agreements:delete',
        ]);

        // Create test unit
        $this->unit = Unit::factory()->create([
            'owner_id' => $this->ownerUser->id,
            'status' => 'occupied',
        ]);

        // Create test tenant
        $this->tenant = Tenant::factory()->create([
            'user_id' => $this->tenantUser->id,
            'unit_id' => $this->unit->id,
            'status' => 'active',
        ]);

        // Create test template
        $this->template = AgreementTemplate::factory()->create([
            'name' => 'Test Template',
            'description' => 'Test agreement template',
            'template_file' => 'agreements/templates/test_template.html',
            'placeholders' => ['tenant_name', 'owner_name', 'unit_number'],
            'created_by' => $this->adminUser->id,
        ]);

        // Create template file in the correct storage location
        $templateStoragePath = storage_path('app/agreements/templates/test_template.html');
        if (!file_exists(dirname($templateStoragePath))) {
            mkdir(dirname($templateStoragePath), 0777, true);
        }
        file_put_contents($templateStoragePath, $this->getTestTemplate());
    }

    public function test_can_generate_agreement()
    {
        $response = $this->postJson('/api/v1/agreements', [
            'tenant_id' => $this->tenant->id,
            'unit_id' => $this->unit->id,
            'template_id' => $this->template->id,
            'start_date' => '2024-01-01',
            'end_date' => '2024-12-31',
            'placeholders' => [
                'tenant_name' => 'John Doe',
                'owner_name' => 'Jane Smith',
                'unit_number' => 'A101',
            ],
        ]);

        $response->assertStatus(201);
        $response->assertJsonStructure([
            'id',
            'tenant_id',
            'unit_id',
            'template_id',
            'file_path',
            'status',
            'start_date',
            'end_date',
        ]);

        $this->assertDatabaseHas('agreements', [
            'tenant_id' => $this->tenant->id,
            'unit_id' => $this->unit->id,
            'template_id' => $this->template->id,
            'status' => 'draft',
        ]);
    }

    public function test_can_download_agreement()
    {
        $agreement = Agreement::factory()->create([
            'tenant_id' => $this->tenant->id,
            'unit_id' => $this->unit->id,
            'template_id' => $this->template->id,
            'file_path' => 'agreements/generated/test.pdf',
            'status' => 'draft',
        ]);

        // Create test PDF file
        Storage::put('agreements/generated/test.pdf', 'test pdf content');

        $response = $this->get("/api/v1/agreements/{$agreement->id}/download");

        $response->assertStatus(200);
        $response->assertHeader('Content-Type', 'application/pdf');
    }

    public function test_can_renew_agreement()
    {
        $originalAgreement = Agreement::factory()->create([
            'tenant_id' => $this->tenant->id,
            'unit_id' => $this->unit->id,
            'template_id' => $this->template->id,
            'status' => 'active',
            'start_date' => '2023-01-01',
            'end_date' => '2023-12-31',
        ]);

        $response = $this->postJson("/api/v1/agreements/{$originalAgreement->id}/renew", [
            'start_date' => '2024-01-01',
            'end_date' => '2024-12-31',
            'placeholders' => [
                'tenant_name' => 'John Doe',
                'owner_name' => 'Jane Smith',
                'unit_number' => 'A101',
            ],
        ]);

        $response->assertStatus(201);
        $response->assertJsonStructure([
            'id',
            'renewed_from',
            'status',
            'start_date',
            'end_date',
        ]);

        $this->assertDatabaseHas('agreements', [
            'renewed_from' => $originalAgreement->id,
            'status' => 'draft',
        ]);
    }

    public function test_can_extend_agreement()
    {
        $agreement = Agreement::factory()->create([
            'tenant_id' => $this->tenant->id,
            'unit_id' => $this->unit->id,
            'template_id' => $this->template->id,
            'status' => 'active',
            'start_date' => '2024-01-01',
            'end_date' => '2024-06-30',
        ]);

        $response = $this->postJson("/api/v1/agreements/{$agreement->id}/extend", [
            'end_date' => '2024-12-31',
            'placeholders' => [
                'tenant_name' => 'John Doe',
                'owner_name' => 'Jane Smith',
                'unit_number' => 'A101',
            ],
        ]);

        $response->assertStatus(200);
        $response->assertJson([
            'end_date' => '2024-12-31T00:00:00.000000Z',
        ]);
    }

    public function test_can_activate_agreement()
    {
        $agreement = Agreement::factory()->create([
            'tenant_id' => $this->tenant->id,
            'unit_id' => $this->unit->id,
            'template_id' => $this->template->id,
            'status' => 'draft',
            'start_date' => now()->subDays(1),
            'end_date' => now()->addYear(),
        ]);

        $response = $this->postJson("/api/v1/agreements/{$agreement->id}/activate");

        $response->assertStatus(200);
        $response->assertJson([
            'status' => 'active',
        ]);
    }

    public function test_can_terminate_agreement()
    {
        $agreement = Agreement::factory()->create([
            'tenant_id' => $this->tenant->id,
            'unit_id' => $this->unit->id,
            'template_id' => $this->template->id,
            'status' => 'active',
        ]);

        $response = $this->postJson("/api/v1/agreements/{$agreement->id}/terminate");

        $response->assertStatus(200);
        $response->assertJson([
            'status' => 'terminated',
        ]);
    }

    public function test_can_sign_agreement_as_owner()
    {
        $agreement = Agreement::factory()->create([
            'tenant_id' => $this->tenant->id,
            'unit_id' => $this->unit->id,
            'template_id' => $this->template->id,
            'status' => 'draft',
        ]);

        // Create a real test PDF file
        $pdfService = app(\App\Services\DigitalSignatureService::class);
        $pdfPath = $pdfService->createTestPdf('Test Agreement Content');
        $agreement->file_path = $pdfPath;
        $agreement->save();

        $response = $this->postJson("/api/v1/agreements/{$agreement->id}/sign", [
            'signer_type' => 'owner',
            'signer_name' => 'Jane Smith',
            'signature_date' => '2024-01-01',
        ]);

        $response->assertStatus(200);
        $response->assertJsonStructure([
            'agreement',
            'signature_certificate',
            'message',
        ]);

        $this->assertDatabaseHas('agreements', [
            'id' => $agreement->id,
            'signed_by_owner' => true,
            'signed_by_tenant' => false,
        ]);
    }

    public function test_can_sign_agreement_as_tenant()
    {
        $agreement = Agreement::factory()->create([
            'tenant_id' => $this->tenant->id,
            'unit_id' => $this->unit->id,
            'template_id' => $this->template->id,
            'status' => 'draft',
        ]);

        // Create a real test PDF file
        $pdfService = app(\App\Services\DigitalSignatureService::class);
        $pdfPath = $pdfService->createTestPdf('Test Agreement Content');
        $agreement->file_path = $pdfPath;
        $agreement->save();

        $response = $this->postJson("/api/v1/agreements/{$agreement->id}/sign", [
            'signer_type' => 'tenant',
            'signer_name' => 'John Doe',
            'signature_date' => '2024-01-01',
        ]);

        $response->assertStatus(200);
        $response->assertJsonStructure([
            'agreement',
            'signature_certificate',
            'message',
        ]);

        $this->assertDatabaseHas('agreements', [
            'id' => $agreement->id,
            'signed_by_owner' => false,
            'signed_by_tenant' => true,
        ]);
    }

    public function test_can_get_signature_status()
    {
        $agreement = Agreement::factory()->create([
            'tenant_id' => $this->tenant->id,
            'unit_id' => $this->unit->id,
            'template_id' => $this->template->id,
            'signed_by_owner' => true,
            'signed_by_tenant' => false,
        ]);

        $response = $this->getJson("/api/v1/agreements/{$agreement->id}/signature-status");

        $response->assertStatus(200);
        $response->assertJson([
            'agreement_id' => $agreement->id,
            'signature_status' => 'partially_signed',
            'signed_by_owner' => true,
            'signed_by_tenant' => false,
        ]);
    }

    public function test_can_verify_signature()
    {
        $agreement = Agreement::factory()->create([
            'tenant_id' => $this->tenant->id,
            'unit_id' => $this->unit->id,
            'template_id' => $this->template->id,
            'signature_hash' => 'test_hash_123',
            'signature_data' => [
                'agreement_id' => 1,
                'signature_date' => '2024-01-01',
            ],
        ]);

        $response = $this->getJson("/api/v1/agreements/verify-signature/test_hash_123");

        $response->assertStatus(200);
        $response->assertJsonStructure([
            'signature_hash',
            'is_valid',
            'agreement_id',
            'signed_at',
            'signature_status',
        ]);
    }

    public function test_can_get_expiring_agreements()
    {
        // Create agreements that will expire soon
        Agreement::factory()->create([
            'tenant_id' => $this->tenant->id,
            'unit_id' => $this->unit->id,
            'template_id' => $this->template->id,
            'status' => 'active',
            'end_date' => now()->addDays(15),
        ]);

        $response = $this->getJson('/api/v1/agreements/expiring?days=30');

        $response->assertStatus(200);
        $response->assertJsonStructure([
            'agreements',
            'count',
            'days_threshold',
        ]);
    }

    public function test_can_get_agreement_statistics()
    {
        // Create various agreements for statistics
        Agreement::factory()->count(3)->create([
            'tenant_id' => $this->tenant->id,
            'unit_id' => $this->unit->id,
            'template_id' => $this->template->id,
            'status' => 'draft',
        ]);

        Agreement::factory()->count(2)->create([
            'tenant_id' => $this->tenant->id,
            'unit_id' => $this->unit->id,
            'template_id' => $this->template->id,
            'status' => 'active',
        ]);

        $response = $this->getJson('/api/v1/agreements/statistics');

        $response->assertStatus(200);
        $response->assertJsonStructure([
            'total',
            'draft',
            'active',
            'expired',
            'expiring_soon',
            'fully_signed',
            'partially_signed',
        ]);
    }

    public function test_prevents_overlapping_agreements()
    {
        // Create an existing active agreement
        Agreement::factory()->create([
            'tenant_id' => $this->tenant->id,
            'unit_id' => $this->unit->id,
            'template_id' => $this->template->id,
            'status' => 'active',
            'start_date' => '2024-01-01',
            'end_date' => '2024-12-31',
        ]);

        $response = $this->postJson('/api/v1/agreements', [
            'tenant_id' => $this->tenant->id,
            'unit_id' => $this->unit->id,
            'template_id' => $this->template->id,
            'start_date' => '2024-06-01',
            'end_date' => '2025-05-31',
            'placeholders' => [
                'tenant_name' => 'John Doe',
                'owner_name' => 'Jane Smith',
                'unit_number' => 'A101',
            ],
        ]);

        $response->assertStatus(409);
        $response->assertJson([
            'error' => 'An active or draft agreement already exists for this unit or tenant with overlapping dates.',
        ]);
    }

    public function test_validates_agreement_dates()
    {
        $response = $this->postJson('/api/v1/agreements', [
            'tenant_id' => $this->tenant->id,
            'unit_id' => $this->unit->id,
            'template_id' => $this->template->id,
            'start_date' => '2024-12-31',
            'end_date' => '2024-01-01', // End date before start date
            'placeholders' => [
                'tenant_name' => 'John Doe',
                'owner_name' => 'Jane Smith',
                'unit_number' => 'A101',
            ],
        ]);

        $response->assertStatus(422);
        $response->assertJson([
            'error' => 'End date must be after start date.',
        ]);
    }

    public function test_can_list_agreements()
    {
        Agreement::factory()->count(3)->create([
            'tenant_id' => $this->tenant->id,
            'unit_id' => $this->unit->id,
            'template_id' => $this->template->id,
        ]);

        $response = $this->getJson('/api/v1/agreements');

        $response->assertStatus(200);
        $response->assertJsonCount(3);
    }

    public function test_can_show_agreement()
    {
        $agreement = Agreement::factory()->create([
            'tenant_id' => $this->tenant->id,
            'unit_id' => $this->unit->id,
            'template_id' => $this->template->id,
        ]);

        $response = $this->getJson("/api/v1/agreements/{$agreement->id}");

        $response->assertStatus(200);
        $response->assertJson([
            'id' => $agreement->id,
            'tenant_id' => $this->tenant->id,
            'unit_id' => $this->unit->id,
        ]);
    }

    public function test_can_delete_agreement()
    {
        $agreement = Agreement::factory()->create([
            'tenant_id' => $this->tenant->id,
            'unit_id' => $this->unit->id,
            'template_id' => $this->template->id,
        ]);

        $response = $this->deleteJson("/api/v1/agreements/{$agreement->id}");

        $response->assertStatus(204);
        $this->assertDatabaseMissing('agreements', ['id' => $agreement->id]);
    }

    private function getTestTemplate(): string
    {
        return '<!DOCTYPE html>
<html>
<head>
    <title>Test Agreement</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 40px; }
    </style>
</head>
<body>
    <h1>Test Agreement</h1>
    <p>This is a test agreement template.</p>
    <p>Tenant: {tenant_name}</p>
    <p>Owner: {owner_name}</p>
    <p>Unit: {unit_number}</p>
</body>
</html>';
    }
} 