<?php

namespace Tests\Feature;

use App\Models\Enquiry;
use App\Models\Unit;
use App\Models\User;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Tests\TestCase;

class EnquiryManagementTest extends TestCase
{
    use RefreshDatabase;

    protected $adminUser;
    protected $ownerUser;
    protected $tenantUser;
    protected $unit;
    protected $owner;

    protected function setUp(): void
    {
        parent::setUp();

        // Create test users
        $this->adminUser = User::factory()->create(['role' => 'admin']);
        $this->ownerUser = User::factory()->create(['role' => 'owner']);
        $this->tenantUser = User::factory()->create(['role' => 'tenant']);

        // Create unit and owner
        $this->unit = Unit::factory()->create(['status' => Unit::STATUS_VACANT]);
        $this->owner = User::factory()->create(['role' => 'owner']);
        
        // Associate unit with owner
        $this->unit->update(['owner_id' => $this->owner->id]);
    }

    /**
     * Test creating a new enquiry
     */
    public function test_can_create_enquiry()
    {
        $response = $this->actingAs($this->tenantUser)
            ->postJson('/api/v1/enquiries', [
                'unit_id' => $this->unit->id,
                'enquiry_type' => 'rental',
                'message' => 'I am interested in renting this unit',
                'priority' => 'medium',
                'enquirer_details' => [
                    'name' => 'John Doe',
                    'email' => '<EMAIL>',
                    'phone' => '1234567890',
                ],
            ]);

        $response->assertStatus(201)
            ->assertJsonStructure([
                'success',
                'data' => [
                    'id',
                    'unit_id',
                    'enquirer_id',
                    'owner_id',
                    'enquiry_type',
                    'status',
                    'priority',
                    'message',
                    'enquirer_details',
                    'communication_history',
                    'created_at',
                    'updated_at',
                ],
            ]);

        $this->assertDatabaseHas('enquiries', [
            'unit_id' => $this->unit->id,
            'enquirer_id' => $this->tenantUser->id,
            'owner_id' => $this->owner->id,
            'enquiry_type' => 'rental',
            'status' => 'new',
            'priority' => 'medium',
        ]);
    }

    /**
     * Test listing enquiries with role-based filtering
     */
    public function test_can_list_enquiries()
    {
        // Create test enquiries
        Enquiry::factory()->count(3)->create([
            'enquirer_id' => $this->tenantUser->id,
            'unit_id' => $this->unit->id,
            'owner_id' => $this->owner->id,
        ]);

        // Test tenant can see their own enquiries
        $response = $this->actingAs($this->tenantUser)
            ->getJson('/api/v1/enquiries');

        $response->assertStatus(200)
            ->assertJsonStructure([
                'success',
                'data' => [
                    'data' => [
                        '*' => [
                            'id',
                            'unit_id',
                            'enquirer_id',
                            'owner_id',
                            'enquiry_type',
                            'status',
                            'priority',
                            'message',
                            'created_at',
                        ],
                    ],
                    'current_page',
                    'per_page',
                    'total',
                ],
            ]);

        $this->assertEquals(3, $response->json('data.total'));
    }

    /**
     * Test owner can see enquiries for their units
     */
    public function test_owner_can_see_their_enquiries()
    {
        // Create enquiries for the owner
        Enquiry::factory()->count(2)->create([
            'owner_id' => $this->owner->id,
            'unit_id' => $this->unit->id,
        ]);

        // Create enquiries for another owner
        $otherOwner = User::factory()->create(['role' => 'owner']);
        $otherUnit = Unit::factory()->create(['owner_id' => $otherOwner->id]);
        Enquiry::factory()->count(1)->create([
            'owner_id' => $otherOwner->id,
            'unit_id' => $otherUnit->id,
        ]);

        $response = $this->actingAs($this->owner)
            ->getJson('/api/v1/enquiries');

        $response->assertStatus(200);
        $this->assertEquals(2, $response->json('data.total'));
    }

    /**
     * Test admin can see all enquiries
     */
    public function test_admin_can_see_all_enquiries()
    {
        // Create enquiries for different owners
        Enquiry::factory()->count(3)->create([
            'owner_id' => $this->owner->id,
            'unit_id' => $this->unit->id,
        ]);

        $otherOwner = User::factory()->create(['role' => 'owner']);
        $otherUnit = Unit::factory()->create(['owner_id' => $otherOwner->id]);
        Enquiry::factory()->count(2)->create([
            'owner_id' => $otherOwner->id,
            'unit_id' => $otherUnit->id,
        ]);

        $response = $this->actingAs($this->adminUser)
            ->getJson('/api/v1/enquiries');

        $response->assertStatus(200);
        $this->assertEquals(5, $response->json('data.total'));
    }

    /**
     * Test filtering enquiries by status
     */
    public function test_can_filter_enquiries_by_status()
    {
        Enquiry::factory()->asNew()->create([
            'enquirer_id' => $this->tenantUser->id,
            'unit_id' => $this->unit->id,
            'owner_id' => $this->owner->id,
        ]);

        Enquiry::factory()->asContacted()->create([
            'enquirer_id' => $this->tenantUser->id,
            'unit_id' => $this->unit->id,
            'owner_id' => $this->owner->id,
        ]);

        $response = $this->actingAs($this->tenantUser)
            ->getJson('/api/v1/enquiries?status=new');

        $response->assertStatus(200);
        $this->assertEquals(1, $response->json('data.total'));
        $this->assertEquals('new', $response->json('data.data.0.status'));
    }

    /**
     * Test filtering enquiries by type
     */
    public function test_can_filter_enquiries_by_type()
    {
        Enquiry::factory()->asRental()->create([
            'enquirer_id' => $this->tenantUser->id,
            'unit_id' => $this->unit->id,
            'owner_id' => $this->owner->id,
        ]);

        Enquiry::factory()->asPurchase()->create([
            'enquirer_id' => $this->tenantUser->id,
            'unit_id' => $this->unit->id,
            'owner_id' => $this->owner->id,
        ]);

        $response = $this->actingAs($this->tenantUser)
            ->getJson('/api/v1/enquiries?type=rental');

        $response->assertStatus(200);
        $this->assertEquals(1, $response->json('data.total'));
        $this->assertEquals('rental', $response->json('data.data.0.enquiry_type'));
    }

    /**
     * Test marking enquiry as contacted
     */
    public function test_can_mark_enquiry_as_contacted()
    {
        $enquiry = Enquiry::factory()->asNew()->create([
            'enquirer_id' => $this->tenantUser->id,
            'unit_id' => $this->unit->id,
            'owner_id' => $this->owner->id,
        ]);

        $response = $this->actingAs($this->owner)
            ->postJson("/api/v1/enquiries/{$enquiry->id}/contacted");

        $response->assertStatus(200)
            ->assertJson([
                'success' => true,
                'message' => 'Enquiry marked as contacted',
            ]);

        $this->assertDatabaseHas('enquiries', [
            'id' => $enquiry->id,
            'status' => 'contacted',
        ]);

        $this->assertNotNull($enquiry->fresh()->contacted_at);
    }

    /**
     * Test marking enquiry as interested
     */
    public function test_can_mark_enquiry_as_interested()
    {
        $enquiry = Enquiry::factory()->asContacted()->create([
            'enquirer_id' => $this->tenantUser->id,
            'unit_id' => $this->unit->id,
            'owner_id' => $this->owner->id,
        ]);

        $response = $this->actingAs($this->owner)
            ->postJson("/api/v1/enquiries/{$enquiry->id}/interested");

        $response->assertStatus(200)
            ->assertJson([
                'success' => true,
                'message' => 'Enquiry marked as interested',
            ]);

        $this->assertDatabaseHas('enquiries', [
            'id' => $enquiry->id,
            'status' => 'interested',
        ]);

        $this->assertNotNull($enquiry->fresh()->responded_at);
    }

    /**
     * Test marking enquiry as not interested
     */
    public function test_can_mark_enquiry_as_not_interested()
    {
        $enquiry = Enquiry::factory()->asContacted()->create([
            'enquirer_id' => $this->tenantUser->id,
            'unit_id' => $this->unit->id,
            'owner_id' => $this->owner->id,
        ]);

        $response = $this->actingAs($this->owner)
            ->postJson("/api/v1/enquiries/{$enquiry->id}/not-interested");

        $response->assertStatus(200)
            ->assertJson([
                'success' => true,
                'message' => 'Enquiry marked as not interested',
            ]);

        $this->assertDatabaseHas('enquiries', [
            'id' => $enquiry->id,
            'status' => 'not_interested',
        ]);
    }

    /**
     * Test closing an enquiry
     */
    public function test_can_close_enquiry()
    {
        $enquiry = Enquiry::factory()->asInterested()->create([
            'enquirer_id' => $this->tenantUser->id,
            'unit_id' => $this->unit->id,
            'owner_id' => $this->owner->id,
        ]);

        $response = $this->actingAs($this->owner)
            ->postJson("/api/v1/enquiries/{$enquiry->id}/close");

        $response->assertStatus(200)
            ->assertJson([
                'success' => true,
                'message' => 'Enquiry closed',
            ]);

        $this->assertDatabaseHas('enquiries', [
            'id' => $enquiry->id,
            'status' => 'closed',
        ]);

        $this->assertNotNull($enquiry->fresh()->closed_at);
    }

    /**
     * Test adding communication entry
     */
    public function test_can_add_communication_entry()
    {
        $enquiry = Enquiry::factory()->create([
            'enquirer_id' => $this->tenantUser->id,
            'unit_id' => $this->unit->id,
            'owner_id' => $this->owner->id,
        ]);

        $response = $this->actingAs($this->owner)
            ->postJson("/api/v1/enquiries/{$enquiry->id}/communication", [
                'type' => 'call',
                'message' => 'Called the enquirer to discuss details',
            ]);

        $response->assertStatus(200)
            ->assertJson([
                'success' => true,
                'message' => 'Communication entry added',
            ]);

        $enquiry->refresh();
        $history = $enquiry->getCommunicationHistory();
        
        $this->assertCount(2, $history); // Original enquiry + new communication
        $this->assertEquals('call', $history[1]['type']);
        $this->assertEquals('Called the enquirer to discuss details', $history[1]['message']);
    }

    /**
     * Test getting enquiry statistics
     */
    public function test_can_get_enquiry_statistics()
    {
        // Create enquiries with different statuses
        Enquiry::factory()->asNew()->count(2)->create([
            'owner_id' => $this->owner->id,
            'unit_id' => $this->unit->id,
        ]);

        Enquiry::factory()->asContacted()->count(1)->create([
            'owner_id' => $this->owner->id,
            'unit_id' => $this->unit->id,
        ]);

        Enquiry::factory()->asInterested()->count(1)->create([
            'owner_id' => $this->owner->id,
            'unit_id' => $this->unit->id,
        ]);

        // Debug: Check what enquiries were created
        $enquiries = Enquiry::where('owner_id', $this->owner->id)->get();
        dump('Created enquiries:', $enquiries->toArray());

        $response = $this->actingAs($this->owner)
            ->getJson('/api/v1/enquiries/statistics/overview');

        $response->assertStatus(200)
            ->assertJsonStructure([
                'success',
                'data' => [
                    'total',
                    'new',
                    'contacted',
                    'interested',
                    'not_interested',
                    'closed',
                    'urgent',
                    'high_priority',
                    'recent_30_days',
                    'recent_7_days',
                ],
            ]);

        $data = $response->json('data');
        dump('Statistics response:', $data);
        
        $this->assertEquals(4, $data['total']);
        $this->assertEquals(2, $data['new']);
        $this->assertEquals(1, $data['contacted']);
        $this->assertEquals(1, $data['interested']);
    }

    /**
     * Test getting available units for enquiry
     */
    public function test_can_get_available_units_for_enquiry()
    {
        // Create available units
        Unit::factory()->count(3)->create(['status' => Unit::STATUS_VACANT]);

        $response = $this->actingAs($this->tenantUser)
            ->getJson('/api/v1/enquiries/available-units');

        $response->assertStatus(200)
            ->assertJsonStructure([
                'success',
                'data' => [
                    '*' => [
                        'id',
                        'unit_number',
                        'block',
                        'floor',
                        'type',
                        'owner',
                    ],
                ],
            ]);

        $this->assertCount(4, $response->json('data')); // 3 new + 1 from setUp
    }

    /**
     * Test validation errors for enquiry creation
     */
    public function test_enquiry_creation_validation()
    {
        $response = $this->actingAs($this->tenantUser)
            ->postJson('/api/v1/enquiries', [
                'unit_id' => 999, // Non-existent unit
                'enquiry_type' => 'invalid_type',
                'message' => '', // Empty message
            ]);

        $response->assertStatus(422)
            ->assertJsonValidationErrors(['unit_id', 'enquiry_type', 'message']);
    }

    /**
     * Test unauthorized access to enquiries
     */
    public function test_unauthorized_access_to_enquiries()
    {
        $enquiry = Enquiry::factory()->create([
            'enquirer_id' => $this->tenantUser->id,
            'unit_id' => $this->unit->id,
            'owner_id' => $this->owner->id,
        ]);

        // Different tenant trying to access enquiry
        $otherTenant = User::factory()->create(['role' => 'tenant']);
        
        $response = $this->actingAs($otherTenant)
            ->getJson("/api/v1/enquiries/{$enquiry->id}");

        $response->assertStatus(403);
    }

    /**
     * Test enquiry priority sorting
     */
    public function test_enquiry_priority_sorting()
    {
        // Create enquiries with different priorities
        Enquiry::factory()->asUrgent()->create([
            'enquirer_id' => $this->tenantUser->id,
            'unit_id' => $this->unit->id,
            'owner_id' => $this->owner->id,
        ]);

        Enquiry::factory()->asHighPriority()->create([
            'enquirer_id' => $this->tenantUser->id,
            'unit_id' => $this->unit->id,
            'owner_id' => $this->owner->id,
        ]);

        Enquiry::factory()->create([
            'enquirer_id' => $this->tenantUser->id,
            'unit_id' => $this->unit->id,
            'owner_id' => $this->owner->id,
            'priority' => 'low',
        ]);

        $response = $this->actingAs($this->tenantUser)
            ->getJson('/api/v1/enquiries');

        $response->assertStatus(200);
        
        $enquiries = $response->json('data.data');
        $this->assertEquals('urgent', $enquiries[0]['priority']);
        $this->assertEquals('high', $enquiries[1]['priority']);
        $this->assertEquals('low', $enquiries[2]['priority']);
    }
}
