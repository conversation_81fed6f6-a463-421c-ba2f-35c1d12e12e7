<?php

namespace Tests\Feature;

use App\Models\User;
use App\Models\Tenant;
use App\Models\EmergencyContact;
use App\Models\Document;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Foundation\Testing\WithFaker;
use Tests\TestCase;
use Laravel\Sanctum\Sanctum;

class TenantProfileTest extends TestCase
{
    use RefreshDatabase, WithFaker;

    protected $tenant;
    protected $user;

    protected function setUp(): void
    {
        parent::setUp();
        
        // Create a tenant user for testing
        $this->user = User::factory()->create([
            'role' => 'tenant',
            'email' => '<EMAIL>',
        ]);
        
        $this->tenant = Tenant::factory()->create([
            'user_id' => $this->user->id,
            'occupation' => 'Software Engineer',
            'monthly_income' => 80000,
            'family_members' => 2,
        ]);
        
        // Create emergency contacts
        EmergencyContact::factory()->count(2)->create([
            'tenant_id' => $this->tenant->id,
        ]);
        
        // Create KYC documents
        Document::factory()->count(3)->create([
            'documentable_type' => Tenant::class,
            'documentable_id' => $this->tenant->id,
            'type' => 'id_proof',
            'category' => 'kyc',
        ]);
    }

    /** @test */
    public function it_can_get_profile_requirements()
    {
        $response = $this->actingAs($this->user, 'sanctum')
            ->getJson('/api/v1/profile/requirements');

        $response->assertStatus(200)
            ->assertJsonStructure([
                'success',
                'data' => [
                    'personal_info' => [
                        'required_fields',
                        'optional_fields',
                        'validation_rules',
                    ],
                    'family_details',
                    'emergency_contacts',
                    'kyc_documents',
                    'references',
                    'preferences',
                ],
            ]);
    }

    /** @test */
    public function it_can_get_tenant_profile_with_completion_status()
    {
        Sanctum::actingAs($this->user);

        $response = $this->getJson('/api/v1/tenant/profile/');

        $response->assertStatus(200)
            ->assertJsonStructure([
                'success',
                'data' => [
                    'profile' => [
                        'id',
                        'tenant_code',
                        'user',
                        'personal_info',
                        'family_details',
                        'emergency_contacts',
                        'status_info',
                    ],
                    'completion' => [
                        'overall_percentage',
                        'status',
                        'sections',
                        'next_steps',
                        'is_complete',
                        'missing_required_fields',
                    ],
                ],
            ]);
    }

    /** @test */
    public function it_can_update_personal_info()
    {
        Sanctum::actingAs($this->user);

        $data = [
            'occupation' => 'Senior Software Engineer',
            'company_name' => 'Tech Corp',
            'company_address' => '123 Tech Street',
            'monthly_income' => 95000,
            'special_requirements' => 'Pet-friendly unit',
        ];

        $response = $this->patchJson('/api/v1/tenant/profile/personal-info', $data);

        $response->assertStatus(200)
            ->assertJson([
                'success' => true,
                'message' => 'Personal information updated successfully',
            ]);

        $this->assertDatabaseHas('tenants', [
            'id' => $this->tenant->id,
            'occupation' => 'Senior Software Engineer',
            'company_name' => 'Tech Corp',
            'monthly_income' => 95000,
        ]);
    }

    /** @test */
    public function it_can_update_family_details()
    {
        Sanctum::actingAs($this->user);

        $data = [
            'family_members' => 3,
            'family_details' => [
                [
                    'name' => 'John Doe',
                    'relationship' => 'Self',
                    'age' => 30,
                    'occupation' => 'Engineer',
                ],
                [
                    'name' => 'Jane Doe',
                    'relationship' => 'Spouse',
                    'age' => 28,
                    'occupation' => 'Designer',
                ],
                [
                    'name' => 'Baby Doe',
                    'relationship' => 'Child',
                    'age' => 2,
                ],
            ],
        ];

        $response = $this->patchJson('/api/v1/tenant/profile/family-details', $data);

        $response->assertStatus(200)
            ->assertJson([
                'success' => true,
                'message' => 'Family details updated successfully',
            ]);

        $this->assertDatabaseHas('tenants', [
            'id' => $this->tenant->id,
            'family_members' => 3,
        ]);
    }

    /** @test */
    public function it_can_update_preferences()
    {
        Sanctum::actingAs($this->user);

        $data = [
            'preferences' => [
                'notifications' => [
                    'email' => true,
                    'sms' => false,
                    'push' => true,
                    'newsletter' => true,
                ],
                'privacy' => [
                    'profile_visibility' => 'private',
                    'contact_sharing' => false,
                ],
                'communication' => [
                    'preferred_language' => 'en',
                    'preferred_contact_method' => 'email',
                ],
                'maintenance' => [
                    'preferred_time_slots' => ['morning', 'afternoon'],
                    'emergency_contact_priority' => true,
                ],
            ],
        ];

        $response = $this->patchJson('/api/v1/tenant/profile/preferences', $data);

        $response->assertStatus(200)
            ->assertJson([
                'success' => true,
                'message' => 'Preferences updated successfully',
            ]);

        $this->tenant->refresh();
        $this->assertNotNull($this->tenant->preferences);
        $this->assertEquals('private', $this->tenant->preferences['privacy']['profile_visibility']);
    }

    /** @test */
    public function it_can_update_references()
    {
        Sanctum::actingAs($this->user);

        $data = [
            'references' => [
                [
                    'name' => 'Reference One',
                    'relationship' => 'Former Landlord',
                    'phone' => '+1234567890',
                    'email' => '<EMAIL>',
                    'address' => '123 Reference St',
                    'years_known' => 3,
                ],
                [
                    'name' => 'Reference Two',
                    'relationship' => 'Colleague',
                    'phone' => '+0987654321',
                    'email' => '<EMAIL>',
                    'years_known' => 5,
                ],
            ],
        ];

        $response = $this->patchJson('/api/v1/tenant/profile/references', $data);

        $response->assertStatus(200)
            ->assertJson([
                'success' => true,
                'message' => 'References updated successfully',
            ]);

        $this->tenant->refresh();
        $this->assertCount(2, $this->tenant->references);
        $this->assertEquals('Reference One', $this->tenant->references[0]['name']);
    }

    /** @test */
    public function it_can_get_completion_status()
    {
        Sanctum::actingAs($this->user);

        $response = $this->getJson('/api/v1/tenant/profile/completion');

        $response->assertStatus(200)
            ->assertJsonStructure([
                'success',
                'data' => [
                    'overall_percentage',
                    'status',
                    'sections',
                    'next_steps',
                    'is_complete',
                    'missing_required_fields',
                ],
            ]);
    }

    /** @test */
    public function it_validates_personal_info_update()
    {
        Sanctum::actingAs($this->user);

        $data = [
            'monthly_income' => -1000, // Invalid negative income
            'move_in_date' => '2020-01-01', // Past date
        ];

        $response = $this->patchJson('/api/v1/tenant/profile/personal-info', $data);

        $response->assertStatus(422)
            ->assertJsonValidationErrors(['monthly_income', 'move_in_date']);
    }

    /** @test */
    public function it_validates_family_details_update()
    {
        Sanctum::actingAs($this->user);

        $data = [
            'family_members' => 25, // Exceeds maximum
            'family_details' => [
                [
                    'name' => '', // Required field missing
                    'relationship' => 'Spouse',
                    'age' => 150, // Invalid age
                ],
            ],
        ];

        $response = $this->patchJson('/api/v1/tenant/profile/family-details', $data);

        $response->assertStatus(422)
            ->assertJsonValidationErrors(['family_members', 'family_details.0.name', 'family_details.0.age']);
    }

    /** @test */
    public function it_validates_references_update()
    {
        Sanctum::actingAs($this->user);

        $data = [
            'references' => [
                [
                    'name' => '', // Required field missing
                    'relationship' => '',
                    'phone' => '',
                ],
            ],
        ];

        $response = $this->patchJson('/api/v1/tenant/profile/references', $data);

        $response->assertStatus(422)
            ->assertJsonValidationErrors(['references.0.name', 'references.0.relationship', 'references.0.phone']);
    }

    /** @test */
    public function admin_can_get_profile_statistics()
    {
        $admin = User::factory()->create(['role' => 'admin']);
        Sanctum::actingAs($admin);

        // Create additional tenants for statistics
        $tenants = Tenant::factory()->count(5)->create();

        $response = $this->getJson('/api/v1/profiles/statistics');

        $response->assertStatus(200)
            ->assertJsonStructure([
                'success',
                'data' => [
                    'overview' => [
                        'total_tenants',
                        'average_completion',
                        'completion_levels',
                    ],
                    'field_completion',
                    'period_days',
                ],
            ]);
    }

    /** @test */
    public function non_tenant_cannot_access_tenant_profile_endpoints()
    {
        $admin = User::factory()->create(['role' => 'admin']);
        Sanctum::actingAs($admin);

        $response = $this->getJson('/api/v1/tenant/profile/');
        $response->assertStatus(403);

        $response = $this->patchJson('/api/v1/tenant/profile/personal-info', []);
        $response->assertStatus(403);
    }

    /** @test */
    public function non_admin_cannot_access_admin_profile_endpoints()
    {
        Sanctum::actingAs($this->user);

        $response = $this->getJson('/api/v1/profiles/statistics');
        $response->assertStatus(403);
    }

    /** @test */
    public function it_calculates_profile_completion_correctly()
    {
        Sanctum::actingAs($this->user);

        // Update tenant with complete information
        $this->tenant->update([
            'occupation' => 'Engineer',
            'monthly_income' => 80000,
            'company_name' => 'Tech Corp',
            'family_members' => 2,
            'references' => [
                [
                    'name' => 'Reference One',
                    'relationship' => 'Former Landlord',
                    'phone' => '+1234567890',
                ],
                [
                    'name' => 'Reference Two',
                    'relationship' => 'Colleague',
                    'phone' => '+0987654321',
                ],
            ],
            'preferences' => [
                'notifications' => ['email' => true],
            ],
        ]);

        $response = $this->getJson('/api/v1/tenant/profile/completion');

        $response->assertStatus(200);
        $data = $response->json('data');
        
        // Should have high completion percentage due to filled fields
        $this->assertGreaterThan(70, $data['overall_percentage']);
        $this->assertIsArray($data['sections']);
        $this->assertIsArray($data['next_steps']);
    }
}
