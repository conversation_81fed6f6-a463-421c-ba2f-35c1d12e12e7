<?php

namespace Tests\Feature;

use Tests\TestCase;
use App\Models\Agreement;
use App\Models\Tenant;
use App\Models\Unit;
use App\Models\AgreementTemplate;
use App\Models\User;
use App\Services\AgreementStorageService;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Http\UploadedFile;
use Illuminate\Support\Facades\Storage;

class AgreementStorageTest extends TestCase
{
    use RefreshDatabase;

    protected function setUp(): void
    {
        parent::setUp();
        Storage::fake('agreements');
    }

    /** @test */
    public function it_can_upload_signed_agreement()
    {
        $user = User::factory()->create(['role' => 'admin']);
        $tenant = Tenant::factory()->create();
        $unit = Unit::factory()->create();
        $template = AgreementTemplate::factory()->create();
        
        $agreement = Agreement::factory()->create([
            'tenant_id' => $tenant->id,
            'unit_id' => $unit->id,
            'template_id' => $template->id,
            'status' => 'draft',
        ]);

        $file = UploadedFile::fake()->create('signed_agreement.pdf', 1024, 'application/pdf');

        $response = $this->actingAs($user)
            ->postJson("/api/v1/agreements/{$agreement->id}/upload-signed", [
                'signed_file' => $file,
            ]);

        $response->assertStatus(200)
            ->assertJsonStructure([
                'message',
                'file_path',
                'agreement' => [
                    'id',
                    'tenant_id',
                    'unit_id',
                    'template_id',
                    'signed_file_path',
                    'signed_at',
                ]
            ]);

        $this->assertDatabaseHas('agreements', [
            'id' => $agreement->id,
            'signed_file_path' => $response->json('file_path'),
        ]);

        $agreement->refresh();
        $this->assertNotNull($agreement->signed_at);
    }

    /** @test */
    public function it_validates_file_type_for_signed_agreement()
    {
        $user = User::factory()->create(['role' => 'admin']);
        $agreement = Agreement::factory()->create();

        $file = UploadedFile::fake()->create('document.txt', 1024, 'text/plain');

        $response = $this->actingAs($user)
            ->postJson("/api/v1/agreements/{$agreement->id}/upload-signed", [
                'signed_file' => $file,
            ]);

        $response->assertStatus(422)
            ->assertJsonValidationErrors(['signed_file']);
    }

    /** @test */
    public function it_validates_file_size_for_signed_agreement()
    {
        $user = User::factory()->create(['role' => 'admin']);
        $agreement = Agreement::factory()->create();

        $file = UploadedFile::fake()->create('large_file.pdf', 11 * 1024, 'application/pdf');

        $response = $this->actingAs($user)
            ->postJson("/api/v1/agreements/{$agreement->id}/upload-signed", [
                'signed_file' => $file,
            ]);

        $response->assertStatus(422)
            ->assertJsonValidationErrors(['signed_file']);
    }

    /** @test */
    public function it_can_download_signed_agreement()
    {
        $user = User::factory()->create(['role' => 'admin']);
        $agreement = Agreement::factory()->create([
            'signed_file_path' => 'agreements/2024/01/test_signed_agreement.pdf',
        ]);

        // Create a fake file
        Storage::disk('agreements')->put('agreements/2024/01/test_signed_agreement.pdf', 'fake pdf content');

        $response = $this->actingAs($user)
            ->getJson("/api/v1/agreements/{$agreement->id}/download-signed");

        $response->assertStatus(200);
        $this->assertEquals('application/pdf', $response->headers->get('content-type'));
    }

    /** @test */
    public function it_returns_404_when_signed_agreement_not_found()
    {
        $user = User::factory()->create(['role' => 'admin']);
        $agreement = Agreement::factory()->create([
            'signed_file_path' => null,
        ]);

        $response = $this->actingAs($user)
            ->getJson("/api/v1/agreements/{$agreement->id}/download-signed");

        $response->assertStatus(404)
            ->assertJson(['error' => 'Signed agreement file not found']);
    }

    /** @test */
    public function it_can_delete_signed_agreement()
    {
        $user = User::factory()->create(['role' => 'admin']);
        $agreement = Agreement::factory()->create([
            'signed_file_path' => 'agreements/2024/01/test_signed_agreement.pdf',
            'signed_at' => now(),
        ]);

        // Create a fake file
        Storage::disk('agreements')->put('agreements/2024/01/test_signed_agreement.pdf', 'fake pdf content');

        $response = $this->actingAs($user)
            ->deleteJson("/api/v1/agreements/{$agreement->id}/delete-signed");

        $response->assertStatus(200)
            ->assertJson(['message' => 'Signed agreement deleted successfully']);

        $agreement->refresh();
        $this->assertNull($agreement->signed_file_path);
        $this->assertNull($agreement->signed_at);
    }

    /** @test */
    public function it_requires_authentication_for_signed_agreement_operations()
    {
        $agreement = Agreement::factory()->create();

        $response = $this->postJson("/api/v1/agreements/{$agreement->id}/upload-signed", [
            'signed_file' => UploadedFile::fake()->create('test.pdf', 1024, 'application/pdf'),
        ]);

        $response->assertStatus(401);

        $response = $this->getJson("/api/v1/agreements/{$agreement->id}/download-signed");
        $response->assertStatus(401);

        $response = $this->deleteJson("/api/v1/agreements/{$agreement->id}/delete-signed");
        $response->assertStatus(401);
    }

    /** @test */
    public function it_requires_authorization_for_signed_agreement_operations()
    {
        $user = User::factory()->create(['role' => 'tenant']);
        $agreement = Agreement::factory()->create();

        $response = $this->actingAs($user)
            ->postJson("/api/v1/agreements/{$agreement->id}/upload-signed", [
                'signed_file' => UploadedFile::fake()->create('test.pdf', 1024, 'application/pdf'),
            ]);

        $response->assertStatus(403);
    }

    /** @test */
    public function it_accepts_multiple_file_types_for_signed_agreement()
    {
        $user = User::factory()->create(['role' => 'admin']);
        $agreement = Agreement::factory()->create();

        $fileTypes = [
            'application/pdf' => 'agreement.pdf',
            'image/jpeg' => 'agreement.jpg',
            'image/png' => 'agreement.png',
        ];

        foreach ($fileTypes as $mimeType => $filename) {
            $file = UploadedFile::fake()->create($filename, 1024, $mimeType);

            $response = $this->actingAs($user)
                ->postJson("/api/v1/agreements/{$agreement->id}/upload-signed", [
                    'signed_file' => $file,
                ]);

            $response->assertStatus(200);

            // Clean up for next iteration
            $agreement->update(['signed_file_path' => null, 'signed_at' => null]);
        }
    }

    /** @test */
    public function it_generates_unique_filenames_for_signed_agreements()
    {
        $user = User::factory()->create(['role' => 'admin']);
        $agreement = Agreement::factory()->create();

        $file1 = UploadedFile::fake()->create('agreement.pdf', 1024, 'application/pdf');
        $file2 = UploadedFile::fake()->create('agreement.pdf', 1024, 'application/pdf');

        $response1 = $this->actingAs($user)
            ->postJson("/api/v1/agreements/{$agreement->id}/upload-signed", [
                'signed_file' => $file1,
            ]);

        $response2 = $this->actingAs($user)
            ->postJson("/api/v1/agreements/{$agreement->id}/upload-signed", [
                'signed_file' => $file2,
            ]);

        $response1->assertStatus(200);
        $response2->assertStatus(200);

        $this->assertNotEquals(
            $response1->json('file_path'),
            $response2->json('file_path')
        );
    }
} 