<?php

namespace Tests\Feature;

use App\Events\PaymentCompletedEvent;
use App\Models\RentPayment;
use App\Models\RentReceipt;
use App\Models\User;
use App\Models\Unit;
use App\Models\Property;
use App\Services\ReceiptGenerationService;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Support\Facades\Event;
use Illuminate\Support\Facades\Storage;
use Tests\TestCase;

class AutomaticReceiptGenerationTest extends TestCase
{
    use RefreshDatabase;

    protected function setUp(): void
    {
        parent::setUp();
        Storage::fake('public');
    }

    /** @test */
    public function it_generates_receipt_automatically_when_payment_is_completed()
    {
        Event::fake();

        // Create test data
        $property = Property::factory()->create();
        $unit = Unit::factory()->create(['property_id' => $property->id]);
        $tenant = User::factory()->create(['role' => 'tenant']);
        $payment = RentPayment::factory()->create([
            'unit_id' => $unit->id,
            'tenant_id' => $tenant->id,
            'status' => 'completed',
            'amount' => 5000,
        ]);

        // Trigger payment completed event
        event(new PaymentCompletedEvent($payment));

        // Assert receipt was created
        $this->assertDatabaseHas('rent_receipts', [
            'payment_id' => $payment->id,
            'recipient_email' => $tenant->email,
            'delivery_status' => 'pending',
        ]);

        $receipt = RentReceipt::where('payment_id', $payment->id)->first();
        $this->assertNotNull($receipt);
        $this->assertNotNull($receipt->receipt_number);
        $this->assertNotNull($receipt->pdf_path);
        $this->assertNotNull($receipt->qr_code_path);

        // Assert audit log was created
        $this->assertDatabaseHas('receipt_audit_logs', [
            'receipt_id' => $receipt->id,
            'action' => 'receipt_generated_automatically',
        ]);
    }

    /** @test */
    public function it_does_not_generate_duplicate_receipts()
    {
        Event::fake();

        // Create test data
        $property = Property::factory()->create();
        $unit = Unit::factory()->create(['property_id' => $property->id]);
        $tenant = User::factory()->create(['role' => 'tenant']);
        $payment = RentPayment::factory()->create([
            'unit_id' => $unit->id,
            'tenant_id' => $tenant->id,
            'status' => 'completed',
        ]);

        // Create existing receipt
        $existingReceipt = RentReceipt::factory()->create([
            'payment_id' => $payment->id,
        ]);

        // Trigger payment completed event
        event(new PaymentCompletedEvent($payment));

        // Assert only one receipt exists
        $receiptCount = RentReceipt::where('payment_id', $payment->id)->count();
        $this->assertEquals(1, $receiptCount);
    }

    /** @test */
    public function it_handles_payment_without_tenant_email()
    {
        Event::fake();

        // Create test data with tenant without email
        $property = Property::factory()->create();
        $unit = Unit::factory()->create(['property_id' => $property->id]);
        $tenant = User::factory()->create([
            'role' => 'tenant',
            'email' => null,
        ]);
        $payment = RentPayment::factory()->create([
            'unit_id' => $unit->id,
            'tenant_id' => $tenant->id,
            'status' => 'completed',
        ]);

        // Trigger payment completed event
        event(new PaymentCompletedEvent($payment));

        // Assert receipt was still created
        $this->assertDatabaseHas('rent_receipts', [
            'payment_id' => $payment->id,
        ]);

        $receipt = RentReceipt::where('payment_id', $payment->id)->first();
        $this->assertNotNull($receipt);
        $this->assertNull($receipt->recipient_email);
    }

    /** @test */
    public function it_generates_receipt_with_correct_numbering()
    {
        Event::fake();

        // Create test data
        $property = Property::factory()->create();
        $unit = Unit::factory()->create(['property_id' => $property->id]);
        $tenant = User::factory()->create(['role' => 'tenant']);
        $payment = RentPayment::factory()->create([
            'unit_id' => $unit->id,
            'tenant_id' => $tenant->id,
            'status' => 'completed',
        ]);

        // Trigger payment completed event
        event(new PaymentCompletedEvent($payment));

        $receipt = RentReceipt::where('payment_id', $payment->id)->first();
        $this->assertNotNull($receipt);

        // Assert receipt number format
        $this->assertMatchesRegularExpression('/^REC-\d{4}-\d{3}$/', $receipt->receipt_number);
    }

    /** @test */
    public function it_creates_pdf_and_qr_code_files()
    {
        Event::fake();

        // Create test data
        $property = Property::factory()->create();
        $unit = Unit::factory()->create(['property_id' => $property->id]);
        $tenant = User::factory()->create(['role' => 'tenant']);
        $payment = RentPayment::factory()->create([
            'unit_id' => $unit->id,
            'tenant_id' => $tenant->id,
            'status' => 'completed',
        ]);

        // Trigger payment completed event
        event(new PaymentCompletedEvent($payment));

        $receipt = RentReceipt::where('payment_id', $payment->id)->first();
        $this->assertNotNull($receipt);

        // Assert files exist
        $this->assertTrue(Storage::disk('public')->exists($receipt->pdf_path));
        $this->assertTrue(Storage::disk('public')->exists($receipt->qr_code_path));

        // Assert file contents
        $pdfContent = Storage::disk('public')->get($receipt->pdf_path);
        $this->assertNotNull($pdfContent);
        $this->assertGreaterThan(0, strlen($pdfContent));

        $qrContent = Storage::disk('public')->get($receipt->qr_code_path);
        $this->assertNotNull($qrContent);
        $this->assertGreaterThan(0, strlen($qrContent));
    }

    /** @test */
    public function it_handles_generation_errors_gracefully()
    {
        Event::fake();

        // Create test data
        $property = Property::factory()->create();
        $unit = Unit::factory()->create(['property_id' => $property->id]);
        $tenant = User::factory()->create(['role' => 'tenant']);
        $payment = RentPayment::factory()->create([
            'unit_id' => $unit->id,
            'tenant_id' => $tenant->id,
            'status' => 'completed',
        ]);

        // Mock ReceiptGenerationService to throw exception
        $this->mock(ReceiptGenerationService::class, function ($mock) {
            $mock->shouldReceive('generateReceipt')
                ->andThrow(new \Exception('PDF generation failed'));
        });

        // Trigger payment completed event
        event(new PaymentCompletedEvent($payment));

        // Assert no receipt was created
        $this->assertDatabaseMissing('rent_receipts', [
            'payment_id' => $payment->id,
        ]);

        // Assert error was logged (this would be handled by the listener)
        // In a real scenario, the error would be logged and the payment processing would continue
    }

    /** @test */
    public function it_generates_receipts_for_multiple_payments()
    {
        Event::fake();

        // Create test data
        $property = Property::factory()->create();
        $unit = Unit::factory()->create(['property_id' => $property->id]);
        $tenant = User::factory()->create(['role' => 'tenant']);

        $payments = RentPayment::factory()->count(3)->create([
            'unit_id' => $unit->id,
            'tenant_id' => $tenant->id,
            'status' => 'completed',
        ]);

        // Trigger events for all payments
        foreach ($payments as $payment) {
            event(new PaymentCompletedEvent($payment));
        }

        // Assert receipts were created for all payments
        foreach ($payments as $payment) {
            $this->assertDatabaseHas('rent_receipts', [
                'payment_id' => $payment->id,
            ]);
        }

        $this->assertEquals(3, RentReceipt::count());
    }

    /** @test */
    public function it_only_generates_receipts_for_completed_payments()
    {
        Event::fake();

        // Create test data
        $property = Property::factory()->create();
        $unit = Unit::factory()->create(['property_id' => $property->id]);
        $tenant = User::factory()->create(['role' => 'tenant']);

        $completedPayment = RentPayment::factory()->create([
            'unit_id' => $unit->id,
            'tenant_id' => $tenant->id,
            'status' => 'completed',
        ]);

        $pendingPayment = RentPayment::factory()->create([
            'unit_id' => $unit->id,
            'tenant_id' => $tenant->id,
            'status' => 'pending',
        ]);

        // Trigger events for both payments
        event(new PaymentCompletedEvent($completedPayment));
        event(new PaymentCompletedEvent($pendingPayment));

        // Assert receipt was only created for completed payment
        $this->assertDatabaseHas('rent_receipts', [
            'payment_id' => $completedPayment->id,
        ]);

        $this->assertDatabaseMissing('rent_receipts', [
            'payment_id' => $pendingPayment->id,
        ]);
    }
} 