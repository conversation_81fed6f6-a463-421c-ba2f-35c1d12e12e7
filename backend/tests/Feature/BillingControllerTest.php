<?php

namespace Tests\Feature;

use Tests\TestCase;
use App\Models\BillingRule;
use App\Models\Unit;
use App\Models\User;
use App\Models\Tenant;
use Illuminate\Foundation\Testing\WithFaker;

class BillingControllerTest extends TestCase
{
    use WithFaker;

    protected $admin;
    protected $owner;
    protected $tenant;
    protected $unit;
    protected $billingRule;

    protected function setUp(): void
    {
        parent::setUp();

        $this->admin = $this->actingAsAdmin();
        $this->owner = User::factory()->create(['role' => 'owner']);
        $this->tenant = User::factory()->create(['role' => 'tenant']);
        
        $this->unit = Unit::factory()->create([
            'owner_id' => $this->owner->id,
            'status' => 'rented',
            'unit_number' => 'A101',
            'area_sqft' => 1200,
        ]);

        $this->billingRule = BillingRule::factory()->create([
            'name' => 'Monthly Rent',
            'type' => 'rent',
            'calculation_method' => 'fixed',
            'amount' => 25000,
            'is_active' => true,
            'applies_to' => 'all_units',
        ]);
    }

    /** @test */
    public function it_can_list_billing_rules()
    {
        BillingRule::factory()->count(3)->create();

        $response = $this->getJson('/api/v1/billing/rules');

        $this->assertPaginatedResponse($response, [
            '*' => [
                'id',
                'name',
                'type',
                'calculation_method',
                'amount',
                'is_active',
                'applies_to',
                'created_at',
                'updated_at',
            ]
        ]);
    }

    /** @test */
    public function it_can_filter_billing_rules_by_type()
    {
        BillingRule::factory()->create(['type' => 'maintenance']);
        BillingRule::factory()->create(['type' => 'utility']);

        $response = $this->getJson('/api/v1/billing/rules?filter[type]=maintenance');

        $response->assertStatus(200);
        $data = $response->json('data');
        
        foreach ($data as $rule) {
            $this->assertEquals('maintenance', $rule['type']);
        }
    }

    /** @test */
    public function it_can_filter_active_billing_rules()
    {
        BillingRule::factory()->create(['is_active' => true]);
        BillingRule::factory()->create(['is_active' => false]);

        $response = $this->getJson('/api/v1/billing/rules?filter[is_active]=true');

        $response->assertStatus(200);
        $data = $response->json('data');
        
        foreach ($data as $rule) {
            $this->assertTrue($rule['is_active']);
        }
    }

    /** @test */
    public function it_can_create_billing_rule()
    {
        $ruleData = [
            'name' => 'Maintenance Charges',
            'type' => 'maintenance',
            'calculation_method' => 'per_sqft',
            'amount' => 5.50,
            'description' => 'Monthly maintenance charges per square foot',
            'is_active' => true,
            'applies_to' => 'all_units',
            'frequency' => 'monthly',
            'due_day' => 5,
            'late_fee_amount' => 100,
            'late_fee_days' => 7,
        ];

        $response = $this->postJson('/api/v1/billing/rules', $ruleData);

        $response->assertStatus(201)
            ->assertJsonStructure([
                'success',
                'message',
                'data' => [
                    'id',
                    'name',
                    'type',
                    'calculation_method',
                    'amount',
                    'is_active',
                    'applies_to',
                    'frequency',
                    'due_day',
                ]
            ]);

        $this->assertDatabaseHas('billing_rules', [
            'name' => 'Maintenance Charges',
            'type' => 'maintenance',
            'calculation_method' => 'per_sqft',
            'amount' => 5.50,
        ]);
    }

    /** @test */
    public function it_validates_required_fields_when_creating_billing_rule()
    {
        $response = $this->postJson('/api/v1/billing/rules', []);

        $this->assertValidationError($response, [
            'name',
            'type',
            'calculation_method',
            'amount',
        ]);
    }

    /** @test */
    public function it_validates_billing_rule_type()
    {
        $response = $this->postJson('/api/v1/billing/rules', [
            'name' => 'Test Rule',
            'type' => 'invalid_type',
            'calculation_method' => 'fixed',
            'amount' => 1000,
        ]);

        $this->assertValidationError($response, ['type']);
    }

    /** @test */
    public function it_validates_calculation_method()
    {
        $response = $this->postJson('/api/v1/billing/rules', [
            'name' => 'Test Rule',
            'type' => 'maintenance',
            'calculation_method' => 'invalid_method',
            'amount' => 1000,
        ]);

        $this->assertValidationError($response, ['calculation_method']);
    }

    /** @test */
    public function it_can_show_specific_billing_rule()
    {
        $response = $this->getJson("/api/v1/billing/rules/{$this->billingRule->id}");

        $this->assertSuccessResponse($response, [
            'id',
            'name',
            'type',
            'calculation_method',
            'amount',
            'description',
            'is_active',
            'applies_to',
            'frequency',
            'due_day',
            'late_fee_amount',
            'late_fee_days',
            'created_at',
            'updated_at',
        ]);
    }

    /** @test */
    public function it_can_update_billing_rule()
    {
        $updateData = [
            'name' => 'Updated Monthly Rent',
            'amount' => 27000,
            'description' => 'Updated rent amount for 2024',
            'late_fee_amount' => 150,
        ];

        $response = $this->putJson("/api/v1/billing/rules/{$this->billingRule->id}", $updateData);

        $this->assertSuccessResponse($response);

        $this->assertDatabaseHas('billing_rules', [
            'id' => $this->billingRule->id,
            'name' => 'Updated Monthly Rent',
            'amount' => 27000,
            'late_fee_amount' => 150,
        ]);
    }

    /** @test */
    public function it_can_toggle_billing_rule_status()
    {
        $this->assertTrue($this->billingRule->is_active);

        $response = $this->putJson("/api/v1/billing/rules/{$this->billingRule->id}", [
            'is_active' => false
        ]);

        $this->assertSuccessResponse($response);

        $this->billingRule->refresh();
        $this->assertFalse($this->billingRule->is_active);
    }

    /** @test */
    public function it_can_delete_billing_rule()
    {
        $response = $this->deleteJson("/api/v1/billing/rules/{$this->billingRule->id}");

        $this->assertSuccessResponse($response);
        $this->assertSoftDeleted('billing_rules', ['id' => $this->billingRule->id]);
    }

    /** @test */
    public function it_can_calculate_bill_for_unit()
    {
        $response = $this->postJson("/api/v1/billing/calculate", [
            'unit_id' => $this->unit->id,
            'billing_period' => '2024-01',
            'rule_ids' => [$this->billingRule->id],
        ]);

        $this->assertSuccessResponse($response, [
            'unit_id',
            'billing_period',
            'calculations' => [
                '*' => [
                    'rule_id',
                    'rule_name',
                    'calculation_method',
                    'base_amount',
                    'calculated_amount',
                    'details',
                ]
            ],
            'total_amount',
            'breakdown',
        ]);
    }

    /** @test */
    public function it_validates_bill_calculation_request()
    {
        $response = $this->postJson("/api/v1/billing/calculate", []);

        $this->assertValidationError($response, [
            'unit_id',
            'billing_period',
        ]);
    }

    /** @test */
    public function it_can_get_billing_summary_for_unit()
    {
        $response = $this->getJson("/api/v1/billing/units/{$this->unit->id}/summary");

        $this->assertSuccessResponse($response, [
            'unit_id',
            'current_period',
            'applicable_rules' => [
                '*' => [
                    'id',
                    'name',
                    'type',
                    'amount',
                    'frequency',
                ]
            ],
            'recent_calculations',
            'total_outstanding',
            'next_due_date',
        ]);
    }

    /** @test */
    public function it_can_get_billing_analytics()
    {
        $response = $this->getJson('/api/v1/billing/analytics');

        $this->assertSuccessResponse($response, [
            'total_rules',
            'active_rules',
            'rules_by_type',
            'total_monthly_revenue',
            'collection_efficiency',
            'outstanding_amounts',
            'recent_activity',
        ]);
    }

    /** @test */
    public function non_admin_cannot_create_billing_rules()
    {
        $this->actingAs($this->owner);

        $response = $this->postJson('/api/v1/billing/rules', [
            'name' => 'Test Rule',
            'type' => 'maintenance',
            'calculation_method' => 'fixed',
            'amount' => 1000,
        ]);

        $this->assertForbidden($response);
    }

    /** @test */
    public function non_admin_cannot_update_billing_rules()
    {
        $this->actingAs($this->owner);

        $response = $this->putJson("/api/v1/billing/rules/{$this->billingRule->id}", [
            'amount' => 30000
        ]);

        $this->assertForbidden($response);
    }

    /** @test */
    public function non_admin_cannot_delete_billing_rules()
    {
        $this->actingAs($this->owner);

        $response = $this->deleteJson("/api/v1/billing/rules/{$this->billingRule->id}");

        $this->assertForbidden($response);
    }

    /** @test */
    public function it_handles_not_found_billing_rules()
    {
        $nonExistentId = 99999;
        
        $this->getJson("/api/v1/billing/rules/{$nonExistentId}")
            ->assertStatus(404);
            
        $this->putJson("/api/v1/billing/rules/{$nonExistentId}", [])
            ->assertStatus(404);
            
        $this->deleteJson("/api/v1/billing/rules/{$nonExistentId}")
            ->assertStatus(404);
    }

    /** @test */
    public function it_requires_authentication_for_all_endpoints()
    {
        // Test without authentication
        $this->postJson('/api/v1/billing/rules', [])->assertStatus(401);
        $this->getJson('/api/v1/billing/rules')->assertStatus(401);
        $this->getJson("/api/v1/billing/rules/{$this->billingRule->id}")->assertStatus(401);
        $this->putJson("/api/v1/billing/rules/{$this->billingRule->id}", [])->assertStatus(401);
        $this->deleteJson("/api/v1/billing/rules/{$this->billingRule->id}")->assertStatus(401);
        $this->postJson('/api/v1/billing/calculate', [])->assertStatus(401);
        $this->getJson('/api/v1/billing/analytics')->assertStatus(401);
    }
}
