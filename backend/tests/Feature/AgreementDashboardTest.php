<?php

namespace Tests\Feature;

use Tests\TestCase;
use App\Models\User;
use App\Models\Tenant;
use App\Models\Unit;
use App\Models\Agreement;
use App\Models\AgreementTemplate;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Foundation\Testing\WithFaker;
use Carbon\Carbon;

class AgreementDashboardTest extends TestCase
{
    use RefreshDatabase, WithFaker;

    protected $user;
    protected $tenant;
    protected $unit;
    protected $template;

    protected function setUp(): void
    {
        parent::setUp();
        $this->user = User::factory()->create(['role' => 'admin']);
        $this->tenant = Tenant::factory()->create();
        $this->unit = Unit::factory()->create(['owner_id' => $this->user->id]);
        $this->template = AgreementTemplate::factory()->create();
    }

    /** @test */
    public function it_returns_dashboard_status_metrics()
    {
        // Create agreements with various statuses
        Agreement::factory()->create([
            'tenant_id' => $this->tenant->id,
            'unit_id' => $this->unit->id,
            'template_id' => $this->template->id,
            'created_by' => $this->user->id,
            'status' => 'active',
            'end_date' => now()->addDays(10),
            'signed_at' => now()->subDays(5),
            'renewal_reminder_sent' => true,
            'renewal_reminder_sent_at' => now()->subDays(2),
        ]);
        Agreement::factory()->create([
            'tenant_id' => $this->tenant->id,
            'unit_id' => $this->unit->id,
            'template_id' => $this->template->id,
            'created_by' => $this->user->id,
            'status' => 'expired',
            'end_date' => now()->subDays(1),
        ]);
        Agreement::factory()->create([
            'tenant_id' => $this->tenant->id,
            'unit_id' => $this->unit->id,
            'template_id' => $this->template->id,
            'created_by' => $this->user->id,
            'status' => 'active',
            'end_date' => now()->addDays(25),
            'signed_at' => now()->subDays(20),
        ]);

        $response = $this->actingAs($this->user)
            ->getJson('/api/v1/agreements/dashboard/status');

        $response->assertStatus(200)
            ->assertJsonStructure([
                'success',
                'data' => [
                    'status_counts',
                    'expiring_soon',
                    'recently_signed',
                    'reminders_sent',
                    'total',
                    'top_expiring' => [
                        '*' => [
                            'id',
                            'tenant',
                            'unit',
                            'end_date',
                            // ... other fields
                        ]
                    ]
                ]
            ]);
    }
} 