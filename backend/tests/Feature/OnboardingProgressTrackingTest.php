<?php

namespace Tests\Feature;

use Tests\TestCase;
use App\Models\User;
use App\Models\Tenant;
use App\Models\Unit;
use App\Models\Document;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Foundation\Testing\WithFaker;

class OnboardingProgressTrackingTest extends TestCase
{
    use RefreshDatabase, WithFaker;

    protected $user;
    protected $tenant;
    protected $admin;

    protected function setUp(): void
    {
        parent::setUp();
        
        // Create admin user
        $this->admin = User::factory()->create([
            'role' => 'admin',
            'first_name' => 'Admin',
            'last_name' => 'User',
            'email' => '<EMAIL>',
            'status' => User::STATUS_ACTIVE,
            'is_verified' => true
        ]);

        // Create tenant user
        $this->user = User::factory()->create([
            'role' => 'tenant',
            'first_name' => 'John',
            'last_name' => 'Doe',
            'email' => '<EMAIL>',
            'phone' => '1234567890',
            'date_of_birth' => '1990-01-01',
            'status' => User::STATUS_ACTIVE,
            'is_verified' => true
        ]);

        // Create tenant record
        $this->tenant = Tenant::create([
            'user_id' => $this->user->id,
            'tenant_code' => 'TN00001',
            'unit_id' => null,
            'owner_id' => null,
            'family_members' => 1,
            'family_details' => [
                [
                    'name' => 'John Doe',
                    'relationship' => 'self',
                    'age' => 30,
                    'gender' => 'male'
                ]
            ],
            'occupation' => 'Software Engineer',
            'company_name' => 'Tech Corp',
            'monthly_income' => 50000,
            'references' => [
                [
                    'name' => 'Reference 1',
                    'phone' => '1111111111',
                    'relationship' => 'Friend'
                ],
                [
                    'name' => 'Reference 2',
                    'phone' => '2222222222',
                    'relationship' => 'Colleague'
                ]
            ],
            'preferences' => [
                'unit_type' => '2bhk',
                'furnished' => 'semi'
            ]
        ]);

        // Add emergency contact to user
        $this->user->update([
            'emergency_contact' => [
                'primary' => [
                    'name' => 'Emergency Contact',
                    'phone' => '9999999999',
                    'relationship' => 'Spouse'
                ]
            ]
        ]);
    }

    /** @test */
    public function it_can_get_onboarding_progress_for_tenant()
    {
        $response = $this->actingAs($this->user)
            ->getJson('/api/v1/onboarding/progress');

        $response->assertStatus(200)
            ->assertJsonStructure([
                'success',
                'data' => [
                    'progress_percentage',
                    'current_step',
                    'completed_steps',
                    'pending_steps',
                    'can_proceed',
                    'kyc_status',
                    'missing_documents',
                    'step_details',
                    'next_steps',
                    'metrics',
                    'is_complete',
                    'last_activity'
                ]
            ]);

        $data = $response->json('data');
        $this->assertGreaterThan(0, $data['progress_percentage']);
        $this->assertIsArray($data['completed_steps']);
        $this->assertIsArray($data['pending_steps']);
    }

    /** @test */
    public function it_can_get_admin_dashboard_for_onboarding_progress()
    {
        $response = $this->actingAs($this->admin)
            ->getJson('/api/v1/admin/onboarding/dashboard');

        $response->assertStatus(200)
            ->assertJsonStructure([
                'success',
                'data' => [
                    'overview' => [
                        'total_tenants',
                        'completed_tenants',
                        'incomplete_tenants',
                        'completion_rate',
                        'average_progress'
                    ],
                    'progress_distribution',
                    'step_completion',
                    'stuck_tenants',
                    'recent_activity'
                ]
            ]);
    }

    /** @test */
    public function it_can_get_specific_tenant_progress_for_admin()
    {
        $response = $this->actingAs($this->admin)
            ->getJson("/api/v1/admin/onboarding/tenants/{$this->tenant->id}/progress");

        $response->assertStatus(200)
            ->assertJsonStructure([
                'success',
                'data' => [
                    'progress_percentage',
                    'current_step',
                    'completed_steps',
                    'pending_steps',
                    'step_details',
                    'next_steps',
                    'admin_notes',
                    'owner_notes',
                    'created_at',
                    'updated_at'
                ]
            ]);
    }

    /** @test */
    public function it_can_send_reminders_to_incomplete_tenants()
    {
        $response = $this->actingAs($this->admin)
            ->postJson('/api/v1/admin/onboarding/reminders', [
                'reminder_type' => 'email',
                'message' => 'Please complete your onboarding process'
            ]);

        $response->assertStatus(200)
            ->assertJsonStructure([
                'success',
                'data' => [
                    'reminders_sent',
                    'total_tenants',
                    'reminder_type'
                ]
            ]);

        $data = $response->json('data');
        $this->assertGreaterThanOrEqual(0, $data['reminders_sent']);
    }

    /** @test */
    public function it_calculates_progress_correctly_for_complete_tenant()
    {
        // Add required documents to make tenant complete
        Document::create([
            'documentable_type' => Tenant::class,
            'documentable_id' => $this->tenant->id,
            'title' => 'ID Proof Document',
            'type' => 'id_proof',
            'category' => 'kyc',
            'file_name' => 'id_proof.pdf',
            'filename' => 'id_proof.pdf',
            'file_path' => 'documents/id_proof.pdf',
            'file_type' => 'pdf',
            'file_size' => 1024,
            'mime_type' => 'application/pdf',
            'status' => Document::STATUS_PENDING,
            'uploaded_by' => $this->user->id
        ]);

        Document::create([
            'documentable_type' => Tenant::class,
            'documentable_id' => $this->tenant->id,
            'title' => 'Address Proof Document',
            'type' => 'address_proof',
            'category' => 'kyc',
            'file_name' => 'address_proof.pdf',
            'filename' => 'address_proof.pdf',
            'file_path' => 'documents/address_proof.pdf',
            'file_type' => 'pdf',
            'file_size' => 1024,
            'mime_type' => 'application/pdf',
            'status' => Document::STATUS_PENDING,
            'uploaded_by' => $this->user->id
        ]);

        Document::create([
            'documentable_type' => Tenant::class,
            'documentable_id' => $this->tenant->id,
            'title' => 'Photo Document',
            'type' => 'photo',
            'category' => 'kyc',
            'file_name' => 'photo.jpg',
            'filename' => 'photo.jpg',
            'file_path' => 'documents/photo.jpg',
            'file_type' => 'jpg',
            'file_size' => 512,
            'mime_type' => 'image/jpeg',
            'status' => Document::STATUS_PENDING,
            'uploaded_by' => $this->user->id
        ]);

        // Update tenant to submitted status
        $this->tenant->update([
            'kyc_status' => Tenant::KYC_SUBMITTED,
            'kyc_submitted_at' => now()
        ]);

        $response = $this->actingAs($this->user)
            ->getJson('/api/v1/onboarding/progress');

        $data = $response->json('data');
        
        $this->assertEquals(100, $data['progress_percentage']);
        $this->assertTrue($data['is_complete']);
        $this->assertEquals('completed', $data['current_step']);
    }

    /** @test */
    public function it_identifies_missing_steps_correctly()
    {
        // Create a tenant with minimal information
        $minimalUser = User::factory()->create([
            'role' => 'tenant',
            'first_name' => 'Jane',
            'last_name' => 'Smith',
            'email' => '<EMAIL>',
            'status' => User::STATUS_ACTIVE,
            'is_verified' => true
            // Missing phone and date_of_birth
        ]);

        $minimalTenant = Tenant::create([
            'user_id' => $minimalUser->id,
            'tenant_code' => 'TN00002',
            'unit_id' => null,
            'owner_id' => null,
            'family_members' => 1
            // Missing most required fields
        ]);

        $response = $this->actingAs($minimalUser)
            ->getJson('/api/v1/onboarding/progress');

        $data = $response->json('data');
        $this->assertLessThan(50, $data['progress_percentage']);
        $this->assertContains('personal_details', $data['pending_steps']);
        $this->assertNotEmpty($data['next_steps']);
    }

    /** @test */
    public function it_tracks_reminder_sent_timestamp()
    {
        $response = $this->actingAs($this->admin)
            ->postJson('/api/v1/admin/onboarding/reminders', [
                'reminder_type' => 'email',
                'tenant_ids' => [$this->tenant->id]
            ]);

        $response->assertStatus(200);

        // Refresh tenant model
        $this->tenant->refresh();
        
        $this->assertNotNull($this->tenant->last_reminder_sent_at);
        $this->assertEquals(1, $this->tenant->reminder_count);
    }

    /** @test */
    public function it_validates_reminder_request()
    {
        $response = $this->actingAs($this->admin)
            ->postJson('/api/v1/admin/onboarding/reminders', [
                'reminder_type' => 'invalid_type'
            ]);

        $response->assertStatus(422)
            ->assertJsonValidationErrors(['reminder_type']);
    }

    /** @test */
    public function it_filters_admin_dashboard_by_status()
    {
        $response = $this->actingAs($this->admin)
            ->getJson('/api/v1/admin/onboarding/dashboard?status=incomplete');

        $response->assertStatus(200);
        
        $data = $response->json('data');
        $this->assertGreaterThanOrEqual(0, $data['overview']['incomplete_tenants']);
    }

    /** @test */
    public function it_calculates_step_completion_percentages()
    {
        $response = $this->actingAs($this->admin)
            ->getJson('/api/v1/admin/onboarding/dashboard');

        $data = $response->json('data');
        
        $this->assertArrayHasKey('step_completion', $data);
        $this->assertArrayHasKey('personal_details', $data['step_completion']);
        $this->assertArrayHasKey('family_details', $data['step_completion']);
        $this->assertArrayHasKey('employment_details', $data['step_completion']);
        $this->assertArrayHasKey('references', $data['step_completion']);
        $this->assertArrayHasKey('emergency_contacts', $data['step_completion']);
        $this->assertArrayHasKey('preferences', $data['step_completion']);
        $this->assertArrayHasKey('documents', $data['step_completion']);
        $this->assertArrayHasKey('review', $data['step_completion']);
    }
}
