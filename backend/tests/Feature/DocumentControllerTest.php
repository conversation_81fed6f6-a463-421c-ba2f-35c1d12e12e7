<?php

namespace Tests\Feature;

use Tests\TestCase;
use App\Models\Document;
use App\Models\User;
use App\Models\Tenant;
use App\Models\Unit;
use Illuminate\Foundation\Testing\WithFaker;
use Illuminate\Http\UploadedFile;
use Illuminate\Support\Facades\Storage;

class DocumentControllerTest extends TestCase
{
    use WithFaker;

    protected $admin;
    protected $owner;
    protected $tenant;
    protected $tenantModel;
    protected $document;

    protected function setUp(): void
    {
        parent::setUp();

        $this->admin = $this->actingAsAdmin();
        $this->owner = User::factory()->create(['role' => 'owner']);
        $this->tenant = User::factory()->create(['role' => 'tenant']);
        
        $this->tenantModel = Tenant::factory()->create([
            'user_id' => $this->tenant->id,
        ]);

        $this->document = Document::factory()->create([
            'documentable_type' => Tenant::class,
            'documentable_id' => $this->tenantModel->id,
            'type' => 'id_proof',
            'category' => 'kyc',
            'name' => 'Aadhaar Card',
            'file_path' => 'documents/aadhaar.pdf',
            'file_size' => 1024000,
            'mime_type' => 'application/pdf',
            'status' => 'pending',
        ]);
    }

    /** @test */
    public function it_can_list_documents()
    {
        Document::factory()->count(3)->create([
            'documentable_type' => Tenant::class,
            'documentable_id' => $this->tenantModel->id,
        ]);

        $response = $this->getJson('/api/v1/documents');

        $this->assertPaginatedResponse($response, [
            '*' => [
                'id',
                'documentable_type',
                'documentable_id',
                'type',
                'category',
                'name',
                'file_path',
                'file_size',
                'mime_type',
                'status',
                'uploaded_by',
                'verified_by',
                'verified_at',
                'created_at',
                'updated_at',
            ]
        ]);
    }

    /** @test */
    public function it_can_filter_documents_by_type()
    {
        Document::factory()->create(['type' => 'address_proof']);
        Document::factory()->create(['type' => 'income_proof']);

        $response = $this->getJson('/api/v1/documents?filter[type]=address_proof');

        $response->assertStatus(200);
        $data = $response->json('data');
        
        foreach ($data as $document) {
            $this->assertEquals('address_proof', $document['type']);
        }
    }

    /** @test */
    public function it_can_filter_documents_by_category()
    {
        Document::factory()->create(['category' => 'kyc']);
        Document::factory()->create(['category' => 'agreement']);

        $response = $this->getJson('/api/v1/documents?filter[category]=kyc');

        $response->assertStatus(200);
        $data = $response->json('data');
        
        foreach ($data as $document) {
            $this->assertEquals('kyc', $document['category']);
        }
    }

    /** @test */
    public function it_can_filter_documents_by_status()
    {
        Document::factory()->create(['status' => 'verified']);
        Document::factory()->create(['status' => 'rejected']);

        $response = $this->getJson('/api/v1/documents?filter[status]=verified');

        $response->assertStatus(200);
        $data = $response->json('data');
        
        foreach ($data as $document) {
            $this->assertEquals('verified', $document['status']);
        }
    }

    /** @test */
    public function tenant_can_only_see_their_documents()
    {
        $this->actingAs($this->tenant);
        
        // Create document for another tenant
        $otherTenant = Tenant::factory()->create();
        Document::factory()->create([
            'documentable_type' => Tenant::class,
            'documentable_id' => $otherTenant->id,
        ]);

        $response = $this->getJson('/api/v1/documents');

        $response->assertStatus(200);
        $data = $response->json('data');
        
        // Should only see documents for their tenant record
        foreach ($data as $document) {
            if ($document['documentable_type'] === Tenant::class) {
                $this->assertEquals($this->tenantModel->id, $document['documentable_id']);
            }
        }
    }

    /** @test */
    public function it_can_upload_document()
    {
        $this->actingAs($this->tenant);

        $file = $this->createTestFile('test-document.pdf', 'application/pdf');

        $documentData = [
            'documentable_type' => Tenant::class,
            'documentable_id' => $this->tenantModel->id,
            'type' => 'address_proof',
            'category' => 'kyc',
            'name' => 'Electricity Bill',
            'description' => 'Latest electricity bill as address proof',
            'file' => $file,
        ];

        $response = $this->postJson('/api/v1/documents', $documentData);

        $response->assertStatus(201)
            ->assertJsonStructure([
                'success',
                'message',
                'data' => [
                    'id',
                    'documentable_type',
                    'documentable_id',
                    'type',
                    'category',
                    'name',
                    'file_path',
                    'file_size',
                    'mime_type',
                    'status',
                ]
            ]);

        $this->assertDatabaseHas('documents', [
            'documentable_type' => Tenant::class,
            'documentable_id' => $this->tenantModel->id,
            'type' => 'address_proof',
            'name' => 'Electricity Bill',
            'status' => 'pending',
        ]);

        // Check file was stored
        $document = Document::latest()->first();
        Storage::disk('public')->assertExists($document->file_path);
    }

    /** @test */
    public function it_validates_required_fields_when_uploading()
    {
        $this->actingAs($this->tenant);

        $response = $this->postJson('/api/v1/documents', []);

        $this->assertValidationError($response, [
            'documentable_type',
            'documentable_id',
            'type',
            'category',
            'name',
            'file',
        ]);
    }

    /** @test */
    public function it_validates_file_type_and_size()
    {
        $this->actingAs($this->tenant);

        // Test invalid file type
        $invalidFile = $this->createTestFile('test.exe', 'application/x-executable');

        $response = $this->postJson('/api/v1/documents', [
            'documentable_type' => Tenant::class,
            'documentable_id' => $this->tenantModel->id,
            'type' => 'id_proof',
            'category' => 'kyc',
            'name' => 'Test Document',
            'file' => $invalidFile,
        ]);

        $this->assertValidationError($response, ['file']);

        // Test file too large (assuming 10MB limit)
        $largeFile = $this->createTestFile('large.pdf', 'application/pdf', 11 * 1024 * 1024);

        $response = $this->postJson('/api/v1/documents', [
            'documentable_type' => Tenant::class,
            'documentable_id' => $this->tenantModel->id,
            'type' => 'id_proof',
            'category' => 'kyc',
            'name' => 'Large Document',
            'file' => $largeFile,
        ]);

        $this->assertValidationError($response, ['file']);
    }

    /** @test */
    public function it_can_show_specific_document()
    {
        $response = $this->getJson("/api/v1/documents/{$this->document->id}");

        $this->assertSuccessResponse($response, [
            'id',
            'documentable_type',
            'documentable_id',
            'type',
            'category',
            'name',
            'description',
            'file_path',
            'file_size',
            'mime_type',
            'status',
            'uploaded_by',
            'verified_by',
            'verified_at',
            'rejection_reason',
            'metadata',
            'created_at',
            'updated_at',
        ]);
    }

    /** @test */
    public function it_can_update_document_metadata()
    {
        $this->actingAs($this->tenant);

        $updateData = [
            'name' => 'Updated Aadhaar Card',
            'description' => 'Updated description for Aadhaar card',
            'metadata' => [
                'document_number' => 'XXXX-XXXX-1234',
                'expiry_date' => '2030-12-31',
            ],
        ];

        $response = $this->putJson("/api/v1/documents/{$this->document->id}", $updateData);

        $this->assertSuccessResponse($response);

        $this->assertDatabaseHas('documents', [
            'id' => $this->document->id,
            'name' => 'Updated Aadhaar Card',
            'description' => 'Updated description for Aadhaar card',
        ]);
    }

    /** @test */
    public function admin_can_verify_document()
    {
        $response = $this->putJson("/api/v1/documents/{$this->document->id}/verify", [
            'status' => 'verified',
            'verification_notes' => 'Document verified successfully',
        ]);

        $this->assertSuccessResponse($response);

        $this->assertDatabaseHas('documents', [
            'id' => $this->document->id,
            'status' => 'verified',
            'verified_by' => $this->admin->id,
        ]);

        $this->document->refresh();
        $this->assertNotNull($this->document->verified_at);
    }

    /** @test */
    public function admin_can_reject_document()
    {
        $response = $this->putJson("/api/v1/documents/{$this->document->id}/verify", [
            'status' => 'rejected',
            'rejection_reason' => 'Document is not clear',
            'verification_notes' => 'Please upload a clearer image',
        ]);

        $this->assertSuccessResponse($response);

        $this->assertDatabaseHas('documents', [
            'id' => $this->document->id,
            'status' => 'rejected',
            'rejection_reason' => 'Document is not clear',
            'verified_by' => $this->admin->id,
        ]);
    }

    /** @test */
    public function non_admin_cannot_verify_documents()
    {
        $this->actingAs($this->tenant);

        $response = $this->putJson("/api/v1/documents/{$this->document->id}/verify", [
            'status' => 'verified',
        ]);

        $this->assertForbidden($response);
    }

    /** @test */
    public function it_can_download_document()
    {
        // Create a real file for download test
        Storage::disk('public')->put($this->document->file_path, 'test content');

        $response = $this->getJson("/api/v1/documents/{$this->document->id}/download");

        $response->assertStatus(200)
            ->assertHeader('Content-Type', $this->document->mime_type);
    }

    /** @test */
    public function it_can_delete_document()
    {
        $this->actingAs($this->tenant);

        // Create a real file to test deletion
        Storage::disk('public')->put($this->document->file_path, 'test content');

        $response = $this->deleteJson("/api/v1/documents/{$this->document->id}");

        $this->assertSuccessResponse($response);
        $this->assertSoftDeleted('documents', ['id' => $this->document->id]);

        // Check file was deleted from storage
        Storage::disk('public')->assertMissing($this->document->file_path);
    }

    /** @test */
    public function it_can_get_document_requirements()
    {
        $response = $this->getJson('/api/v1/documents/requirements');

        $this->assertSuccessResponse($response, [
            'kyc' => [
                '*' => [
                    'type',
                    'name',
                    'description',
                    'required',
                    'accepted_formats',
                    'max_size',
                ]
            ],
            'agreement' => [
                '*' => [
                    'type',
                    'name',
                    'description',
                    'required',
                    'accepted_formats',
                    'max_size',
                ]
            ],
        ]);
    }

    /** @test */
    public function it_can_get_document_statistics()
    {
        $response = $this->getJson('/api/v1/documents/statistics');

        $this->assertSuccessResponse($response, [
            'total_documents',
            'by_status' => [
                'pending',
                'verified',
                'rejected',
            ],
            'by_type',
            'by_category',
            'recent_uploads',
            'verification_pending',
        ]);
    }

    /** @test */
    public function it_handles_not_found_documents()
    {
        $nonExistentId = 99999;
        
        $this->getJson("/api/v1/documents/{$nonExistentId}")
            ->assertStatus(404);
            
        $this->putJson("/api/v1/documents/{$nonExistentId}", [])
            ->assertStatus(404);
            
        $this->deleteJson("/api/v1/documents/{$nonExistentId}")
            ->assertStatus(404);
    }

    /** @test */
    public function it_requires_authentication_for_all_endpoints()
    {
        // Test without authentication
        $this->postJson('/api/v1/documents', [])->assertStatus(401);
        $this->getJson('/api/v1/documents')->assertStatus(401);
        $this->getJson("/api/v1/documents/{$this->document->id}")->assertStatus(401);
        $this->putJson("/api/v1/documents/{$this->document->id}", [])->assertStatus(401);
        $this->deleteJson("/api/v1/documents/{$this->document->id}")->assertStatus(401);
        $this->getJson("/api/v1/documents/{$this->document->id}/download")->assertStatus(401);
        $this->getJson('/api/v1/documents/requirements')->assertStatus(401);
        $this->getJson('/api/v1/documents/statistics')->assertStatus(401);
    }
}
