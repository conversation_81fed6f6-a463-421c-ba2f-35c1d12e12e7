<?php

namespace Tests\Feature;

use Tests\TestCase;
use App\Models\Notification;
use App\Models\User;
use Illuminate\Foundation\Testing\WithFaker;
use Illuminate\Support\Facades\Queue;

class NotificationControllerTest extends TestCase
{
    use WithFaker;

    protected $admin;
    protected $owner;
    protected $tenant;
    protected $notification;

    protected function setUp(): void
    {
        parent::setUp();

        $this->admin = $this->actingAsAdmin();
        $this->owner = User::factory()->create(['role' => 'owner']);
        $this->tenant = User::factory()->create(['role' => 'tenant']);
        
        $this->notification = Notification::factory()->create([
            'user_id' => $this->tenant->id,
            'type' => 'payment_reminder',
            'title' => 'Payment Due Reminder',
            'message' => 'Your monthly rent payment is due in 3 days.',
            'data' => [
                'amount' => 25000,
                'due_date' => '2024-01-05',
                'unit_number' => 'A101',
            ],
            'channels' => ['email', 'in_app'],
            'priority' => 'medium',
            'is_read' => false,
            'scheduled_at' => now(),
        ]);
    }

    /** @test */
    public function it_can_list_user_notifications()
    {
        $this->actingAs($this->tenant);

        // Create additional notifications for the user
        Notification::factory()->count(3)->create([
            'user_id' => $this->tenant->id,
        ]);

        // Create notification for another user (should not appear)
        Notification::factory()->create([
            'user_id' => $this->owner->id,
        ]);

        $response = $this->getJson('/api/v1/notifications');

        $this->assertPaginatedResponse($response, [
            '*' => [
                'id',
                'type',
                'title',
                'message',
                'data',
                'channels',
                'priority',
                'is_read',
                'read_at',
                'scheduled_at',
                'sent_at',
                'created_at',
                'updated_at',
            ]
        ]);

        $data = $response->json('data');
        
        // Should only see notifications for the authenticated user
        foreach ($data as $notification) {
            $this->assertEquals($this->tenant->id, $notification['user_id']);
        }
    }

    /** @test */
    public function it_can_filter_notifications_by_type()
    {
        $this->actingAs($this->tenant);

        Notification::factory()->create([
            'user_id' => $this->tenant->id,
            'type' => 'agreement_signed',
        ]);
        Notification::factory()->create([
            'user_id' => $this->tenant->id,
            'type' => 'maintenance_request',
        ]);

        $response = $this->getJson('/api/v1/notifications?filter[type]=payment_reminder');

        $response->assertStatus(200);
        $data = $response->json('data');
        
        foreach ($data as $notification) {
            $this->assertEquals('payment_reminder', $notification['type']);
        }
    }

    /** @test */
    public function it_can_filter_notifications_by_read_status()
    {
        $this->actingAs($this->tenant);

        Notification::factory()->create([
            'user_id' => $this->tenant->id,
            'is_read' => true,
        ]);
        Notification::factory()->create([
            'user_id' => $this->tenant->id,
            'is_read' => false,
        ]);

        $response = $this->getJson('/api/v1/notifications?filter[is_read]=false');

        $response->assertStatus(200);
        $data = $response->json('data');
        
        foreach ($data as $notification) {
            $this->assertFalse($notification['is_read']);
        }
    }

    /** @test */
    public function it_can_filter_notifications_by_priority()
    {
        $this->actingAs($this->tenant);

        Notification::factory()->create([
            'user_id' => $this->tenant->id,
            'priority' => 'high',
        ]);
        Notification::factory()->create([
            'user_id' => $this->tenant->id,
            'priority' => 'low',
        ]);

        $response = $this->getJson('/api/v1/notifications?filter[priority]=high');

        $response->assertStatus(200);
        $data = $response->json('data');
        
        foreach ($data as $notification) {
            $this->assertEquals('high', $notification['priority']);
        }
    }

    /** @test */
    public function admin_can_create_notification()
    {
        $notificationData = [
            'user_id' => $this->tenant->id,
            'type' => 'system_announcement',
            'title' => 'System Maintenance Notice',
            'message' => 'The system will be under maintenance on Sunday from 2 AM to 6 AM.',
            'data' => [
                'maintenance_date' => '2024-01-07',
                'duration' => '4 hours',
            ],
            'channels' => ['email', 'in_app', 'sms'],
            'priority' => 'high',
            'scheduled_at' => now()->addHours(1),
        ];

        $response = $this->postJson('/api/v1/notifications', $notificationData);

        $response->assertStatus(201)
            ->assertJsonStructure([
                'success',
                'message',
                'data' => [
                    'id',
                    'user_id',
                    'type',
                    'title',
                    'message',
                    'data',
                    'channels',
                    'priority',
                    'scheduled_at',
                ]
            ]);

        $this->assertDatabaseHas('notifications', [
            'user_id' => $this->tenant->id,
            'type' => 'system_announcement',
            'title' => 'System Maintenance Notice',
            'priority' => 'high',
        ]);
    }

    /** @test */
    public function it_validates_required_fields_when_creating_notification()
    {
        $response = $this->postJson('/api/v1/notifications', []);

        $this->assertValidationError($response, [
            'user_id',
            'type',
            'title',
            'message',
        ]);
    }

    /** @test */
    public function it_validates_notification_type()
    {
        $response = $this->postJson('/api/v1/notifications', [
            'user_id' => $this->tenant->id,
            'type' => 'invalid_type',
            'title' => 'Test Notification',
            'message' => 'Test message',
        ]);

        $this->assertValidationError($response, ['type']);
    }

    /** @test */
    public function it_validates_notification_priority()
    {
        $response = $this->postJson('/api/v1/notifications', [
            'user_id' => $this->tenant->id,
            'type' => 'general',
            'title' => 'Test Notification',
            'message' => 'Test message',
            'priority' => 'invalid_priority',
        ]);

        $this->assertValidationError($response, ['priority']);
    }

    /** @test */
    public function it_validates_notification_channels()
    {
        $response = $this->postJson('/api/v1/notifications', [
            'user_id' => $this->tenant->id,
            'type' => 'general',
            'title' => 'Test Notification',
            'message' => 'Test message',
            'channels' => ['invalid_channel'],
        ]);

        $this->assertValidationError($response, ['channels.0']);
    }

    /** @test */
    public function non_admin_cannot_create_notifications()
    {
        $this->actingAs($this->tenant);

        $response = $this->postJson('/api/v1/notifications', [
            'user_id' => $this->owner->id,
            'type' => 'general',
            'title' => 'Test Notification',
            'message' => 'Test message',
        ]);

        $this->assertForbidden($response);
    }

    /** @test */
    public function it_can_show_specific_notification()
    {
        $this->actingAs($this->tenant);

        $response = $this->getJson("/api/v1/notifications/{$this->notification->id}");

        $this->assertSuccessResponse($response, [
            'id',
            'user_id',
            'type',
            'title',
            'message',
            'data',
            'channels',
            'priority',
            'is_read',
            'read_at',
            'scheduled_at',
            'sent_at',
            'delivery_status',
            'created_at',
            'updated_at',
        ]);
    }

    /** @test */
    public function user_cannot_see_others_notifications()
    {
        $this->actingAs($this->owner);

        $response = $this->getJson("/api/v1/notifications/{$this->notification->id}");

        $this->assertForbidden($response);
    }

    /** @test */
    public function it_can_mark_notification_as_read()
    {
        $this->actingAs($this->tenant);

        $this->assertFalse($this->notification->is_read);

        $response = $this->putJson("/api/v1/notifications/{$this->notification->id}/read");

        $this->assertSuccessResponse($response);

        $this->notification->refresh();
        $this->assertTrue($this->notification->is_read);
        $this->assertNotNull($this->notification->read_at);
    }

    /** @test */
    public function it_can_mark_notification_as_unread()
    {
        $this->actingAs($this->tenant);

        // First mark as read
        $this->notification->update(['is_read' => true, 'read_at' => now()]);

        $response = $this->putJson("/api/v1/notifications/{$this->notification->id}/unread");

        $this->assertSuccessResponse($response);

        $this->notification->refresh();
        $this->assertFalse($this->notification->is_read);
        $this->assertNull($this->notification->read_at);
    }

    /** @test */
    public function it_can_mark_all_notifications_as_read()
    {
        $this->actingAs($this->tenant);

        // Create additional unread notifications
        Notification::factory()->count(3)->create([
            'user_id' => $this->tenant->id,
            'is_read' => false,
        ]);

        $response = $this->putJson('/api/v1/notifications/mark-all-read');

        $this->assertSuccessResponse($response);

        // Check all user's notifications are marked as read
        $unreadCount = Notification::where('user_id', $this->tenant->id)
            ->where('is_read', false)
            ->count();
        
        $this->assertEquals(0, $unreadCount);
    }

    /** @test */
    public function it_can_delete_notification()
    {
        $this->actingAs($this->tenant);

        $response = $this->deleteJson("/api/v1/notifications/{$this->notification->id}");

        $this->assertSuccessResponse($response);
        $this->assertSoftDeleted('notifications', ['id' => $this->notification->id]);
    }

    /** @test */
    public function it_can_get_notification_statistics()
    {
        $this->actingAs($this->tenant);

        $response = $this->getJson('/api/v1/notifications/statistics');

        $this->assertSuccessResponse($response, [
            'total_notifications',
            'unread_count',
            'read_count',
            'by_type',
            'by_priority',
            'recent_notifications',
            'delivery_stats',
        ]);
    }

    /** @test */
    public function admin_can_get_system_notification_statistics()
    {
        $response = $this->getJson('/api/v1/notifications/system-statistics');

        $this->assertSuccessResponse($response, [
            'total_notifications',
            'notifications_by_type',
            'notifications_by_priority',
            'delivery_statistics',
            'user_engagement',
            'recent_activity',
        ]);
    }

    /** @test */
    public function non_admin_cannot_access_system_statistics()
    {
        $this->actingAs($this->tenant);

        $response = $this->getJson('/api/v1/notifications/system-statistics');

        $this->assertForbidden($response);
    }

    /** @test */
    public function it_can_send_bulk_notifications()
    {
        Queue::fake();

        $bulkData = [
            'user_ids' => [$this->tenant->id, $this->owner->id],
            'type' => 'system_announcement',
            'title' => 'Important Announcement',
            'message' => 'This is an important system announcement.',
            'channels' => ['email', 'in_app'],
            'priority' => 'high',
            'scheduled_at' => now()->addMinutes(30),
        ];

        $response = $this->postJson('/api/v1/notifications/bulk', $bulkData);

        $this->assertSuccessResponse($response, [
            'total_notifications',
            'scheduled_notifications',
            'failed_notifications',
        ]);

        // Check notifications were created
        $this->assertDatabaseHas('notifications', [
            'user_id' => $this->tenant->id,
            'type' => 'system_announcement',
            'title' => 'Important Announcement',
        ]);

        $this->assertDatabaseHas('notifications', [
            'user_id' => $this->owner->id,
            'type' => 'system_announcement',
            'title' => 'Important Announcement',
        ]);
    }

    /** @test */
    public function it_handles_not_found_notifications()
    {
        $this->actingAs($this->tenant);
        
        $nonExistentId = 99999;
        
        $this->getJson("/api/v1/notifications/{$nonExistentId}")
            ->assertStatus(404);
            
        $this->putJson("/api/v1/notifications/{$nonExistentId}/read")
            ->assertStatus(404);
            
        $this->deleteJson("/api/v1/notifications/{$nonExistentId}")
            ->assertStatus(404);
    }

    /** @test */
    public function it_requires_authentication_for_all_endpoints()
    {
        // Test without authentication
        $this->postJson('/api/v1/notifications', [])->assertStatus(401);
        $this->getJson('/api/v1/notifications')->assertStatus(401);
        $this->getJson("/api/v1/notifications/{$this->notification->id}")->assertStatus(401);
        $this->putJson("/api/v1/notifications/{$this->notification->id}/read")->assertStatus(401);
        $this->deleteJson("/api/v1/notifications/{$this->notification->id}")->assertStatus(401);
        $this->getJson('/api/v1/notifications/statistics')->assertStatus(401);
        $this->postJson('/api/v1/notifications/bulk', [])->assertStatus(401);
    }
}
