<?php

namespace Tests\Feature;

use Tests\TestCase;
use App\Models\Agreement;
use App\Models\Tenant;
use App\Models\Unit;
use App\Models\AgreementTemplate;
use App\Models\User;
use App\Services\DigitalSignatureWorkflowService;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Support\Facades\Storage;

class DigitalSignatureWorkflowTest extends TestCase
{
    use RefreshDatabase;

    protected function setUp(): void
    {
        parent::setUp();
        Storage::fake('local');
    }

    /** @test */
    public function it_can_initiate_digital_signature_workflow()
    {
        $user = User::factory()->create(['role' => 'admin']);
        $tenant = Tenant::factory()->create();
        $unit = Unit::factory()->create();
        $template = AgreementTemplate::factory()->create();
        
        $agreement = Agreement::factory()->create([
            'tenant_id' => $tenant->id,
            'unit_id' => $unit->id,
            'template_id' => $template->id,
            'status' => 'draft',
        ]);

        $response = $this->actingAs($user)
            ->postJson("/api/v1/agreements/{$agreement->id}/initiate-digital-signature");

        $response->assertStatus(200)
            ->assertJsonStructure([
                'message',
                'data' => [
                    'signature_request',
                    'invitations',
                    'workflow_status'
                ]
            ]);

        $agreement->refresh();
        $this->assertEquals('pending_signature', $agreement->status);
        $this->assertNotNull($agreement->signature_request_id);
        $this->assertNotNull($agreement->signature_workflow_started_at);
    }

    /** @test */
    public function it_cannot_initiate_workflow_for_non_draft_agreement()
    {
        $user = User::factory()->create(['role' => 'admin']);
        $agreement = Agreement::factory()->create(['status' => 'active']);

        $response = $this->actingAs($user)
            ->postJson("/api/v1/agreements/{$agreement->id}/initiate-digital-signature");

        $response->assertStatus(422)
            ->assertJson(['error' => 'Agreement must be in draft status to initiate signature workflow']);
    }

    /** @test */
    public function it_cannot_initiate_workflow_twice()
    {
        $user = User::factory()->create(['role' => 'admin']);
        $agreement = Agreement::factory()->create([
            'status' => 'draft',
            'signature_request_id' => 'existing_request_id'
        ]);

        $response = $this->actingAs($user)
            ->postJson("/api/v1/agreements/{$agreement->id}/initiate-digital-signature");

        $response->assertStatus(422)
            ->assertJson(['error' => 'Signature workflow already initiated for this agreement']);
    }

    /** @test */
    public function it_can_get_digital_signature_status()
    {
        $user = User::factory()->create(['role' => 'admin']);
        $agreement = Agreement::factory()->create([
            'status' => 'pending_signature',
            'signature_request_id' => 'test_request_id',
            'signature_workflow_started_at' => now(),
        ]);

        $response = $this->actingAs($user)
            ->getJson("/api/v1/agreements/{$agreement->id}/digital-signature-status");

        $response->assertStatus(200)
            ->assertJsonStructure([
                'agreement_id',
                'workflow_status',
                'signature_request',
                'signature_progress' => [
                    'owner_signed',
                    'tenant_signed',
                    'total_signers',
                    'signed_count'
                ],
                'next_actions',
                'timeline'
            ]);
    }

    /** @test */
    public function it_can_cancel_digital_signature_workflow()
    {
        $user = User::factory()->create(['role' => 'admin']);
        $agreement = Agreement::factory()->create([
            'status' => 'pending_signature',
            'signature_request_id' => 'test_request_id',
            'signature_workflow_started_at' => now(),
        ]);

        $response = $this->actingAs($user)
            ->postJson("/api/v1/agreements/{$agreement->id}/cancel-digital-signature", [
                'reason' => 'Agreement terms need revision'
            ]);

        $response->assertStatus(200)
            ->assertJsonStructure([
                'message',
                'data' => [
                    'agreement_id',
                    'workflow_status',
                    'cancelled_by',
                    'cancellation_reason'
                ]
            ]);

        $agreement->refresh();
        $this->assertEquals('draft', $agreement->status);
        $this->assertNull($agreement->signature_request_id);
        $this->assertNotNull($agreement->signature_workflow_cancelled_at);
        $this->assertEquals($user->id, $agreement->signature_workflow_cancelled_by);
        $this->assertEquals('Agreement terms need revision', $agreement->signature_workflow_cancellation_reason);
    }

    /** @test */
    public function it_can_resend_signature_invitations()
    {
        $user = User::factory()->create(['role' => 'admin']);
        $agreement = Agreement::factory()->create([
            'status' => 'pending_signature',
            'signature_request_id' => 'test_request_id',
            'signature_workflow_started_at' => now(),
        ]);

        $response = $this->actingAs($user)
            ->postJson("/api/v1/agreements/{$agreement->id}/resend-signature-invitations");

        $response->assertStatus(200)
            ->assertJsonStructure([
                'message',
                'data' => [
                    'agreement_id',
                    'invitations_resent',
                    'invitations'
                ]
            ]);
    }

    /** @test */
    public function it_cannot_resend_invitations_without_active_request()
    {
        $user = User::factory()->create(['role' => 'admin']);
        $agreement = Agreement::factory()->create([
            'status' => 'pending_signature',
            'signature_request_id' => null,
        ]);

        $response = $this->actingAs($user)
            ->postJson("/api/v1/agreements/{$agreement->id}/resend-signature-invitations");

        $response->assertStatus(500)
            ->assertJson(['error' => 'No active signature request found']);
    }

    /** @test */
    public function it_can_get_signature_reminders()
    {
        $user = User::factory()->create(['role' => 'admin']);
        
        // Create agreements that should trigger reminders
        $oldAgreement = Agreement::factory()->create([
            'status' => 'pending_signature',
            'signature_workflow_started_at' => now()->subDays(5),
        ]);

        $response = $this->actingAs($user)
            ->getJson("/api/v1/agreements/signature-reminders");

        $response->assertStatus(200)
            ->assertJsonStructure([
                'reminders',
                'count'
            ]);

        $this->assertGreaterThan(0, $response->json('count'));
    }

    /** @test */
    public function it_can_process_external_signature()
    {
        $user = User::factory()->create(['role' => 'admin']);
        $agreement = Agreement::factory()->create([
            'status' => 'pending_signature',
            'signature_request_id' => 'test_request_id',
        ]);

        $signatureData = [
            'signer_id' => 'owner_signer',
            'signature_hash' => hash('sha256', 'test_request_id' . 'owner_signer' . now()->toISOString()),
            'signed_at' => now()->toISOString(),
            'signer_role' => 'owner',
            'signer_name' => 'John Owner'
        ];

        $response = $this->actingAs($user)
            ->postJson("/api/v1/agreements/process-external-signature/test_request_id", [
                'signature_data' => $signatureData
            ]);

        $response->assertStatus(200)
            ->assertJsonStructure([
                'message',
                'data' => [
                    'agreement_id',
                    'signature_applied',
                    'workflow_complete',
                    'next_signers'
                ]
            ]);

        $agreement->refresh();
        $this->assertTrue($agreement->signed_by_owner);
        $this->assertFalse($agreement->signed_by_tenant);
    }

    /** @test */
    public function it_validates_signature_data()
    {
        $user = User::factory()->create(['role' => 'admin']);

        $response = $this->actingAs($user)
            ->postJson("/api/v1/agreements/process-external-signature/test_request_id", [
                'signature_data' => [
                    'signer_id' => 'owner_signer',
                    // Missing required fields
                ]
            ]);

        $response->assertStatus(422)
            ->assertJsonValidationErrors(['signature_data.signature_hash', 'signature_data.signed_at', 'signature_data.signer_role', 'signature_data.signer_name']);
    }

    /** @test */
    public function it_requires_authentication_for_workflow_operations()
    {
        $agreement = Agreement::factory()->create();

        $response = $this->postJson("/api/v1/agreements/{$agreement->id}/initiate-digital-signature");
        $response->assertStatus(401);

        $response = $this->getJson("/api/v1/agreements/{$agreement->id}/digital-signature-status");
        $response->assertStatus(401);

        $response = $this->postJson("/api/v1/agreements/{$agreement->id}/cancel-digital-signature");
        $response->assertStatus(401);

        $response = $this->postJson("/api/v1/agreements/{$agreement->id}/resend-signature-invitations");
        $response->assertStatus(401);

        $response = $this->getJson("/api/v1/agreements/signature-reminders");
        $response->assertStatus(401);
    }

    /** @test */
    public function it_requires_authorization_for_workflow_operations()
    {
        $user = User::factory()->create(['role' => 'tenant']);
        $agreement = Agreement::factory()->create();

        $response = $this->actingAs($user)
            ->postJson("/api/v1/agreements/{$agreement->id}/initiate-digital-signature");
        $response->assertStatus(403);

        $response = $this->actingAs($user)
            ->postJson("/api/v1/agreements/{$agreement->id}/cancel-digital-signature");
        $response->assertStatus(403);
    }

    /** @test */
    public function it_completes_workflow_when_both_parties_sign()
    {
        $user = User::factory()->create(['role' => 'admin']);
        $agreement = Agreement::factory()->create([
            'status' => 'pending_signature',
            'signature_request_id' => 'test_request_id',
        ]);

        // Owner signs
        $ownerSignatureData = [
            'signer_id' => 'owner_signer',
            'signature_hash' => hash('sha256', 'test_request_id' . 'owner_signer' . now()->toISOString()),
            'signed_at' => now()->toISOString(),
            'signer_role' => 'owner',
            'signer_name' => 'John Owner'
        ];

        $response = $this->actingAs($user)
            ->postJson("/api/v1/agreements/process-external-signature/test_request_id", [
                'signature_data' => $ownerSignatureData
            ]);

        $response->assertStatus(200);
        $this->assertFalse($response->json('data.workflow_complete'));

        // Tenant signs
        $tenantSignatureData = [
            'signer_id' => 'tenant_signer',
            'signature_hash' => hash('sha256', 'test_request_id' . 'tenant_signer' . now()->toISOString()),
            'signed_at' => now()->toISOString(),
            'signer_role' => 'tenant',
            'signer_name' => 'Jane Tenant'
        ];

        $response = $this->actingAs($user)
            ->postJson("/api/v1/agreements/process-external-signature/test_request_id", [
                'signature_data' => $tenantSignatureData
            ]);

        $response->assertStatus(200);
        $this->assertTrue($response->json('data.workflow_complete'));

        $agreement->refresh();
        $this->assertEquals('active', $agreement->status);
        $this->assertTrue($agreement->signed_by_owner);
        $this->assertTrue($agreement->signed_by_tenant);
        $this->assertNotNull($agreement->signature_workflow_completed_at);
    }

    /** @test */
    public function it_tracks_signature_timeline()
    {
        $user = User::factory()->create(['role' => 'admin']);
        $agreement = Agreement::factory()->create([
            'status' => 'pending_signature',
            'signature_request_id' => 'test_request_id',
            'signature_workflow_started_at' => now()->subDays(1),
        ]);

        $response = $this->actingAs($user)
            ->getJson("/api/v1/agreements/{$agreement->id}/digital-signature-status");

        $response->assertStatus(200);
        
        $timeline = $response->json('timeline');
        $this->assertIsArray($timeline);
        $this->assertGreaterThan(0, count($timeline));
        
        // Check for workflow initiation event
        $initiationEvent = collect($timeline)->firstWhere('event', 'workflow_initiated');
        $this->assertNotNull($initiationEvent);
    }
} 