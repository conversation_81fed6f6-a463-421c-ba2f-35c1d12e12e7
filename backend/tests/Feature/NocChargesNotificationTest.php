<?php

namespace Tests\Feature;

use Tests\TestCase;
use App\Models\User;
use App\Models\Unit;
use App\Models\Tenant;
use App\Models\NocApplication;
use App\Models\NocTemplate;
use App\Services\NocChargesNotificationService;
use App\Events\NocChargesNotificationEvent;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Support\Facades\Event;
use Illuminate\Support\Facades\Log;

class NocChargesNotificationTest extends TestCase
{
    use RefreshDatabase;

    protected $adminUser;
    protected $tenant;
    protected $unit;
    protected $template;

    protected function setUp(): void
    {
        parent::setUp();
        
        // Create admin user
        $this->adminUser = User::factory()->create([
            'role' => 'admin',
        ]);

        // Create tenant
        $this->tenant = Tenant::factory()->create([
            'user_id' => User::factory()->create(['role' => 'tenant'])->id,
        ]);

        // Create unit
        $this->unit = Unit::factory()->create([
            'status' => 'occupied',
        ]);

        // Create NOC template
        $this->template = NocTemplate::factory()->create([
            'noc_type' => 'rental',
            'is_active' => true,
        ]);

        // Set up event listening
        Event::fake();
    }

    public function test_noc_approval_triggers_charges_notification()
    {
        // Create NOC application with proper relationships
        $application = NocApplication::factory()->create([
            'applicant_id' => $this->tenant->user_id,
            'unit_id' => $this->unit->id,
            'template_id' => $this->template->id,
            'noc_type' => 'rental',
            'status' => 'under_review',
            'start_date' => now(),
            'end_date' => now()->addMonths(6),
        ]);

        // Ensure relationships are loaded
        $application->load(['applicant', 'unit', 'template']);

        // Approve the application
        $response = $this->actingAs($this->adminUser)
            ->postJson("/api/v1/noc/applications/{$application->id}/approve");

        $response->assertStatus(200);

        // Assert that the NOC charges notification event was dispatched
        Event::assertDispatched(NocChargesNotificationEvent::class, function ($event) use ($application) {
            return $event->action === 'apply_charges' &&
                   $event->nocData['noc_id'] === $application->id &&
                   $event->nocData['noc_type'] === 'rental';
        });
    }

    public function test_noc_rejection_triggers_cancellation_notification()
    {
        // Create NOC application with proper relationships
        $application = NocApplication::factory()->create([
            'applicant_id' => $this->tenant->user_id,
            'unit_id' => $this->unit->id,
            'template_id' => $this->template->id,
            'noc_type' => 'rental',
            'status' => 'under_review',
            'start_date' => now(),
            'end_date' => now()->addMonths(6),
        ]);

        // Ensure relationships are loaded
        $application->load(['applicant', 'unit', 'template']);

        // Reject the application
        $response = $this->actingAs($this->adminUser)
            ->postJson("/api/v1/noc/applications/{$application->id}/reject", [
                'remarks' => 'Application rejected due to incomplete documentation',
            ]);

        $response->assertStatus(200);

        // Assert that the NOC charges cancellation event was dispatched
        Event::assertDispatched(NocChargesNotificationEvent::class, function ($event) use ($application) {
            return $event->action === 'cancel_charges' &&
                   $event->nocData['noc_id'] === $application->id;
        });
    }

    public function test_noc_charges_service_prepares_correct_data()
    {
        $service = app(NocChargesNotificationService::class);

        // Create NOC application
        $application = NocApplication::factory()->create([
            'applicant_id' => $this->tenant->user_id,
            'unit_id' => $this->unit->id,
            'template_id' => $this->template->id,
            'noc_type' => 'rental',
            'status' => 'approved',
            'start_date' => now(),
            'end_date' => now()->addMonths(6),
        ]);

        // Mock the event dispatch
        Event::fake();

        // Send notification
        $service->notifyNocApproved($application);

        // Assert event was dispatched with correct data
        Event::assertDispatched(NocChargesNotificationEvent::class, function ($event) use ($application) {
            $data = $event->nocData;
            
            return $data['noc_id'] === $application->id &&
                   $data['noc_type'] === 'rental' &&
                   $data['status'] === 'approved' &&
                   isset($data['unit']) &&
                   isset($data['tenant']) &&
                   isset($data['billing_info']) &&
                   $data['billing_info']['charge_type'] === 'noc_charges' &&
                   $data['billing_info']['charge_category'] === 'rental_noc_charges';
        });
    }

    public function test_noc_charges_notification_includes_billing_period()
    {
        // Create NOC application with specific dates
        $startDate = now();
        $endDate = now()->addMonths(3);
        
        $application = NocApplication::factory()->create([
            'applicant_id' => $this->tenant->user_id,
            'unit_id' => $this->unit->id,
            'template_id' => $this->template->id,
            'noc_type' => 'rental',
            'status' => 'under_review',
            'start_date' => $startDate,
            'end_date' => $endDate,
        ]);

        // Ensure relationships are loaded
        $application->load(['applicant', 'unit', 'template']);

        Event::fake();

        // Approve the application to trigger the notification
        $response = $this->actingAs($this->adminUser)
            ->postJson("/api/v1/noc/applications/{$application->id}/approve");

        $response->assertStatus(200);

        // Assert billing period is correctly calculated
        Event::assertDispatched(NocChargesNotificationEvent::class, function ($event) use ($startDate, $endDate) {
            $billingPeriod = $event->nocData['billing_info']['billing_period'];
            
            return $billingPeriod['start_date'] === $startDate->format('Y-m-d') &&
                   $billingPeriod['end_date'] === $endDate->format('Y-m-d') &&
                   $billingPeriod['duration_months'] === 3.0;
        });
    }

    public function test_noc_charges_notification_handles_different_noc_types()
    {
        $service = app(NocChargesNotificationService::class);

        $nocTypes = ['rental', 'residence', 'vehicle', 'renovation', 'transfer'];
        
        foreach ($nocTypes as $nocType) {
            // Create template for this NOC type
            $template = NocTemplate::factory()->create([
                'noc_type' => $nocType,
                'is_active' => true,
            ]);

            // Create NOC application
            $application = NocApplication::factory()->create([
                'applicant_id' => $this->tenant->user_id,
                'unit_id' => $this->unit->id,
                'template_id' => $template->id,
                'noc_type' => $nocType,
                'status' => 'approved',
                'start_date' => now(),
                'end_date' => now()->addMonths(6),
            ]);

            Event::fake();

            // Send notification
            $service->notifyNocApproved($application);

            // Assert correct charge category
            Event::assertDispatched(NocChargesNotificationEvent::class, function ($event) use ($nocType) {
                $expectedCategory = $nocType . '_noc_charges';
                return $event->nocData['billing_info']['charge_category'] === $expectedCategory;
            });
        }
    }

    public function test_noc_charges_notification_logs_success()
    {
        Log::spy();

        $service = app(NocChargesNotificationService::class);

        // Create NOC application
        $application = NocApplication::factory()->create([
            'applicant_id' => $this->tenant->user_id,
            'unit_id' => $this->unit->id,
            'template_id' => $this->template->id,
            'noc_type' => 'rental',
            'status' => 'approved',
            'start_date' => now(),
            'end_date' => now()->addMonths(6),
        ]);

        Event::fake();

        // Send notification
        $service->notifyNocApproved($application);

        // Assert success log was written
        Log::shouldHaveReceived('info')
            ->with('NOC charges notification sent to onesociety', \Mockery::on(function ($args) use ($application) {
                return $args['noc_id'] === $application->id &&
                       $args['action'] === 'apply_charges' &&
                       $args['unit_id'] === $application->unit_id &&
                       $args['tenant_id'] === $application->applicant_id;
            }));
    }
} 