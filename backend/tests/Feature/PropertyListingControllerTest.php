<?php

namespace Tests\Feature;

use Tests\TestCase;
use App\Models\PropertyListing;
use App\Models\Unit;
use App\Models\User;
use App\Models\PropertyLead;
use App\Services\PortalIntegrationService;
use Illuminate\Foundation\Testing\WithFaker;
use Illuminate\Support\Facades\Queue;
use Mockery;

class PropertyListingControllerTest extends TestCase
{
    use WithFaker;

    protected $admin;
    protected $owner;
    protected $tenant;
    protected $unit;
    protected $listing;

    protected function setUp(): void
    {
        parent::setUp();

        $this->admin = $this->actingAsAdmin();
        $this->owner = User::factory()->create(['role' => 'owner']);
        $this->tenant = User::factory()->create(['role' => 'tenant']);
        
        $this->unit = Unit::factory()->create([
            'owner_id' => $this->owner->id,
            'status' => 'to-let',
            'unit_number' => 'A101',
            'bedrooms' => 2,
            'bathrooms' => 2,
            'area_sqft' => 1200,
            'market_rent' => 25000,
        ]);

        $this->listing = PropertyListing::factory()->create([
            'unit_id' => $this->unit->id,
            'title' => 'Beautiful 2BHK Apartment',
            'description' => 'Spacious apartment with modern amenities',
            'rent_amount' => 24000,
            'deposit_amount' => 48000,
            'status' => 'published',
            'amenities' => ['parking', 'gym', 'security'],
            'preferences' => ['family_type' => 'family'],
        ]);
    }

    /** @test */
    public function it_can_list_property_listings()
    {
        $response = $this->getJson('/api/v1/property-listings');

        $this->assertPaginatedResponse($response, [
            '*' => [
                'id',
                'unit_id',
                'title',
                'description',
                'rent_amount',
                'deposit_amount',
                'status',
                'amenities',
                'preferences',
                'created_at',
                'updated_at',
            ]
        ]);
    }

    /** @test */
    public function it_can_filter_listings_by_status()
    {
        PropertyListing::factory()->create(['status' => 'draft']);
        PropertyListing::factory()->create(['status' => 'published']);

        $response = $this->getJson('/api/v1/property-listings?filter[status]=published');

        $response->assertStatus(200);
        $data = $response->json('data');
        
        foreach ($data as $listing) {
            $this->assertEquals('published', $listing['status']);
        }
    }

    /** @test */
    public function it_can_filter_listings_by_rent_range()
    {
        PropertyListing::factory()->create(['rent_amount' => 15000]);
        PropertyListing::factory()->create(['rent_amount' => 35000]);

        $response = $this->getJson('/api/v1/property-listings?filter[rent_range]=20000-30000');

        $response->assertStatus(200);
        $data = $response->json('data');
        
        foreach ($data as $listing) {
            $this->assertGreaterThanOrEqual(20000, $listing['rent_amount']);
            $this->assertLessThanOrEqual(30000, $listing['rent_amount']);
        }
    }

    /** @test */
    public function owner_can_only_see_their_listings()
    {
        $this->actingAs($this->owner);
        
        // Create listing for another owner
        $otherOwner = User::factory()->create(['role' => 'owner']);
        $otherUnit = Unit::factory()->create(['owner_id' => $otherOwner->id]);
        PropertyListing::factory()->create(['unit_id' => $otherUnit->id]);

        $response = $this->getJson('/api/v1/property-listings');

        $response->assertStatus(200);
        $data = $response->json('data');
        
        // Should only see listings for units they own
        foreach ($data as $listing) {
            $this->assertEquals($this->owner->id, $listing['unit']['owner_id']);
        }
    }

    /** @test */
    public function it_can_create_property_listing()
    {
        $this->actingAs($this->owner);

        $availableUnit = Unit::factory()->create([
            'owner_id' => $this->owner->id,
            'status' => 'to-let'
        ]);

        $listingData = [
            'unit_id' => $availableUnit->id,
            'title' => 'Luxury 3BHK Apartment',
            'description' => 'Premium apartment with all modern amenities',
            'rent_amount' => 35000,
            'deposit_amount' => 70000,
            'amenities' => ['parking', 'gym', 'swimming_pool'],
            'preferences' => [
                'family_type' => 'family',
                'pet_friendly' => false,
                'smoking_allowed' => false,
            ],
            'media_urls' => [
                'https://example.com/image1.jpg',
                'https://example.com/image2.jpg',
            ],
        ];

        $response = $this->postJson('/api/v1/property-listings', $listingData);

        $response->assertStatus(201)
            ->assertJsonStructure([
                'success',
                'message',
                'data' => [
                    'id',
                    'unit_id',
                    'title',
                    'description',
                    'rent_amount',
                    'deposit_amount',
                    'status',
                    'amenities',
                    'preferences',
                    'media_urls',
                ]
            ]);

        $this->assertDatabaseHas('property_listings', [
            'unit_id' => $availableUnit->id,
            'title' => 'Luxury 3BHK Apartment',
            'rent_amount' => 35000,
            'status' => 'draft',
        ]);
    }

    /** @test */
    public function it_validates_required_fields_when_creating_listing()
    {
        $this->actingAs($this->owner);

        $response = $this->postJson('/api/v1/property-listings', []);

        $this->assertValidationError($response, [
            'unit_id',
            'title',
            'rent_amount',
            'deposit_amount',
        ]);
    }

    /** @test */
    public function it_prevents_duplicate_listings_for_same_unit()
    {
        $this->actingAs($this->owner);

        // Unit already has an active listing
        $response = $this->postJson('/api/v1/property-listings', [
            'unit_id' => $this->unit->id,
            'title' => 'Another listing',
            'rent_amount' => 25000,
            'deposit_amount' => 50000,
        ]);

        $this->assertValidationError($response, ['unit_id']);
    }

    /** @test */
    public function it_validates_rent_amount_against_market_rate()
    {
        $this->actingAs($this->owner);

        $availableUnit = Unit::factory()->create([
            'owner_id' => $this->owner->id,
            'status' => 'to-let',
            'market_rent' => 20000,
        ]);

        // Try to set rent 200% of market rate (should fail)
        $response = $this->postJson('/api/v1/property-listings', [
            'unit_id' => $availableUnit->id,
            'title' => 'Overpriced listing',
            'rent_amount' => 40000, // 200% of market rate
            'deposit_amount' => 80000,
        ]);

        $this->assertValidationError($response, ['rent_amount']);
    }

    /** @test */
    public function it_validates_deposit_amount_limit()
    {
        $this->actingAs($this->owner);

        $availableUnit = Unit::factory()->create([
            'owner_id' => $this->owner->id,
            'status' => 'to-let'
        ]);

        // Try to set deposit more than 12 months rent
        $response = $this->postJson('/api/v1/property-listings', [
            'unit_id' => $availableUnit->id,
            'title' => 'High deposit listing',
            'rent_amount' => 25000,
            'deposit_amount' => 350000, // 14 months rent
        ]);

        $this->assertValidationError($response, ['deposit_amount']);
    }

    /** @test */
    public function owner_cannot_create_listing_for_others_unit()
    {
        $this->actingAs($this->owner);

        $otherOwnerUnit = Unit::factory()->create([
            'owner_id' => User::factory()->create(['role' => 'owner'])->id,
            'status' => 'to-let'
        ]);

        $response = $this->postJson('/api/v1/property-listings', [
            'unit_id' => $otherOwnerUnit->id,
            'title' => 'Unauthorized listing',
            'rent_amount' => 25000,
            'deposit_amount' => 50000,
        ]);

        $response->assertStatus(403);
    }

    /** @test */
    public function it_can_show_specific_listing()
    {
        $response = $this->getJson("/api/v1/property-listings/{$this->listing->id}");

        $this->assertSuccessResponse($response, [
            'id',
            'unit_id',
            'title',
            'description',
            'rent_amount',
            'deposit_amount',
            'status',
            'amenities',
            'preferences',
            'view_count',
            'inquiry_count',
            'unit' => [
                'id',
                'unit_number',
                'bedrooms',
                'bathrooms',
                'area_sqft',
            ],
        ]);
    }

    /** @test */
    public function it_increments_view_count_for_published_listings()
    {
        $initialViewCount = $this->listing->view_count ?? 0;

        $this->getJson("/api/v1/property-listings/{$this->listing->id}");

        $this->listing->refresh();
        $this->assertEquals($initialViewCount + 1, $this->listing->view_count);
    }

    /** @test */
    public function it_can_update_property_listing()
    {
        $this->actingAs($this->owner);

        $updateData = [
            'title' => 'Updated Apartment Title',
            'description' => 'Updated description with new details',
            'rent_amount' => 26000,
            'amenities' => ['parking', 'gym', 'security', 'swimming_pool'],
        ];

        $response = $this->putJson("/api/v1/property-listings/{$this->listing->id}", $updateData);

        $this->assertSuccessResponse($response);

        $this->assertDatabaseHas('property_listings', [
            'id' => $this->listing->id,
            'title' => 'Updated Apartment Title',
            'rent_amount' => 26000,
        ]);
    }

    /** @test */
    public function it_validates_status_transitions()
    {
        $this->actingAs($this->admin);

        $draftListing = PropertyListing::factory()->create(['status' => 'draft']);

        // Try invalid transition from draft to published (should go through pending_approval)
        $response = $this->putJson("/api/v1/property-listings/{$draftListing->id}", [
            'status' => 'published'
        ]);

        $this->assertValidationError($response, ['status']);
    }

    /** @test */
    public function it_can_delete_property_listing()
    {
        $this->actingAs($this->owner);

        $response = $this->deleteJson("/api/v1/property-listings/{$this->listing->id}");

        $this->assertSuccessResponse($response);
        $this->assertSoftDeleted('property_listings', ['id' => $this->listing->id]);
    }

    /** @test */
    public function it_can_sync_listing_to_portals()
    {
        $this->actingAs($this->owner);

        // Mock the portal integration service
        $this->mock(PortalIntegrationService::class, function ($mock) {
            $mock->shouldReceive('syncToPortals')
                ->once()
                ->andReturn([
                    '99acres' => ['success' => true, 'portal_listing_id' => 'ABC123'],
                    'magicbricks' => ['success' => true, 'portal_listing_id' => 'XYZ789'],
                ]);
        });

        $response = $this->postJson("/api/v1/property-listings/{$this->listing->id}/sync", [
            'portals' => ['99acres', 'magicbricks'],
            'force_update' => true,
        ]);

        $this->assertSuccessResponse($response, [
            'synced_portals',
            'failed_portals',
            'sync_results',
            'total_synced',
            'total_failed',
        ]);
    }

    /** @test */
    public function it_prevents_sync_for_non_publishable_listings()
    {
        $this->actingAs($this->owner);

        $draftListing = PropertyListing::factory()->create([
            'unit_id' => Unit::factory()->create(['owner_id' => $this->owner->id])->id,
            'status' => 'draft'
        ]);

        $response = $this->postJson("/api/v1/property-listings/{$draftListing->id}/sync");

        $response->assertStatus(400)
            ->assertJson([
                'success' => false,
                'message' => 'Listing must be approved or published to sync to portals'
            ]);
    }

    /** @test */
    public function it_can_get_available_units_for_listing()
    {
        $this->actingAs($this->owner);

        // Create additional available units
        Unit::factory()->count(3)->create([
            'owner_id' => $this->owner->id,
            'status' => 'to-let'
        ]);

        // Create unavailable units
        Unit::factory()->create([
            'owner_id' => $this->owner->id,
            'status' => 'occupied'
        ]);

        $response = $this->getJson('/api/v1/available-units');

        $this->assertSuccessResponse($response, [
            '*' => [
                'id',
                'unit_number',
                'unit_type',
                'status',
                'bedrooms',
                'bathrooms',
                'area_sqft',
                'is_available_for_listing',
            ]
        ]);

        $data = $response->json('data');
        foreach ($data as $unit) {
            $this->assertEquals('to-let', $unit['status']);
            $this->assertTrue($unit['is_available_for_listing']);
        }
    }

    /** @test */
    public function it_can_get_listing_leads()
    {
        $this->actingAs($this->owner);

        // Create leads for the listing
        PropertyLead::factory()->count(3)->create([
            'property_listing_id' => $this->listing->id,
            'portal_name' => '99acres',
            'status' => 'new',
        ]);

        $response = $this->getJson("/api/v1/property-listings/{$this->listing->id}/leads");

        $this->assertSuccessResponse($response, [
            '*' => [
                'id',
                'property_listing_id',
                'portal_name',
                'enquirer_name',
                'enquirer_email',
                'enquirer_phone',
                'status',
                'priority',
                'created_at',
            ]
        ]);

        $meta = $response->json('meta');
        $this->assertArrayHasKey('total_leads', $meta);
        $this->assertArrayHasKey('new_leads', $meta);
        $this->assertArrayHasKey('converted_leads', $meta);
        $this->assertArrayHasKey('conversion_rate', $meta);
    }

    /** @test */
    public function it_can_process_portal_webhooks()
    {
        // Mock the portal integration service
        $this->mock(PortalIntegrationService::class, function ($mock) {
            $mock->shouldReceive('processWebhook')
                ->once()
                ->with('99acres', Mockery::any())
                ->andReturn([
                    'success' => true,
                    'message' => 'Lead processed successfully',
                    'lead_id' => 123
                ]);
        });

        $webhookData = [
            'event_type' => 'lead_received',
            'listing_id' => 'ABC123',
            'lead_data' => [
                'name' => 'John Doe',
                'email' => '<EMAIL>',
                'phone' => '9876543210',
                'message' => 'Interested in this property',
            ],
            'timestamp' => now()->toISOString(),
        ];

        $response = $this->postJson('/api/v1/webhooks/portals/99acres', $webhookData);

        $this->assertSuccessResponse($response);
    }

    /** @test */
    public function it_rejects_webhooks_from_unsupported_portals()
    {
        $response = $this->postJson('/api/v1/webhooks/portals/unsupported-portal', [
            'event_type' => 'lead_received',
        ]);

        $response->assertStatus(400)
            ->assertJson([
                'success' => false,
                'message' => 'Unsupported portal'
            ]);
    }

    /** @test */
    public function it_requires_authentication_for_all_endpoints()
    {
        // Test without authentication
        $this->postJson('/api/v1/property-listings', [])->assertStatus(401);
        $this->getJson('/api/v1/property-listings')->assertStatus(401);
        $this->getJson("/api/v1/property-listings/{$this->listing->id}")->assertStatus(401);
        $this->putJson("/api/v1/property-listings/{$this->listing->id}", [])->assertStatus(401);
        $this->deleteJson("/api/v1/property-listings/{$this->listing->id}")->assertStatus(401);
        $this->postJson("/api/v1/property-listings/{$this->listing->id}/sync")->assertStatus(401);
        $this->getJson('/api/v1/available-units')->assertStatus(401);
    }

    /** @test */
    public function it_handles_not_found_listings()
    {
        $this->actingAs($this->admin);

        $nonExistentId = 99999;

        $this->getJson("/api/v1/property-listings/{$nonExistentId}")
            ->assertStatus(404);

        $this->putJson("/api/v1/property-listings/{$nonExistentId}", [])
            ->assertStatus(404);

        $this->deleteJson("/api/v1/property-listings/{$nonExistentId}")
            ->assertStatus(404);
    }
}
