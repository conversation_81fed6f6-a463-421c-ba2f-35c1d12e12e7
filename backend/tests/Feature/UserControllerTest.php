<?php

namespace Tests\Feature;

use Tests\TestCase;
use App\Models\User;
use App\Models\Unit;
use Illuminate\Foundation\Testing\WithFaker;
use Illuminate\Support\Facades\Hash;

class UserControllerTest extends TestCase
{
    use WithFaker;

    protected $admin;
    protected $manager;
    protected $owner;
    protected $tenant;

    protected function setUp(): void
    {
        parent::setUp();

        $this->admin = $this->actingAsAdmin();
        $this->manager = User::factory()->create(['role' => 'manager']);
        $this->owner = User::factory()->create(['role' => 'owner']);
        $this->tenant = User::factory()->create(['role' => 'tenant']);
    }

    /** @test */
    public function admin_can_list_all_users()
    {
        User::factory()->count(5)->create();

        $response = $this->getJson('/api/v1/users');

        $this->assertPaginatedResponse($response, [
            '*' => [
                'id',
                'name',
                'email',
                'phone',
                'role',
                'status',
                'verification_status',
                'last_login_at',
                'created_at',
                'updated_at',
            ]
        ]);
    }

    /** @test */
    public function it_can_filter_users_by_role()
    {
        User::factory()->create(['role' => 'owner']);
        User::factory()->create(['role' => 'tenant']);

        $response = $this->getJson('/api/v1/users?filter[role]=owner');

        $response->assertStatus(200);
        $data = $response->json('data');
        
        foreach ($data as $user) {
            $this->assertEquals('owner', $user['role']);
        }
    }

    /** @test */
    public function it_can_filter_users_by_status()
    {
        User::factory()->create(['status' => 'active']);
        User::factory()->create(['status' => 'inactive']);

        $response = $this->getJson('/api/v1/users?filter[status]=active');

        $response->assertStatus(200);
        $data = $response->json('data');
        
        foreach ($data as $user) {
            $this->assertEquals('active', $user['status']);
        }
    }

    /** @test */
    public function it_can_filter_users_by_verification_status()
    {
        User::factory()->create(['verification_status' => 'verified']);
        User::factory()->create(['verification_status' => 'pending']);

        $response = $this->getJson('/api/v1/users?filter[verification_status]=verified');

        $response->assertStatus(200);
        $data = $response->json('data');
        
        foreach ($data as $user) {
            $this->assertEquals('verified', $user['verification_status']);
        }
    }

    /** @test */
    public function it_can_search_users_by_name_or_email()
    {
        $user1 = User::factory()->create(['name' => 'John Doe', 'email' => '<EMAIL>']);
        $user2 = User::factory()->create(['name' => 'Jane Smith', 'email' => '<EMAIL>']);

        $response = $this->getJson('/api/v1/users?search=john');

        $response->assertStatus(200);
        $data = $response->json('data');
        
        $this->assertCount(1, $data);
        $this->assertEquals('John Doe', $data[0]['name']);
    }

    /** @test */
    public function admin_can_create_user()
    {
        $userData = [
            'name' => 'New User',
            'email' => '<EMAIL>',
            'phone' => '9876543210',
            'password' => 'password123',
            'password_confirmation' => 'password123',
            'role' => 'owner',
            'status' => 'active',
            'address' => '123 Main Street, City',
            'emergency_contact_name' => 'Emergency Contact',
            'emergency_contact_phone' => '9876543211',
        ];

        $response = $this->postJson('/api/v1/users', $userData);

        $response->assertStatus(201)
            ->assertJsonStructure([
                'success',
                'message',
                'data' => [
                    'id',
                    'name',
                    'email',
                    'phone',
                    'role',
                    'status',
                    'verification_status',
                ]
            ]);

        $this->assertDatabaseHas('users', [
            'name' => 'New User',
            'email' => '<EMAIL>',
            'role' => 'owner',
            'status' => 'active',
        ]);

        // Check password was hashed
        $user = User::where('email', '<EMAIL>')->first();
        $this->assertTrue(Hash::check('password123', $user->password));
    }

    /** @test */
    public function it_validates_required_fields_when_creating_user()
    {
        $response = $this->postJson('/api/v1/users', []);

        $this->assertValidationError($response, [
            'name',
            'email',
            'phone',
            'password',
            'role',
        ]);
    }

    /** @test */
    public function it_validates_unique_email_when_creating_user()
    {
        $response = $this->postJson('/api/v1/users', [
            'name' => 'Test User',
            'email' => $this->owner->email, // Use existing email
            'phone' => '9876543210',
            'password' => 'password123',
            'password_confirmation' => 'password123',
            'role' => 'tenant',
        ]);

        $this->assertValidationError($response, ['email']);
    }

    /** @test */
    public function it_validates_password_confirmation()
    {
        $response = $this->postJson('/api/v1/users', [
            'name' => 'Test User',
            'email' => '<EMAIL>',
            'phone' => '9876543210',
            'password' => 'password123',
            'password_confirmation' => 'different_password',
            'role' => 'tenant',
        ]);

        $this->assertValidationError($response, ['password']);
    }

    /** @test */
    public function it_validates_user_role()
    {
        $response = $this->postJson('/api/v1/users', [
            'name' => 'Test User',
            'email' => '<EMAIL>',
            'phone' => '9876543210',
            'password' => 'password123',
            'password_confirmation' => 'password123',
            'role' => 'invalid_role',
        ]);

        $this->assertValidationError($response, ['role']);
    }

    /** @test */
    public function non_admin_cannot_create_users()
    {
        $this->actingAs($this->owner);

        $response = $this->postJson('/api/v1/users', [
            'name' => 'Test User',
            'email' => '<EMAIL>',
            'phone' => '9876543210',
            'password' => 'password123',
            'password_confirmation' => 'password123',
            'role' => 'tenant',
        ]);

        $this->assertForbidden($response);
    }

    /** @test */
    public function it_can_show_specific_user()
    {
        $response = $this->getJson("/api/v1/users/{$this->owner->id}");

        $this->assertSuccessResponse($response, [
            'id',
            'name',
            'email',
            'phone',
            'role',
            'status',
            'verification_status',
            'address',
            'emergency_contact_name',
            'emergency_contact_phone',
            'last_login_at',
            'created_at',
            'updated_at',
        ]);
    }

    /** @test */
    public function user_can_view_their_own_profile()
    {
        $this->actingAs($this->owner);

        $response = $this->getJson("/api/v1/users/{$this->owner->id}");

        $this->assertSuccessResponse($response);
    }

    /** @test */
    public function user_cannot_view_other_users_profiles()
    {
        $this->actingAs($this->owner);

        $response = $this->getJson("/api/v1/users/{$this->tenant->id}");

        $this->assertForbidden($response);
    }

    /** @test */
    public function it_can_update_user()
    {
        $updateData = [
            'name' => 'Updated Name',
            'phone' => '9876543299',
            'address' => 'Updated Address',
            'emergency_contact_name' => 'Updated Emergency Contact',
        ];

        $response = $this->putJson("/api/v1/users/{$this->owner->id}", $updateData);

        $this->assertSuccessResponse($response);

        $this->assertDatabaseHas('users', [
            'id' => $this->owner->id,
            'name' => 'Updated Name',
            'phone' => '9876543299',
            'address' => 'Updated Address',
        ]);
    }

    /** @test */
    public function user_can_update_their_own_profile()
    {
        $this->actingAs($this->owner);

        $updateData = [
            'name' => 'Self Updated Name',
            'phone' => '9876543288',
        ];

        $response = $this->putJson("/api/v1/users/{$this->owner->id}", $updateData);

        $this->assertSuccessResponse($response);

        $this->assertDatabaseHas('users', [
            'id' => $this->owner->id,
            'name' => 'Self Updated Name',
            'phone' => '9876543288',
        ]);
    }

    /** @test */
    public function user_cannot_update_their_role()
    {
        $this->actingAs($this->owner);

        $response = $this->putJson("/api/v1/users/{$this->owner->id}", [
            'role' => 'admin'
        ]);

        // Should succeed but role should not change
        $response->assertStatus(200);
        
        $this->owner->refresh();
        $this->assertEquals('owner', $this->owner->role);
    }

    /** @test */
    public function admin_can_update_user_role()
    {
        $response = $this->putJson("/api/v1/users/{$this->owner->id}", [
            'role' => 'manager'
        ]);

        $this->assertSuccessResponse($response);

        $this->assertDatabaseHas('users', [
            'id' => $this->owner->id,
            'role' => 'manager',
        ]);
    }

    /** @test */
    public function it_can_update_user_status()
    {
        $response = $this->putJson("/api/v1/users/{$this->owner->id}", [
            'status' => 'inactive'
        ]);

        $this->assertSuccessResponse($response);

        $this->assertDatabaseHas('users', [
            'id' => $this->owner->id,
            'status' => 'inactive',
        ]);
    }

    /** @test */
    public function it_can_update_user_verification_status()
    {
        $response = $this->putJson("/api/v1/users/{$this->owner->id}/verify", [
            'verification_status' => 'verified',
            'verification_notes' => 'All documents verified successfully',
        ]);

        $this->assertSuccessResponse($response);

        $this->assertDatabaseHas('users', [
            'id' => $this->owner->id,
            'verification_status' => 'verified',
        ]);
    }

    /** @test */
    public function non_admin_cannot_verify_users()
    {
        $this->actingAs($this->owner);

        $response = $this->putJson("/api/v1/users/{$this->tenant->id}/verify", [
            'verification_status' => 'verified',
        ]);

        $this->assertForbidden($response);
    }

    /** @test */
    public function it_can_delete_user()
    {
        $userToDelete = User::factory()->create(['role' => 'tenant']);

        $response = $this->deleteJson("/api/v1/users/{$userToDelete->id}");

        $this->assertSuccessResponse($response);
        $this->assertSoftDeleted('users', ['id' => $userToDelete->id]);
    }

    /** @test */
    public function it_cannot_delete_user_with_active_units()
    {
        // Create unit owned by the user
        Unit::factory()->create(['owner_id' => $this->owner->id]);

        $response = $this->deleteJson("/api/v1/users/{$this->owner->id}");

        $response->assertStatus(400)
            ->assertJson([
                'success' => false,
                'message' => 'Cannot delete user with active units or tenancies',
            ]);
    }

    /** @test */
    public function it_can_get_user_statistics()
    {
        $response = $this->getJson('/api/v1/users/statistics');

        $this->assertSuccessResponse($response, [
            'total_users',
            'users_by_role',
            'users_by_status',
            'users_by_verification_status',
            'recent_registrations',
            'active_users_last_30_days',
        ]);
    }

    /** @test */
    public function it_can_get_user_activity()
    {
        $response = $this->getJson("/api/v1/users/{$this->owner->id}/activity");

        $this->assertSuccessResponse($response, [
            'user_id',
            'login_history',
            'recent_actions',
            'units_owned',
            'agreements_signed',
            'documents_uploaded',
            'notifications_received',
        ]);
    }

    /** @test */
    public function user_can_view_their_own_activity()
    {
        $this->actingAs($this->owner);

        $response = $this->getJson("/api/v1/users/{$this->owner->id}/activity");

        $this->assertSuccessResponse($response);
    }

    /** @test */
    public function user_cannot_view_others_activity()
    {
        $this->actingAs($this->owner);

        $response = $this->getJson("/api/v1/users/{$this->tenant->id}/activity");

        $this->assertForbidden($response);
    }

    /** @test */
    public function it_handles_not_found_users()
    {
        $nonExistentId = 99999;
        
        $this->getJson("/api/v1/users/{$nonExistentId}")
            ->assertStatus(404);
            
        $this->putJson("/api/v1/users/{$nonExistentId}", [])
            ->assertStatus(404);
            
        $this->deleteJson("/api/v1/users/{$nonExistentId}")
            ->assertStatus(404);
    }

    /** @test */
    public function it_requires_authentication_for_all_endpoints()
    {
        // Test without authentication
        $this->postJson('/api/v1/users', [])->assertStatus(401);
        $this->getJson('/api/v1/users')->assertStatus(401);
        $this->getJson("/api/v1/users/{$this->owner->id}")->assertStatus(401);
        $this->putJson("/api/v1/users/{$this->owner->id}", [])->assertStatus(401);
        $this->deleteJson("/api/v1/users/{$this->owner->id}")->assertStatus(401);
        $this->getJson('/api/v1/users/statistics')->assertStatus(401);
        $this->getJson("/api/v1/users/{$this->owner->id}/activity")->assertStatus(401);
    }
}
