<?php

namespace Tests\Feature;

use App\Console\Commands\ScheduledReceiptGenerationCommand;
use App\Models\RentPayment;
use App\Models\RentReceipt;
use App\Models\User;
use App\Models\Unit;
use App\Models\Property;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Support\Facades\Storage;
use Tests\TestCase;

class ScheduledReceiptGenerationTest extends TestCase
{
    use RefreshDatabase;

    protected function setUp(): void
    {
        parent::setUp();
        Storage::fake('public');
    }

    /** @test */
    public function it_generates_receipts_for_completed_payments_via_scheduled_command()
    {
        // Create test data
        $property = Property::factory()->create();
        $unit = Unit::factory()->create(['property_id' => $property->id]);
        $tenant = User::factory()->create(['role' => 'tenant']);

        // Create completed payments without receipts
        $payments = RentPayment::factory()->count(3)->create([
            'unit_id' => $unit->id,
            'tenant_id' => $tenant->id,
            'status' => 'completed',
            'payment_date' => now()->subHours(2),
        ]);

        // Run the scheduled command
        $this->artisan('receipts:schedule')
            ->expectsOutput('Starting scheduled receipt generation...')
            ->expectsOutput('Found 3 payments requiring receipt generation.')
            ->assertExitCode(0);

        // Assert receipts were created
        foreach ($payments as $payment) {
            $this->assertDatabaseHas('rent_receipts', [
                'payment_id' => $payment->id,
            ]);
        }

        $this->assertEquals(3, RentReceipt::count());
    }

    /** @test */
    public function it_skips_payments_with_existing_receipts()
    {
        // Create test data
        $property = Property::factory()->create();
        $unit = Unit::factory()->create(['property_id' => $property->id]);
        $tenant = User::factory()->create(['role' => 'tenant']);

        // Create payment with existing receipt
        $paymentWithReceipt = RentPayment::factory()->create([
            'unit_id' => $unit->id,
            'tenant_id' => $tenant->id,
            'status' => 'completed',
        ]);
        RentReceipt::factory()->create([
            'payment_id' => $paymentWithReceipt->id,
        ]);

        // Create payment without receipt
        $paymentWithoutReceipt = RentPayment::factory()->create([
            'unit_id' => $unit->id,
            'tenant_id' => $tenant->id,
            'status' => 'completed',
        ]);

        // Run the scheduled command
        $this->artisan('receipts:schedule')
            ->expectsOutput('Starting scheduled receipt generation...')
            ->expectsOutput('Found 1 payments requiring receipt generation.')
            ->assertExitCode(0);

        // Assert only one receipt was created
        $this->assertEquals(2, RentReceipt::count());
    }

    /** @test */
    public function it_respects_batch_size_limit()
    {
        // Create test data
        $property = Property::factory()->create();
        $unit = Unit::factory()->create(['property_id' => $property->id]);
        $tenant = User::factory()->create(['role' => 'tenant']);

        // Create more payments than batch size
        $payments = RentPayment::factory()->count(10)->create([
            'unit_id' => $unit->id,
            'tenant_id' => $tenant->id,
            'status' => 'completed',
        ]);

        // Run the scheduled command with batch size of 3
        $this->artisan('receipts:schedule', ['--batch-size' => 3])
            ->expectsOutput('Starting scheduled receipt generation...')
            ->expectsOutput('Found 3 payments requiring receipt generation.')
            ->assertExitCode(0);

        // Assert only 3 receipts were created
        $this->assertEquals(3, RentReceipt::count());
    }

    /** @test */
    public function it_handles_empty_payment_list()
    {
        // Run the scheduled command with no payments
        $this->artisan('receipts:schedule')
            ->expectsOutput('Starting scheduled receipt generation...')
            ->expectsOutput('No payments found that require receipt generation.')
            ->assertExitCode(0);

        $this->assertEquals(0, RentReceipt::count());
    }

    /** @test */
    public function it_auto_delivers_receipts_when_flag_is_set()
    {
        // Create test data
        $property = Property::factory()->create();
        $unit = Unit::factory()->create(['property_id' => $property->id]);
        $tenant = User::factory()->create(['role' => 'tenant']);
        $payment = RentPayment::factory()->create([
            'unit_id' => $unit->id,
            'tenant_id' => $tenant->id,
            'status' => 'completed',
        ]);

        // Run the scheduled command with auto-deliver flag
        $this->artisan('receipts:schedule', ['--auto-deliver' => true])
            ->expectsOutput('Starting scheduled receipt generation...')
            ->expectsOutput('Found 1 payments requiring receipt generation.')
            ->assertExitCode(0);

        // Assert receipt was created and delivered
        $receipt = RentReceipt::where('payment_id', $payment->id)->first();
        $this->assertNotNull($receipt);
        $this->assertEquals('delivered', $receipt->delivery_status);
    }

    /** @test */
    public function it_logs_generation_activities()
    {
        // Create test data
        $property = Property::factory()->create();
        $unit = Unit::factory()->create(['property_id' => $property->id]);
        $tenant = User::factory()->create(['role' => 'tenant']);
        $payment = RentPayment::factory()->create([
            'unit_id' => $unit->id,
            'tenant_id' => $tenant->id,
            'status' => 'completed',
        ]);

        // Run the scheduled command
        $this->artisan('receipts:schedule')
            ->assertExitCode(0);

        $receipt = RentReceipt::where('payment_id', $payment->id)->first();
        
        // Assert audit log was created
        $this->assertDatabaseHas('receipt_audit_logs', [
            'receipt_id' => $receipt->id,
            'action' => 'receipt_generated_automatically',
        ]);
    }

    /** @test */
    public function it_handles_generation_errors_gracefully()
    {
        // Create test data
        $property = Property::factory()->create();
        $unit = Unit::factory()->create(['property_id' => $property->id]);
        $tenant = User::factory()->create(['role' => 'tenant']);
        $payment = RentPayment::factory()->create([
            'unit_id' => $unit->id,
            'tenant_id' => $tenant->id,
            'status' => 'completed',
        ]);

        // Mock ReceiptGenerationService to throw exception
        $this->mock(\App\Services\ReceiptGenerationService::class, function ($mock) {
            $mock->shouldReceive('generateReceipt')
                ->andThrow(new \Exception('PDF generation failed'));
        });

        // Run the scheduled command
        $this->artisan('receipts:schedule')
            ->expectsOutput('Starting scheduled receipt generation...')
            ->expectsOutput('Found 1 payments requiring receipt generation.')
            ->assertExitCode(0);

        // Assert no receipt was created due to error
        $this->assertDatabaseMissing('rent_receipts', [
            'payment_id' => $payment->id,
        ]);
    }
} 