<?php

namespace Tests\Feature;

use Tests\TestCase;
use App\Models\Society;
use App\Models\Unit;
use App\Models\Tenant;
use App\Models\User;
use App\Models\SyncAuditLog;
use App\Events\Sync\UnitUpdated;
use App\Events\Sync\EntityDeleted;
use App\Listeners\Sync\HandleUnitUpdated;
use App\Services\SyncService;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Support\Facades\Event;
use Illuminate\Support\Facades\Queue;

class SyncSystemTest extends TestCase
{
    use RefreshDatabase;

    protected function setUp(): void
    {
        parent::setUp();
        
        // Set up test configuration
        config(['sync.enabled' => true]);
        config(['sync.source_system' => 'tms']);
        config(['sync.security.validate_society_access' => true]);
    }

    /** @test */
    public function it_triggers_sync_event_when_unit_is_created()
    {
        Event::fake();
        
        $society = Society::factory()->create();
        $unit = Unit::factory()->create(['society_id' => $society->id]);
        
        Event::assertDispatched(UnitUpdated::class, function ($event) use ($unit) {
            return $event->entityData['id'] === $unit->id && 
                   $event->operation === 'created' &&
                   $event->societyId === $unit->society_id;
        });
    }

    /** @test */
    public function it_triggers_sync_event_when_unit_is_updated()
    {
        Event::fake();
        
        $society = Society::factory()->create();
        $unit = Unit::factory()->create(['society_id' => $society->id]);
        
        // Clear events from creation
        Event::fake();
        
        $unit->update(['status' => 'rented']);
        
        Event::assertDispatched(UnitUpdated::class, function ($event) use ($unit) {
            return $event->entityData['id'] === $unit->id && 
                   $event->operation === 'updated';
        });
    }

    /** @test */
    public function it_triggers_deletion_event_when_unit_is_deleted()
    {
        Event::fake();
        
        $society = Society::factory()->create();
        $unit = Unit::factory()->create(['society_id' => $society->id]);
        $unitId = $unit->id;
        $societyId = $unit->society_id;
        
        // Clear events from creation
        Event::fake();
        
        $unit->delete();
        
        Event::assertDispatched(EntityDeleted::class, function ($event) use ($unitId, $societyId) {
            return $event->entityData['id'] === $unitId && 
                   $event->operation === 'deleted' &&
                   $event->societyId === $societyId;
        });
    }

    /** @test */
    public function it_processes_sync_event_correctly()
    {
        Queue::fake();
        
        $society = Society::factory()->create();
        $unitData = [
            'id' => 123,
            'society_id' => $society->id,
            'unit_number' => 'A101',
            'status' => 'vacant',
            'created_at' => now()->toISOString(),
            'updated_at' => now()->toISOString(),
        ];
        
        $event = new UnitUpdated($unitData, 'created');
        $listener = new HandleUnitUpdated();
        
        // Mock external system event (different source)
        $event->sourceSystem = 'onesociety';
        
        $listener->handle($event);
        
        // Check that unit was created
        $this->assertDatabaseHas('units', [
            'id' => 123,
            'society_id' => $society->id,
            'unit_number' => 'A101',
            'status' => 'vacant',
        ]);
        
        // Check audit log
        $this->assertDatabaseHas('sync_audit_logs', [
            'entity_type' => 'Unit',
            'entity_id' => 123,
            'operation' => 'created',
            'source_system' => 'onesociety',
            'status' => SyncAuditLog::STATUS_SUCCESS,
        ]);
    }

    /** @test */
    public function it_prevents_sync_loops_by_ignoring_own_events()
    {
        $society = Society::factory()->create();
        $unitData = [
            'id' => 123,
            'society_id' => $society->id,
            'unit_number' => 'A101',
            'status' => 'vacant',
            'created_at' => now()->toISOString(),
            'updated_at' => now()->toISOString(),
        ];
        
        $event = new UnitUpdated($unitData, 'created');
        $listener = new HandleUnitUpdated();
        
        // Event from our own system should be ignored
        $event->sourceSystem = 'tms';
        
        $listener->handle($event);
        
        // Unit should not be created
        $this->assertDatabaseMissing('units', ['id' => 123]);
        
        // No audit log should be created
        $this->assertDatabaseMissing('sync_audit_logs', [
            'entity_type' => 'Unit',
            'entity_id' => 123,
        ]);
    }

    /** @test */
    public function it_enforces_tenant_isolation()
    {
        $society1 = Society::factory()->create();
        $society2 = Society::factory()->create();
        
        $unitData = [
            'id' => 123,
            'society_id' => $society1->id,
            'unit_number' => 'A101',
            'status' => 'vacant',
            'created_at' => now()->toISOString(),
            'updated_at' => now()->toISOString(),
        ];
        
        $event = new UnitUpdated($unitData, 'created');
        $event->sourceSystem = 'onesociety';
        
        $listener = new HandleUnitUpdated();
        
        // Simulate user context for society2 (different society)
        // In real implementation, this would be handled by middleware
        
        $listener->handle($event);
        
        // Unit should still be created as society exists
        // In production, additional access control would be implemented
        $this->assertDatabaseHas('units', [
            'id' => 123,
            'society_id' => $society1->id,
        ]);
    }

    /** @test */
    public function it_handles_conflict_resolution_by_timestamp()
    {
        config(['sync.conflict_resolution.strategy' => 'timestamp']);
        
        $society = Society::factory()->create();
        
        // Create existing unit with newer timestamp
        $existingUnit = Unit::factory()->create([
            'id' => 123,
            'society_id' => $society->id,
            'unit_number' => 'A101',
            'status' => 'occupied',
            'updated_at' => now(),
        ]);
        
        // Try to sync older data
        $olderData = [
            'id' => 123,
            'society_id' => $society->id,
            'unit_number' => 'A101',
            'status' => 'vacant',
            'created_at' => now()->subHour()->toISOString(),
            'updated_at' => now()->subHour()->toISOString(),
        ];
        
        $event = new UnitUpdated($olderData, 'updated');
        $event->sourceSystem = 'onesociety';
        $event->syncVersion = now()->subHour()->toISOString();
        
        $listener = new HandleUnitUpdated();
        $listener->handle($event);
        
        // Unit should not be updated (local version is newer)
        $this->assertDatabaseHas('units', [
            'id' => 123,
            'status' => 'occupied', // Original status preserved
        ]);
        
        // Should be logged as skipped
        $this->assertDatabaseHas('sync_audit_logs', [
            'entity_type' => 'Unit',
            'entity_id' => 123,
            'status' => SyncAuditLog::STATUS_SKIPPED,
        ]);
    }

    /** @test */
    public function it_creates_audit_log_for_failed_sync()
    {
        $unitData = [
            'id' => 123,
            'society_id' => 999, // Non-existent society
            'unit_number' => 'A101',
            'status' => 'vacant',
            'created_at' => now()->toISOString(),
            'updated_at' => now()->toISOString(),
        ];
        
        $event = new UnitUpdated($unitData, 'created');
        $event->sourceSystem = 'onesociety';
        
        $listener = new HandleUnitUpdated();
        
        try {
            $listener->handle($event);
        } catch (\Exception $e) {
            // Expected to fail due to foreign key constraint
        }
        
        // Should have audit log with failed status
        $this->assertDatabaseHas('sync_audit_logs', [
            'entity_type' => 'Unit',
            'entity_id' => 123,
            'status' => SyncAuditLog::STATUS_FAILED,
        ]);
    }

    /** @test */
    public function sync_service_can_manually_trigger_sync()
    {
        Event::fake();
        
        $society = Society::factory()->create();
        $unit = Unit::factory()->create(['society_id' => $society->id]);
        
        $syncService = app(SyncService::class);
        $result = $syncService->syncEntity($unit, 'updated');
        
        $this->assertTrue($result);
        
        Event::assertDispatched(UnitUpdated::class, function ($event) use ($unit) {
            return $event->entityData['id'] === $unit->id && 
                   $event->operation === 'updated';
        });
    }

    /** @test */
    public function sync_service_can_bulk_sync_entities()
    {
        Event::fake();
        
        $society = Society::factory()->create();
        $units = Unit::factory()->count(3)->create(['society_id' => $society->id]);
        
        $syncService = app(SyncService::class);
        $results = $syncService->bulkSync($units->toArray(), 'updated');
        
        $this->assertCount(3, $results);
        $this->assertTrue(collect($results)->every('success'));
        
        Event::assertDispatchedTimes(UnitUpdated::class, 3);
    }

    /** @test */
    public function it_filters_syncable_fields_correctly()
    {
        $society = Society::factory()->create();
        $unitData = [
            'id' => 123,
            'society_id' => $society->id,
            'unit_number' => 'A101',
            'status' => 'vacant',
            'internal_notes' => 'This should not be synced',
            'created_at' => now()->toISOString(),
            'updated_at' => now()->toISOString(),
        ];
        
        $event = new UnitUpdated($unitData, 'created');
        
        // Check that only syncable fields are included
        $this->assertArrayHasKey('id', $event->entityData);
        $this->assertArrayHasKey('society_id', $event->entityData);
        $this->assertArrayHasKey('unit_number', $event->entityData);
        $this->assertArrayHasKey('status', $event->entityData);
        $this->assertArrayNotHasKey('internal_notes', $event->entityData);
    }

    /** @test */
    public function it_generates_correlation_ids_for_tracking()
    {
        $society = Society::factory()->create();
        $unitData = [
            'id' => 123,
            'society_id' => $society->id,
            'unit_number' => 'A101',
            'status' => 'vacant',
            'created_at' => now()->toISOString(),
            'updated_at' => now()->toISOString(),
        ];
        
        $event = new UnitUpdated($unitData, 'created');
        
        $this->assertNotEmpty($event->correlationId);
        $this->assertStringContainsString('sync_Unit_created', $event->correlationId);
    }
}
