<?php

namespace Tests\Feature;

use Tests\TestCase;
use App\Models\User;
use App\Models\Tenant;
use App\Models\Unit;
use App\Models\Agreement;
use App\Models\AgreementTemplate;
use App\Models\AgreementAuditLog;
use App\Services\AgreementAuditService;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Foundation\Testing\WithFaker;

class AgreementAuditTest extends TestCase
{
    use RefreshDatabase, WithFaker;

    protected $user;
    protected $tenant;
    protected $unit;
    protected $template;
    protected $auditService;

    protected function setUp(): void
    {
        parent::setUp();
        
        $this->user = User::factory()->create(['role' => 'admin']);
        $this->tenant = Tenant::factory()->create();
        $this->unit = Unit::factory()->create(['owner_id' => $this->user->id]);
        $this->template = AgreementTemplate::factory()->create();
        $this->auditService = new AgreementAuditService();
    }

    /** @test */
    public function it_logs_agreement_creation_automatically()
    {
        $this->actingAs($this->user);

        $agreement = Agreement::factory()->create([
            'tenant_id' => $this->tenant->id,
            'unit_id' => $this->unit->id,
            'template_id' => $this->template->id,
            'created_by' => $this->user->id,
        ]);

        $this->assertDatabaseHas('agreement_audit_logs', [
            'agreement_id' => $agreement->id,
            'user_id' => $this->user->id,
            'action' => 'created',
        ]);
    }

    /** @test */
    public function it_logs_agreement_updates_automatically()
    {
        $this->actingAs($this->user);

        $agreement = Agreement::factory()->create([
            'tenant_id' => $this->tenant->id,
            'unit_id' => $this->unit->id,
            'template_id' => $this->template->id,
            'created_by' => $this->user->id,
            'status' => 'draft',
        ]);

        // Clear creation log
        AgreementAuditLog::truncate();

        $agreement->update(['status' => 'active']);

        $this->assertDatabaseHas('agreement_audit_logs', [
            'agreement_id' => $agreement->id,
            'user_id' => $this->user->id,
            'action' => 'updated',
        ]);

        $this->assertDatabaseHas('agreement_audit_logs', [
            'agreement_id' => $agreement->id,
            'user_id' => $this->user->id,
            'action' => 'status_changed',
        ]);
    }

    /** @test */
    public function it_logs_agreement_signing_automatically()
    {
        $this->actingAs($this->user);

        $agreement = Agreement::factory()->create([
            'tenant_id' => $this->tenant->id,
            'unit_id' => $this->unit->id,
            'template_id' => $this->template->id,
            'created_by' => $this->user->id,
            'signed_by_tenant' => false,
        ]);

        // Clear creation log
        AgreementAuditLog::truncate();

        $agreement->update(['signed_by_tenant' => true]);

        $this->assertDatabaseHas('agreement_audit_logs', [
            'agreement_id' => $agreement->id,
            'user_id' => $this->user->id,
            'action' => 'signed_by_tenant',
        ]);
    }

    /** @test */
    public function it_logs_full_signing_when_both_parties_sign()
    {
        $this->actingAs($this->user);

        $agreement = Agreement::factory()->create([
            'tenant_id' => $this->tenant->id,
            'unit_id' => $this->unit->id,
            'template_id' => $this->template->id,
            'created_by' => $this->user->id,
            'signed_by_tenant' => true,
            'signed_by_owner' => false,
        ]);

        // Clear creation log
        AgreementAuditLog::truncate();

        $agreement->update(['signed_by_owner' => true]);

        $this->assertDatabaseHas('agreement_audit_logs', [
            'agreement_id' => $agreement->id,
            'action' => 'signed_by_owner',
        ]);

        $this->assertDatabaseHas('agreement_audit_logs', [
            'agreement_id' => $agreement->id,
            'action' => 'fully_signed',
        ]);
    }

    /** @test */
    public function it_can_manually_log_actions_via_service()
    {
        $agreement = Agreement::factory()->create([
            'tenant_id' => $this->tenant->id,
            'unit_id' => $this->unit->id,
            'template_id' => $this->template->id,
            'created_by' => $this->user->id,
        ]);

        $auditLog = $this->auditService->logAction(
            $agreement,
            'custom_action',
            ['old_field' => 'old_value'],
            ['new_field' => 'new_value'],
            ['custom_metadata' => 'test'],
            $this->user
        );

        $this->assertInstanceOf(AgreementAuditLog::class, $auditLog);
        $this->assertEquals($agreement->id, $auditLog->agreement_id);
        $this->assertEquals($this->user->id, $auditLog->user_id);
        $this->assertEquals('custom_action', $auditLog->action);
        $this->assertEquals(['old_field' => 'old_value'], $auditLog->old_values);
        $this->assertEquals(['new_field' => 'new_value'], $auditLog->new_values);
    }

    /** @test */
    public function it_can_get_audit_logs_for_agreement()
    {
        $agreement = Agreement::factory()->create([
            'tenant_id' => $this->tenant->id,
            'unit_id' => $this->unit->id,
            'template_id' => $this->template->id,
            'created_by' => $this->user->id,
        ]);

        // Create some audit logs
        AgreementAuditLog::factory()->forAgreement($agreement)->created()->create();
        AgreementAuditLog::factory()->forAgreement($agreement)->signed()->create();
        AgreementAuditLog::factory()->forAgreement($agreement)->statusChanged()->create();

        $auditLogs = $this->auditService->getAgreementAuditLogs($agreement->id);

        $this->assertCount(4, $auditLogs); // 3 manual + 1 from creation observer
        $this->assertTrue($auditLogs->contains('action', 'created'));
        $this->assertTrue($auditLogs->contains('action', 'status_changed'));
    }

    /** @test */
    public function it_can_filter_audit_logs_by_action()
    {
        $agreement = Agreement::factory()->create([
            'tenant_id' => $this->tenant->id,
            'unit_id' => $this->unit->id,
            'template_id' => $this->template->id,
            'created_by' => $this->user->id,
        ]);

        AgreementAuditLog::factory()->forAgreement($agreement)->created()->create();
        AgreementAuditLog::factory()->forAgreement($agreement)->signed()->create();

        $auditLogs = $this->auditService->getAgreementAuditLogs($agreement->id, ['action' => 'created']);

        $this->assertCount(2, $auditLogs); // 1 manual + 1 from creation observer
        $this->assertTrue($auditLogs->every(fn($log) => $log->action === 'created'));
    }

    /** @test */
    public function it_can_get_audit_statistics()
    {
        $agreement = Agreement::factory()->create([
            'tenant_id' => $this->tenant->id,
            'unit_id' => $this->unit->id,
            'template_id' => $this->template->id,
            'created_by' => $this->user->id,
        ]);

        AgreementAuditLog::factory()->forAgreement($agreement)->created()->create();
        AgreementAuditLog::factory()->forAgreement($agreement)->signed()->create();
        AgreementAuditLog::factory()->forAgreement($agreement)->recent()->create();

        $statistics = $this->auditService->getAuditStatistics($agreement->id);

        $this->assertArrayHasKey('total_logs', $statistics);
        $this->assertArrayHasKey('recent_logs', $statistics);
        $this->assertArrayHasKey('action_counts', $statistics);
        $this->assertGreaterThan(0, $statistics['total_logs']);
    }

    /** @test */
    public function it_can_get_audit_logs_via_api()
    {
        $agreement = Agreement::factory()->create([
            'tenant_id' => $this->tenant->id,
            'unit_id' => $this->unit->id,
            'template_id' => $this->template->id,
            'created_by' => $this->user->id,
        ]);

        AgreementAuditLog::factory()->forAgreement($agreement)->created()->create();

        $response = $this->actingAs($this->user)
            ->getJson("/api/v1/agreements/{$agreement->id}/audit-logs");

        $response->assertStatus(200)
            ->assertJsonStructure([
                'success',
                'data' => [
                    'agreement_id',
                    'agreement_status',
                    'logs' => [
                        '*' => [
                            'id',
                            'action',
                            'action_description',
                            'user',
                            'old_values',
                            'new_values',
                            'changes',
                            'metadata',
                            'performed_at'
                        ]
                    ],
                    'total_logs'
                ]
            ]);
    }

    /** @test */
    public function it_can_get_audit_statistics_via_api()
    {
        $agreement = Agreement::factory()->create([
            'tenant_id' => $this->tenant->id,
            'unit_id' => $this->unit->id,
            'template_id' => $this->template->id,
            'created_by' => $this->user->id,
        ]);

        $response = $this->actingAs($this->user)
            ->getJson("/api/v1/agreements/{$agreement->id}/audit/statistics");

        $response->assertStatus(200)
            ->assertJsonStructure([
                'success',
                'data' => [
                    'total_logs',
                    'recent_logs',
                    'action_counts',
                    'user_counts',
                    'most_active_actions'
                ]
            ]);
    }

    /** @test */
    public function it_can_get_recent_audit_activity_via_api()
    {
        $agreement = Agreement::factory()->create([
            'tenant_id' => $this->tenant->id,
            'unit_id' => $this->unit->id,
            'template_id' => $this->template->id,
            'created_by' => $this->user->id,
        ]);

        AgreementAuditLog::factory()->forAgreement($agreement)->recent()->create();

        $response = $this->actingAs($this->user)
            ->getJson('/api/v1/agreements/audit/recent-activity');

        $response->assertStatus(200)
            ->assertJsonStructure([
                'success',
                'data' => [
                    'recent_activity' => [
                        '*' => [
                            'id',
                            'action',
                            'action_description',
                            'agreement',
                            'user',
                            'performed_at'
                        ]
                    ],
                    'period_days',
                    'total_activity'
                ]
            ]);
    }

    /** @test */
    public function it_validates_audit_log_api_requests()
    {
        $agreement = Agreement::factory()->create([
            'tenant_id' => $this->tenant->id,
            'unit_id' => $this->unit->id,
            'template_id' => $this->template->id,
            'created_by' => $this->user->id,
        ]);

        $response = $this->actingAs($this->user)
            ->getJson("/api/v1/agreements/{$agreement->id}/audit-logs?user_id=invalid");

        $response->assertStatus(422);

        $response = $this->actingAs($this->user)
            ->getJson("/api/v1/agreements/{$agreement->id}/audit-logs?end_date=2023-01-01&start_date=2023-01-02");

        $response->assertStatus(422);
    }

    /** @test */
    public function it_requires_authentication_for_audit_endpoints()
    {
        $agreement = Agreement::factory()->create([
            'tenant_id' => $this->tenant->id,
            'unit_id' => $this->unit->id,
            'template_id' => $this->template->id,
            'created_by' => $this->user->id,
        ]);

        $response = $this->getJson("/api/v1/agreements/{$agreement->id}/audit-logs");
        $response->assertStatus(401);

        $response = $this->getJson('/api/v1/agreements/audit/statistics');
        $response->assertStatus(401);

        $response = $this->getJson('/api/v1/agreements/audit/recent-activity');
        $response->assertStatus(401);
    }

    /** @test */
    public function it_tracks_ip_address_and_user_agent()
    {
        $agreement = Agreement::factory()->create([
            'tenant_id' => $this->tenant->id,
            'unit_id' => $this->unit->id,
            'template_id' => $this->template->id,
            'created_by' => $this->user->id,
        ]);

        $auditLog = $this->auditService->logAction(
            $agreement,
            'test_action',
            null,
            null,
            null,
            $this->user,
            request()
        );

        $this->assertNotNull($auditLog->ip_address);
        $this->assertNotNull($auditLog->user_agent);
    }

    /** @test */
    public function it_provides_readable_action_descriptions()
    {
        $auditLog = AgreementAuditLog::factory()->create(['action' => 'created']);
        $this->assertEquals('Agreement created', $auditLog->action_description);

        $auditLog = AgreementAuditLog::factory()->create(['action' => 'signed_by_tenant']);
        $this->assertEquals('Signed by tenant', $auditLog->action_description);

        $auditLog = AgreementAuditLog::factory()->create(['action' => 'status_changed']);
        $this->assertEquals('Status changed', $auditLog->action_description);
    }

    /** @test */
    public function it_provides_readable_changes_format()
    {
        $auditLog = AgreementAuditLog::factory()->create([
            'old_values' => ['status' => 'draft', 'signed_by_tenant' => false],
            'new_values' => ['status' => 'active', 'signed_by_tenant' => true],
        ]);

        $changes = $auditLog->changes;

        $this->assertArrayHasKey('status', $changes);
        $this->assertArrayHasKey('signed_by_tenant', $changes);
        $this->assertEquals('draft', $changes['status']['old']);
        $this->assertEquals('active', $changes['status']['new']);
        $this->assertEquals(false, $changes['signed_by_tenant']['old']);
        $this->assertEquals(true, $changes['signed_by_tenant']['new']);
    }
} 