<?php

namespace Tests\Feature;

use App\Models\RentPayment;
use App\Models\Unit;
use App\Models\User;
use App\Services\ExternalPaymentService;
use App\Services\RentPaymentService;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Support\Facades\Artisan;
use Tests\TestCase;

class PaymentsSyncCommandTest extends TestCase
{
    use RefreshDatabase;

    protected function setUp(): void
    {
        parent::setUp();
        
        // Mock the external service
        $this->mock(ExternalPaymentService::class, function ($mock) {
            $mock->shouldReceive('fetchPayments')
                ->andReturn([
                    [
                        'unit_id' => 1,
                        'tenant_id' => 1,
                        'amount' => 2500.00,
                        'payment_date' => '2024-01-15',
                        'external_reference' => 'EXT123456',
                        'status' => 'completed',
                    ]
                ]);
        });
    }

    /** @test */
    public function it_syncs_payments_successfully()
    {
        $unit = Unit::factory()->create();
        $tenant = User::factory()->create(['role' => 'tenant']);

        $this->artisan('payments:sync')
            ->expectsOutput('Starting rent payment sync...')
            ->expectsOutput('Sync completed successfully!')
            ->expectsOutput('Reconciled: 1 payments')
            ->expectsOutput('Discrepancies: 0 issues')
            ->assertExitCode(0);

        $this->assertDatabaseHas('rent_payments', [
            'external_reference' => 'EXT123456',
            'status' => 'completed',
        ]);
    }

    /** @test */
    public function it_handles_sync_with_custom_days_parameter()
    {
        $unit = Unit::factory()->create();
        $tenant = User::factory()->create(['role' => 'tenant']);

        $this->artisan('payments:sync', ['--days' => 14])
            ->expectsOutput('Starting rent payment sync...')
            ->expectsOutput('Sync completed successfully!')
            ->assertExitCode(0);
    }

    /** @test */
    public function it_handles_sync_errors_gracefully()
    {
        // Mock the service to throw an exception
        $this->mock(RentPaymentService::class, function ($mock) {
            $mock->shouldReceive('reconcilePayments')
                ->andThrow(new \Exception('API connection failed'));
        });

        $this->artisan('payments:sync')
            ->expectsOutput('Starting rent payment sync...')
            ->expectsOutput('Sync failed: API connection failed')
            ->assertExitCode(1);
    }

    /** @test */
    public function it_handles_discrepancies()
    {
        // Mock the service to return discrepancies
        $this->mock(RentPaymentService::class, function ($mock) {
            $mock->shouldReceive('reconcilePayments')
                ->andReturn([
                    'reconciled' => [],
                    'discrepancies' => [
                        'Missing external reference',
                        'Invalid payment amount'
                    ]
                ]);
        });

        $this->artisan('payments:sync')
            ->expectsOutput('Starting rent payment sync...')
            ->expectsOutput('Sync completed successfully!')
            ->expectsOutput('Reconciled: 0 payments')
            ->expectsOutput('Discrepancies: 2 issues')
            ->expectsOutput('Discrepancies found:')
            ->expectsOutput('- Missing external reference')
            ->expectsOutput('- Invalid payment amount')
            ->assertExitCode(0);
    }

    /** @test */
    public function it_validates_days_parameter()
    {
        $this->artisan('payments:sync', ['--days' => -1])
            ->expectsOutput('Starting rent payment sync...')
            ->assertExitCode(0); // Command should still run but with default days
    }

    /** @test */
    public function it_logs_sync_results()
    {
        $unit = Unit::factory()->create();
        $tenant = User::factory()->create(['role' => 'tenant']);

        $this->artisan('payments:sync')
            ->assertExitCode(0);

        // Check that the sync was logged
        $this->assertDatabaseHas('rent_payments', [
            'external_reference' => 'EXT123456',
        ]);
    }

    /** @test */
    public function it_handles_empty_external_payments()
    {
        // Mock the service to return empty payments
        $this->mock(ExternalPaymentService::class, function ($mock) {
            $mock->shouldReceive('fetchPayments')
                ->andReturn([]);
        });

        $this->artisan('payments:sync')
            ->expectsOutput('Starting rent payment sync...')
            ->expectsOutput('Sync completed successfully!')
            ->expectsOutput('Reconciled: 0 payments')
            ->expectsOutput('Discrepancies: 0 issues')
            ->assertExitCode(0);
    }

    /** @test */
    public function it_updates_existing_payments()
    {
        $unit = Unit::factory()->create();
        $tenant = User::factory()->create(['role' => 'tenant']);

        // Create an existing payment
        RentPayment::create([
            'unit_id' => $unit->id,
            'tenant_id' => $tenant->id,
            'amount' => 2500.00,
            'payment_date' => '2024-01-15',
            'external_reference' => 'EXT123456',
            'status' => 'pending',
        ]);

        $this->artisan('payments:sync')
            ->expectsOutput('Starting rent payment sync...')
            ->expectsOutput('Sync completed successfully!')
            ->assertExitCode(0);

        // Check that the payment was updated
        $this->assertDatabaseHas('rent_payments', [
            'external_reference' => 'EXT123456',
            'status' => 'completed',
        ]);
    }
}
