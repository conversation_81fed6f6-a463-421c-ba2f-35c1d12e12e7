<?php

namespace Tests\Feature;

use Tests\TestCase;
use App\Models\User;
use App\Models\Notification;
use App\Models\NotificationPreference;
use App\Models\NotificationTemplate;
use App\Services\NotificationService;
use App\Services\SmsNotificationService;
use App\Services\PushNotificationService;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Support\Facades\Mail;
use Illuminate\Support\Facades\Notification as NotificationFacade;

class NotificationSystemTest extends TestCase
{
    use RefreshDatabase;

    protected $user;
    protected $notificationService;

    protected function setUp(): void
    {
        parent::setUp();
        
        $this->user = User::factory()->create([
            'role' => 'tenant',
            'phone' => '+919876543210',
        ]);
        
        $this->notificationService = app(NotificationService::class);
        
        // Disable actual email sending for tests
        Mail::fake();
        NotificationFacade::fake();
    }

    /** @test */
    public function it_can_create_notification_preferences()
    {
        $response = $this->actingAs($this->user)
            ->postJson('/api/v1/notification-preferences', [
                'notification_type' => 'system',
                'channel' => 'email',
                'enabled' => true,
                'frequency' => 'immediate',
            ]);

        $response->assertStatus(201)
            ->assertJsonStructure([
                'success',
                'message',
                'data' => [
                    'id',
                    'user_id',
                    'notification_type',
                    'channel',
                    'enabled',
                    'frequency',
                ]
            ]);

        $this->assertDatabaseHas('notification_preferences', [
            'user_id' => $this->user->id,
            'notification_type' => 'system',
            'channel' => 'email',
            'enabled' => true,
        ]);
    }

    /** @test */
    public function it_can_list_notification_preferences()
    {
        NotificationPreference::factory()->create([
            'user_id' => $this->user->id,
            'notification_type' => 'system',
            'channel' => 'email',
        ]);

        $response = $this->actingAs($this->user)
            ->getJson('/api/v1/notification-preferences');

        $response->assertStatus(200)
            ->assertJsonStructure([
                'success',
                'message',
                'data' => [
                    '*' => [
                        'id',
                        'user_id',
                        'notification_type',
                        'channel',
                        'enabled',
                    ]
                ]
            ]);
    }

    /** @test */
    public function it_can_update_notification_preferences()
    {
        $preference = NotificationPreference::factory()->create([
            'user_id' => $this->user->id,
            'notification_type' => 'system',
            'channel' => 'email',
            'enabled' => true,
        ]);

        $response = $this->actingAs($this->user)
            ->putJson("/api/v1/notification-preferences/{$preference->id}", [
                'enabled' => false,
                'frequency' => 'daily',
            ]);

        $response->assertStatus(200)
            ->assertJson([
                'success' => true,
                'data' => [
                    'enabled' => false,
                    'frequency' => 'daily',
                ]
            ]);
    }

    /** @test */
    public function it_can_bulk_update_notification_preferences()
    {
        $response = $this->actingAs($this->user)
            ->patchJson('/api/v1/notification-preferences/bulk-update', [
                'preferences' => [
                    [
                        'notification_type' => 'system',
                        'channel' => 'email',
                        'enabled' => true,
                    ],
                    [
                        'notification_type' => 'system',
                        'channel' => 'sms',
                        'enabled' => false,
                    ],
                ]
            ]);

        $response->assertStatus(200)
            ->assertJsonStructure([
                'success',
                'message',
                'data' => [
                    '*' => [
                        'id',
                        'user_id',
                        'notification_type',
                        'channel',
                        'enabled',
                    ]
                ]
            ]);
    }

    /** @test */
    public function it_can_create_notification_templates()
    {
        $admin = User::factory()->create(['role' => 'admin']);

        $response = $this->actingAs($admin)
            ->postJson('/api/v1/notification-templates', [
                'name' => 'test_template',
                'type' => 'email',
                'category' => 'system',
                'title' => 'Test Notification',
                'content' => 'This is a test notification with {{variable}}',
                'variables' => ['variable'],
                'channels' => ['email'],
                'is_active' => true,
            ]);

        $response->assertStatus(201)
            ->assertJsonStructure([
                'success',
                'message',
                'data' => [
                    'id',
                    'name',
                    'type',
                    'category',
                    'title',
                    'content',
                    'variables',
                    'channels',
                    'is_active',
                ]
            ]);
    }

    /** @test */
    public function it_can_list_notification_templates()
    {
        NotificationTemplate::factory()->create([
            'name' => 'test_template',
            'type' => 'email',
            'category' => 'system',
        ]);

        $response = $this->actingAs($this->user)
            ->getJson('/api/v1/notification-templates');

        $response->assertStatus(200)
            ->assertJsonStructure([
                'success',
                'message',
                'data' => [
                    '*' => [
                        'id',
                        'name',
                        'type',
                        'category',
                        'title',
                        'content',
                    ]
                ]
            ]);
    }

    /** @test */
    public function it_can_preview_notification_template()
    {
        $admin = User::factory()->create(['role' => 'admin']);
        $template = NotificationTemplate::factory()->create([
            'name' => 'test_template',
            'type' => 'email',
            'category' => 'system',
            'title' => 'Hello {{name}}',
            'content' => 'Welcome to {{system_name}}, {{name}}!',
        ]);

        $response = $this->actingAs($admin)
            ->postJson("/api/v1/notification-templates/{$template->id}/preview", [
                'variables' => [
                    'name' => 'John Doe',
                    'system_name' => 'TMS',
                ]
            ]);

        $response->assertStatus(200)
            ->assertJson([
                'success' => true,
                'data' => [
                    'rendered_title' => 'Hello John Doe',
                    'rendered_content' => 'Welcome to TMS, John Doe!',
                    'variables_used' => [
                        'name' => 'John Doe',
                        'system_name' => 'TMS',
                    ]
                ]
            ]);
    }

    /** @test */
    public function it_can_send_multi_channel_notifications()
    {
        // Create preferences for multiple channels
        NotificationPreference::factory()->create([
            'user_id' => $this->user->id,
            'notification_type' => 'system',
            'channel' => 'email',
            'enabled' => true,
        ]);

        NotificationPreference::factory()->create([
            'user_id' => $this->user->id,
            'notification_type' => 'system',
            'channel' => 'in_app',
            'enabled' => true,
        ]);

        $notification = Notification::factory()->create([
            'user_id' => $this->user->id,
            'type' => 'system',
            'title' => 'Test Notification',
            'message' => 'This is a test notification',
            'status' => 'pending',
        ]);

        $result = $this->notificationService->send($notification);

        $this->assertTrue($result);
        $this->assertDatabaseHas('notifications', [
            'id' => $notification->id,
            'status' => 'sent',
        ]);
    }

    /** @test */
    public function it_respects_user_notification_preferences()
    {
        // Disable email notifications
        NotificationPreference::factory()->create([
            'user_id' => $this->user->id,
            'notification_type' => 'system',
            'channel' => 'email',
            'enabled' => false,
        ]);

        // Enable in-app notifications
        NotificationPreference::factory()->create([
            'user_id' => $this->user->id,
            'notification_type' => 'system',
            'channel' => 'in_app',
            'enabled' => true,
        ]);

        $notification = Notification::factory()->create([
            'user_id' => $this->user->id,
            'type' => 'system',
            'title' => 'Test Notification',
            'message' => 'This is a test notification',
            'status' => 'pending',
        ]);

        $result = $this->notificationService->send($notification);

        $this->assertTrue($result);
        
        // Check that email was not sent but in-app notification was created
        $this->assertDatabaseHas('notifications', [
            'id' => $notification->id,
            'status' => 'sent',
        ]);
    }

    /** @test */
    public function it_can_send_sms_notifications()
    {
        $smsService = app(SmsNotificationService::class);
        
        // Mock the SMS service to avoid actual SMS sending
        $this->mock(SmsNotificationService::class, function ($mock) {
            $mock->shouldReceive('send')->andReturn(true);
        });

        $notification = Notification::factory()->create([
            'user_id' => $this->user->id,
            'type' => 'sms',
            'title' => 'SMS Test',
            'message' => 'This is an SMS test',
            'status' => 'pending',
        ]);

        $result = $smsService->send($notification);

        $this->assertTrue($result);
    }

    /** @test */
    public function it_can_send_push_notifications()
    {
        $pushService = app(PushNotificationService::class);
        
        // Mock the push service to avoid actual push sending
        $this->mock(PushNotificationService::class, function ($mock) {
            $mock->shouldReceive('send')->andReturn(true);
        });

        $notification = Notification::factory()->create([
            'user_id' => $this->user->id,
            'type' => 'push',
            'title' => 'Push Test',
            'message' => 'This is a push test',
            'status' => 'pending',
        ]);

        $result = $pushService->send($notification);

        $this->assertTrue($result);
    }

    /** @test */
    public function it_can_mark_notifications_as_read()
    {
        $notification = Notification::factory()->create([
            'user_id' => $this->user->id,
            'read_at' => null,
        ]);

        $response = $this->actingAs($this->user)
            ->patchJson("/api/v1/notifications/{$notification->id}/read");

        $response->assertStatus(200)
            ->assertJson([
                'success' => true,
                'message' => 'Notification marked as read',
            ]);

        $this->assertDatabaseHas('notifications', [
            'id' => $notification->id,
            'read_at' => now(),
        ]);
    }

    /** @test */
    public function it_can_mark_all_notifications_as_read()
    {
        Notification::factory()->count(3)->create([
            'user_id' => $this->user->id,
            'read_at' => null,
        ]);

        $response = $this->actingAs($this->user)
            ->patchJson('/api/v1/notifications/mark-all-read');

        $response->assertStatus(200)
            ->assertJson([
                'success' => true,
                'message' => 'All notifications marked as read',
            ]);

        $this->assertDatabaseMissing('notifications', [
            'user_id' => $this->user->id,
            'read_at' => null,
        ]);
    }

    /** @test */
    public function it_can_get_notification_statistics()
    {
        Notification::factory()->count(5)->create([
            'user_id' => $this->user->id,
        ]);

        Notification::factory()->create([
            'user_id' => $this->user->id,
            'read_at' => now(),
        ]);

        $response = $this->actingAs($this->user)
            ->getJson('/api/v1/notifications/statistics');

        $response->assertStatus(200)
            ->assertJsonStructure([
                'success',
                'message',
                'data' => [
                    'total_notifications',
                    'unread_notifications',
                    'read_notifications',
                    'recent_notifications',
                ]
            ]);
    }

    /** @test */
    public function it_can_get_preference_statistics()
    {
        NotificationPreference::factory()->count(3)->create([
            'user_id' => $this->user->id,
        ]);

        $response = $this->actingAs($this->user)
            ->getJson('/api/v1/notification-preferences/statistics');

        $response->assertStatus(200)
            ->assertJsonStructure([
                'success',
                'message',
                'data' => [
                    'total_preferences',
                    'enabled_preferences',
                    'disabled_preferences',
                    'by_type',
                    'by_channel',
                ]
            ]);
    }

    /** @test */
    public function it_can_get_template_statistics()
    {
        $admin = User::factory()->create(['role' => 'admin']);
        NotificationTemplate::factory()->count(3)->create();

        $response = $this->actingAs($admin)
            ->getJson('/api/v1/notification-templates/statistics');

        $response->assertStatus(200)
            ->assertJsonStructure([
                'success',
                'message',
                'data' => [
                    'total_templates',
                    'active_templates',
                    'inactive_templates',
                    'by_type',
                    'by_category',
                ]
            ]);
    }

    /** @test */
    public function it_can_duplicate_notification_template()
    {
        $admin = User::factory()->create(['role' => 'admin']);
        $template = NotificationTemplate::factory()->create([
            'name' => 'original_template',
            'is_active' => true,
        ]);

        $response = $this->actingAs($admin)
            ->postJson("/api/v1/notification-templates/{$template->id}/duplicate");

        $response->assertStatus(200)
            ->assertJson([
                'success' => true,
                'message' => 'Notification template duplicated successfully',
            ]);

        $this->assertDatabaseHas('notification_templates', [
            'name' => 'original_template (Copy)',
            'is_active' => false,
        ]);
    }

    /** @test */
    public function it_can_reset_preferences_to_defaults()
    {
        $response = $this->actingAs($this->user)
            ->postJson('/api/v1/notification-preferences/reset-to-defaults');

        $response->assertStatus(200)
            ->assertJson([
                'success' => true,
                'message' => 'Notification preferences reset to defaults successfully',
            ]);

        // Check that default preferences were created
        $this->assertDatabaseHas('notification_preferences', [
            'user_id' => $this->user->id,
            'notification_type' => 'system',
            'channel' => 'email',
        ]);
    }

    /** @test */
    public function it_can_get_notification_options()
    {
        $response = $this->actingAs($this->user)
            ->getJson('/api/v1/notification-preferences/options');

        $response->assertStatus(200)
            ->assertJsonStructure([
                'success',
                'message',
                'data' => [
                    'types',
                    'channels',
                    'frequencies',
                ]
            ]);
    }

    /** @test */
    public function it_can_get_template_options()
    {
        $response = $this->actingAs($this->user)
            ->getJson('/api/v1/notification-templates/options');

        $response->assertStatus(200)
            ->assertJsonStructure([
                'success',
                'message',
                'data' => [
                    'types',
                    'categories',
                ]
            ]);
    }

    /** @test */
    public function it_can_get_templates_by_category()
    {
        NotificationTemplate::factory()->create([
            'category' => 'system',
            'is_active' => true,
        ]);

        $response = $this->actingAs($this->user)
            ->getJson('/api/v1/notification-templates/category/system');

        $response->assertStatus(200)
            ->assertJson([
                'success' => true,
                'message' => "Templates for category 'system' retrieved successfully",
            ]);
    }

    /** @test */
    public function it_can_get_templates_by_type()
    {
        NotificationTemplate::factory()->create([
            'type' => 'email',
            'is_active' => true,
        ]);

        $response = $this->actingAs($this->user)
            ->getJson('/api/v1/notification-templates/type/email');

        $response->assertStatus(200)
            ->assertJson([
                'success' => true,
                'message' => "Templates for type 'email' retrieved successfully",
            ]);
    }
} 