<?php

namespace Tests\Feature;

use App\Models\RentPayment;
use App\Models\Unit;
use App\Models\User;
use App\Notifications\PaymentReceivedNotification;
use App\Notifications\PaymentOverdueNotification;
use App\Notifications\PaymentReconciliationFailedNotification;
use App\Services\RentPaymentService;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Support\Facades\Notification;
use Tests\TestCase;

class PaymentNotificationTest extends TestCase
{
    use RefreshDatabase;

    protected function setUp(): void
    {
        parent::setUp();
        Notification::fake();
    }

    /** @test */
    public function it_sends_payment_received_notification_to_tenant()
    {
        $tenant = User::factory()->create(['role' => 'tenant']);
        $unit = Unit::factory()->create();
        
        $payment = RentPayment::factory()->create([
            'tenant_id' => $tenant->id,
            'unit_id' => $unit->id,
            'status' => 'completed',
        ]);

        $tenant->notify(new PaymentReceivedNotification($payment));

        Notification::assertSentTo(
            $tenant,
            PaymentReceivedNotification::class,
            function ($notification) use ($payment) {
                return $notification->payment->id === $payment->id;
            }
        );
    }

    /** @test */
    public function it_sends_payment_overdue_notification()
    {
        $tenant = User::factory()->create(['role' => 'tenant']);
        $unit = Unit::factory()->create();
        
        $notification = new PaymentOverdueNotification($unit, 5, 1500.00);
        $tenant->notify($notification);

        Notification::assertSentTo(
            $tenant,
            PaymentOverdueNotification::class
        );
    }

    /** @test */
    public function it_sends_reconciliation_failure_notification_to_admins()
    {
        $admin = User::factory()->create(['role' => 'admin']);
        
        $notification = new PaymentReconciliationFailedNotification(
            'External API connection failed',
            ['context' => 'test']
        );
        $admin->notify($notification);

        Notification::assertSentTo(
            $admin,
            PaymentReconciliationFailedNotification::class
        );
    }

    /** @test */
    public function it_handles_payment_notification_preferences()
    {
        $tenant = User::factory()->create(['role' => 'tenant']);
        $unit = Unit::factory()->create();
        
        $payment = RentPayment::factory()->create([
            'tenant_id' => $tenant->id,
            'unit_id' => $unit->id,
            'status' => 'completed',
        ]);

        // Test that notification is sent when preferences are enabled
        $tenant->notify(new PaymentReceivedNotification($payment));

        Notification::assertSentTo(
            $tenant,
            PaymentReceivedNotification::class
        );
    }

    /** @test */
    public function it_includes_payment_details_in_notification()
    {
        $tenant = User::factory()->create(['role' => 'tenant']);
        $unit = Unit::factory()->create(['unit_number' => 'A101']);
        
        $payment = RentPayment::factory()->create([
            'tenant_id' => $tenant->id,
            'unit_id' => $unit->id,
            'amount' => 2500.00,
            'status' => 'completed',
        ]);

        $notification = new PaymentReceivedNotification($payment);
        $arrayData = $notification->toArray($tenant);

        $this->assertEquals($payment->id, $arrayData['payment_id']);
        $this->assertEquals(2500.00, $arrayData['amount']);
        $this->assertEquals('A101', $arrayData['unit_number']);
        $this->assertEquals('completed', $arrayData['status']);
        $this->assertEquals('payment_received', $arrayData['type']);
    }
}
