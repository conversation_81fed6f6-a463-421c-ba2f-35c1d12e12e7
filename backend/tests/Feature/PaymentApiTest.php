<?php

namespace Tests\Feature;

use App\Models\RentPayment;
use App\Models\Unit;
use App\Models\User;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Tests\TestCase;

class PaymentApiTest extends TestCase
{
    use RefreshDatabase;

    protected User $admin;
    protected User $tenant;
    protected Unit $unit;

    protected function setUp(): void
    {
        parent::setUp();
        
        $this->admin = User::factory()->create(['role' => 'admin']);
        $this->tenant = User::factory()->create(['role' => 'tenant']);
        $this->unit = Unit::factory()->create();
    }

    /** @test */
    public function it_returns_unit_payments()
    {
        $this->actingAs($this->admin);

        RentPayment::create([
            'unit_id' => $this->unit->id,
            'tenant_id' => $this->tenant->id,
            'amount' => 2500.00,
            'payment_date' => '2024-01-15',
            'status' => 'completed',
        ]);

        $response = $this->getJson("/api/v1/units/{$this->unit->id}/payments");

        $response->assertStatus(200)
            ->assertJsonStructure([
                'data' => [
                    '*' => [
                        'id',
                        'unit_id',
                        'tenant_id',
                        'amount',
                        'payment_date',
                        'status',
                        'tenant' => [
                            'id',
                            'name',
                            'email'
                        ]
                    ]
                ],
                'pagination' => [
                    'current_page',
                    'last_page',
                    'per_page',
                    'total'
                ]
            ]);
    }

    /** @test */
    public function it_filters_unit_payments_by_status()
    {
        $this->actingAs($this->admin);

        RentPayment::create([
            'unit_id' => $this->unit->id,
            'tenant_id' => $this->tenant->id,
            'amount' => 2500.00,
            'payment_date' => '2024-01-15',
            'status' => 'completed',
        ]);

        RentPayment::create([
            'unit_id' => $this->unit->id,
            'tenant_id' => $this->tenant->id,
            'amount' => 2500.00,
            'payment_date' => '2024-01-16',
            'status' => 'pending',
        ]);

        $response = $this->getJson("/api/v1/units/{$this->unit->id}/payments?status=completed");

        $response->assertStatus(200);
        $this->assertCount(1, $response->json('data'));
        $this->assertEquals('completed', $response->json('data.0.status'));
    }

    /** @test */
    public function it_filters_unit_payments_by_date_range()
    {
        $this->actingAs($this->admin);

        RentPayment::create([
            'unit_id' => $this->unit->id,
            'tenant_id' => $this->tenant->id,
            'amount' => 2500.00,
            'payment_date' => '2024-01-15',
            'status' => 'completed',
        ]);

        RentPayment::create([
            'unit_id' => $this->unit->id,
            'tenant_id' => $this->tenant->id,
            'amount' => 2500.00,
            'payment_date' => '2024-02-15',
            'status' => 'completed',
        ]);

        $response = $this->getJson("/api/v1/units/{$this->unit->id}/payments?start_date=2024-01-01&end_date=2024-01-31");

        $response->assertStatus(200);
        $this->assertCount(1, $response->json('data'));
    }

    /** @test */
    public function it_returns_tenant_payments()
    {
        $this->actingAs($this->admin);

        RentPayment::create([
            'unit_id' => $this->unit->id,
            'tenant_id' => $this->tenant->id,
            'amount' => 2500.00,
            'payment_date' => '2024-01-15',
            'status' => 'completed',
        ]);

        $response = $this->getJson("/api/v1/tenants/{$this->tenant->id}/payments");

        $response->assertStatus(200)
            ->assertJsonStructure([
                'data' => [
                    '*' => [
                        'id',
                        'unit_id',
                        'tenant_id',
                        'amount',
                        'payment_date',
                        'status',
                        'unit' => [
                            'id',
                            'unit_number',
                            'property_id'
                        ]
                    ]
                ],
                'pagination' => [
                    'current_page',
                    'last_page',
                    'per_page',
                    'total'
                ]
            ]);
    }

    /** @test */
    public function it_returns_unit_payment_statistics()
    {
        $this->actingAs($this->admin);

        RentPayment::create([
            'unit_id' => $this->unit->id,
            'tenant_id' => $this->tenant->id,
            'amount' => 2500.00,
            'payment_date' => '2024-01-15',
            'status' => 'completed',
        ]);

        RentPayment::create([
            'unit_id' => $this->unit->id,
            'tenant_id' => $this->tenant->id,
            'amount' => 2500.00,
            'payment_date' => '2024-01-16',
            'status' => 'pending',
        ]);

        $response = $this->getJson("/api/v1/units/{$this->unit->id}/payments/statistics");

        $response->assertStatus(200)
            ->assertJsonStructure([
                'data' => [
                    'total_payments',
                    'total_paid',
                    'total_pending',
                    'total_failed'
                ]
            ]);

        $data = $response->json('data');
        $this->assertEquals(2, $data['total_payments']);
        $this->assertEquals(2500.00, $data['total_paid']);
        $this->assertEquals(2500.00, $data['total_pending']);
    }

    /** @test */
    public function it_returns_tenant_payment_statistics()
    {
        $this->actingAs($this->admin);

        RentPayment::create([
            'unit_id' => $this->unit->id,
            'tenant_id' => $this->tenant->id,
            'amount' => 2500.00,
            'payment_date' => '2024-01-15',
            'status' => 'completed',
        ]);

        RentPayment::create([
            'unit_id' => $this->unit->id,
            'tenant_id' => $this->tenant->id,
            'amount' => 2500.00,
            'payment_date' => '2024-01-16',
            'status' => 'pending',
        ]);

        $response = $this->getJson("/api/v1/tenants/{$this->tenant->id}/payments/statistics");

        $response->assertStatus(200)
            ->assertJsonStructure([
                'data' => [
                    'total_payments',
                    'total_paid',
                    'total_pending',
                    'total_failed'
                ]
            ]);

        $data = $response->json('data');
        $this->assertEquals(2, $data['total_payments']);
        $this->assertEquals(2500.00, $data['total_paid']);
        $this->assertEquals(2500.00, $data['total_pending']);
    }

    /** @test */
    public function it_validates_date_range_parameters()
    {
        $this->actingAs($this->admin);

        $response = $this->getJson("/api/v1/units/{$this->unit->id}/payments?start_date=2024-01-15&end_date=2024-01-10");

        $response->assertStatus(422);
    }

    /** @test */
    public function it_validates_status_parameter()
    {
        $this->actingAs($this->admin);

        $response = $this->getJson("/api/v1/units/{$this->unit->id}/payments?status=invalid_status");

        $response->assertStatus(422);
    }

    /** @test */
    public function it_validates_per_page_parameter()
    {
        $this->actingAs($this->admin);

        $response = $this->getJson("/api/v1/units/{$this->unit->id}/payments?per_page=150");

        $response->assertStatus(422);
    }

    /** @test */
    public function it_requires_authentication()
    {
        $response = $this->getJson("/api/v1/units/{$this->unit->id}/payments");

        $response->assertStatus(401);
    }

    /** @test */
    public function it_requires_proper_permissions()
    {
        $user = User::factory()->create(['role' => 'tenant']);
        $this->actingAs($user);

        $response = $this->getJson("/api/v1/units/{$this->unit->id}/payments");

        $response->assertStatus(403);
    }
}
