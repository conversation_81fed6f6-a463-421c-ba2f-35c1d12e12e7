-- Setup script for TMS database
-- Run this with: mysql -u root -p < setup-db.sql

-- Create database
CREATE DATABASE IF NOT EXISTS tms_db;

-- Create user and grant privileges
CREATE USER IF NOT EXISTS 'tms_user'@'localhost' IDENTIFIED BY 'tms_password';
CREATE USER IF NOT EXISTS 'tms_user'@'127.0.0.1' IDENTIFIED BY 'tms_password';
CREATE USER IF NOT EXISTS 'tms_user'@'%' IDENTIFIED BY 'tms_password';

-- Grant all privileges on the database
GRANT ALL PRIVILEGES ON tms_db.* TO 'tms_user'@'localhost';
GRANT ALL PRIVILEGES ON tms_db.* TO 'tms_user'@'127.0.0.1';
GRANT ALL PRIVILEGES ON tms_db.* TO 'tms_user'@'%';

-- Flush privileges to apply changes
FLUSH PRIVILEGES;

-- Show databases to confirm
SHOW DATABASES;
