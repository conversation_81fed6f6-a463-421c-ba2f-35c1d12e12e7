<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <title>Rent Receipt</title>
    <style>
        body { font-family: DejaVu Sans, Arial, Helvetica, sans-serif; color: #222; margin: 0; padding: 0; }
        .container { max-width: 700px; margin: 0 auto; padding: 32px; border: 1px solid #eee; background: #fff; }
        .header { text-align: center; margin-bottom: 32px; }
        .branding { font-size: 1.5em; font-weight: bold; color: #2a3f54; }
        .receipt-title { font-size: 2em; margin: 16px 0 8px 0; }
        .section { margin-bottom: 24px; }
        .section-title { font-weight: bold; margin-bottom: 8px; color: #2a3f54; }
        .details-table { width: 100%; border-collapse: collapse; margin-bottom: 16px; }
        .details-table th, .details-table td { padding: 8px 12px; border: 1px solid #ddd; }
        .details-table th { background: #f5f5f5; }
        .legal { font-size: 0.9em; color: #666; margin-top: 32px; }
        .footer { text-align: center; font-size: 0.9em; color: #888; margin-top: 32px; }
        .qr { float: right; margin-left: 16px; }
        .signature { margin-top: 40px; text-align: right; }
        .signature img { height: 60px; }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <div class="branding">
                {{ config('app.society_name', 'Your Housing Society') }}
            </div>
            <div class="receipt-title">Rent Receipt</div>
            <div>Receipt #: <strong>{{ $receipt->receipt_number ?? 'N/A' }}</strong></div>
            <div>Date: <strong>{{ $receipt->generated_at ? $receipt->generated_at->format('d M Y') : now()->format('d M Y') }}</strong></div>
        </div>

        <div class="section">
            <div class="section-title">Tenant & Unit Details</div>
            <table class="details-table">
                <tr>
                    <th>Tenant Name</th>
                    <td>{{ $receipt->payment->tenant->name ?? 'N/A' }}</td>
                    <th>Unit Number</th>
                    <td>{{ $receipt->payment->unit->unit_number ?? 'N/A' }}</td>
                </tr>
                <tr>
                    <th>Email</th>
                    <td>{{ $receipt->recipient_email ?? ($receipt->payment->tenant->email ?? 'N/A') }}</td>
                    <th>Property</th>
                    <td>{{ $receipt->payment->unit->property->name ?? 'N/A' }}</td>
                </tr>
            </table>
        </div>

        <div class="section">
            <div class="section-title">Payment Details</div>
            <table class="details-table">
                <tr>
                    <th>Payment Date</th>
                    <td>{{ $receipt->payment->payment_date ? $receipt->payment->payment_date->format('d M Y') : 'N/A' }}</td>
                    <th>Amount</th>
                    <td>₹{{ number_format($receipt->payment->amount, 2) }}</td>
                </tr>
                <tr>
                    <th>Payment Method</th>
                    <td>{{ ucfirst($receipt->payment->payment_method ?? 'N/A') }}</td>
                    <th>Status</th>
                    <td>{{ ucfirst($receipt->payment->status ?? 'N/A') }}</td>
                </tr>
                <tr>
                    <th>External Ref</th>
                    <td colspan="3">{{ $receipt->payment->external_reference ?? 'N/A' }}</td>
                </tr>
            </table>
        </div>

        @if($receipt->qr_code_path)
        <div class="section">
            <div class="section-title">Verification</div>
            <img class="qr" src="{{ public_path('storage/' . $receipt->qr_code_path) }}" alt="QR Code" width="100" height="100">
            <div>Scan to verify receipt: <strong>{{ $receipt->verification_code }}</strong></div>
        </div>
        @endif

        <div class="legal">
            <strong>Legal/Compliance:</strong><br>
            This receipt is generated for the payment of rent for the above-mentioned unit. Please retain this document for your records. The receipt is valid only if payment is confirmed and is subject to verification. For any queries, contact the society office.<br>
            <br>
            <em>Generated electronically by {{ config('app.society_name', 'Your Housing Society') }} on {{ now()->format('d M Y H:i') }}.</em>
        </div>

        <div class="signature">
            <div>Authorized Signatory</div>
            @if(config('app.society_signature_path'))
                <img src="{{ public_path(config('app.society_signature_path')) }}" alt="Signature">
            @endif
        </div>

        <div class="footer">
            &copy; {{ now()->year }} {{ config('app.society_name', 'Your Housing Society') }}. All rights reserved.
        </div>
    </div>
</body>
</html> 