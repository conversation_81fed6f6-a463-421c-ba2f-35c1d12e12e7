-- MySQL initialization script for TMS
-- This script runs automatically when the MySQL container starts for the first time

-- Create the database
CREATE DATABASE IF NOT EXISTS tms_db;

-- Create the user with proper permissions
CREATE USER IF NOT EXISTS 'tms_user'@'%' IDENTIFIED BY 'tms_password';
CREATE USER IF NOT EXISTS 'tms_user'@'localhost' IDENTIFIED BY 'tms_password';

-- Grant all privileges on the database
GRANT ALL PRIVILEGES ON tms_db.* TO 'tms_user'@'%';
GRANT ALL PRIVILEGES ON tms_db.* TO 'tms_user'@'localhost';

-- Flush privileges to apply changes
FLUSH PRIVILEGES;

-- Use the database
USE tms_db;

-- Show that everything is set up correctly
SELECT 'Database and user created successfully!' as status;
