#!/bin/bash

# Frontend Task Runner for Housing Society TMS
# This script helps execute frontend development tasks systematically

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
NC='\033[0m' # No Color

# Project paths
PROJECT_ROOT="$(cd "$(dirname "${BASH_SOURCE[0]}")/../.." && pwd)"
FRONTEND_DIR="$PROJECT_ROOT/frontend"
TASKMASTER_DIR="$PROJECT_ROOT/.taskmaster"

# Logging functions
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

log_task() {
    echo -e "${PURPLE}[TASK]${NC} $1"
}

# Display help
show_help() {
    echo "Frontend Task Runner for Housing Society TMS"
    echo ""
    echo "Usage: $0 [COMMAND] [OPTIONS]"
    echo ""
    echo "Commands:"
    echo "  start [task_id]     Start a specific frontend task"
    echo "  complete [task_id]  Mark a task as completed"
    echo "  status              Show status of all frontend tasks"
    echo "  next                Show next recommended task"
    echo "  setup               Setup frontend development environment"
    echo "  test [task_id]      Run tests for a specific task"
    echo "  build               Build frontend application"
    echo "  dev                 Start development server"
    echo "  help                Show this help message"
    echo ""
    echo "Frontend Task IDs:"
    echo "  14 - Frontend Foundation Setup"
    echo "  15 - Core UI Components Library"
    echo "  16 - Authentication & User Management UI"
    echo "  17 - Property Management Interface"
    echo "  18 - Tenant Management & Onboarding Interface"
    echo "  19 - Financial Management & Billing Interface"
    echo "  20 - Document Management & Digital Signatures"
    echo "  21 - Communication & Notification System"
    echo "  22 - Analytics Dashboard & Reporting"
    echo "  23 - Advanced Features & Integrations"
    echo "  24 - Testing & Quality Assurance"
    echo "  25 - Performance Optimization & Production Readiness"
    echo ""
    echo "Examples:"
    echo "  $0 setup                    # Setup development environment"
    echo "  $0 start 14                 # Start frontend foundation setup"
    echo "  $0 status                   # Show all task statuses"
    echo "  $0 next                     # Show next recommended task"
    echo "  $0 test 15                  # Run tests for UI components"
}

# Check if frontend directory exists
check_frontend_dir() {
    if [ ! -d "$FRONTEND_DIR" ]; then
        log_error "Frontend directory not found: $FRONTEND_DIR"
        log_info "Run '$0 setup' to initialize the frontend project"
        exit 1
    fi
}

# Setup frontend development environment
setup_frontend() {
    log_task "Setting up frontend development environment..."
    
    # Create frontend directory if it doesn't exist
    if [ ! -d "$FRONTEND_DIR" ]; then
        log_info "Creating frontend directory..."
        mkdir -p "$FRONTEND_DIR"
    fi
    
    cd "$FRONTEND_DIR"
    
    # Check if package.json exists
    if [ ! -f "package.json" ]; then
        log_info "Initializing Next.js project..."
        npx create-next-app@latest . --typescript --tailwind --eslint --app --src-dir --import-alias "@/*"
    else
        log_info "Frontend project already initialized"
    fi
    
    # Install additional dependencies
    log_info "Installing additional dependencies..."
    npm install @mui/material @emotion/react @emotion/styled @mui/icons-material
    npm install @tanstack/react-query zustand react-hook-form @hookform/resolvers zod
    npm install keycloak-js axios recharts
    npm install -D @storybook/nextjs storybook @testing-library/jest-dom @testing-library/react
    npm install -D @playwright/test jest jest-environment-jsdom
    
    log_success "Frontend development environment setup completed!"
    log_info "Next steps:"
    log_info "1. Run '$0 dev' to start development server"
    log_info "2. Run '$0 start 14' to begin with frontend foundation setup"
}

# Start development server
start_dev_server() {
    check_frontend_dir
    log_info "Starting frontend development server..."
    cd "$FRONTEND_DIR"
    npm run dev
}

# Build frontend application
build_frontend() {
    check_frontend_dir
    log_info "Building frontend application..."
    cd "$FRONTEND_DIR"
    npm run build
}

# Get task information
get_task_info() {
    local task_id=$1
    case $task_id in
        14)
            echo "Frontend Foundation Setup - Next.js 14, TypeScript, Material-UI setup"
            ;;
        15)
            echo "Core UI Components Library - Reusable component library with Storybook"
            ;;
        16)
            echo "Authentication & User Management UI - Keycloak integration and user interfaces"
            ;;
        17)
            echo "Property Management Interface - Property listings and unit management"
            ;;
        18)
            echo "Tenant Management & Onboarding Interface - KYC verification and onboarding"
            ;;
        19)
            echo "Financial Management & Billing Interface - Billing and payment interfaces"
            ;;
        20)
            echo "Document Management & Digital Signatures - Document workflows and e-signatures"
            ;;
        21)
            echo "Communication & Notification System - Multi-channel messaging and notifications"
            ;;
        22)
            echo "Analytics Dashboard & Reporting - Interactive charts and business intelligence"
            ;;
        23)
            echo "Advanced Features & Integrations - Advanced search and third-party integrations"
            ;;
        24)
            echo "Testing & Quality Assurance - Comprehensive testing and QA processes"
            ;;
        25)
            echo "Performance Optimization & Production Readiness - Performance and production setup"
            ;;
        *)
            echo "Unknown task ID: $task_id"
            return 1
            ;;
    esac
}

# Start a specific task
start_task() {
    local task_id=$1
    
    if [ -z "$task_id" ]; then
        log_error "Task ID is required"
        log_info "Usage: $0 start [task_id]"
        exit 1
    fi
    
    local task_info=$(get_task_info $task_id)
    if [ $? -ne 0 ]; then
        log_error "$task_info"
        exit 1
    fi
    
    log_task "Starting Task $task_id: $task_info"
    
    # Task-specific setup
    case $task_id in
        14)
            log_info "Setting up Next.js 14 foundation..."
            log_info "1. Configure TypeScript strict mode"
            log_info "2. Set up Material-UI theme"
            log_info "3. Configure Keycloak authentication"
            log_info "4. Set up API client with Axios"
            ;;
        15)
            log_info "Building UI components library..."
            log_info "1. Create layout components"
            log_info "2. Build form components"
            log_info "3. Create data display components"
            log_info "4. Set up Storybook documentation"
            ;;
        16)
            log_info "Implementing authentication interfaces..."
            log_info "1. Create login/logout pages"
            log_info "2. Build user profile management"
            log_info "3. Implement role-based access control"
            log_info "4. Create user management interfaces"
            ;;
        *)
            log_info "Follow the task details in .taskmaster/tasks/task_${task_id}.txt"
            ;;
    esac
    
    # Open task file if it exists
    local task_file="$TASKMASTER_DIR/tasks/task_${task_id}.txt"
    if [ -f "$task_file" ]; then
        log_info "Task details available at: $task_file"
    fi
    
    log_success "Task $task_id started. Happy coding! 🚀"
}

# Show status of all frontend tasks
show_status() {
    log_info "Frontend Development Task Status:"
    echo ""
    
    for task_id in {14..25}; do
        local task_info=$(get_task_info $task_id)
        echo -e "${PURPLE}Task $task_id:${NC} $task_info"
        echo -e "  Status: ${YELLOW}Pending${NC}"
        echo ""
    done
}

# Show next recommended task
show_next_task() {
    log_info "Next recommended task:"
    echo ""
    echo -e "${PURPLE}Task 14:${NC} Frontend Foundation Setup"
    echo "This is the starting point for frontend development."
    echo "Run: $0 start 14"
}

# Run tests for a specific task
run_task_tests() {
    local task_id=$1
    
    if [ -z "$task_id" ]; then
        log_error "Task ID is required"
        log_info "Usage: $0 test [task_id]"
        exit 1
    fi
    
    check_frontend_dir
    cd "$FRONTEND_DIR"
    
    log_info "Running tests for Task $task_id..."
    
    case $task_id in
        15|16|17|18|19|20|21|22|23)
            log_info "Running component tests..."
            npm run test
            ;;
        24)
            log_info "Running comprehensive test suite..."
            npm run test
            npm run test:e2e
            ;;
        25)
            log_info "Running performance tests..."
            npm run test
            npm run lighthouse
            ;;
        *)
            log_info "Running general tests..."
            npm run test
            ;;
    esac
}

# Main script logic
case "${1:-help}" in
    setup)
        setup_frontend
        ;;
    start)
        start_task "$2"
        ;;
    complete)
        log_info "Task completion tracking will be implemented with Taskmaster integration"
        ;;
    status)
        show_status
        ;;
    next)
        show_next_task
        ;;
    test)
        run_task_tests "$2"
        ;;
    build)
        build_frontend
        ;;
    dev)
        start_dev_server
        ;;
    help|--help|-h)
        show_help
        ;;
    *)
        log_error "Unknown command: $1"
        show_help
        exit 1
        ;;
esac
