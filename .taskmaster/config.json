{"models": {"main": {"provider": "bedrock", "modelId": "us.anthropic.claude-sonnet-4-20250514-v1:0", "maxTokens": 120000, "temperature": 0.2}, "research": {"provider": "bedrock", "modelId": "us.anthropic.claude-sonnet-4-20250514-v1:0", "maxTokens": 8700, "temperature": 0.1}, "fallback": {"provider": "bedrock", "modelId": "us.anthropic.claude-3-7-sonnet-20250219-v1:0", "maxTokens": 65536, "temperature": 0.2}}, "global": {"logLevel": "info", "debug": false, "defaultNumTasks": 10, "defaultSubtasks": 5, "defaultPriority": "medium", "projectName": "Taskmaster", "ollamaBaseURL": "http://localhost:11434/api", "bedrockBaseURL": "https://bedrock.us-east-1.amazonaws.com", "responseLanguage": "English", "defaultTag": "master", "azureOpenaiBaseURL": "https://your-endpoint.openai.azure.com/", "userId": "**********"}, "claudeCode": {}}