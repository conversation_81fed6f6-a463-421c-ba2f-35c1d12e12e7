# Task ID: 11
# Title: Rent Payment Tracking Service
# Status: pending
# Dependencies: None
# Priority: medium
# Description: Implement rent payment tracking service that fetches rent payment information from the external OneApp mobile application payment service, tracks payments per unit and tenant, reconciles payments, and provides reporting and notifications
# Details:


# Test Strategy:


# Subtasks:
## 1. Design rent payment data models [pending]
### Dependencies: None
### Description: Create database migrations and Eloquent model for rent payments
### Details:
Define `rent_payments` table with fields: unit_id, tenant_id, amount, payment_date, payment_method, external_reference, status, metadata; implement `RentPayment` model with relationships to `Unit` and `User` and necessary casts.

## 2. Implement external payment fetching service [pending]
### Dependencies: None
### Description: Integrate with OneApp external payment API
### Details:
Create `ExternalPaymentService` to call OneApp payment endpoints using configured credentials, handle pagination, errors, and parse payment records.

## 3. Implement rent payment reconciliation service [pending]
### Dependencies: None
### Description: Match external payments with local records and handle discrepancies
### Details:
Develop `RentPaymentService` to fetch latest payments, store new records, update statuses, mark missing or duplicate payments, and log discrepancies.

## 4. Create scheduled sync command [pending]
### Dependencies: None
### Description: Automate periodic fetching of rent payments
### Details:
Implement an Artisan command `payments:sync` to call the reconciliation service, and register it in the scheduler to run daily or configurable interval.

## 5. Build API endpoints for rent payment history [pending]
### Dependencies: None
### Description: Expose payment data to unit owners and tenants
### Details:
Create `PaymentController` with endpoints: `GET /api/v1/units/{unit}/payments`, `GET /api/v1/tenants/{tenant}/payments` returning paginated payment history with filters.

## 6. Implement payment notifications [pending]
### Dependencies: None
### Description: Notify landlords and tenants of payment events
### Details:
Use `NotificationService` to send notifications on new payment, overdue payment, and failed reconciliation; update user preferences for payment notifications.

## 7. Write comprehensive tests for payment tracker [pending]
### Dependencies: None
### Description: Ensure correct functionality of models, services, scheduler, and APIs
### Details:
Write unit tests for model and services (`ExternalPaymentService`, `RentPaymentService`), feature tests for API endpoints, and integration tests for scheduled command. 