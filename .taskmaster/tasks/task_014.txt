TASK: Frontend Foundation Setup
PRIORITY: high
STATUS: pending
PHASE: Phase 1: Frontend Foundation
ESTIMATED_HOURS: 20
DEPENDENCIES: [1]
TAGS: frontend, nextjs, setup, foundation

DESCRIPTION:
Set up the complete Next.js 14 frontend foundation with TypeScript, Material-UI, and modern React architecture for the Housing Society TMS application.

SUBTASKS:
1. Next.js 14 Project Initialization
   - Initialize Next.js 14 project with TypeScript
   - Configure app router and modern React features
   - Set up project structure and folder organization
   - Configure ESLint, Prettier, and TypeScript strict mode

2. Material-UI and Styling Setup
   - Install and configure Material-UI v5 with emotion
   - Set up custom theme with housing society branding
   - Configure responsive breakpoints and typography
   - Install and configure Tailwind CSS for utility classes

3. State Management Configuration
   - Install and configure TanStack Query for server state
   - Set up Zustand for client state management
   - Configure React Hook Form with Zod validation
   - Set up error boundaries and loading states

4. Authentication Integration
   - Install and configure Keycloak JavaScript adapter
   - Set up authentication context and hooks
   - Implement protected route components
   - Configure token management and refresh logic

5. API Client Setup
   - Configure Axios with interceptors for API calls
   - Set up base API client with error handling
   - Implement request/response transformers
   - Configure API endpoints and environment variables

ACCEPTANCE_CRITERIA:
- Next.js 14 project running with TypeScript
- Material-UI components rendering correctly
- Authentication flow working with Keycloak
- API client making successful requests to backend
- Development environment fully functional
