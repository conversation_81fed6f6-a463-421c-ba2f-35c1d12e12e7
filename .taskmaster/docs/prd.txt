# Housing Society Tenant Management System (TMS) - PRD

## Project Overview
A comprehensive multi-tenant management system for housing societies with authentication via Keycloak and API Gateway using Kong, specifically designed to handle unit management, tenant onboarding, agreement generation, and society administration workflows.

## Core Features (5.1)

### 1. Unit Status Management
- Mark Unit as "To-Let" or "Rented"
- Four-state system: "Occupied", "Vacant", "To-Let", "Rented"
- Status change tracking with audit logs
- Automatic notifications on status changes

### 2. Tenant Onboarding
- Add/capture tenant details for any member-owned unit
- Complete tenant profile management
- Emergency contact information
- Tenant history tracking

### 3. Tenant Verification
- Upload KYC documents (ID proof, address proof, photo)
- Document verification workflow
- Verification status tracking
- Automated verification processes

### 4. Leave & Licence Agreement
- Generate agreements from templates
- Set start/end dates with automatic calculations
- Manage renewals and extensions
- Digital signature integration
- Agreement status tracking

### 5. Society NOC Workflow
- Apply for NOC (No Objection Certificate)
- Track application status
- Admin approval process
- Document upload for NOC applications
- Automated notifications

### 6. Non-Occupancy Charges
- Auto-calculate charges when owner is not resident
- Billing integration
- Payment tracking
- Charge calculation rules
- Financial reporting

### 7. Enquiries Management
- Capture rental interest from within the app
- Route enquiries to unit owners/admin
- Enquiry status tracking
- Communication history
- Response management

### 8. Status Management
- "Occupied", "Vacant", "To-Let", "Rented" (slider/toggle)
- Real-time status updates
- Status change notifications
- Historical status tracking

### 9. Notifications System
- Expiry notifications for agreements
- Renewal reminders
- NOC status updates
- New enquiry notifications
- Multi-channel delivery (email, SMS, push)

### 10. Role-based Access Control
- Primary Member permissions
- Secondary Member permissions
- Society Admin permissions
- Manager permissions
- Granular access control

## Integration Requirements (5.2)

### APIs for Multiple Platforms
- Next.js Admin Console APIs
- Flutter Member App APIs
- External portal integration
- Third-party service APIs

### Event Hooks
- Agreement generated events
- NOC approved events
- Status change events
- Payment events
- Notification events

### Billing Integration
- Non-occupancy charge calculation
- Payment gateway integration
- Financial reporting
- Invoice generation

## UX/UI Requirements (5.3)

### Admin Console
- Dashboard with key metrics
- Units/Flats management interface
- Tenancy management system
- Enquiry inbox
- Agreement/NOC section
- Financial reporting

### Owner/Member Interface
- Unit status toggle functionality
- Tenant onboarding form
- Agreement upload/generation
- Notification center
- Personal dashboard

### Tenant Interface
- Access to view own details
- Agreement viewing
- Expiry information
- Renewal request system (future phase)

## Configurable Terminology (5.4)
- System labels default to "Member", "Unit", "Flat"
- Per-society customization
- Multi-language support
- Terminology management

## Technology Stack

### Frontend
- Next.js 14 with TypeScript
- Material-UI (MUI) Design 3
- Tailwind CSS for styling
- React Query for state management
- React Hook Form for forms

### Backend
- Laravel 11 with PHP 8.3
- MySQL/PostgreSQL database
- Laravel Sanctum for API authentication
- Spatie packages for permissions and activity logging

### Infrastructure
- Keycloak for authentication
- Kong API Gateway
- Docker containerization
- Redis for caching

### Security & Monitoring
- JWT token authentication
- Role-based access control (RBAC)
- Comprehensive audit logging
- API rate limiting
- CORS protection

## Development Phases

### Phase 1: Foundation (Weeks 1-3)
- Project setup with Laravel and Next.js
- Database schema design
- Core models and migrations
- Basic authentication setup

### Phase 2: Unit & Tenant Management (Weeks 4-6)
- Unit status management system
- Tenant onboarding workflow
- KYC document upload and verification
- Emergency contact management

### Phase 3: Agreement & NOC System (Weeks 7-9)
- Agreement generation from templates
- NOC application and approval workflow
- Document management system
- Digital signature integration

### Phase 4: Billing & Charges (Weeks 10-12)
- Non-occupancy charge calculation
- Billing integration
- Payment tracking
- Financial reporting

### Phase 5: Enquiries & Notifications (Weeks 13-15)
- Enquiry management system
- Multi-channel notification system
- Status update workflows
- Communication templates

### Phase 6: Admin Console & Member App (Weeks 16-18)
- Admin dashboard with analytics
- Member interface for unit management
- Role-based access control implementation
- Mobile-responsive design

### Phase 7: Integration & Testing (Weeks 19-20)
- API integration for Flutter app
- External portal integration
- Event hooks implementation
- Comprehensive testing and QA

## Success Metrics

### Performance Targets
- System uptime > 99.9%
- API response time < 200ms
- Support 1000+ concurrent users
- Handle 10,000+ requests/minute

### Quality Standards
- 100% test coverage for critical paths
- Zero security vulnerabilities
- Complete API documentation
- Full audit trail coverage

### User Experience
- Page load time < 2 seconds
- 100% mobile compatibility
- WCAG 2.1 AA accessibility compliance
- Error rate < 0.1%

## Security Requirements

### Authentication & Authorization
- Multi-factor authentication
- Session management
- Role-based access control
- API security with rate limiting

### Data Protection
- Data encryption at rest and in transit
- Regular security audits
- GDPR compliance
- Data backup and recovery

### Audit & Compliance
- Complete audit logging
- Activity tracking
- Compliance reporting
- Data retention policies

## Deployment Strategy

### Development Environment
- Docker containers
- Local development setup
- Hot reloading
- Development tools

### Staging Environment
- Production-like setup
- Integration testing
- Performance testing
- User acceptance testing

### Production Environment
- CI/CD pipeline
- Monitoring and alerting
- Backup strategies
- Disaster recovery

## Risk Mitigation

### Technical Risks
- Regular security audits
- Automated testing
- Performance monitoring
- Code quality checks

### Business Risks
- User feedback integration
- Regular stakeholder reviews
- Change management processes
- Training and documentation

This PRD provides a comprehensive foundation for building a robust, scalable, and secure Housing Society Tenant Management System that meets all specified requirements while maintaining enterprise-grade standards. 