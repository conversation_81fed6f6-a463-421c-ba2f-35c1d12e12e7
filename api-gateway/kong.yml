# Kong Declarative Configuration for Housing Society TMS
# Version: 1.0.0
# Description: Complete Kong setup for TMS with Keycloak integration

_format_version: "3.0"
_transform: true

# Services Configuration
services:
  # TMS Backend Laravel API
  - name: tms-backend
    url: http://backend:8000
    protocol: http
    host: backend
    port: 8000
    path: /
    connect_timeout: 60000
    write_timeout: 60000
    read_timeout: 60000
    retries: 5
    tags:
      - tms
      - backend
      - laravel

  # TMS Frontend Next.js Application  
  - name: tms-frontend
    url: http://frontend:3000
    protocol: http
    host: frontend
    port: 3000
    path: /
    connect_timeout: 60000
    write_timeout: 60000
    read_timeout: 60000
    retries: 5
    tags:
      - tms
      - frontend
      - nextjs

  # Keycloak Authentication Service
  - name: keycloak-auth
    url: http://keycloak:8080
    protocol: http
    host: keycloak
    port: 8080
    path: /
    connect_timeout: 60000
    write_timeout: 60000
    read_timeout: 60000
    retries: 5
    tags:
      - auth
      - keycloak
      - sso

# Routes Configuration
routes:
  # API Routes - Backend Laravel
  - name: api-routes
    service: tms-backend
    protocols:
      - http
      - https
    methods:
      - GET
      - POST
      - PUT
      - PATCH
      - DELETE
      - OPTIONS
    paths:
      - /api
    strip_path: false
    preserve_host: false
    tags:
      - api
      - backend

  # Authentication Routes - Keycloak
  - name: auth-routes
    service: keycloak-auth
    protocols:
      - http
      - https
    methods:
      - GET
      - POST
      - PUT
      - DELETE
      - OPTIONS
    paths:
      - /auth
    strip_path: false
    preserve_host: false
    tags:
      - auth
      - keycloak

  # Public Webhook Routes - No Auth Required
  - name: webhook-routes
    service: tms-backend
    protocols:
      - http
      - https
    methods:
      - POST
    paths:
      - /api/v1/public/webhooks
    strip_path: false
    preserve_host: false
    tags:
      - webhooks
      - public

  # Frontend Routes - Next.js Application
  - name: frontend-routes
    service: tms-frontend
    protocols:
      - http
      - https
    methods:
      - GET
    paths:
      - /
    strip_path: false
    preserve_host: false
    tags:
      - frontend
      - nextjs

# Consumers Configuration
consumers:
  # TMS Frontend Application
  - username: tms-frontend-app
    custom_id: tms-frontend
    tags:
      - frontend
      - application

  # TMS Mobile Application
  - username: tms-mobile-app
    custom_id: tms-mobile
    tags:
      - mobile
      - application

  # Property Portal Integrations
  - username: property-portals
    custom_id: property-portals
    tags:
      - external
      - portals

# Plugins Configuration
plugins:
  # CORS Plugin - Global
  - name: cors
    config:
      origins:
        - "http://localhost:3000"
        - "http://localhost:3001"
        - "https://tms.yourdomain.com"
      methods:
        - GET
        - POST
        - PUT
        - PATCH
        - DELETE
        - OPTIONS
      headers:
        - Accept
        - Accept-Version
        - Content-Length
        - Content-MD5
        - Content-Type
        - Date
        - Authorization
        - X-Auth-Token
      exposed_headers:
        - X-Auth-Token
      credentials: true
      max_age: 3600
    tags:
      - security
      - cors

  # Rate Limiting Plugin - API Routes
  - name: rate-limiting
    route: api-routes
    config:
      minute: 100
      hour: 1000
      day: 10000
      month: 100000
      year: 1000000
      limit_by: consumer
      policy: local
      fault_tolerant: true
      hide_client_headers: false
    tags:
      - security
      - rate-limiting

  # JWT Authentication Plugin - API Routes
  - name: jwt
    route: api-routes
    config:
      uri_param_names:
        - jwt
      cookie_names:
        - jwt
      header_names:
        - authorization
      claims_to_verify:
        - exp
        - iat
      key_claim_name: iss
      secret_is_base64: false
      anonymous: ""
      run_on_preflight: true
    tags:
      - security
      - jwt
      - auth

  # Request Transformer Plugin - API Routes
  - name: request-transformer
    route: api-routes
    config:
      remove:
        headers:
          - x-forwarded-for
      add:
        headers:
          - "X-API-Gateway: Kong"
          - "X-Service: TMS-Backend"
      append:
        headers:
          - "X-Request-ID: $(uuid)"
    tags:
      - transformation
      - headers

  # Response Transformer Plugin - API Routes  
  - name: response-transformer
    route: api-routes
    config:
      add:
        headers:
          - "X-API-Gateway: Kong"
          - "X-Response-Time: $(latency)"
      append:
        headers:
          - "X-Served-By: Kong-Gateway"
    tags:
      - transformation
      - response

  # Request Size Limiting Plugin - API Routes
  - name: request-size-limiting
    route: api-routes
    config:
      allowed_payload_size: 10
      size_unit: megabytes
      require_content_length: false
    tags:
      - security
      - size-limiting

  # IP Restriction Plugin - Webhook Routes (Optional)
  - name: ip-restriction
    route: webhook-routes
    config:
      allow:
        - "127.0.0.1"
        - "10.0.0.0/8"
        - "**********/12"
        - "***********/16"
      deny: []
    tags:
      - security
      - ip-restriction

  # Request Termination Plugin - Maintenance Mode (Disabled by default)
  - name: request-termination
    enabled: false
    config:
      status_code: 503
      content_type: "application/json"
      body: '{"message": "TMS is under maintenance. Please try again later.", "status": 503}'
    tags:
      - maintenance
      - termination

# Upstreams Configuration (for load balancing)
upstreams:
  # Backend Load Balancer
  - name: tms-backend-upstream
    algorithm: round-robin
    hash_on: none
    hash_fallback: none
    healthchecks:
      active:
        timeout: 1
        concurrency: 10
        http_path: "/api/health"
        healthy:
          interval: 10
          http_statuses:
            - 200
            - 302
          successes: 2
        unhealthy:
          interval: 10
          http_statuses:
            - 429
            - 404
            - 500
            - 501
            - 502
            - 503
            - 504
            - 505
          tcp_failures: 3
          timeouts: 3
          http_failures: 5
      passive:
        healthy:
          http_statuses:
            - 200
            - 201
            - 202
            - 203
            - 204
            - 205
            - 206
            - 300
            - 301
            - 302
            - 303
            - 304
            - 307
            - 308
          successes: 3
        unhealthy:
          http_statuses:
            - 429
            - 500
            - 503
          tcp_failures: 3
          timeouts: 3
          http_failures: 3
    tags:
      - load-balancer
      - backend

# Targets for Upstreams
targets:
  # Backend Targets
  - target: backend:8000
    upstream: tms-backend-upstream
    weight: 100
    tags:
      - backend
      - primary
