/**
 * Simple test script to verify the dashboard is working
 * Run this in the browser console to test API endpoints
 */

const API_BASE_URL = 'http://localhost:8000/api/v1';

async function testDashboardAPIs() {
  console.log('🚀 Testing Dashboard APIs...\n');

  const tests = [
    {
      name: 'Financial Metrics',
      url: `${API_BASE_URL}/analytics/metrics`,
      expectedFields: ['id', 'name', 'value', 'currency']
    },
    {
      name: 'Revenue Analytics',
      url: `${API_BASE_URL}/analytics/revenue`,
      expectedFields: ['month', 'revenue', 'occupancy']
    },
    {
      name: 'Payment Methods',
      url: `${API_BASE_URL}/analytics/payment-methods`,
      expectedFields: ['method', 'amount', 'percentage']
    },
    {
      name: 'Outstanding Dues',
      url: `${API_BASE_URL}/analytics/outstanding-dues`,
      expectedFields: ['range', 'count', 'amount']
    },
    {
      name: 'Reports Dashboard',
      url: `${API_BASE_URL}/reports/dashboard`,
      expectedFields: ['overview', 'financial_summary', 'occupancy_trends']
    },
    {
      name: 'Receipts Statistics',
      url: `${API_BASE_URL}/receipts/statistics`,
      expectedFields: []
    },
    {
      name: 'Receipts List',
      url: `${API_BASE_URL}/receipts`,
      expectedFields: []
    }
  ];

  let passedTests = 0;
  let totalTests = tests.length;

  for (const test of tests) {
    try {
      console.log(`📊 Testing ${test.name}...`);
      
      const response = await fetch(test.url, {
        method: 'GET',
        headers: {
          'Accept': 'application/json',
          'Content-Type': 'application/json'
        }
      });

      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }

      const data = await response.json();
      
      if (!data.success) {
        throw new Error(`API returned success: false - ${data.message}`);
      }

      // Check if expected fields exist in the data
      if (test.expectedFields.length > 0 && Array.isArray(data.data) && data.data.length > 0) {
        const firstItem = data.data[0];
        const missingFields = test.expectedFields.filter(field => !(field in firstItem));
        
        if (missingFields.length > 0) {
          console.warn(`⚠️  Missing fields in ${test.name}: ${missingFields.join(', ')}`);
        }
      }

      console.log(`✅ ${test.name}: PASSED`);
      console.log(`   Data items: ${Array.isArray(data.data) ? data.data.length : 'N/A'}`);
      console.log(`   Response time: ${response.headers.get('x-response-time') || 'N/A'}\n`);
      
      passedTests++;
    } catch (error) {
      console.error(`❌ ${test.name}: FAILED`);
      console.error(`   Error: ${error.message}\n`);
    }
  }

  console.log(`\n📈 Test Results: ${passedTests}/${totalTests} tests passed`);
  
  if (passedTests === totalTests) {
    console.log('🎉 All dashboard APIs are working correctly!');
  } else {
    console.log('⚠️  Some APIs need attention. Check the errors above.');
  }

  return { passed: passedTests, total: totalTests };
}

// Test dashboard component loading
function testDashboardComponents() {
  console.log('\n🔍 Testing Dashboard Components...\n');

  const components = [
    'ExecutiveDashboard',
    'FinancialAnalyticsDashboard', 
    'PropertyAnalyticsDashboard',
    'TenantAnalyticsDashboard',
    'CustomReportBuilder'
  ];

  components.forEach(component => {
    const elements = document.querySelectorAll(`[data-testid="${component}"], .${component}`);
    if (elements.length > 0) {
      console.log(`✅ ${component}: Found ${elements.length} element(s)`);
    } else {
      console.log(`⚠️  ${component}: Not found in DOM`);
    }
  });

  // Check for React Query provider
  const hasReactQuery = window.React && window.React.version;
  console.log(`${hasReactQuery ? '✅' : '❌'} React Query: ${hasReactQuery ? 'Available' : 'Not detected'}`);

  // Check for Material-UI
  const hasMUI = document.querySelector('[class*="MuiTab"], [class*="MuiCard"], [class*="MuiGrid"]');
  console.log(`${hasMUI ? '✅' : '❌'} Material-UI: ${hasMUI ? 'Components detected' : 'Not detected'}`);

  // Check for charts
  const hasCharts = document.querySelector('svg[class*="recharts"]');
  console.log(`${hasCharts ? '✅' : '❌'} Charts: ${hasCharts ? 'Recharts detected' : 'Not detected'}`);
}

// Test browser console for errors
function checkConsoleErrors() {
  console.log('\n🐛 Checking for Console Errors...\n');
  
  // This is a simple check - in a real test environment you'd capture console.error calls
  console.log('ℹ️  Check the browser console manually for any React or JavaScript errors');
  console.log('ℹ️  Look for red error messages or warnings');
  console.log('ℹ️  Common issues to check:');
  console.log('   - Failed API calls (network errors)');
  console.log('   - React component errors');
  console.log('   - Missing dependencies');
  console.log('   - TypeScript type errors');
}

// Main test function
async function runDashboardTests() {
  console.clear();
  console.log('🧪 Dashboard Integration Test Suite\n');
  console.log('=====================================\n');

  // Test APIs
  const apiResults = await testDashboardAPIs();
  
  // Test components
  testDashboardComponents();
  
  // Check for errors
  checkConsoleErrors();

  console.log('\n📋 Test Summary');
  console.log('================');
  console.log(`API Tests: ${apiResults.passed}/${apiResults.total} passed`);
  console.log('Component Tests: Manual verification required');
  console.log('Console Errors: Manual verification required');
  
  console.log('\n✨ Dashboard testing complete!');
  console.log('💡 Tip: Refresh the page and run this test again to verify consistency');
}

// Auto-run if in browser console
if (typeof window !== 'undefined') {
  console.log('🎯 Dashboard Test Script Loaded');
  console.log('📝 Run runDashboardTests() to start testing');
  console.log('📝 Or run individual test functions:');
  console.log('   - testDashboardAPIs()');
  console.log('   - testDashboardComponents()');
  console.log('   - checkConsoleErrors()');
}
