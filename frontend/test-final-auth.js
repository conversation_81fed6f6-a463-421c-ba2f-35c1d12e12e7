/**
 * Final test script to verify authentication and notifications are working
 * Run this in the browser console on the notifications page
 */

async function testFinalAuthentication() {
  console.log('🎯 Final Authentication & Notifications Test\n');
  console.log('===========================================\n');

  // Test 1: Check page location
  if (!window.location.pathname.includes('/notifications')) {
    console.error('❌ Please navigate to /notifications first');
    return;
  }
  console.log('✅ On notifications page');

  // Test 2: Check authentication status
  const accessToken = localStorage.getItem('access_token');
  const authUser = localStorage.getItem('auth_user');
  
  console.log('\n🔐 Authentication Status:');
  console.log(`- Access token: ${accessToken ? '✅ Present' : '❌ Missing'}`);
  console.log(`- Auth user: ${authUser ? '✅ Present' : '❌ Missing'}`);
  
  if (authUser) {
    try {
      const user = JSON.parse(authUser);
      console.log(`- User: ${user.name} (${user.email})`);
      console.log(`- Role: ${user.role}`);
    } catch (error) {
      console.log(`- User parse error: ${error.message}`);
    }
  }

  // Test 3: Check for authentication errors
  const hasAuthErrors = document.body.textContent.includes('401') ||
                       document.body.textContent.includes('Unauthorized') ||
                       document.body.textContent.includes('Authentication required') ||
                       document.body.textContent.includes('Failed to load notifications: Request failed with status code 401');

  console.log(`\n🚫 Authentication Errors: ${hasAuthErrors ? '❌ Present' : '✅ None detected'}`);

  // Test 4: Check for network errors
  const hasNetworkErrors = document.body.textContent.includes('Network Error') ||
                          document.body.textContent.includes('Failed to load notifications: Network Error');

  console.log(`🌐 Network Errors: ${hasNetworkErrors ? '❌ Present' : '✅ None detected'}`);

  // Test 5: Check for real notification data
  const hasRealData = document.body.textContent.includes('Unit A101 Status Changed') ||
                     document.body.textContent.includes('Status changed from') ||
                     document.body.textContent.includes('System Administrator');

  const hasEmptyState = document.body.textContent.includes('No notifications found') ||
                       document.body.textContent.includes('You\'re all caught up');

  const hasLoadingState = document.querySelector('[role="progressbar"]') ||
                         document.querySelector('.MuiCircularProgress-root') ||
                         document.querySelector('.MuiSkeleton-root');

  console.log('\n📊 Data Status:');
  if (hasRealData) {
    console.log('✅ Real notification data detected');
  } else if (hasEmptyState) {
    console.log('ℹ️ Empty state (no notifications available)');
  } else if (hasLoadingState) {
    console.log('⏳ Loading state detected');
  } else {
    console.log('⚠️ Unknown data state');
  }

  // Test 6: Check statistics cards
  let statsWorking = false;
  let statsData = {};

  const statisticsCards = document.querySelectorAll('.MuiCard-root');
  statisticsCards.forEach(card => {
    const cardText = card.textContent;
    if (cardText.includes('Total Notifications')) {
      const match = cardText.match(/(\d+)/);
      if (match) {
        statsData.total = parseInt(match[1]);
        statsWorking = true;
      }
    } else if (cardText.includes('Unread')) {
      const match = cardText.match(/(\d+)/);
      if (match) {
        statsData.unread = parseInt(match[1]);
      }
    } else if (cardText.includes('High Priority')) {
      const match = cardText.match(/(\d+)/);
      if (match) {
        statsData.highPriority = parseInt(match[1]);
      }
    } else if (cardText.includes('Today')) {
      const match = cardText.match(/(\d+)/);
      if (match) {
        statsData.today = parseInt(match[1]);
      }
    }
  });

  console.log(`📈 Statistics Cards: ${statsWorking ? '✅ Working' : '❌ Not working'}`);
  if (statsWorking) {
    console.log(`   - Total: ${statsData.total || 'N/A'}`);
    console.log(`   - Unread: ${statsData.unread || 'N/A'}`);
    console.log(`   - High Priority: ${statsData.highPriority || 'N/A'}`);
    console.log(`   - Today: ${statsData.today || 'N/A'}`);
  }

  // Test 7: Test API endpoints directly
  console.log('\n🔌 Testing API Endpoints:');
  await testAPIEndpointsDirect();

  // Test 8: Check for interactive features
  const hasMarkAsReadButtons = document.querySelector('button[aria-label*="Mark"]') ||
                              document.querySelector('button[title*="Mark"]') ||
                              document.querySelector('.MuiIconButton-root');

  console.log(`\n🎮 Interactive Features: ${hasMarkAsReadButtons ? '✅ Present' : '❌ Missing'}`);

  // Test 9: Check for tabs
  const hasTabs = document.querySelector('[role="tablist"]') ||
                 document.querySelector('.MuiTabs-root');

  console.log(`📑 Navigation Tabs: ${hasTabs ? '✅ Present' : '❌ Missing'}`);

  // Final Summary
  console.log('\n📋 FINAL TEST SUMMARY');
  console.log('=====================');
  
  const authWorking = accessToken && authUser && !hasAuthErrors;
  const dataWorking = (hasRealData || hasEmptyState) && !hasNetworkErrors;
  const uiWorking = statsWorking && hasTabs;
  
  console.log(`🔐 Authentication: ${authWorking ? '✅ WORKING' : '❌ FAILED'}`);
  console.log(`📊 Data Loading: ${dataWorking ? '✅ WORKING' : '❌ FAILED'}`);
  console.log(`🎨 UI Components: ${uiWorking ? '✅ WORKING' : '❌ FAILED'}`);
  console.log(`🌐 Network: ${!hasNetworkErrors ? '✅ WORKING' : '❌ FAILED'}`);

  if (authWorking && dataWorking && uiWorking && !hasNetworkErrors) {
    console.log('\n🎉 SUCCESS! All tests passed - Notifications page is fully functional!');
  } else {
    console.log('\n⚠️ Some issues detected. Check the details above.');
  }

  return {
    authWorking,
    dataWorking,
    uiWorking,
    networkWorking: !hasNetworkErrors,
    statsData,
    hasRealData,
    hasEmptyState
  };
}

async function testAPIEndpointsDirect() {
  const endpoints = [
    { url: '/api/v1/notifications', name: 'Notifications List' },
    { url: '/api/v1/notifications/statistics', name: 'Statistics' }
  ];

  for (const endpoint of endpoints) {
    try {
      console.log(`📡 Testing ${endpoint.name}...`);
      
      const response = await fetch(endpoint.url, {
        headers: {
          'Accept': 'application/json',
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${localStorage.getItem('access_token')}`,
        },
        credentials: 'include'
      });

      console.log(`   Status: ${response.status} ${response.statusText}`);
      
      if (response.ok) {
        const data = await response.json();
        console.log(`   Success: ${data.success}`);
        
        if (endpoint.url.includes('statistics')) {
          console.log(`   Total: ${data.data?.total_notifications || 'N/A'}`);
          console.log(`   Unread: ${data.data?.unread_notifications || 'N/A'}`);
        } else {
          console.log(`   Count: ${data.data?.data?.length || 'N/A'}`);
          console.log(`   Total: ${data.data?.total || 'N/A'}`);
        }
      } else if (response.status === 401) {
        console.log(`   ❌ Authentication failed`);
      } else {
        console.log(`   ❌ Error: ${response.status}`);
      }
    } catch (error) {
      console.log(`   ❌ Network error: ${error.message}`);
    }
  }
}

// Auto-run if in browser console
if (typeof window !== 'undefined') {
  console.log('🎯 Final Authentication Test Script Loaded');
  console.log('📝 Run testFinalAuthentication() to run all tests');
  
  // Auto-check if we're on notifications page
  if (window.location.pathname.includes('/notifications')) {
    console.log('ℹ️ Notifications page detected. Running tests in 2 seconds...');
    
    setTimeout(() => {
      testFinalAuthentication();
    }, 2000);
  }
}
