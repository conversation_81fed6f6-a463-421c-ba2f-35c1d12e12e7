import apiClient from '@/lib/api-client';

export interface Notification {
  id: string;
  type: string;
  data: {
    title: string;
    message: string;
    category?: string;
    priority?: 'low' | 'medium' | 'high' | 'urgent';
    channel?: 'email' | 'sms' | 'push' | 'in_app';
    sender?: string;
    metadata?: Record<string, any>;
  };
  read_at: string | null;
  created_at: string;
  updated_at: string;
}

export interface NotificationStatistics {
  total_notifications: number;
  unread_notifications: number;
  read_notifications: number;
  recent_notifications: Notification[];
}

export interface NotificationFilters {
  unread_only?: boolean;
  type?: string;
  per_page?: number;
  page?: number;
}

export interface PaginatedNotifications {
  data: Notification[];
  current_page: number;
  last_page: number;
  per_page: number;
  total: number;
  from: number;
  to: number;
}

class NotificationsService {
  private baseUrl = '/notifications';

  /**
   * Get paginated notifications for the authenticated user
   */
  async getNotifications(filters: NotificationFilters = {}): Promise<PaginatedNotifications> {
    const params = new URLSearchParams();
    
    if (filters.unread_only) {
      params.append('unread_only', '1');
    }
    if (filters.type) {
      params.append('type', filters.type);
    }
    if (filters.per_page) {
      params.append('per_page', filters.per_page.toString());
    }
    if (filters.page) {
      params.append('page', filters.page.toString());
    }

    const queryString = params.toString();
    const url = queryString ? `${this.baseUrl}?${queryString}` : this.baseUrl;

    const response = await apiClient.get<{ success: boolean; data: PaginatedNotifications }>(url);
    return response.data.data;
  }

  /**
   * Get notification statistics
   */
  async getStatistics(): Promise<NotificationStatistics> {
    const response = await apiClient.get<{ success: boolean; data: NotificationStatistics }>(`${this.baseUrl}/statistics`);
    return response.data.data;
  }

  /**
   * Mark a specific notification as read
   */
  async markAsRead(notificationId: string): Promise<Notification> {
    const response = await apiClient.patch<{ success: boolean; data: Notification }>(`${this.baseUrl}/${notificationId}/read`);
    return response.data.data;
  }

  /**
   * Mark all notifications as read
   */
  async markAllAsRead(): Promise<void> {
    await apiClient.patch<{ success: boolean; message: string }>(`${this.baseUrl}/mark-all-read`);
  }

  /**
   * Send a test notification (for testing purposes)
   */
  async sendTestNotification(data: {
    unit_id: string;
    previous_status: string;
    new_status: string;
    reason?: string;
  }): Promise<any> {
    const response = await apiClient.post<{ success: boolean; data: any }>(`${this.baseUrl}/test`, data);
    return response.data.data;
  }

  /**
   * Transform backend notification to frontend format
   */
  transformNotification(backendNotification: any): Notification {
    return {
      id: backendNotification.id,
      type: backendNotification.type || 'info',
      data: {
        title: backendNotification.data?.title || backendNotification.title || 'Notification',
        message: backendNotification.data?.message || backendNotification.message || '',
        category: backendNotification.data?.category || 'general',
        priority: this.mapPriority(backendNotification.priority || backendNotification.data?.priority),
        channel: this.mapChannel(backendNotification.channel || backendNotification.data?.channel),
        sender: backendNotification.data?.sender || 'System',
        metadata: backendNotification.data?.metadata || backendNotification.metadata,
      },
      read_at: backendNotification.read_at,
      created_at: backendNotification.created_at,
      updated_at: backendNotification.updated_at,
    };
  }

  /**
   * Map backend priority to frontend priority
   */
  private mapPriority(priority?: string): 'low' | 'medium' | 'high' | 'urgent' {
    switch (priority?.toLowerCase()) {
      case 'urgent':
        return 'urgent';
      case 'high':
        return 'high';
      case 'medium':
      case 'normal':
        return 'medium';
      case 'low':
      default:
        return 'low';
    }
  }

  /**
   * Map backend channel to frontend channel
   */
  private mapChannel(channel?: string): 'email' | 'sms' | 'push' | 'in_app' {
    switch (channel?.toLowerCase()) {
      case 'email':
        return 'email';
      case 'sms':
        return 'sms';
      case 'push':
        return 'push';
      case 'in_app':
      default:
        return 'in_app';
    }
  }

  /**
   * Map notification type to display type
   */
  mapNotificationType(type: string): 'info' | 'warning' | 'error' | 'success' {
    // Map Laravel notification types to Material-UI alert types
    const typeMapping: Record<string, 'info' | 'warning' | 'error' | 'success'> = {
      'App\\Notifications\\UnitStatusChangedNotification': 'info',
      'App\\Notifications\\PaymentReceivedNotification': 'success',
      'App\\Notifications\\PaymentOverdueNotification': 'warning',
      'App\\Notifications\\LeaseExpiryNotification': 'warning',
      'App\\Notifications\\MaintenanceRequestNotification': 'info',
      'App\\Notifications\\SystemNotification': 'info',
      'App\\Notifications\\ErrorNotification': 'error',
      'payment': 'success',
      'maintenance': 'info',
      'lease': 'warning',
      'system': 'info',
      'error': 'error',
      'warning': 'warning',
      'success': 'success',
      'info': 'info',
    };

    return typeMapping[type] || 'info';
  }

  /**
   * Get notification category from type
   */
  getNotificationCategory(type: string): string {
    const categoryMapping: Record<string, string> = {
      'App\\Notifications\\UnitStatusChangedNotification': 'property',
      'App\\Notifications\\PaymentReceivedNotification': 'payment',
      'App\\Notifications\\PaymentOverdueNotification': 'payment',
      'App\\Notifications\\LeaseExpiryNotification': 'lease',
      'App\\Notifications\\MaintenanceRequestNotification': 'maintenance',
      'App\\Notifications\\SystemNotification': 'system',
      'App\\Notifications\\ErrorNotification': 'system',
    };

    return categoryMapping[type] || 'general';
  }

  /**
   * Format notification for display
   */
  formatNotificationForDisplay(notification: any) {
    const transformed = this.transformNotification(notification);
    
    return {
      id: transformed.id,
      title: transformed.data.title,
      message: transformed.data.message,
      type: this.mapNotificationType(notification.type),
      priority: transformed.data.priority,
      channel: transformed.data.channel,
      isRead: !!transformed.read_at,
      createdAt: transformed.created_at,
      sender: transformed.data.sender,
      category: this.getNotificationCategory(notification.type),
    };
  }
}

export const notificationsService = new NotificationsService();
