import { BaseApiService } from './base-api.service';

export interface ReportOverview {
  total_units: number;
  occupied_units: number;
  total_tenants: number;
  active_agreements: number;
  monthly_revenue: number;
  pending_payments: number;
}

export interface FinancialSummary {
  current_month_revenue: number;
  last_month_revenue: number;
  outstanding_dues: number;
  collection_rate: number;
}

export interface OccupancyTrends {
  current_occupancy_rate: number;
  vacant_units: number;
  maintenance_units: number;
}

export interface RecentActivity {
  recent_payments: any[];
  recent_agreements: any[];
}

export interface ReportsDashboard {
  overview: ReportOverview;
  financial_summary: FinancialSummary;
  occupancy_trends: OccupancyTrends;
  recent_activities: RecentActivity;
}

export interface FinancialReportFilters {
  start_date?: string;
  end_date?: string;
  report_type?: 'revenue' | 'expenses' | 'profit_loss' | 'cash_flow';
  group_by?: 'day' | 'week' | 'month' | 'quarter' | 'year';
}

export interface ChartDataPoint {
  period: string;
  total_revenue: number;
  payment_count: number;
}

export interface FinancialReport {
  chart_data: ChartDataPoint[];
  total_revenue: number;
  total_payments: number;
  average_payment: number;
}

export interface OccupancyReportFilters {
  start_date?: string;
  end_date?: string;
  unit_type?: string;
}

export interface OccupancyReport {
  occupancy_rate: any;
  vacancy_trends: any;
  unit_turnover: any;
  lease_expirations: any;
  by_unit_type?: any;
}

export interface TenantReportFilters {
  start_date?: string;
  end_date?: string;
  status?: 'active' | 'inactive' | 'pending';
}

export interface TenantReport {
  tenant_statistics: any;
  payment_behavior: any;
  lease_renewals: any;
  tenant_satisfaction: any;
}

export interface ExportReportRequest {
  report_type: 'financial' | 'occupancy' | 'tenant' | 'comprehensive';
  format: 'csv' | 'excel' | 'pdf';
  start_date?: string;
  end_date?: string;
}

export interface ExportReportResponse {
  filename: string;
  download_url: string;
  expires_at: string;
}

export class ReportService extends BaseApiService {
  private readonly baseUrl = '/reports';

  /**
   * Get reports dashboard data
   */
  async getDashboard(): Promise<ReportsDashboard> {
    const response = await this.get<ReportsDashboard>(`${this.baseUrl}/dashboard`);
    return response.data;
  }

  /**
   * Get financial reports
   */
  async getFinancialReports(filters: FinancialReportFilters = {}): Promise<{
    data: FinancialReport;
    filters: FinancialReportFilters;
  }> {
    const params = new URLSearchParams();
    Object.entries(filters).forEach(([key, value]) => {
      if (value !== undefined) {
        params.append(key, value.toString());
      }
    });

    const response = await this.get<{
      data: FinancialReport;
      filters: FinancialReportFilters;
    }>(`${this.baseUrl}/financial?${params.toString()}`);
    
    return response.data;
  }

  /**
   * Get occupancy reports
   */
  async getOccupancyReports(filters: OccupancyReportFilters = {}): Promise<OccupancyReport> {
    const params = new URLSearchParams();
    Object.entries(filters).forEach(([key, value]) => {
      if (value !== undefined) {
        params.append(key, value.toString());
      }
    });

    const response = await this.get<OccupancyReport>(`${this.baseUrl}/occupancy?${params.toString()}`);
    return response.data;
  }

  /**
   * Get tenant reports
   */
  async getTenantReports(filters: TenantReportFilters = {}): Promise<TenantReport> {
    const params = new URLSearchParams();
    Object.entries(filters).forEach(([key, value]) => {
      if (value !== undefined) {
        params.append(key, value.toString());
      }
    });

    const response = await this.get<TenantReport>(`${this.baseUrl}/tenant?${params.toString()}`);
    return response.data;
  }

  /**
   * Export report
   */
  async exportReport(request: ExportReportRequest): Promise<ExportReportResponse> {
    const response = await this.post<ExportReportResponse>(`${this.baseUrl}/export`, request);
    return response.data;
  }

  /**
   * Get report statistics for overview cards
   */
  async getReportStatistics(): Promise<{
    total_reports: number;
    reports_this_month: number;
    most_popular_report: string;
    last_generated: string;
  }> {
    // This would typically come from a dedicated endpoint
    // For now, we'll derive it from dashboard data
    const dashboard = await this.getDashboard();
    
    return {
      total_reports: 156, // Mock data
      reports_this_month: 23,
      most_popular_report: 'Financial Report',
      last_generated: new Date().toISOString(),
    };
  }
}

export const reportService = new ReportService();
