import axios from 'axios';

export interface LoginCredentials {
  email: string;
  password: string;
  remember?: boolean;
}

export interface User {
  id: string;
  email: string;
  name: string;
  role: string;
  permissions: string[];
  first_name?: string;
  last_name?: string;
  phone?: string;
  status: string;
  is_verified: boolean;
}

export interface LoginResponse {
  success: boolean;
  message: string;
  data: {
    user: User;
    token: string;
    token_type: string;
    expires_at: string;
    abilities: string[];
  };
}

export interface AuthError {
  message: string;
  errors?: Record<string, string[]>;
}

class AuthService {
  private baseURL = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:8000/api/v1';
  private tokenKey = 'access_token';
  private userKey = 'auth_user';

  /**
   * Login with email and password
   */
  async login(credentials: LoginCredentials): Promise<LoginResponse> {
    try {
      console.log('🔐 Attempting login with:', credentials.email);
      
      const response = await axios.post<LoginResponse>(`${this.baseURL}/auth/login`, {
        email: credentials.email,
        password: credentials.password,
        remember: credentials.remember || false,
      }, {
        headers: {
          'Content-Type': 'application/json',
          'Accept': 'application/json',
        },
      });

      if (response.data.success) {
        // Store token and user data
        this.setToken(response.data.data.token);
        this.setUser(response.data.data.user);
        
        console.log('✅ Login successful for:', response.data.data.user.email);
        return response.data;
      } else {
        throw new Error(response.data.message || 'Login failed');
      }
    } catch (error: any) {
      console.error('❌ Login failed:', error.response?.data || error.message);
      
      if (error.response?.data) {
        throw {
          message: error.response.data.message || 'Login failed',
          errors: error.response.data.errors,
        } as AuthError;
      }
      
      throw {
        message: error.message || 'Network error during login',
      } as AuthError;
    }
  }

  /**
   * Auto-login with demo credentials
   */
  async demoLogin(email: string = '<EMAIL>'): Promise<LoginResponse> {
    console.log('🎭 Attempting demo login for:', email);
    
    return this.login({
      email,
      password: 'password', // Demo password
      remember: true,
    });
  }

  /**
   * Logout user
   */
  async logout(): Promise<void> {
    try {
      const token = this.getToken();
      if (token) {
        // Call backend logout endpoint
        await axios.post(`${this.baseURL}/auth/logout`, {}, {
          headers: {
            'Authorization': `Bearer ${token}`,
            'Accept': 'application/json',
          },
        });
      }
    } catch (error) {
      console.warn('Logout API call failed:', error);
    } finally {
      // Always clear local storage
      this.clearAuth();
      console.log('🚪 User logged out');
    }
  }

  /**
   * Get current user from API
   */
  async getCurrentUser(): Promise<User | null> {
    try {
      const token = this.getToken();
      if (!token) {
        return null;
      }

      const response = await axios.get(`${this.baseURL}/auth/user`, {
        headers: {
          'Authorization': `Bearer ${token}`,
          'Accept': 'application/json',
        },
      });

      if (response.data.success) {
        this.setUser(response.data.data);
        return response.data.data;
      }
      
      return null;
    } catch (error) {
      console.warn('Failed to get current user:', error);
      // If token is invalid, clear it
      this.clearAuth();
      return null;
    }
  }

  /**
   * Check if user is authenticated
   */
  isAuthenticated(): boolean {
    const token = this.getToken();
    const user = this.getUser();
    return !!(token && user);
  }

  /**
   * Get stored token
   */
  getToken(): string | null {
    if (typeof window === 'undefined') return null;
    return localStorage.getItem(this.tokenKey);
  }

  /**
   * Set token in localStorage
   */
  setToken(token: string): void {
    if (typeof window === 'undefined') return;
    localStorage.setItem(this.tokenKey, token);
  }

  /**
   * Get stored user
   */
  getUser(): User | null {
    if (typeof window === 'undefined') return null;
    
    try {
      const userStr = localStorage.getItem(this.userKey);
      return userStr ? JSON.parse(userStr) : null;
    } catch (error) {
      console.warn('Failed to parse stored user:', error);
      return null;
    }
  }

  /**
   * Set user in localStorage
   */
  setUser(user: User): void {
    if (typeof window === 'undefined') return;
    localStorage.setItem(this.userKey, JSON.stringify(user));
  }

  /**
   * Clear all authentication data
   */
  clearAuth(): void {
    if (typeof window === 'undefined') return;
    
    localStorage.removeItem(this.tokenKey);
    localStorage.removeItem(this.userKey);
    localStorage.removeItem('mock_user'); // Clean up old mock data
  }

  /**
   * Refresh token (if supported by backend)
   */
  async refreshToken(): Promise<string | null> {
    try {
      const token = this.getToken();
      if (!token) return null;

      const response = await axios.post(`${this.baseURL}/auth/refresh`, {}, {
        headers: {
          'Authorization': `Bearer ${token}`,
          'Accept': 'application/json',
        },
      });

      if (response.data.success && response.data.data.token) {
        this.setToken(response.data.data.token);
        return response.data.data.token;
      }
      
      return null;
    } catch (error) {
      console.warn('Token refresh failed:', error);
      return null;
    }
  }

  /**
   * Initialize authentication on app start
   */
  async initializeAuth(): Promise<User | null> {
    console.log('🔄 Initializing authentication...');
    
    // Check if we have a stored token
    const token = this.getToken();
    if (token) {
      console.log('📱 Found stored token, verifying...');
      const user = await this.getCurrentUser();
      if (user) {
        console.log('✅ Authentication restored for:', user.email);
        return user;
      }
    }

    // No valid authentication, try demo login
    console.log('🎭 No valid auth found, attempting demo login...');
    try {
      const loginResponse = await this.demoLogin();
      return loginResponse.data.user;
    } catch (error) {
      console.warn('Demo login failed:', error);
      return null;
    }
  }

  /**
   * Get demo users for development
   */
  getDemoUsers(): Array<{ email: string; role: string; name: string }> {
    return [
      { email: '<EMAIL>', role: 'admin', name: 'System Administrator' },
      { email: '<EMAIL>', role: 'owner', name: 'Property Owner' },
      { email: '<EMAIL>', role: 'tenant', name: 'Tenant User' },
    ];
  }
}

export const authService = new AuthService();
