import apiClient from '@/lib/api-client';

export interface DashboardMetrics {
  totalUnits: number;
  occupiedUnits: number;
  vacantUnits: number;
  rentedUnits: number;
  activeTenantsCount: number;
  activeAgreements: number;
  expiringSoon: number;
  totalRevenue: number;
  monthlyRevenue: number;
  collectionRate: number;
  avgCollectionDays: number;
}

export interface FinancialMetrics {
  id: string;
  name: string;
  value: number;
  currency: string;
  change: number;
  changePercentage: number;
  trend: 'up' | 'down';
}

export interface RevenueData {
  month: string;
  revenue: number;
  occupancy: number;
}

export interface PaymentMethod {
  method: string;
  amount: number;
  percentage: number;
  count: number;
}

export interface OutstandingDue {
  range: string;
  count: number;
  amount: number;
  percentage: number;
}

export interface PropertyAnalytics {
  totalProperties: number;
  occupancyRate: number;
  averageRent: number;
  maintenanceRequests: number;
  propertyValue: number;
  vacancyTrends: Array<{
    month: string;
    vacancyRate: number;
  }>;
}

export interface TenantAnalytics {
  totalTenants: number;
  activeTenants: number;
  newTenants: number;
  satisfactionScore: number;
  renewalRate: number;
  paymentBehavior: Array<{
    category: string;
    count: number;
    percentage: number;
  }>;
}

export interface ExecutiveSummary {
  overview: {
    totalUnits: number;
    occupiedUnits: number;
    totalRevenue: number;
    netIncome: number;
  };
  financialSummary: {
    monthlyRevenue: number;
    expenses: number;
    profitMargin: number;
    cashFlow: number;
  };
  occupancyTrends: Array<{
    month: string;
    occupancyRate: number;
    revenue: number;
  }>;
  recentActivities: Array<{
    id: number;
    type: string;
    message: string;
    time: string;
    icon: string;
    color: string;
  }>;
}

export interface ReportData {
  success: boolean;
  message: string;
  data: any;
}

class DashboardService {
  private baseUrl = '';

  // Financial Analytics
  async getFinancialMetrics(): Promise<FinancialMetrics[]> {
    const response = await apiClient.get<{ success: boolean; data: FinancialMetrics[] }>('/analytics/metrics');
    return response.data.data;
  }

  async getRevenueAnalytics(): Promise<RevenueData[]> {
    const response = await apiClient.get<{ success: boolean; data: RevenueData[] }>('/analytics/revenue');
    return response.data.data;
  }

  async getPaymentMethods(): Promise<PaymentMethod[]> {
    const response = await apiClient.get<{ success: boolean; data: PaymentMethod[] }>('/analytics/payment-methods');
    return response.data.data;
  }

  async getOutstandingDues(): Promise<OutstandingDue[]> {
    const response = await apiClient.get<{ success: boolean; data: OutstandingDue[] }>('/analytics/outstanding-dues');
    return response.data.data;
  }

  // Reports Dashboard
  async getReportsDashboard(): Promise<ExecutiveSummary> {
    const response = await apiClient.get<{ success: boolean; data: ExecutiveSummary }>('/reports/dashboard');
    return response.data.data;
  }

  async getReportStatistics(): Promise<any> {
    const response = await apiClient.get<ReportData>('/reports/statistics');
    return response.data.data;
  }

  // Property Analytics (mock for now - to be implemented when backend is ready)
  async getPropertyAnalytics(): Promise<PropertyAnalytics> {
    // This would be replaced with actual API call when backend is ready
    return {
      totalProperties: 4,
      occupancyRate: 75,
      averageRent: 25000,
      maintenanceRequests: 3,
      propertyValue: 5000000,
      vacancyTrends: [
        { month: 'Jan', vacancyRate: 25 },
        { month: 'Feb', vacancyRate: 20 },
        { month: 'Mar', vacancyRate: 15 },
        { month: 'Apr', vacancyRate: 25 },
        { month: 'May', vacancyRate: 20 },
        { month: 'Jun', vacancyRate: 25 },
      ]
    };
  }

  // Tenant Analytics (mock for now - to be implemented when backend is ready)
  async getTenantAnalytics(): Promise<TenantAnalytics> {
    // This would be replaced with actual API call when backend is ready
    return {
      totalTenants: 3,
      activeTenants: 3,
      newTenants: 1,
      satisfactionScore: 4.2,
      renewalRate: 85,
      paymentBehavior: [
        { category: 'On Time', count: 2, percentage: 67 },
        { category: 'Late', count: 1, percentage: 33 },
        { category: 'Defaulted', count: 0, percentage: 0 },
      ]
    };
  }

  // Receipts Analytics
  async getReceiptsStatistics(): Promise<any> {
    const response = await apiClient.get<ReportData>('/receipts/statistics');
    return response.data.data;
  }

  async getReceipts(): Promise<any[]> {
    const response = await apiClient.get<{ success: boolean; data: any[] }>('/receipts');
    return response.data.data;
  }

  // Combined Dashboard Data
  async getDashboardMetrics(): Promise<DashboardMetrics> {
    try {
      const [
        financialMetrics,
        revenueData,
        reportsDashboard,
        receiptsStats
      ] = await Promise.all([
        this.getFinancialMetrics(),
        this.getRevenueAnalytics(),
        this.getReportsDashboard(),
        this.getReceiptsStatistics()
      ]);

      // Extract metrics from various sources
      const totalRevenueMetric = financialMetrics.find(m => m.id === 'total_revenue');
      const monthlyRevenueMetric = financialMetrics.find(m => m.id === 'monthly_revenue');
      const collectionRateMetric = financialMetrics.find(m => m.id === 'collection');
      const avgDaysMetric = financialMetrics.find(m => m.id === 'avg_days');

      return {
        totalUnits: reportsDashboard.overview.totalUnits || 4,
        occupiedUnits: reportsDashboard.overview.occupiedUnits || 3,
        vacantUnits: (reportsDashboard.overview.totalUnits || 4) - (reportsDashboard.overview.occupiedUnits || 3),
        rentedUnits: reportsDashboard.overview.occupiedUnits || 3,
        activeTenantsCount: reportsDashboard.overview.occupiedUnits || 3,
        activeAgreements: reportsDashboard.overview.occupiedUnits || 3,
        expiringSoon: 1,
        totalRevenue: totalRevenueMetric?.value || reportsDashboard.overview.totalRevenue || 0,
        monthlyRevenue: monthlyRevenueMetric?.value || reportsDashboard.financialSummary.monthlyRevenue || 0,
        collectionRate: collectionRateMetric?.value || 85,
        avgCollectionDays: avgDaysMetric?.value || 25,
      };
    } catch (error) {
      console.warn('Failed to fetch dashboard metrics, using fallback data:', error);
      // Return fallback data
      return {
        totalUnits: 4,
        occupiedUnits: 3,
        vacantUnits: 1,
        rentedUnits: 3,
        activeTenantsCount: 3,
        activeAgreements: 3,
        expiringSoon: 1,
        totalRevenue: 150000,
        monthlyRevenue: 75000,
        collectionRate: 85,
        avgCollectionDays: 25,
      };
    }
  }
}

export const dashboardService = new DashboardService();
