import { BaseApiService } from './base-api.service';

export interface Receipt {
  id: string;
  payment_id: string;
  receipt_number: string;
  amount: number;
  status: 'pending' | 'delivered' | 'failed';
  recipient_email: string;
  generated_at: string;
  delivered_at?: string;
  file_path?: string;
  payment: {
    id: string;
    amount: number;
    payment_date: string;
    tenant: {
      id: string;
      name: string;
      email: string;
    };
    unit: {
      id: string;
      unit_number: string;
      property: {
        id: string;
        name: string;
      };
    };
  };
}

export interface ReceiptFilters {
  payment_id?: string;
  tenant_id?: string;
  unit_id?: string;
  status?: 'pending' | 'delivered' | 'failed';
  start_date?: string;
  end_date?: string;
  per_page?: number;
  page?: number;
}

export interface ReceiptListResponse {
  data: Receipt[];
  pagination: {
    current_page: number;
    per_page: number;
    total: number;
    last_page: number;
  };
}

export interface ReceiptStats {
  total_receipts: number;
  delivered_receipts: number;
  pending_receipts: number;
  failed_receipts: number;
  delivery_rate: number;
}

export interface ReceiptAnalytics {
  overview: {
    total_receipts: number;
    delivered_receipts: number;
    failed_receipts: number;
    pending_receipts: number;
    delivery_rate: number;
  };
  time_series: Array<{
    period: string;
    receipt_count: number;
    delivered_count: number;
    failed_count: number;
  }>;
  filters: {
    start_date: string;
    end_date: string;
    group_by: string;
  };
}

export interface GenerateReceiptRequest {
  recipient_email: string;
  template?: string;
}

export interface ReceiptAnalyticsFilters {
  start_date?: string;
  end_date?: string;
  group_by?: 'day' | 'week' | 'month';
}

export class ReceiptService extends BaseApiService {
  private readonly baseUrl = '/receipts';

  /**
   * Get list of receipts with filtering and pagination
   */
  async getReceipts(filters: ReceiptFilters = {}): Promise<ReceiptListResponse> {
    const params = new URLSearchParams();
    Object.entries(filters).forEach(([key, value]) => {
      if (value !== undefined) {
        params.append(key, value.toString());
      }
    });

    const response = await this.get<ReceiptListResponse>(`${this.baseUrl}?${params.toString()}`);
    return response.data;
  }

  /**
   * Get receipt by ID
   */
  async getReceipt(id: string): Promise<Receipt> {
    const response = await this.get<Receipt>(`${this.baseUrl}/${id}`);
    return response.data;
  }

  /**
   * Generate receipt for a payment
   */
  async generateReceipt(paymentId: string, request: GenerateReceiptRequest): Promise<Receipt> {
    const response = await this.post<Receipt>(`/api/v1/payments/${paymentId}/receipts`, request);
    return response.data;
  }

  /**
   * Download receipt
   */
  async downloadReceipt(id: string): Promise<Blob> {
    const response = await fetch(`${this.getBaseUrl()}${this.baseUrl}/${id}/download`, {
      method: 'GET',
      headers: this.getHeaders(),
    });

    if (!response.ok) {
      throw new Error('Failed to download receipt');
    }

    return response.blob();
  }

  /**
   * Regenerate receipt
   */
  async regenerateReceipt(id: string): Promise<Receipt> {
    const response = await this.post<Receipt>(`${this.baseUrl}/${id}/regenerate`);
    return response.data;
  }

  /**
   * Deliver receipt via email
   */
  async deliverReceipt(id: string): Promise<Receipt> {
    const response = await this.post<Receipt>(`${this.baseUrl}/${id}/deliver`);
    return response.data;
  }

  /**
   * Delete receipt
   */
  async deleteReceipt(id: string): Promise<void> {
    await this.delete(`${this.baseUrl}/${id}`);
  }

  /**
   * Get receipt statistics
   */
  async getReceiptStats(filters: { start_date?: string; end_date?: string } = {}): Promise<ReceiptStats> {
    const params = new URLSearchParams();
    Object.entries(filters).forEach(([key, value]) => {
      if (value !== undefined) {
        params.append(key, value.toString());
      }
    });

    const response = await this.get<ReceiptStats>(`${this.baseUrl}/statistics?${params.toString()}`);
    return response.data;
  }

  /**
   * Get receipt analytics
   */
  async getReceiptAnalytics(filters: ReceiptAnalyticsFilters = {}): Promise<ReceiptAnalytics> {
    const params = new URLSearchParams();
    Object.entries(filters).forEach(([key, value]) => {
      if (value !== undefined) {
        params.append(key, value.toString());
      }
    });

    const response = await this.get<ReceiptAnalytics>(`${this.baseUrl}/analytics?${params.toString()}`);
    return response.data;
  }

  /**
   * Verify receipt by code (public endpoint)
   */
  async verifyReceipt(code: string): Promise<{
    valid: boolean;
    receipt?: Receipt;
    message: string;
  }> {
    const response = await this.get<{
      valid: boolean;
      receipt?: Receipt;
      message: string;
    }>(`${this.baseUrl}/verify/${code}`);
    return response.data;
  }

  /**
   * Get receipt management statistics for dashboard
   */
  async getReceiptManagementStats(): Promise<{
    total_receipts: number;
    receipts_this_month: number;
    delivery_success_rate: number;
    pending_deliveries: number;
  }> {
    const stats = await this.getReceiptStats();
    
    return {
      total_receipts: stats.total_receipts,
      receipts_this_month: Math.floor(stats.total_receipts * 0.15), // Mock calculation
      delivery_success_rate: stats.delivery_rate,
      pending_deliveries: stats.pending_receipts,
    };
  }
}

export const receiptService = new ReceiptService();
