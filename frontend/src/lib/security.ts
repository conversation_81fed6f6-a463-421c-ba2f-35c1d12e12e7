import DOMPurify from 'dompurify';

// Security configuration
export const SECURITY_CONFIG = {
  // Content Security Policy
  CSP: {
    'default-src': ["'self'"],
    'script-src': ["'self'", "'unsafe-inline'", "'unsafe-eval'"],
    'style-src': ["'self'", "'unsafe-inline'", 'https://fonts.googleapis.com'],
    'font-src': ["'self'", 'https://fonts.gstatic.com'],
    'img-src': ["'self'", 'data:', 'https:'],
    'connect-src': ["'self'", 'https://api.example.com'],
    'frame-src': ["'none'"],
    'object-src': ["'none'"],
    'base-uri': ["'self'"],
    'form-action': ["'self'"],
  },
  
  // Security headers
  HEADERS: {
    'X-Frame-Options': 'DENY',
    'X-Content-Type-Options': 'nosniff',
    'X-XSS-Protection': '1; mode=block',
    'Referrer-Policy': 'strict-origin-when-cross-origin',
    'Permissions-Policy': 'camera=(), microphone=(), geolocation=()',
    'Strict-Transport-Security': 'max-age=31536000; includeSubDomains',
  },
  
  // Input validation patterns
  PATTERNS: {
    EMAIL: /^[^\s@]+@[^\s@]+\.[^\s@]+$/,
    PHONE: /^[\+]?[1-9][\d]{0,15}$/,
    PASSWORD: /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[@$!%*?&])[A-Za-z\d@$!%*?&]{8,}$/,
    ALPHANUMERIC: /^[a-zA-Z0-9\s]+$/,
    NUMERIC: /^[0-9]+$/,
    DECIMAL: /^[0-9]+(\.[0-9]+)?$/,
  },
  
  // Rate limiting
  RATE_LIMIT: {
    LOGIN_ATTEMPTS: 5,
    LOGIN_WINDOW: 15 * 60 * 1000, // 15 minutes
    API_CALLS: 100,
    API_WINDOW: 60 * 1000, // 1 minute
  },
};

// Input sanitization utilities
export class InputSanitizer {
  // Sanitize HTML content
  static sanitizeHTML(html: string): string {
    if (typeof window === 'undefined') return html;
    return DOMPurify.sanitize(html, {
      ALLOWED_TAGS: ['b', 'i', 'em', 'strong', 'a', 'p', 'br'],
      ALLOWED_ATTR: ['href', 'target'],
    });
  }

  // Sanitize text content
  static sanitizeText(text: string): string {
    return text
      .replace(/[<>]/g, '')
      .replace(/javascript:/gi, '')
      .replace(/on\w+=/gi, '')
      .trim();
  }

  // Validate email format
  static validateEmail(email: string): boolean {
    return SECURITY_CONFIG.PATTERNS.EMAIL.test(email);
  }

  // Validate phone number
  static validatePhone(phone: string): boolean {
    return SECURITY_CONFIG.PATTERNS.PHONE.test(phone);
  }

  // Validate password strength
  static validatePassword(password: string): {
    isValid: boolean;
    errors: string[];
  } {
    const errors: string[] = [];
    
    if (password.length < 8) {
      errors.push('Password must be at least 8 characters long');
    }
    
    if (!/[a-z]/.test(password)) {
      errors.push('Password must contain at least one lowercase letter');
    }
    
    if (!/[A-Z]/.test(password)) {
      errors.push('Password must contain at least one uppercase letter');
    }
    
    if (!/\d/.test(password)) {
      errors.push('Password must contain at least one number');
    }
    
    if (!/[@$!%*?&]/.test(password)) {
      errors.push('Password must contain at least one special character (@$!%*?&)');
    }
    
    return {
      isValid: errors.length === 0,
      errors,
    };
  }

  // Escape HTML entities
  static escapeHTML(text: string): string {
    const map: { [key: string]: string } = {
      '&': '&amp;',
      '<': '&lt;',
      '>': '&gt;',
      '"': '&quot;',
      "'": '&#x27;',
      '/': '&#x2F;',
    };
    return text.replace(/[&<>"'/]/g, (m) => map[m]);
  }

  // Validate file upload
  static validateFile(file: File, allowedTypes: string[], maxSize: number): {
    isValid: boolean;
    error?: string;
  } {
    // Check file type
    if (!allowedTypes.includes(file.type)) {
      return {
        isValid: false,
        error: `File type ${file.type} is not allowed`,
      };
    }

    // Check file size
    if (file.size > maxSize) {
      return {
        isValid: false,
        error: `File size ${file.size} bytes exceeds maximum ${maxSize} bytes`,
      };
    }

    return { isValid: true };
  }
}

// XSS Protection utilities
export class XSSProtection {
  // Check for XSS patterns in input
  static detectXSS(input: string): boolean {
    const xssPatterns = [
      /<script\b[^<]*(?:(?!<\/script>)<[^<]*)*<\/script>/gi,
      /javascript:/gi,
      /on\w+\s*=/gi,
      /<iframe\b[^<]*(?:(?!<\/iframe>)<[^<]*)*<\/iframe>/gi,
      /<object\b[^<]*(?:(?!<\/object>)<[^<]*)*<\/object>/gi,
      /<embed\b[^<]*(?:(?!<\/embed>)<[^<]*)*<\/embed>/gi,
      /<link\b[^<]*(?:(?!<\/link>)<[^<]*)*<\/link>/gi,
      /<meta\b[^<]*(?:(?!<\/meta>)<[^<]*)*<\/meta>/gi,
    ];

    return xssPatterns.some(pattern => pattern.test(input));
  }

  // Sanitize user input
  static sanitizeInput(input: string): string {
    if (this.detectXSS(input)) {
      throw new Error('Potential XSS attack detected');
    }
    return InputSanitizer.sanitizeText(input);
  }

  // Validate and sanitize form data
  static sanitizeFormData(data: Record<string, any>): Record<string, any> {
    const sanitized: Record<string, any> = {};
    
    for (const [key, value] of Object.entries(data)) {
      if (typeof value === 'string') {
        sanitized[key] = this.sanitizeInput(value);
      } else if (Array.isArray(value)) {
        sanitized[key] = value.map(item => 
          typeof item === 'string' ? this.sanitizeInput(item) : item
        );
      } else {
        sanitized[key] = value;
      }
    }
    
    return sanitized;
  }
}

// CSRF Protection utilities
export class CSRFProtection {
  private static tokenKey = 'csrf_token';

  // Generate CSRF token (SSR-safe)
  static generateToken(): string {
    if (typeof window === 'undefined') {
      // Server-side: use a simple fallback
      return 'ssr-token-' + Math.floor(Math.random() * 1000000);
    }
    // Client-side: use proper token generation
    const token = Math.random().toString(36).substring(2) + Date.now().toString(36);
    sessionStorage.setItem(this.tokenKey, token);
    return token;
  }

  // Get stored CSRF token
  static getToken(): string | null {
    if (typeof window === 'undefined') return null;
    return sessionStorage.getItem(this.tokenKey);
  }

  // Validate CSRF token
  static validateToken(token: string): boolean {
    const storedToken = this.getToken();
    return storedToken === token;
  }

  // Add CSRF token to request headers
  static addTokenToHeaders(headers: Record<string, string>): Record<string, string> {
    const token = this.getToken();
    if (token) {
      headers['X-CSRF-Token'] = token;
    }
    return headers;
  }
}

// Rate limiting utilities
export class RateLimiter {
  private static attempts = new Map<string, { count: number; timestamp: number }>();

  // Check if action is allowed
  static isAllowed(key: string, maxAttempts: number, windowMs: number): boolean {
    const now = Date.now();
    const attempt = this.attempts.get(key);

    if (!attempt) {
      this.attempts.set(key, { count: 1, timestamp: now });
      return true;
    }

    // Reset if window has passed
    if (now - attempt.timestamp > windowMs) {
      this.attempts.set(key, { count: 1, timestamp: now });
      return true;
    }

    // Check if within limits
    if (attempt.count < maxAttempts) {
      attempt.count++;
      return true;
    }

    return false;
  }

  // Record an attempt
  static recordAttempt(key: string): void {
    const attempt = this.attempts.get(key);
    if (attempt) {
      attempt.count++;
    } else {
      this.attempts.set(key, { count: 1, timestamp: Date.now() });
    }
  }

  // Get remaining attempts
  static getRemainingAttempts(key: string, maxAttempts: number): number {
    const attempt = this.attempts.get(key);
    if (!attempt) return maxAttempts;
    return Math.max(0, maxAttempts - attempt.count);
  }

  // Clear attempts for a key
  static clearAttempts(key: string): void {
    this.attempts.delete(key);
  }

  // Clean up expired entries
  static cleanup(windowMs: number): void {
    const now = Date.now();
    for (const [key, attempt] of this.attempts.entries()) {
      if (now - attempt.timestamp > windowMs) {
        this.attempts.delete(key);
      }
    }
  }
}

// Security headers utility
export class SecurityHeaders {
  // Generate CSP header
  static generateCSP(): string {
    const csp = SECURITY_CONFIG.CSP;
    return Object.entries(csp)
      .map(([key, values]) => `${key} ${values.join(' ')}`)
      .join('; ');
  }

  // Get all security headers
  static getHeaders(): Record<string, string> {
    return {
      ...SECURITY_CONFIG.HEADERS,
      'Content-Security-Policy': this.generateCSP(),
    };
  }
}

// Cookie security utilities
export class SecureCookies {
  // Set secure cookie
  static setCookie(
    name: string,
    value: string,
    options: {
      maxAge?: number;
      httpOnly?: boolean;
      secure?: boolean;
      sameSite?: 'Strict' | 'Lax' | 'None';
      path?: string;
    } = {}
  ): void {
    if (typeof document === 'undefined') return;

    const {
      maxAge = 24 * 60 * 60 * 1000, // 24 hours
      httpOnly = false,
      secure = true,
      sameSite = 'Strict',
      path = '/',
    } = options;

    let cookie = `${name}=${encodeURIComponent(value)}`;
    cookie += `; Max-Age=${maxAge / 1000}`;
    cookie += `; Path=${path}`;
    cookie += `; SameSite=${sameSite}`;
    
    if (secure) {
      cookie += '; Secure';
    }
    
    if (httpOnly) {
      cookie += '; HttpOnly';
    }

    document.cookie = cookie;
  }

  // Get cookie value
  static getCookie(name: string): string | null {
    if (typeof document === 'undefined') return null;
    
    const cookies = document.cookie.split(';');
    for (const cookie of cookies) {
      const [cookieName, cookieValue] = cookie.trim().split('=');
      if (cookieName === name) {
        return decodeURIComponent(cookieValue);
      }
    }
    return null;
  }

  // Delete cookie
  static deleteCookie(name: string, path = '/'): void {
    if (typeof document === 'undefined') return;
    
    document.cookie = `${name}=; Max-Age=0; Path=${path}`;
  }
} 