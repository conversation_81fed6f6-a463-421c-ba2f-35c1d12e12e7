import axios, { AxiosInstance, AxiosRequestConfig, AxiosResponse, AxiosError } from 'axios';

// Create axios instance
const apiClient: AxiosInstance = axios.create({
  baseURL: process.env.NEXT_PUBLIC_API_URL || 'http://localhost:8000/api/v1',
  timeout: 10000,
  headers: {
    'Content-Type': 'application/json',
  },
});

// Request interceptor - Add auth token
apiClient.interceptors.request.use(
  async (config: AxiosRequestConfig) => {
    // Check for different token sources
    let token = localStorage.getItem('access_token');

    // If no access_token, check for mock user (demo mode)
    if (!token) {
      const mockUser = localStorage.getItem('mock_user');
      if (mockUser) {
        // In demo mode, try to get a real token from the backend
        try {
          const user = JSON.parse(mockUser);
          // For demo, we'll create a session-based request or use a demo token
          token = 'demo-token-' + user.id;
        } catch (error) {
          console.warn('Error parsing mock user:', error);
        }
      }
    }

    // Add auth token if available
    if (token && config.headers) {
      config.headers.Authorization = `Bearer ${token}`;
    }

    // For demo mode, also add session-based auth headers
    config.withCredentials = true;

    return config;
  },
  (error) => {
    return Promise.reject(error);
  }
);

// Response interceptor
apiClient.interceptors.response.use(
  (response: AxiosResponse) => {
    return response;
  },
  async (error) => {
    const originalRequest = error.config;

    // Handle 401 errors - In demo mode, try to authenticate with backend
    if (error.response?.status === 401 && !originalRequest._retry) {
      originalRequest._retry = true;
      console.log('401 Unauthorized - attempting demo authentication...');

      // Check if we have a mock user for demo mode
      const mockUser = localStorage.getItem('mock_user');
      if (mockUser) {
        try {
          const user = JSON.parse(mockUser);
          console.log('Demo mode: Attempting to authenticate user:', user.email);

          // Try to login with the backend using demo credentials
          const loginResponse = await fetch(`${process.env.NEXT_PUBLIC_API_URL || 'http://localhost:8000/api/v1'}/auth/login`, {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json',
              'Accept': 'application/json',
            },
            body: JSON.stringify({
              email: user.email,
              password: 'password', // Demo password
            }),
          });

          if (loginResponse.ok) {
            const authData = await loginResponse.json();
            if (authData.success && authData.data.token) {
              // Store the real token
              localStorage.setItem('access_token', authData.data.token);

              // Retry the original request with the new token
              originalRequest.headers.Authorization = `Bearer ${authData.data.token}`;
              console.log('Demo authentication successful, retrying request...');
              return apiClient(originalRequest);
            }
          }
        } catch (authError) {
          console.warn('Demo authentication failed:', authError);
        }
      }

      // If demo auth fails, try with a mock token for development
      console.log('Using fallback mock token for development...');
      originalRequest.headers.Authorization = `Bearer mock-demo-token`;
      return apiClient(originalRequest);
    }

    return Promise.reject(error);
  }
);

// Error handler utility
export const handleApiError = (error: any) => {
  if (error.response) {
    // Server responded with error status
    const message = error.response.data?.message || error.response.statusText || 'An error occurred';
    return new Error(message);
  } else if (error.request) {
    // Request was made but no response received
    return new Error('Network error - please check your connection');
  } else {
    // Something else happened
    return new Error(error.message || 'An unexpected error occurred');
  }
};

export default apiClient;
