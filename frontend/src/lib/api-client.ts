import axios, { AxiosInstance, AxiosRequestConfig, AxiosResponse, AxiosError } from 'axios';

// Create axios instance
const apiClient: AxiosInstance = axios.create({
  baseURL: process.env.NEXT_PUBLIC_API_URL || 'http://localhost:8000/api/v1',
  timeout: 10000,
  headers: {
    'Content-Type': 'application/json',
  },
});

// Request interceptor - Add auth token
apiClient.interceptors.request.use(
  async (config: AxiosRequestConfig) => {
    // Get the real access token
    const token = localStorage.getItem('access_token');

    // Add auth token if available
    if (token && config.headers) {
      config.headers.Authorization = `Bearer ${token}`;
    }

    // Add CSRF protection and session support
    config.withCredentials = true;

    return config;
  },
  (error) => {
    return Promise.reject(error);
  }
);

// Response interceptor
apiClient.interceptors.response.use(
  (response: AxiosResponse) => {
    return response;
  },
  async (error) => {
    const originalRequest = error.config;

    // Handle 401 errors - token expired or invalid
    if (error.response?.status === 401 && !originalRequest._retry) {
      originalRequest._retry = true;
      console.log('🔐 401 Unauthorized - attempting to re-authenticate...');

      // Clear invalid token
      localStorage.removeItem('access_token');

      // Try to re-authenticate with demo credentials
      try {
        const loginResponse = await fetch(`${process.env.NEXT_PUBLIC_API_URL || 'http://localhost:8000/api/v1'}/auth/login`, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
            'Accept': 'application/json',
          },
          body: JSON.stringify({
            email: '<EMAIL>',
            password: 'password',
          }),
        });

        if (loginResponse.ok) {
          const authData = await loginResponse.json();
          if (authData.success && authData.data.token) {
            // Store the new token
            localStorage.setItem('access_token', authData.data.token);
            localStorage.setItem('auth_user', JSON.stringify(authData.data.user));

            // Retry the original request with the new token
            originalRequest.headers.Authorization = `Bearer ${authData.data.token}`;
            console.log('✅ Re-authentication successful, retrying request...');
            return apiClient(originalRequest);
          }
        }
      } catch (authError) {
        console.error('❌ Re-authentication failed:', authError);
      }

      // If re-auth fails, redirect to login or show error
      console.error('❌ Authentication failed - user needs to log in');

      // For now, we'll reject the error to show it in the UI
      return Promise.reject(new Error('Authentication required. Please refresh the page.'));
    }

    return Promise.reject(error);
  }
);

// Error handler utility
export const handleApiError = (error: any) => {
  if (error.response) {
    // Server responded with error status
    const message = error.response.data?.message || error.response.statusText || 'An error occurred';
    return new Error(message);
  } else if (error.request) {
    // Request was made but no response received
    return new Error('Network error - please check your connection');
  } else {
    // Something else happened
    return new Error(error.message || 'An unexpected error occurred');
  }
};

export default apiClient;
