'use client';

import React from 'react';
import {
  Box,
  Card,
  CardContent,
  Typography,
  Grid,
  Skeleton,
  Alert,
  IconButton,
  useTheme,
  alpha,
  List,
  ListItem,
  ListItemText,
  ListItemIcon,
  Divider,
  Chip,
} from '@mui/material';
import {
  Refresh as RefreshIcon,
  TrendingUp as TrendingUpIcon,
  TrendingDown as TrendingDownIcon,
  Home as HomeIcon,
  People as PeopleIcon,
  AccountBalance as AccountBalanceIcon,
  Assignment as AssignmentIcon,
  CheckCircle as CheckCircleIcon,
  Description as DescriptionIcon,
  PersonAdd as PersonAddIcon,
} from '@mui/icons-material';
import {
  LineChart,
  Line,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  ResponsiveContainer,
  Legend,
} from 'recharts';
import { useDashboard } from '@/hooks/useDashboard';
import { formatCurrency, formatDate } from '@/utils/format';
import type { DashboardComponentProps, Activity } from '@/types/dashboard';

interface ExecutiveDashboardProps extends DashboardComponentProps {
  compact?: boolean;
}

const ExecutiveDashboard: React.FC<ExecutiveDashboardProps> = ({
  compact = false,
  onRefresh,
  className,
  style,
}) => {
  const theme = useTheme();
  const { metrics, activities, tasks, alerts, loading, error, refresh } = useDashboard();

  const handleRefresh = async () => {
    await refresh();
    onRefresh?.();
  };

  const futuristicCardSx = {
    background: `linear-gradient(135deg, ${alpha(theme.palette.background.paper, 0.8)} 0%, ${alpha(theme.palette.background.paper, 0.95)} 100%)`,
    backdropFilter: 'blur(20px)',
    border: `1px solid ${alpha(theme.palette.divider, 0.1)}`,
    borderRadius: 3,
    boxShadow: `0 8px 32px ${alpha(theme.palette.common.black, 0.1)}`,
    transition: 'all 0.3s cubic-bezier(0.4, 0, 0.2, 1)',
    '&:hover': {
      transform: 'translateY(-2px)',
      boxShadow: `0 12px 40px ${alpha(theme.palette.common.black, 0.15)}`,
      border: `1px solid ${alpha(theme.palette.primary.main, 0.2)}`,
    }
  };

  const getActivityIcon = (type: string) => {
    switch (type) {
      case 'agreement': return AssignmentIcon;
      case 'tenant': return PeopleIcon;
      case 'payment': return AccountBalanceIcon;
      case 'noc': return CheckCircleIcon;
      case 'enquiry': return PersonAddIcon;
      default: return DescriptionIcon;
    }
  };

  const getActivityColor = (type: string) => {
    switch (type) {
      case 'agreement': return 'warning';
      case 'tenant': return 'info';
      case 'payment': return 'success';
      case 'noc': return 'success';
      case 'enquiry': return 'primary';
      default: return 'default';
    }
  };

  if (error) {
    return (
      <Alert 
        severity="error" 
        action={
          <IconButton size="small" onClick={handleRefresh}>
            <RefreshIcon />
          </IconButton>
        }
        sx={{ borderRadius: 2 }}
      >
        Failed to load executive dashboard: {error}
      </Alert>
    );
  }

  // Mock occupancy trend data
  const occupancyTrends = [
    { month: 'Jan', occupancy: 75, revenue: 180000 },
    { month: 'Feb', occupancy: 80, revenue: 195000 },
    { month: 'Mar', occupancy: 85, revenue: 210000 },
    { month: 'Apr', occupancy: 75, revenue: 185000 },
    { month: 'May', occupancy: 80, revenue: 200000 },
    { month: 'Jun', occupancy: 85, revenue: 215000 },
  ];

  return (
    <Box className={className} style={style}>
      {/* Header */}
      <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between', mb: 3 }}>
        <Box>
          <Typography variant="h5" fontWeight={600} gutterBottom>
            Executive Dashboard
          </Typography>
          <Typography variant="body2" color="text.secondary">
            High-level overview and key performance indicators
          </Typography>
        </Box>
        <IconButton onClick={handleRefresh} disabled={loading}>
          <RefreshIcon />
        </IconButton>
      </Box>

      <Grid container spacing={3}>
        {/* Key Metrics */}
        <Grid item xs={12}>
          <Grid container spacing={2}>
            {loading ? (
              Array.from({ length: 6 }).map((_, index) => (
                <Grid item xs={12} sm={6} md={2} key={index}>
                  <Card sx={futuristicCardSx}>
                    <CardContent sx={{ p: 2 }}>
                      <Skeleton variant="circular" width={40} height={40} sx={{ mb: 2 }} />
                      <Skeleton variant="text" width="60%" height={32} />
                      <Skeleton variant="text" width="40%" />
                    </CardContent>
                  </Card>
                </Grid>
              ))
            ) : (
              metrics.slice(0, 6).map((metric) => (
                <Grid item xs={12} sm={6} md={2} key={metric.id}>
                  <Card 
                    sx={{
                      ...futuristicCardSx,
                      cursor: 'pointer',
                    }}
                    onClick={metric.onClick}
                  >
                    <CardContent sx={{ p: 2, textAlign: 'center' }}>
                      <Box sx={{
                        width: 40,
                        height: 40,
                        borderRadius: '50%',
                        background: alpha(theme.palette[metric.color].main, 0.1),
                        display: 'flex',
                        alignItems: 'center',
                        justifyContent: 'center',
                        mx: 'auto',
                        mb: 2,
                      }}>
                        <metric.icon sx={{ color: `${metric.color}.main`, fontSize: 20 }} />
                      </Box>

                      <Typography variant="h6" fontWeight={700} sx={{ mb: 1 }}>
                        {metric.value}
                      </Typography>

                      <Typography variant="caption" color="text.secondary">
                        {metric.title}
                      </Typography>

                      {metric.subtitle && (
                        <Typography variant="caption" color="text.secondary" sx={{ display: 'block', mt: 0.5 }}>
                          {metric.subtitle}
                        </Typography>
                      )}
                    </CardContent>
                  </Card>
                </Grid>
              ))
            )}
          </Grid>
        </Grid>

        {/* Occupancy & Revenue Trends */}
        <Grid item xs={12} md={8}>
          <Card sx={futuristicCardSx}>
            <CardContent sx={{ p: 3 }}>
              <Typography variant="h6" fontWeight={600} sx={{ mb: 3 }}>
                Occupancy & Revenue Trends
              </Typography>
              
              {loading ? (
                <Skeleton variant="rectangular" height={300} />
              ) : (
                <ResponsiveContainer width="100%" height={300}>
                  <LineChart data={occupancyTrends}>
                    <CartesianGrid strokeDasharray="3 3" stroke={alpha(theme.palette.divider, 0.3)} />
                    <XAxis 
                      dataKey="month" 
                      stroke={theme.palette.text.secondary}
                      fontSize={12}
                    />
                    <YAxis 
                      yAxisId="left"
                      stroke={theme.palette.text.secondary}
                      fontSize={12}
                      tickFormatter={(value) => `${value}%`}
                    />
                    <YAxis 
                      yAxisId="right"
                      orientation="right"
                      stroke={theme.palette.text.secondary}
                      fontSize={12}
                      tickFormatter={(value) => `₹${(value / 1000).toFixed(0)}K`}
                    />
                    <Tooltip 
                      contentStyle={{
                        backgroundColor: theme.palette.background.paper,
                        border: `1px solid ${alpha(theme.palette.divider, 0.2)}`,
                        borderRadius: 8,
                        boxShadow: `0 8px 32px ${alpha(theme.palette.common.black, 0.1)}`,
                      }}
                      formatter={(value: any, name: string) => [
                        name === 'occupancy' ? `${value}%` : formatCurrency(value),
                        name === 'occupancy' ? 'Occupancy' : 'Revenue'
                      ]}
                    />
                    <Legend />
                    <Line
                      yAxisId="left"
                      type="monotone"
                      dataKey="occupancy"
                      stroke={theme.palette.primary.main}
                      strokeWidth={3}
                      dot={{ fill: theme.palette.primary.main, strokeWidth: 2, r: 6 }}
                    />
                    <Line
                      yAxisId="right"
                      type="monotone"
                      dataKey="revenue"
                      stroke={theme.palette.success.main}
                      strokeWidth={3}
                      dot={{ fill: theme.palette.success.main, strokeWidth: 2, r: 6 }}
                    />
                  </LineChart>
                </ResponsiveContainer>
              )}
            </CardContent>
          </Card>
        </Grid>

        {/* Recent Activities */}
        <Grid item xs={12} md={4}>
          <Card sx={futuristicCardSx}>
            <CardContent sx={{ p: 3 }}>
              <Typography variant="h6" fontWeight={600} sx={{ mb: 3 }}>
                Recent Activities
              </Typography>
              
              {loading ? (
                Array.from({ length: 4 }).map((_, index) => (
                  <Box key={index} sx={{ mb: 2 }}>
                    <Skeleton variant="circular" width={32} height={32} sx={{ display: 'inline-block', mr: 2 }} />
                    <Box sx={{ display: 'inline-block', width: 'calc(100% - 48px)' }}>
                      <Skeleton variant="text" width="80%" />
                      <Skeleton variant="text" width="40%" />
                    </Box>
                  </Box>
                ))
              ) : (
                <List sx={{ p: 0 }}>
                  {activities.slice(0, 5).map((activity, index) => {
                    const IconComponent = getActivityIcon(activity.type);
                    const color = getActivityColor(activity.type);
                    
                    return (
                      <React.Fragment key={activity.id}>
                        <ListItem sx={{ px: 0, py: 1 }}>
                          <ListItemIcon sx={{ minWidth: 40 }}>
                            <Box sx={{
                              p: 1,
                              borderRadius: '50%',
                              background: alpha(theme.palette[color as keyof typeof theme.palette].main, 0.1),
                              border: `1px solid ${alpha(theme.palette[color as keyof typeof theme.palette].main, 0.2)}`,
                            }}>
                              <IconComponent
                                sx={{
                                  color: `${color}.main`,
                                  fontSize: 16
                                }}
                              />
                            </Box>
                          </ListItemIcon>
                          <ListItemText
                            primary={
                              <Typography variant="body2" fontWeight={500} sx={{ mb: 0.5 }}>
                                {activity.title}
                              </Typography>
                            }
                            secondary={
                              <Typography variant="caption" color="text.secondary">
                                {formatDate(activity.timestamp)}
                              </Typography>
                            }
                          />
                        </ListItem>
                        {index < activities.slice(0, 5).length - 1 && (
                          <Divider sx={{ mx: 2, borderColor: alpha(theme.palette.divider, 0.3) }} />
                        )}
                      </React.Fragment>
                    );
                  })}
                </List>
              )}
            </CardContent>
          </Card>
        </Grid>

        {/* Alerts & Tasks */}
        <Grid item xs={12}>
          <Grid container spacing={3}>
            {/* Alerts */}
            <Grid item xs={12} md={6}>
              <Card sx={futuristicCardSx}>
                <CardContent sx={{ p: 3 }}>
                  <Typography variant="h6" fontWeight={600} sx={{ mb: 3 }}>
                    Active Alerts
                  </Typography>
                  
                  {loading ? (
                    Array.from({ length: 2 }).map((_, index) => (
                      <Skeleton key={index} variant="rectangular" height={60} sx={{ mb: 2, borderRadius: 2 }} />
                    ))
                  ) : (
                    alerts.slice(0, 3).map((alert) => (
                      <Alert
                        key={alert.id}
                        severity={alert.type}
                        sx={{
                          mb: 2,
                          borderRadius: 2,
                          border: `1px solid ${alpha(theme.palette[alert.type].main, 0.2)}`,
                          background: alpha(theme.palette[alert.type].main, 0.1),
                        }}
                        action={
                          alert.actionable && alert.action ? (
                            <Chip
                              label={alert.action.label}
                              size="small"
                              onClick={alert.action.onClick}
                              sx={{ cursor: 'pointer' }}
                            />
                          ) : null
                        }
                      >
                        <Typography variant="body2" fontWeight={500}>
                          {alert.message}
                        </Typography>
                      </Alert>
                    ))
                  )}
                </CardContent>
              </Card>
            </Grid>

            {/* Upcoming Tasks */}
            <Grid item xs={12} md={6}>
              <Card sx={futuristicCardSx}>
                <CardContent sx={{ p: 3 }}>
                  <Typography variant="h6" fontWeight={600} sx={{ mb: 3 }}>
                    Upcoming Tasks
                  </Typography>
                  
                  {loading ? (
                    Array.from({ length: 3 }).map((_, index) => (
                      <Box key={index} sx={{ mb: 2 }}>
                        <Skeleton variant="text" width="80%" />
                        <Skeleton variant="text" width="40%" />
                      </Box>
                    ))
                  ) : (
                    <List sx={{ p: 0 }}>
                      {tasks.slice(0, 3).map((task, index) => (
                        <React.Fragment key={task.id}>
                          <ListItem sx={{ px: 0, py: 1 }}>
                            <ListItemText
                              primary={
                                <Typography variant="body2" fontWeight={500} sx={{ mb: 1 }}>
                                  {task.title}
                                </Typography>
                              }
                              secondary={
                                <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                                  <Chip
                                    label={task.priority.toUpperCase()}
                                    size="small"
                                    sx={{
                                      height: 20,
                                      fontSize: '0.7rem',
                                      fontWeight: 600,
                                      background: task.priority === 'high' ? alpha(theme.palette.error.main, 0.1) :
                                                 task.priority === 'medium' ? alpha(theme.palette.warning.main, 0.1) :
                                                 alpha(theme.palette.action.hover, 0.5),
                                      color: task.priority === 'high' ? 'error.main' :
                                             task.priority === 'medium' ? 'warning.main' : 'text.secondary',
                                    }}
                                  />
                                  <Typography variant="caption" color="text.secondary">
                                    Due {formatDate(task.dueDate)}
                                  </Typography>
                                </Box>
                              }
                            />
                          </ListItem>
                          {index < tasks.slice(0, 3).length - 1 && (
                            <Divider sx={{ mx: 2, borderColor: alpha(theme.palette.divider, 0.3) }} />
                          )}
                        </React.Fragment>
                      ))}
                    </List>
                  )}
                </CardContent>
              </Card>
            </Grid>
          </Grid>
        </Grid>
      </Grid>
    </Box>
  );
};

export default ExecutiveDashboard;
