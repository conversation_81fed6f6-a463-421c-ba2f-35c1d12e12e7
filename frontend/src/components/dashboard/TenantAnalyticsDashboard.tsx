'use client';

import React from 'react';
import {
  <PERSON>,
  Card,
  CardContent,
  Typography,
  Grid,
  Skeleton,
  Alert,
  IconButton,
  useTheme,
  alpha,
  Rating,
  Chip,
} from '@mui/material';
import {
  Refresh as RefreshIcon,
  People as PeopleIcon,
  PersonAdd as PersonAddIcon,
  Star as StarIcon,
  Autorenew as RenewIcon,
} from '@mui/icons-material';
import {
  PieChart,
  Pie,
  Cell,
  BarChart,
  Bar,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  ResponsiveContainer,
  Legend,
} from 'recharts';
import { useTenantAnalytics } from '@/hooks/useDashboard';
import { formatNumber } from '@/utils/format';
import type { DashboardComponentProps } from '@/types/dashboard';

interface TenantAnalyticsDashboardProps extends DashboardComponentProps {
  compact?: boolean;
}

const TenantAnalyticsDashboard: React.FC<TenantAnalyticsDashboardProps> = ({
  compact = false,
  onRefresh,
  className,
  style,
}) => {
  const theme = useTheme();
  const { analytics, loading, error, refresh } = useTenantAnalytics();

  const handleRefresh = async () => {
    await refresh();
    onRefresh?.();
  };

  const futuristicCardSx = {
    background: `linear-gradient(135deg, ${alpha(theme.palette.background.paper, 0.8)} 0%, ${alpha(theme.palette.background.paper, 0.95)} 100%)`,
    backdropFilter: 'blur(20px)',
    border: `1px solid ${alpha(theme.palette.divider, 0.1)}`,
    borderRadius: 3,
    boxShadow: `0 8px 32px ${alpha(theme.palette.common.black, 0.1)}`,
    transition: 'all 0.3s cubic-bezier(0.4, 0, 0.2, 1)',
    '&:hover': {
      transform: 'translateY(-2px)',
      boxShadow: `0 12px 40px ${alpha(theme.palette.common.black, 0.15)}`,
      border: `1px solid ${alpha(theme.palette.primary.main, 0.2)}`,
    }
  };

  const COLORS = [
    theme.palette.success.main,
    theme.palette.warning.main,
    theme.palette.error.main,
  ];

  if (error) {
    return (
      <Alert 
        severity="error" 
        action={
          <IconButton size="small" onClick={handleRefresh}>
            <RefreshIcon />
          </IconButton>
        }
        sx={{ borderRadius: 2 }}
      >
        Failed to load tenant analytics: {error}
      </Alert>
    );
  }

  const tenantMetrics = analytics ? [
    {
      id: 'total-tenants',
      title: 'Total Tenants',
      value: analytics.totalTenants,
      icon: PeopleIcon,
      color: theme.palette.primary.main,
    },
    {
      id: 'active-tenants',
      title: 'Active Tenants',
      value: analytics.activeTenants,
      icon: PeopleIcon,
      color: theme.palette.success.main,
    },
    {
      id: 'new-tenants',
      title: 'New This Month',
      value: analytics.newTenants,
      icon: PersonAddIcon,
      color: theme.palette.info.main,
    },
    {
      id: 'renewal-rate',
      title: 'Renewal Rate',
      value: `${analytics.renewalRate}%`,
      icon: RenewIcon,
      color: theme.palette.warning.main,
    },
  ] : [];

  return (
    <Box className={className} style={style}>
      {/* Header */}
      <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between', mb: 3 }}>
        <Box>
          <Typography variant="h5" fontWeight={600} gutterBottom>
            Tenant Analytics
          </Typography>
          <Typography variant="body2" color="text.secondary">
            Tenant satisfaction and demographic insights
          </Typography>
        </Box>
        <IconButton onClick={handleRefresh} disabled={loading}>
          <RefreshIcon />
        </IconButton>
      </Box>

      <Grid container spacing={3}>
        {/* Tenant Metrics Cards */}
        <Grid item xs={12}>
          <Grid container spacing={2}>
            {loading ? (
              Array.from({ length: 4 }).map((_, index) => (
                <Grid item xs={12} sm={6} md={3} key={index}>
                  <Card sx={futuristicCardSx}>
                    <CardContent sx={{ p: 2 }}>
                      <Skeleton variant="circular" width={40} height={40} sx={{ mb: 2 }} />
                      <Skeleton variant="text" width="60%" height={32} />
                      <Skeleton variant="text" width="40%" />
                    </CardContent>
                  </Card>
                </Grid>
              ))
            ) : (
              tenantMetrics.map((metric) => (
                <Grid item xs={12} sm={6} md={3} key={metric.id}>
                  <Card sx={futuristicCardSx}>
                    <CardContent sx={{ p: 2, textAlign: 'center' }}>
                      <Box sx={{
                        width: 48,
                        height: 48,
                        borderRadius: '50%',
                        background: alpha(metric.color, 0.1),
                        display: 'flex',
                        alignItems: 'center',
                        justifyContent: 'center',
                        mx: 'auto',
                        mb: 2,
                      }}>
                        <metric.icon sx={{ color: metric.color, fontSize: 24 }} />
                      </Box>

                      <Typography variant="h6" fontWeight={700} sx={{ mb: 1 }}>
                        {metric.value}
                      </Typography>

                      <Typography variant="body2" color="text.secondary">
                        {metric.title}
                      </Typography>
                    </CardContent>
                  </Card>
                </Grid>
              ))
            )}
          </Grid>
        </Grid>

        {/* Tenant Satisfaction */}
        <Grid item xs={12} md={6}>
          <Card sx={futuristicCardSx}>
            <CardContent sx={{ p: 3 }}>
              <Typography variant="h6" fontWeight={600} sx={{ mb: 3 }}>
                Tenant Satisfaction
              </Typography>
              
              {loading ? (
                <Box sx={{ textAlign: 'center' }}>
                  <Skeleton variant="text" width="60%" height={40} sx={{ mx: 'auto', mb: 2 }} />
                  <Skeleton variant="rectangular" width={200} height={30} sx={{ mx: 'auto', mb: 2 }} />
                  <Skeleton variant="text" width="40%" height={24} sx={{ mx: 'auto' }} />
                </Box>
              ) : (
                <Box sx={{ textAlign: 'center' }}>
                  <Typography variant="h3" fontWeight={700} color="primary.main" sx={{ mb: 2 }}>
                    {analytics?.satisfactionScore?.toFixed(1) || '0.0'}
                  </Typography>
                  
                  <Rating
                    value={analytics?.satisfactionScore || 0}
                    precision={0.1}
                    readOnly
                    size="large"
                    sx={{ mb: 2 }}
                  />
                  
                  <Typography variant="body2" color="text.secondary">
                    Average satisfaction rating
                  </Typography>
                  
                  <Box sx={{ mt: 3, display: 'flex', justifyContent: 'center', gap: 1 }}>
                    <Chip
                      icon={<StarIcon />}
                      label="Excellent Service"
                      size="small"
                      color="success"
                      variant="outlined"
                    />
                  </Box>
                </Box>
              )}
            </CardContent>
          </Card>
        </Grid>

        {/* Payment Behavior */}
        <Grid item xs={12} md={6}>
          <Card sx={futuristicCardSx}>
            <CardContent sx={{ p: 3 }}>
              <Typography variant="h6" fontWeight={600} sx={{ mb: 3 }}>
                Payment Behavior
              </Typography>
              
              {loading ? (
                <Skeleton variant="circular" width={200} height={200} sx={{ mx: 'auto' }} />
              ) : (
                <ResponsiveContainer width="100%" height={250}>
                  <PieChart>
                    <Pie
                      data={analytics?.paymentBehavior || []}
                      cx="50%"
                      cy="50%"
                      labelLine={false}
                      label={({ category, percentage }) => `${category} ${percentage}%`}
                      outerRadius={80}
                      fill="#8884d8"
                      dataKey="count"
                    >
                      {(analytics?.paymentBehavior || []).map((entry, index) => (
                        <Cell key={`cell-${index}`} fill={COLORS[index % COLORS.length]} />
                      ))}
                    </Pie>
                    <Tooltip 
                      contentStyle={{
                        backgroundColor: theme.palette.background.paper,
                        border: `1px solid ${alpha(theme.palette.divider, 0.2)}`,
                        borderRadius: 8,
                        boxShadow: `0 8px 32px ${alpha(theme.palette.common.black, 0.1)}`,
                      }}
                      formatter={(value: any, name: string) => [value, 'Tenants']}
                    />
                  </PieChart>
                </ResponsiveContainer>
              )}
            </CardContent>
          </Card>
        </Grid>

        {/* Tenant Demographics */}
        <Grid item xs={12}>
          <Card sx={futuristicCardSx}>
            <CardContent sx={{ p: 3 }}>
              <Typography variant="h6" fontWeight={600} sx={{ mb: 3 }}>
                Tenant Demographics
              </Typography>
              
              {loading ? (
                <Skeleton variant="rectangular" height={200} />
              ) : (
                <Grid container spacing={3}>
                  <Grid item xs={12} sm={6} md={3}>
                    <Box sx={{ textAlign: 'center' }}>
                      <Typography variant="h4" fontWeight={700} color="primary.main">
                        {analytics?.totalTenants || 0}
                      </Typography>
                      <Typography variant="body2" color="text.secondary">
                        Total Tenants
                      </Typography>
                    </Box>
                  </Grid>
                  
                  <Grid item xs={12} sm={6} md={3}>
                    <Box sx={{ textAlign: 'center' }}>
                      <Typography variant="h4" fontWeight={700} color="success.main">
                        {analytics?.activeTenants || 0}
                      </Typography>
                      <Typography variant="body2" color="text.secondary">
                        Active Tenants
                      </Typography>
                    </Box>
                  </Grid>
                  
                  <Grid item xs={12} sm={6} md={3}>
                    <Box sx={{ textAlign: 'center' }}>
                      <Typography variant="h4" fontWeight={700} color="info.main">
                        {analytics?.newTenants || 0}
                      </Typography>
                      <Typography variant="body2" color="text.secondary">
                        New This Month
                      </Typography>
                    </Box>
                  </Grid>
                  
                  <Grid item xs={12} sm={6} md={3}>
                    <Box sx={{ textAlign: 'center' }}>
                      <Typography variant="h4" fontWeight={700} color="warning.main">
                        {analytics?.renewalRate || 0}%
                      </Typography>
                      <Typography variant="body2" color="text.secondary">
                        Renewal Rate
                      </Typography>
                    </Box>
                  </Grid>
                </Grid>
              )}
            </CardContent>
          </Card>
        </Grid>
      </Grid>
    </Box>
  );
};

export default TenantAnalyticsDashboard;
