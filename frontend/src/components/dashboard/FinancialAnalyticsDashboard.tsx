'use client';

import React from 'react';
import {
  Box,
  Card,
  CardContent,
  Typography,
  Grid,
  Skeleton,
  Alert,
  IconButton,
  useTheme,
  alpha,
} from '@mui/material';
import {
  Refresh as RefreshIcon,
  TrendingUp as TrendingUpIcon,
  TrendingDown as TrendingDownIcon,
  AccountBalance as AccountBalanceIcon,
} from '@mui/icons-material';
import {
  LineChart,
  Line,
  BarChart,
  Bar,
  PieChart,
  Pie,
  Cell,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  ResponsiveContainer,
  Legend,
} from 'recharts';
import { useFinancialAnalytics } from '@/hooks/useDashboard';
import { formatCurrency, formatNumber } from '@/utils/format';
import type { DashboardComponentProps } from '@/types/dashboard';

interface FinancialAnalyticsDashboardProps extends DashboardComponentProps {
  compact?: boolean;
}

const FinancialAnalyticsDashboard: React.FC<FinancialAnalyticsDashboardProps> = ({
  compact = false,
  onRefresh,
  className,
  style,
}) => {
  const theme = useTheme();
  const { metrics, revenueData, paymentMethods, outstandingDues, loading, error, refresh } = useFinancialAnalytics();

  const handleRefresh = async () => {
    await refresh();
    onRefresh?.();
  };

  const futuristicCardSx = {
    background: `linear-gradient(135deg, ${alpha(theme.palette.background.paper, 0.8)} 0%, ${alpha(theme.palette.background.paper, 0.95)} 100%)`,
    backdropFilter: 'blur(20px)',
    border: `1px solid ${alpha(theme.palette.divider, 0.1)}`,
    borderRadius: 3,
    boxShadow: `0 8px 32px ${alpha(theme.palette.common.black, 0.1)}`,
    transition: 'all 0.3s cubic-bezier(0.4, 0, 0.2, 1)',
    '&:hover': {
      transform: 'translateY(-2px)',
      boxShadow: `0 12px 40px ${alpha(theme.palette.common.black, 0.15)}`,
      border: `1px solid ${alpha(theme.palette.primary.main, 0.2)}`,
    }
  };

  const COLORS = [
    theme.palette.primary.main,
    theme.palette.secondary.main,
    theme.palette.success.main,
    theme.palette.warning.main,
    theme.palette.error.main,
    theme.palette.info.main,
  ];

  if (error) {
    return (
      <Alert 
        severity="error" 
        action={
          <IconButton size="small" onClick={handleRefresh}>
            <RefreshIcon />
          </IconButton>
        }
        sx={{ borderRadius: 2 }}
      >
        Failed to load financial analytics: {error}
      </Alert>
    );
  }

  return (
    <Box className={className} style={style}>
      {/* Header */}
      <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between', mb: 3 }}>
        <Box>
          <Typography variant="h5" fontWeight={600} gutterBottom>
            Financial Analytics
          </Typography>
          <Typography variant="body2" color="text.secondary">
            Revenue, expenses, and financial performance metrics
          </Typography>
        </Box>
        <IconButton onClick={handleRefresh} disabled={loading}>
          <RefreshIcon />
        </IconButton>
      </Box>

      <Grid container spacing={3}>
        {/* Financial Metrics Cards */}
        <Grid item xs={12}>
          <Grid container spacing={2}>
            {loading ? (
              Array.from({ length: 4 }).map((_, index) => (
                <Grid item xs={12} sm={6} md={3} key={index}>
                  <Card sx={futuristicCardSx}>
                    <CardContent sx={{ p: 2 }}>
                      <Skeleton variant="circular" width={40} height={40} sx={{ mb: 2 }} />
                      <Skeleton variant="text" width="60%" height={32} />
                      <Skeleton variant="text" width="40%" />
                    </CardContent>
                  </Card>
                </Grid>
              ))
            ) : (
              metrics.map((metric) => (
                <Grid item xs={12} sm={6} md={3} key={metric.id}>
                  <Card sx={futuristicCardSx}>
                    <CardContent sx={{ p: 2, textAlign: 'center' }}>
                      <Box sx={{
                        width: 48,
                        height: 48,
                        borderRadius: '50%',
                        background: alpha(theme.palette.primary.main, 0.1),
                        display: 'flex',
                        alignItems: 'center',
                        justifyContent: 'center',
                        mx: 'auto',
                        mb: 2,
                      }}>
                        <AccountBalanceIcon sx={{ color: 'primary.main', fontSize: 24 }} />
                      </Box>

                      <Typography variant="h6" fontWeight={700} sx={{ mb: 1 }}>
                        {metric.currency === 'INR' ? formatCurrency(metric.value) : 
                         metric.currency === '%' ? `${formatNumber(metric.value)}%` : 
                         metric.currency === 'days' ? `${formatNumber(metric.value)} days` : 
                         formatNumber(metric.value)}
                      </Typography>

                      <Typography variant="body2" color="text.secondary" sx={{ mb: 1 }}>
                        {metric.name}
                      </Typography>

                      {metric.change !== undefined && (
                        <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'center' }}>
                          {metric.trend === 'up' ? (
                            <TrendingUpIcon sx={{ color: 'success.main', fontSize: 16, mr: 0.5 }} />
                          ) : (
                            <TrendingDownIcon sx={{ color: 'error.main', fontSize: 16, mr: 0.5 }} />
                          )}
                          <Typography 
                            variant="caption" 
                            color={metric.trend === 'up' ? 'success.main' : 'error.main'}
                            fontWeight={600}
                          >
                            {Math.abs(metric.change)}%
                          </Typography>
                        </Box>
                      )}
                    </CardContent>
                  </Card>
                </Grid>
              ))
            )}
          </Grid>
        </Grid>

        {/* Revenue Trend Chart */}
        <Grid item xs={12} md={8}>
          <Card sx={futuristicCardSx}>
            <CardContent sx={{ p: 3 }}>
              <Typography variant="h6" fontWeight={600} sx={{ mb: 3 }}>
                Revenue Trend
              </Typography>
              
              {loading ? (
                <Skeleton variant="rectangular" height={300} />
              ) : (
                <ResponsiveContainer width="100%" height={300}>
                  <LineChart data={revenueData}>
                    <CartesianGrid strokeDasharray="3 3" stroke={alpha(theme.palette.divider, 0.3)} />
                    <XAxis 
                      dataKey="month" 
                      stroke={theme.palette.text.secondary}
                      fontSize={12}
                    />
                    <YAxis 
                      stroke={theme.palette.text.secondary}
                      fontSize={12}
                      tickFormatter={(value) => `₹${(value / 1000).toFixed(0)}K`}
                    />
                    <Tooltip 
                      contentStyle={{
                        backgroundColor: theme.palette.background.paper,
                        border: `1px solid ${alpha(theme.palette.divider, 0.2)}`,
                        borderRadius: 8,
                        boxShadow: `0 8px 32px ${alpha(theme.palette.common.black, 0.1)}`,
                      }}
                      formatter={(value: any) => [formatCurrency(value), 'Revenue']}
                    />
                    <Legend />
                    <Line
                      type="monotone"
                      dataKey="revenue"
                      stroke={theme.palette.primary.main}
                      strokeWidth={3}
                      dot={{ fill: theme.palette.primary.main, strokeWidth: 2, r: 6 }}
                      activeDot={{ r: 8, stroke: theme.palette.primary.main, strokeWidth: 2 }}
                    />
                  </LineChart>
                </ResponsiveContainer>
              )}
            </CardContent>
          </Card>
        </Grid>

        {/* Payment Methods Distribution */}
        <Grid item xs={12} md={4}>
          <Card sx={futuristicCardSx}>
            <CardContent sx={{ p: 3 }}>
              <Typography variant="h6" fontWeight={600} sx={{ mb: 3 }}>
                Payment Methods
              </Typography>
              
              {loading ? (
                <Skeleton variant="circular" width={200} height={200} sx={{ mx: 'auto' }} />
              ) : (
                <ResponsiveContainer width="100%" height={250}>
                  <PieChart>
                    <Pie
                      data={paymentMethods}
                      cx="50%"
                      cy="50%"
                      labelLine={false}
                      label={({ method, percentage }) => `${method} ${percentage}%`}
                      outerRadius={80}
                      fill="#8884d8"
                      dataKey="amount"
                    >
                      {paymentMethods.map((entry, index) => (
                        <Cell key={`cell-${index}`} fill={COLORS[index % COLORS.length]} />
                      ))}
                    </Pie>
                    <Tooltip 
                      contentStyle={{
                        backgroundColor: theme.palette.background.paper,
                        border: `1px solid ${alpha(theme.palette.divider, 0.2)}`,
                        borderRadius: 8,
                        boxShadow: `0 8px 32px ${alpha(theme.palette.common.black, 0.1)}`,
                      }}
                      formatter={(value: any) => [formatCurrency(value), 'Amount']}
                    />
                  </PieChart>
                </ResponsiveContainer>
              )}
            </CardContent>
          </Card>
        </Grid>

        {/* Outstanding Dues */}
        <Grid item xs={12}>
          <Card sx={futuristicCardSx}>
            <CardContent sx={{ p: 3 }}>
              <Typography variant="h6" fontWeight={600} sx={{ mb: 3 }}>
                Outstanding Dues by Age
              </Typography>
              
              {loading ? (
                <Skeleton variant="rectangular" height={250} />
              ) : (
                <ResponsiveContainer width="100%" height={250}>
                  <BarChart data={outstandingDues}>
                    <CartesianGrid strokeDasharray="3 3" stroke={alpha(theme.palette.divider, 0.3)} />
                    <XAxis 
                      dataKey="range" 
                      stroke={theme.palette.text.secondary}
                      fontSize={12}
                    />
                    <YAxis 
                      stroke={theme.palette.text.secondary}
                      fontSize={12}
                      tickFormatter={(value) => `₹${(value / 1000).toFixed(0)}K`}
                    />
                    <Tooltip 
                      contentStyle={{
                        backgroundColor: theme.palette.background.paper,
                        border: `1px solid ${alpha(theme.palette.divider, 0.2)}`,
                        borderRadius: 8,
                        boxShadow: `0 8px 32px ${alpha(theme.palette.common.black, 0.1)}`,
                      }}
                      formatter={(value: any) => [formatCurrency(value), 'Amount']}
                    />
                    <Bar 
                      dataKey="amount" 
                      fill={theme.palette.warning.main}
                      radius={[4, 4, 0, 0]}
                    />
                  </BarChart>
                </ResponsiveContainer>
              )}
            </CardContent>
          </Card>
        </Grid>
      </Grid>
    </Box>
  );
};

export default FinancialAnalyticsDashboard;
