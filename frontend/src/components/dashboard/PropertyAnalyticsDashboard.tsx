'use client';

import React from 'react';
import {
  Box,
  Card,
  CardContent,
  Typography,
  Grid,
  Skeleton,
  Alert,
  IconButton,
  useTheme,
  alpha,
  LinearProgress,
} from '@mui/material';
import {
  Refresh as RefreshIcon,
  Home as HomeIcon,
  TrendingUp as TrendingUpIcon,
  Build as BuildIcon,
  AttachMoney as MoneyIcon,
} from '@mui/icons-material';
import {
  LineChart,
  Line,
  BarChart,
  Bar,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  ResponsiveContainer,
  Legend,
} from 'recharts';
import { usePropertyAnalytics } from '@/hooks/useDashboard';
import { formatCurrency, formatNumber } from '@/utils/format';
import type { DashboardComponentProps } from '@/types/dashboard';

interface PropertyAnalyticsDashboardProps extends DashboardComponentProps {
  compact?: boolean;
}

const PropertyAnalyticsDashboard: React.FC<PropertyAnalyticsDashboardProps> = ({
  compact = false,
  onRefresh,
  className,
  style,
}) => {
  const theme = useTheme();
  const { analytics, loading, error, refresh } = usePropertyAnalytics();

  const handleRefresh = async () => {
    await refresh();
    onRefresh?.();
  };

  const futuristicCardSx = {
    background: `linear-gradient(135deg, ${alpha(theme.palette.background.paper, 0.8)} 0%, ${alpha(theme.palette.background.paper, 0.95)} 100%)`,
    backdropFilter: 'blur(20px)',
    border: `1px solid ${alpha(theme.palette.divider, 0.1)}`,
    borderRadius: 3,
    boxShadow: `0 8px 32px ${alpha(theme.palette.common.black, 0.1)}`,
    transition: 'all 0.3s cubic-bezier(0.4, 0, 0.2, 1)',
    '&:hover': {
      transform: 'translateY(-2px)',
      boxShadow: `0 12px 40px ${alpha(theme.palette.common.black, 0.15)}`,
      border: `1px solid ${alpha(theme.palette.primary.main, 0.2)}`,
    }
  };

  if (error) {
    return (
      <Alert 
        severity="error" 
        action={
          <IconButton size="small" onClick={handleRefresh}>
            <RefreshIcon />
          </IconButton>
        }
        sx={{ borderRadius: 2 }}
      >
        Failed to load property analytics: {error}
      </Alert>
    );
  }

  const propertyMetrics = analytics ? [
    {
      id: 'total-properties',
      title: 'Total Properties',
      value: analytics.totalProperties,
      icon: HomeIcon,
      color: theme.palette.primary.main,
    },
    {
      id: 'occupancy-rate',
      title: 'Occupancy Rate',
      value: `${analytics.occupancyRate}%`,
      icon: TrendingUpIcon,
      color: theme.palette.success.main,
    },
    {
      id: 'average-rent',
      title: 'Average Rent',
      value: formatCurrency(analytics.averageRent),
      icon: MoneyIcon,
      color: theme.palette.info.main,
    },
    {
      id: 'maintenance-requests',
      title: 'Maintenance Requests',
      value: analytics.maintenanceRequests,
      icon: BuildIcon,
      color: theme.palette.warning.main,
    },
  ] : [];

  return (
    <Box className={className} style={style}>
      {/* Header */}
      <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between', mb: 3 }}>
        <Box>
          <Typography variant="h5" fontWeight={600} gutterBottom>
            Property Analytics
          </Typography>
          <Typography variant="body2" color="text.secondary">
            Property performance and occupancy insights
          </Typography>
        </Box>
        <IconButton onClick={handleRefresh} disabled={loading}>
          <RefreshIcon />
        </IconButton>
      </Box>

      <Grid container spacing={3}>
        {/* Property Metrics Cards */}
        <Grid item xs={12}>
          <Grid container spacing={2}>
            {loading ? (
              Array.from({ length: 4 }).map((_, index) => (
                <Grid item xs={12} sm={6} md={3} key={index}>
                  <Card sx={futuristicCardSx}>
                    <CardContent sx={{ p: 2 }}>
                      <Skeleton variant="circular" width={40} height={40} sx={{ mb: 2 }} />
                      <Skeleton variant="text" width="60%" height={32} />
                      <Skeleton variant="text" width="40%" />
                    </CardContent>
                  </Card>
                </Grid>
              ))
            ) : (
              propertyMetrics.map((metric) => (
                <Grid item xs={12} sm={6} md={3} key={metric.id}>
                  <Card sx={futuristicCardSx}>
                    <CardContent sx={{ p: 2, textAlign: 'center' }}>
                      <Box sx={{
                        width: 48,
                        height: 48,
                        borderRadius: '50%',
                        background: alpha(metric.color, 0.1),
                        display: 'flex',
                        alignItems: 'center',
                        justifyContent: 'center',
                        mx: 'auto',
                        mb: 2,
                      }}>
                        <metric.icon sx={{ color: metric.color, fontSize: 24 }} />
                      </Box>

                      <Typography variant="h6" fontWeight={700} sx={{ mb: 1 }}>
                        {metric.value}
                      </Typography>

                      <Typography variant="body2" color="text.secondary">
                        {metric.title}
                      </Typography>
                    </CardContent>
                  </Card>
                </Grid>
              ))
            )}
          </Grid>
        </Grid>

        {/* Vacancy Trends Chart */}
        <Grid item xs={12} md={8}>
          <Card sx={futuristicCardSx}>
            <CardContent sx={{ p: 3 }}>
              <Typography variant="h6" fontWeight={600} sx={{ mb: 3 }}>
                Vacancy Trends
              </Typography>
              
              {loading ? (
                <Skeleton variant="rectangular" height={300} />
              ) : (
                <ResponsiveContainer width="100%" height={300}>
                  <LineChart data={analytics?.vacancyTrends || []}>
                    <CartesianGrid strokeDasharray="3 3" stroke={alpha(theme.palette.divider, 0.3)} />
                    <XAxis 
                      dataKey="month" 
                      stroke={theme.palette.text.secondary}
                      fontSize={12}
                    />
                    <YAxis 
                      stroke={theme.palette.text.secondary}
                      fontSize={12}
                      tickFormatter={(value) => `${value}%`}
                    />
                    <Tooltip 
                      contentStyle={{
                        backgroundColor: theme.palette.background.paper,
                        border: `1px solid ${alpha(theme.palette.divider, 0.2)}`,
                        borderRadius: 8,
                        boxShadow: `0 8px 32px ${alpha(theme.palette.common.black, 0.1)}`,
                      }}
                      formatter={(value: any) => [`${value}%`, 'Vacancy Rate']}
                    />
                    <Legend />
                    <Line
                      type="monotone"
                      dataKey="vacancyRate"
                      stroke={theme.palette.warning.main}
                      strokeWidth={3}
                      dot={{ fill: theme.palette.warning.main, strokeWidth: 2, r: 6 }}
                      activeDot={{ r: 8, stroke: theme.palette.warning.main, strokeWidth: 2 }}
                    />
                  </LineChart>
                </ResponsiveContainer>
              )}
            </CardContent>
          </Card>
        </Grid>

        {/* Property Value & Occupancy */}
        <Grid item xs={12} md={4}>
          <Card sx={futuristicCardSx}>
            <CardContent sx={{ p: 3 }}>
              <Typography variant="h6" fontWeight={600} sx={{ mb: 3 }}>
                Property Overview
              </Typography>
              
              {loading ? (
                <Box>
                  <Skeleton variant="text" width="80%" height={24} sx={{ mb: 2 }} />
                  <Skeleton variant="rectangular" width="100%" height={8} sx={{ mb: 3 }} />
                  <Skeleton variant="text" width="80%" height={24} sx={{ mb: 2 }} />
                  <Skeleton variant="rectangular" width="100%" height={8} />
                </Box>
              ) : (
                <Box>
                  <Box sx={{ mb: 3 }}>
                    <Typography variant="body2" color="text.secondary" gutterBottom>
                      Total Property Value
                    </Typography>
                    <Typography variant="h5" fontWeight={700} color="primary.main">
                      {formatCurrency(analytics?.propertyValue || 0)}
                    </Typography>
                  </Box>

                  <Box sx={{ mb: 3 }}>
                    <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 1 }}>
                      <Typography variant="body2" color="text.secondary">
                        Occupancy Rate
                      </Typography>
                      <Typography variant="body2" fontWeight={600}>
                        {analytics?.occupancyRate || 0}%
                      </Typography>
                    </Box>
                    <LinearProgress
                      variant="determinate"
                      value={analytics?.occupancyRate || 0}
                      sx={{
                        height: 8,
                        borderRadius: 4,
                        backgroundColor: alpha(theme.palette.success.main, 0.2),
                        '& .MuiLinearProgress-bar': {
                          backgroundColor: theme.palette.success.main,
                          borderRadius: 4,
                        },
                      }}
                    />
                  </Box>

                  <Box>
                    <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 1 }}>
                      <Typography variant="body2" color="text.secondary">
                        Maintenance Load
                      </Typography>
                      <Typography variant="body2" fontWeight={600}>
                        {analytics?.maintenanceRequests || 0} requests
                      </Typography>
                    </Box>
                    <LinearProgress
                      variant="determinate"
                      value={((analytics?.maintenanceRequests || 0) / 10) * 100}
                      sx={{
                        height: 8,
                        borderRadius: 4,
                        backgroundColor: alpha(theme.palette.warning.main, 0.2),
                        '& .MuiLinearProgress-bar': {
                          backgroundColor: theme.palette.warning.main,
                          borderRadius: 4,
                        },
                      }}
                    />
                  </Box>
                </Box>
              )}
            </CardContent>
          </Card>
        </Grid>
      </Grid>
    </Box>
  );
};

export default PropertyAnalyticsDashboard;
