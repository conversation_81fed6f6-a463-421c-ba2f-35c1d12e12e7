'use client';

import React, { useState, useEffect } from 'react';
import {
  Box,
  Paper,
  Typography,
  Grid,
  Card,
  CardContent,
  IconButton,
  Chip,
  Alert,
  Button,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  TextField,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Tooltip,
  LinearProgress,
  Rating,
  Accordion,
  AccordionSummary,
  AccordionDetails,
  List,
  ListItem,
  ListItemText,
  ListItemIcon,
  Divider,
  Switch,
  FormControlLabel,
  Checkbox,
  Radio,
  RadioGroup,
  Slider,
  Tabs,
  Tab,
} from '@mui/material';
import {
  TrendingUp as TrendingUpIcon,
  TrendingDown as TrendingDownIcon,
  Refresh as RefreshIcon,
  Settings as SettingsIcon,
  Download as DownloadIcon,
  Share as ShareIcon,
  Visibility as VisibilityIcon,
  Add as AddIcon,
  Delete as DeleteIcon,
  Edit as EditIcon,
  Save as SaveIcon,
  Schedule as ScheduleIcon,
  Email as EmailIcon,
  Print as PrintIcon,
  PictureAsPdf as PdfIcon,
  TableChart as TableIcon,
  BarChart as BarChartIcon,
  ShowChart as LineChartIcon,
  PieChart as PieChartIcon,
  ScatterPlot as ScatterIcon,
  Map as MapIcon,
  FilterList as FilterIcon,
  Sort as SortIcon,
  ExpandMore as ExpandMoreIcon,
  DragIndicator as DragIcon,
  ContentCopy as CopyIcon,
  VisibilityOff as HideIcon,
  Visibility as ShowIcon,
  Assessment as AssessmentIcon,
} from '@mui/icons-material';
import {
  LineChart,
  Line,
  AreaChart,
  Area,
  BarChart,
  Bar,
  PieChart,
  Pie,
  Cell,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip as RechartsTooltip,
  ResponsiveContainer,
  Legend,
  ComposedChart,
  ScatterChart,
  Scatter,
} from 'recharts';
import { useTheme } from '@mui/material/styles';

interface ReportComponent {
  id: string;
  type: 'chart' | 'table' | 'metric' | 'filter';
  title: string;
  dataSource: string;
  chartType?: 'line' | 'bar' | 'pie' | 'area' | 'scatter';
  position: { x: number; y: number };
  size: { width: number; height: number };
  config: any;
  visible: boolean;
}

interface DataSource {
  id: string;
  name: string;
  description: string;
  fields: string[];
  type: 'financial' | 'property' | 'tenant' | 'maintenance';
}

interface CustomReportBuilderProps {
  onSave?: (report: any) => void;
  onExport?: (format: string) => void;
  onShare?: () => void;
}

const CustomReportBuilder: React.FC<CustomReportBuilderProps> = ({
  onSave,
  onExport,
  onShare,
}) => {
  const theme = useTheme();
  const [activeTab, setActiveTab] = useState(0);
  const [showDataSourceDialog, setShowDataSourceDialog] = useState(false);
  const [showChartDialog, setShowChartDialog] = useState(false);
  const [showFilterDialog, setShowFilterDialog] = useState(false);
  const [showScheduleDialog, setShowScheduleDialog] = useState(false);
  const [selectedComponent, setSelectedComponent] = useState<string | null>(null);
  const [reportName, setReportName] = useState('Custom Report');
  const [reportDescription, setReportDescription] = useState('');

  // Mock data sources
  const [dataSources] = useState<DataSource[]>([
    {
      id: 'financial',
      name: 'Financial Data',
      description: 'Revenue, expenses, and financial metrics',
      fields: ['revenue', 'expenses', 'netIncome', 'profitMargin', 'cashFlow'],
      type: 'financial',
    },
    {
      id: 'property',
      name: 'Property Data',
      description: 'Property performance and occupancy metrics',
      fields: ['occupancy', 'averageRent', 'maintenanceCosts', 'propertyValue'],
      type: 'property',
    },
    {
      id: 'tenant',
      name: 'Tenant Data',
      description: 'Tenant satisfaction and demographic data',
      fields: ['satisfaction', 'paymentHistory', 'demographics', 'renewalRate'],
      type: 'tenant',
    },
    {
      id: 'maintenance',
      name: 'Maintenance Data',
      description: 'Maintenance requests and costs',
      fields: ['requests', 'costs', 'responseTime', 'completionRate'],
      type: 'maintenance',
    },
  ]);

  // Mock report components
  const [components, setComponents] = useState<ReportComponent[]>([
    {
      id: '1',
      type: 'chart',
      title: 'Revenue Trend',
      dataSource: 'financial',
      chartType: 'line',
      position: { x: 0, y: 0 },
      size: { width: 600, height: 300 },
      config: {
        xAxis: 'period',
        yAxis: 'revenue',
        color: theme.palette.primary.main,
      },
      visible: true,
    },
    {
      id: '2',
      type: 'chart',
      title: 'Property Performance',
      dataSource: 'property',
      chartType: 'bar',
      position: { x: 0, y: 320 },
      size: { width: 400, height: 250 },
      config: {
        xAxis: 'property',
        yAxis: 'occupancy',
        color: theme.palette.success.main,
      },
      visible: true,
    },
    {
      id: '3',
      type: 'metric',
      title: 'Total Revenue',
      dataSource: 'financial',
      position: { x: 620, y: 0 },
      size: { width: 200, height: 100 },
      config: {
        value: '$2,847,392',
        change: 12.5,
        changeType: 'increase',
      },
      visible: true,
    },
    {
      id: '4',
      type: 'filter',
      title: 'Date Range Filter',
      dataSource: 'all',
      position: { x: 620, y: 120 },
      size: { width: 200, height: 80 },
      config: {
        type: 'dateRange',
        startDate: '2024-01-01',
        endDate: '2024-12-31',
      },
      visible: true,
    },
  ]);

  // Mock chart data
  const [chartData] = useState([
    { period: 'Jan', revenue: 240000, expenses: 120000, occupancy: 92 },
    { period: 'Feb', revenue: 265000, expenses: 125000, occupancy: 93 },
    { period: 'Mar', revenue: 280000, expenses: 130000, occupancy: 94 },
    { period: 'Apr', revenue: 295000, expenses: 132000, occupancy: 94.2 },
    { period: 'May', revenue: 310000, expenses: 135000, occupancy: 95 },
    { period: 'Jun', revenue: 325000, expenses: 138000, occupancy: 95.5 },
  ]);

  const [propertyData] = useState([
    { property: 'Sunset Apartments', occupancy: 96, revenue: 450000, satisfaction: 4.8 },
    { property: 'Downtown Lofts', occupancy: 92, revenue: 380000, satisfaction: 4.5 },
    { property: 'Garden Villas', occupancy: 89, revenue: 320000, satisfaction: 4.3 },
    { property: 'Riverside Complex', occupancy: 91, revenue: 290000, satisfaction: 4.6 },
    { property: 'Mountain View', occupancy: 88, revenue: 260000, satisfaction: 4.2 },
  ]);

  const handleAddComponent = (type: string) => {
    const newComponent: ReportComponent = {
      id: Date.now().toString(),
      type: type as any,
      title: `New ${type}`,
      dataSource: 'financial',
      chartType: type === 'chart' ? 'line' : undefined,
      position: { x: 0, y: components.length * 100 },
      size: { width: 400, height: 300 },
      config: {},
      visible: true,
    };
    setComponents([...components, newComponent]);
  };

  const handleDeleteComponent = (id: string) => {
    setComponents(components.filter(c => c.id !== id));
  };

  const handleComponentSelect = (id: string) => {
    setSelectedComponent(id);
  };

  const handleComponentConfig = (id: string, config: any) => {
    setComponents(components.map(c => 
      c.id === id ? { ...c, config: { ...c.config, ...config } } : c
    ));
  };

  const renderChart = (component: ReportComponent) => {
    const data = component.dataSource === 'financial' ? chartData : propertyData;
    
    switch (component.chartType) {
      case 'line':
        return (
          <ResponsiveContainer width="100%" height="100%">
            <LineChart data={data}>
              <CartesianGrid strokeDasharray="3 3" />
              <XAxis dataKey={component.config.xAxis} />
              <YAxis />
              <RechartsTooltip />
              <Legend />
              <Line
                type="monotone"
                dataKey={component.config.yAxis}
                stroke={component.config.color}
                strokeWidth={2}
              />
            </LineChart>
          </ResponsiveContainer>
        );
      case 'bar':
        return (
          <ResponsiveContainer width="100%" height="100%">
            <BarChart data={data}>
              <CartesianGrid strokeDasharray="3 3" />
              <XAxis dataKey={component.config.xAxis} />
              <YAxis />
              <RechartsTooltip />
              <Legend />
              <Bar dataKey={component.config.yAxis} fill={component.config.color} />
            </BarChart>
          </ResponsiveContainer>
        );
      case 'pie':
        return (
          <ResponsiveContainer width="100%" height="100%">
            <PieChart>
              <Pie
                data={data}
                cx="50%"
                cy="50%"
                labelLine={false}
                label={({ name, percent }) => `${name} ${(percent * 100).toFixed(0)}%`}
                outerRadius={80}
                fill="#8884d8"
                dataKey={component.config.yAxis}
              >
                {data.map((entry, index) => (
                  <Cell key={`cell-${index}`} fill={theme.palette.primary.main} />
                ))}
              </Pie>
              <RechartsTooltip />
            </PieChart>
          </ResponsiveContainer>
        );
      default:
        return <Typography>Select chart type</Typography>;
    }
  };

  const renderComponent = (component: ReportComponent) => {
    if (!component.visible) return null;

    return (
      <Card
        key={component.id}
        sx={{
          position: 'absolute',
          left: component.position.x,
          top: component.position.y,
          width: component.size.width,
          height: component.size.height,
          cursor: 'pointer',
          border: selectedComponent === component.id ? '2px solid' + theme.palette.primary.main : '1px solid',
          '&:hover': {
            boxShadow: 3,
          },
        }}
        onClick={() => handleComponentSelect(component.id)}
      >
        <CardContent sx={{ height: '100%', p: 1 }}>
          <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between', mb: 1 }}>
            <Typography variant="subtitle2" fontWeight="bold">
              {component.title}
            </Typography>
            <Box>
              <IconButton size="small" onClick={(e) => { e.stopPropagation(); setShowChartDialog(true); }}>
                <EditIcon />
              </IconButton>
              <IconButton size="small" onClick={(e) => { e.stopPropagation(); handleDeleteComponent(component.id); }}>
                <DeleteIcon />
              </IconButton>
            </Box>
          </Box>
          
          <Box sx={{ height: 'calc(100% - 40px)' }}>
            {component.type === 'chart' && renderChart(component)}
            {component.type === 'metric' && (
              <Box sx={{ textAlign: 'center', pt: 2 }}>
                <Typography variant="h4" color="primary">
                  {component.config.value}
                </Typography>
                <Typography variant="body2" color="text.secondary">
                  {component.title}
                </Typography>
                {component.config.change && (
                  <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'center', mt: 1 }}>
                    {component.config.changeType === 'increase' ? <TrendingUpIcon color="success" /> : <TrendingDownIcon color="error" />}
                    <Typography variant="caption" sx={{ ml: 0.5 }}>
                      {component.config.change}%
                    </Typography>
                  </Box>
                )}
              </Box>
            )}
            {component.type === 'filter' && (
              <Box sx={{ p: 2 }}>
                <Typography variant="body2" gutterBottom>
                  Date Range Filter
                </Typography>
                <TextField
                  size="small"
                  label="Start Date"
                  type="date"
                  defaultValue={component.config.startDate}
                  sx={{ width: '100%', mb: 1 }}
                />
                <TextField
                  size="small"
                  label="End Date"
                  type="date"
                  defaultValue={component.config.endDate}
                  sx={{ width: '100%' }}
                />
              </Box>
            )}
          </Box>
        </CardContent>
      </Card>
    );
  };

  return (
    <Box sx={{ p: 3 }}>
      {/* Header */}
      <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between', mb: 3 }}>
        <Box>
          <Typography variant="h4" gutterBottom>
            Custom Report Builder
          </Typography>
          <Typography variant="body2" color="text.secondary">
            Create custom reports with drag-and-drop components
          </Typography>
        </Box>
        <Box sx={{ display: 'flex', gap: 1 }}>
          <Button
            variant="outlined"
            startIcon={<SaveIcon />}
            onClick={() => onSave?.({ name: reportName, components })}
          >
            Save Report
          </Button>
          <Tooltip title="Export">
            <IconButton onClick={() => onExport?.('pdf')} size="small">
              <DownloadIcon />
            </IconButton>
          </Tooltip>
          <Tooltip title="Share">
            <IconButton onClick={onShare} size="small">
              <ShareIcon />
            </IconButton>
          </Tooltip>
        </Box>
      </Box>

      <Grid container spacing={3}>
        {/* Left Sidebar - Components */}
        <Grid item xs={12} md={3}>
          <Card>
            <CardContent>
              <Typography variant="h6" gutterBottom>
                Components
              </Typography>
              
              <Box sx={{ mb: 2 }}>
                <Typography variant="subtitle2" gutterBottom>
                  Charts
                </Typography>
                <Grid container spacing={1}>
                  <Grid item xs={6}>
                    <Button
                      variant="outlined"
                      size="small"
                      startIcon={<LineChartIcon />}
                      onClick={() => handleAddComponent('chart')}
                      fullWidth
                    >
                      Line Chart
                    </Button>
                  </Grid>
                  <Grid item xs={6}>
                    <Button
                      variant="outlined"
                      size="small"
                      startIcon={<BarChartIcon />}
                      onClick={() => handleAddComponent('chart')}
                      fullWidth
                    >
                      Bar Chart
                    </Button>
                  </Grid>
                  <Grid item xs={6}>
                    <Button
                      variant="outlined"
                      size="small"
                      startIcon={<PieChartIcon />}
                      onClick={() => handleAddComponent('chart')}
                      fullWidth
                    >
                      Pie Chart
                    </Button>
                  </Grid>
                  <Grid item xs={6}>
                    <Button
                      variant="outlined"
                      size="small"
                      startIcon={<ScatterIcon />}
                      onClick={() => handleAddComponent('chart')}
                      fullWidth
                    >
                      Scatter Plot
                    </Button>
                  </Grid>
                </Grid>
              </Box>

              <Box sx={{ mb: 2 }}>
                <Typography variant="subtitle2" gutterBottom>
                  Metrics
                </Typography>
                <Button
                  variant="outlined"
                  size="small"
                  startIcon={<AssessmentIcon />}
                  onClick={() => handleAddComponent('metric')}
                  fullWidth
                >
                  KPI Metric
                </Button>
              </Box>

              <Box sx={{ mb: 2 }}>
                <Typography variant="subtitle2" gutterBottom>
                  Filters
                </Typography>
                <Button
                  variant="outlined"
                  size="small"
                  startIcon={<FilterIcon />}
                  onClick={() => handleAddComponent('filter')}
                  fullWidth
                >
                  Date Filter
                </Button>
              </Box>

              <Divider sx={{ my: 2 }} />

              <Typography variant="subtitle2" gutterBottom>
                Data Sources
              </Typography>
              <List dense>
                {dataSources.map((source) => (
                  <ListItem key={source.id} dense>
                    <ListItemIcon>
                      <TableIcon />
                    </ListItemIcon>
                    <ListItemText
                      primary={source.name}
                      secondary={source.description}
                    />
                  </ListItem>
                ))}
              </List>
            </CardContent>
          </Card>
        </Grid>

        {/* Main Canvas */}
        <Grid item xs={12} md={9}>
          <Card sx={{ height: '70vh', position: 'relative', overflow: 'hidden' }}>
            <CardContent sx={{ height: '100%', p: 2 }}>
              <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between', mb: 2 }}>
                <TextField
                  label="Report Name"
                  value={reportName}
                  onChange={(e) => setReportName(e.target.value)}
                  size="small"
                  sx={{ width: 300 }}
                />
                <Box sx={{ display: 'flex', gap: 1 }}>
                  <Button
                    variant="outlined"
                    size="small"
                    startIcon={<ScheduleIcon />}
                    onClick={() => setShowScheduleDialog(true)}
                  >
                    Schedule
                  </Button>
                  <Button
                    variant="outlined"
                    size="small"
                    startIcon={<EmailIcon />}
                  >
                    Email Report
                  </Button>
                </Box>
              </Box>

              <Box sx={{ height: 'calc(100% - 60px)', position: 'relative', border: '1px dashed #ccc', borderRadius: 1 }}>
                {components.map(renderComponent)}
                
                {components.length === 0 && (
                  <Box sx={{ 
                    display: 'flex', 
                    flexDirection: 'column', 
                    alignItems: 'center', 
                    justifyContent: 'center', 
                    height: '100%',
                    color: 'text.secondary'
                  }}>
                    <DragIcon sx={{ fontSize: 48, mb: 2 }} />
                    <Typography variant="h6" gutterBottom>
                      Drag components here
                    </Typography>
                    <Typography variant="body2">
                      Start building your report by adding components from the sidebar
                    </Typography>
                  </Box>
                )}
              </Box>
            </CardContent>
          </Card>
        </Grid>
      </Grid>

      {/* Component Configuration Dialog */}
      <Dialog open={showChartDialog} onClose={() => setShowChartDialog(false)} maxWidth="md" fullWidth>
        <DialogTitle>Configure Chart</DialogTitle>
        <DialogContent>
          <Box sx={{ display: 'flex', flexDirection: 'column', gap: 2, mt: 1 }}>
            <TextField
              label="Chart Title"
              fullWidth
              defaultValue="Revenue Trend"
            />
            
            <FormControl fullWidth>
              <InputLabel>Data Source</InputLabel>
              <Select defaultValue="financial" label="Data Source">
                {dataSources.map((source) => (
                  <MenuItem key={source.id} value={source.id}>
                    {source.name}
                  </MenuItem>
                ))}
              </Select>
            </FormControl>

            <FormControl fullWidth>
              <InputLabel>Chart Type</InputLabel>
              <Select defaultValue="line" label="Chart Type">
                <MenuItem value="line">Line Chart</MenuItem>
                <MenuItem value="bar">Bar Chart</MenuItem>
                <MenuItem value="pie">Pie Chart</MenuItem>
                <MenuItem value="area">Area Chart</MenuItem>
                <MenuItem value="scatter">Scatter Plot</MenuItem>
              </Select>
            </FormControl>

            <Grid container spacing={2}>
              <Grid item xs={6}>
                <FormControl fullWidth>
                  <InputLabel>X Axis</InputLabel>
                  <Select defaultValue="period" label="X Axis">
                    <MenuItem value="period">Period</MenuItem>
                    <MenuItem value="property">Property</MenuItem>
                    <MenuItem value="category">Category</MenuItem>
                  </Select>
                </FormControl>
              </Grid>
              <Grid item xs={6}>
                <FormControl fullWidth>
                  <InputLabel>Y Axis</InputLabel>
                  <Select defaultValue="revenue" label="Y Axis">
                    <MenuItem value="revenue">Revenue</MenuItem>
                    <MenuItem value="expenses">Expenses</MenuItem>
                    <MenuItem value="occupancy">Occupancy</MenuItem>
                    <MenuItem value="satisfaction">Satisfaction</MenuItem>
                  </Select>
                </FormControl>
              </Grid>
            </Grid>

            <FormControlLabel
              control={<Switch defaultChecked />}
              label="Show Legend"
            />
            
            <FormControlLabel
              control={<Switch defaultChecked />}
              label="Show Grid"
            />
          </Box>
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setShowChartDialog(false)}>Cancel</Button>
          <Button variant="contained" onClick={() => setShowChartDialog(false)}>
            Apply
          </Button>
        </DialogActions>
      </Dialog>

      {/* Schedule Report Dialog */}
      <Dialog open={showScheduleDialog} onClose={() => setShowScheduleDialog(false)} maxWidth="sm" fullWidth>
        <DialogTitle>Schedule Report</DialogTitle>
        <DialogContent>
          <Box sx={{ display: 'flex', flexDirection: 'column', gap: 2, mt: 1 }}>
            <FormControl fullWidth>
              <InputLabel>Frequency</InputLabel>
              <Select defaultValue="weekly" label="Frequency">
                <MenuItem value="daily">Daily</MenuItem>
                <MenuItem value="weekly">Weekly</MenuItem>
                <MenuItem value="monthly">Monthly</MenuItem>
                <MenuItem value="quarterly">Quarterly</MenuItem>
              </Select>
            </FormControl>

            <TextField
              label="Email Recipients"
              fullWidth
              placeholder="<EMAIL>, <EMAIL>"
            />

            <FormControl fullWidth>
              <InputLabel>Export Format</InputLabel>
              <Select defaultValue="pdf" label="Export Format">
                <MenuItem value="pdf">PDF</MenuItem>
                <MenuItem value="excel">Excel</MenuItem>
                <MenuItem value="csv">CSV</MenuItem>
              </Select>
            </FormControl>

            <FormControlLabel
              control={<Switch defaultChecked />}
              label="Include charts and graphs"
            />
          </Box>
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setShowScheduleDialog(false)}>Cancel</Button>
          <Button variant="contained" onClick={() => setShowScheduleDialog(false)}>
            Schedule Report
          </Button>
        </DialogActions>
      </Dialog>
    </Box>
  );
};

export default CustomReportBuilder; 