/**
 * Utility functions for consistent formatting across SSR and CSR
 */

/**
 * Format currency with consistent locale
 */
export function formatCurrency(amount: number, currency: string = 'INR'): string {
  if (typeof window === 'undefined') {
    // Server-side: use simple formatting to avoid locale mismatches
    return `₹${amount.toString().replace(/\B(?=(\d{3})+(?!\d))/g, ',')}`;
  }
  
  // Client-side: use proper locale formatting
  try {
    return new Intl.NumberFormat('en-IN', {
      style: 'currency',
      currency: currency,
      minimumFractionDigits: 0,
      maximumFractionDigits: 0,
    }).format(amount);
  } catch (error) {
    // Fallback if Intl is not available
    return `₹${amount.toString().replace(/\B(?=(\d{3})+(?!\d))/g, ',')}`;
  }
}

/**
 * Format number with consistent locale
 */
export function formatNumber(num: number): string {
  if (typeof window === 'undefined') {
    // Server-side: use simple formatting
    return num.toString().replace(/\B(?=(\d{3})+(?!\d))/g, ',');
  }
  
  // Client-side: use proper locale formatting
  try {
    return new Intl.NumberFormat('en-IN').format(num);
  } catch (error) {
    // Fallback
    return num.toString().replace(/\B(?=(\d{3})+(?!\d))/g, ',');
  }
}

/**
 * Format date with consistent locale
 */
export function formatDate(date: string | Date): string {
  if (typeof window === 'undefined') {
    // Server-side: use ISO format to avoid locale mismatches
    const d = new Date(date);
    return d.toISOString().split('T')[0]; // YYYY-MM-DD format
  }
  
  // Client-side: use proper locale formatting
  try {
    return new Date(date).toLocaleDateString('en-IN', {
      year: 'numeric',
      month: 'short',
      day: 'numeric'
    });
  } catch (error) {
    // Fallback
    const d = new Date(date);
    return d.toISOString().split('T')[0];
  }
}

/**
 * Format time with consistent locale
 */
export function formatTime(date: string | Date): string {
  if (typeof window === 'undefined') {
    // Server-side: use simple format
    const d = new Date(date);
    return d.toISOString().split('T')[1].split('.')[0]; // HH:MM:SS format
  }
  
  // Client-side: use proper locale formatting
  try {
    return new Date(date).toLocaleTimeString('en-IN', {
      hour: '2-digit',
      minute: '2-digit',
      hour12: true
    });
  } catch (error) {
    // Fallback
    const d = new Date(date);
    return d.toISOString().split('T')[1].split('.')[0];
  }
}

/**
 * Format date and time together
 */
export function formatDateTime(date: string | Date): string {
  return `${formatDate(date)} ${formatTime(date)}`;
}

/**
 * Check if we're on the client side
 */
export function isClient(): boolean {
  return typeof window !== 'undefined';
}

/**
 * Safe wrapper for client-side only operations
 */
export function clientOnly<T>(fn: () => T, fallback: T): T {
  if (isClient()) {
    try {
      return fn();
    } catch (error) {
      console.warn('Client-only operation failed:', error);
      return fallback;
    }
  }
  return fallback;
}
