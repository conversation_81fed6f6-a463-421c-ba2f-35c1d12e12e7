'use client';

import React, { useState } from 'react';
import {
  Box,
  Card,
  CardContent,
  Typography,
  Button,
  Grid,
  useTheme,
  alpha,
  Stack,
  Chip,
  IconButton,
  Tooltip,
  TextField,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Tabs,
  Tab,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Paper,
  LinearProgress,
  Divider,
  Alert,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  CircularProgress
} from '@mui/material';
import {
  Assessment as AssessmentIcon,
  TrendingUp as TrendingUpIcon,
  Download as DownloadIcon,
  Refresh as RefreshIcon,
  Add as AddIcon,
  AttachMoney as AttachMoneyIcon,
  People as PeopleIcon,
  Home as HomeIcon,
  BarChart as BarChartIcon,
  <PERSON><PERSON>hart as PieChartIcon,
  Show<PERSON>hart as ShowChartIcon,
  FileDownload as FileDownloadIcon,
  DateRange as DateRangeIcon,
  FilterList as FilterIcon
} from '@mui/icons-material';
import { DashboardLayout } from '@/components/layout/DashboardLayout';
import { PageHeader } from '@/components/layout/PageHeader';
import { InfoCard } from '@/components/data-display/InfoCard';
import { LoadingSpinner } from '@/components/feedback/LoadingSpinner';
import { useReports } from '@/hooks/useReports';
import { useAuth } from '@/hooks/useAuth';
import { useRouter } from 'next/navigation';

interface ReportTemplate {
  id: string;
  name: string;
  description: string;
  icon: React.ReactNode;
  category: 'financial' | 'occupancy' | 'tenant' | 'comprehensive';
  estimatedTime: string;
  reportType: 'financial' | 'occupancy' | 'tenant' | 'comprehensive';
}

interface TabPanelProps {
  children?: React.ReactNode;
  index: number;
  value: number;
}

function TabPanel(props: TabPanelProps) {
  const { children, value, index, ...other } = props;
  return (
    <div
      role="tabpanel"
      hidden={value !== index}
      id={`reports-tabpanel-${index}`}
      aria-labelledby={`reports-tab-${index}`}
      {...other}
    >
      {value === index && <Box sx={{ p: 3 }}>{children}</Box>}
    </div>
  );
}

const reportTemplates: ReportTemplate[] = [
  {
    id: 'financial-revenue',
    name: 'Revenue Report',
    description: 'Comprehensive revenue analysis with trends and forecasts',
    icon: <AttachMoneyIcon />,
    category: 'financial',
    estimatedTime: '2-3 minutes',
    reportType: 'financial'
  },
  {
    id: 'financial-expenses',
    name: 'Expense Analysis',
    description: 'Detailed breakdown of operational expenses',
    icon: <BarChartIcon />,
    category: 'financial',
    estimatedTime: '2-3 minutes',
    reportType: 'financial'
  },
  {
    id: 'occupancy-overview',
    name: 'Occupancy Overview',
    description: 'Current occupancy rates and vacancy trends',
    icon: <HomeIcon />,
    category: 'occupancy',
    estimatedTime: '1-2 minutes',
    reportType: 'occupancy'
  },
  {
    id: 'tenant-analysis',
    name: 'Tenant Analysis',
    description: 'Tenant demographics and behavior patterns',
    icon: <PeopleIcon />,
    category: 'tenant',
    estimatedTime: '3-4 minutes',
    reportType: 'tenant'
  },
  {
    id: 'comprehensive-dashboard',
    name: 'Executive Dashboard',
    description: 'Complete overview of all key metrics',
    icon: <AssessmentIcon />,
    category: 'comprehensive',
    estimatedTime: '5-7 minutes',
    reportType: 'comprehensive'
  }
];

export default function ReportsManagementPage() {
  useAuth();
  const theme = useTheme();
  const router = useRouter();
  
  // State management
  const [tabValue, setTabValue] = useState(0);
  const [reportType, setReportType] = useState<'financial' | 'occupancy' | 'tenant' | 'comprehensive'>('financial');
  const [fromDate, setFromDate] = useState('');
  const [toDate, setToDate] = useState('');
  const [selectedTemplate, setSelectedTemplate] = useState<ReportTemplate | null>(null);
  const [exportDialogOpen, setExportDialogOpen] = useState(false);
  const [exportFormat, setExportFormat] = useState<'csv' | 'excel' | 'pdf'>('pdf');
  
  // API hooks
  const {
    dashboard,
    statistics,
    dashboardLoading,
    statisticsLoading,
    dashboardError,
    statisticsError,
    exportReport,
    exportLoading,
    refreshDashboard,
    getFinancialReports,
    getOccupancyReports,
    getTenantReports
  } = useReports();

  // Event handlers
  const handleTabChange = (event: React.SyntheticEvent, newValue: number) => {
    setTabValue(newValue);
  };

  const handleGenerateReport = async (template: ReportTemplate) => {
    setSelectedTemplate(template);
    
    try {
      switch (template.reportType) {
        case 'financial':
          await getFinancialReports({
            start_date: fromDate,
            end_date: toDate,
            report_type: 'revenue'
          });
          break;
        case 'occupancy':
          await getOccupancyReports({
            start_date: fromDate,
            end_date: toDate
          });
          break;
        case 'tenant':
          await getTenantReports({
            start_date: fromDate,
            end_date: toDate
          });
          break;
        default:
          // Handle comprehensive report
          break;
      }
    } catch (error) {
      console.error('Failed to generate report:', error);
    }
  };

  const handleExportReport = () => {
    if (!selectedTemplate) return;
    
    exportReport({
      report_type: selectedTemplate.reportType,
      format: exportFormat,
      start_date: fromDate,
      end_date: toDate
    });
    
    setExportDialogOpen(false);
  };

  const handleRefresh = () => {
    refreshDashboard();
  };

  // Card styles
  const cardSx = {
    background: `linear-gradient(135deg, ${alpha(theme.palette.background.paper, 0.8)} 0%, ${alpha(theme.palette.background.paper, 0.95)} 100%)`,
    backdropFilter: 'blur(20px)',
    border: `1px solid ${alpha(theme.palette.divider, 0.1)}`,
    borderRadius: 3,
    boxShadow: `0 8px 32px ${alpha(theme.palette.common.black, 0.1)}`,
    transition: 'all 0.3s cubic-bezier(0.4, 0, 0.2, 1)',
    '&:hover': {
      transform: 'translateY(-4px)',
      boxShadow: `0 12px 40px ${alpha(theme.palette.common.black, 0.15)}`,
      border: `1px solid ${alpha(theme.palette.primary.main, 0.2)}`,
    }
  };

  if (dashboardLoading || statisticsLoading) {
    return (
      <DashboardLayout>
        <LoadingSpinner />
      </DashboardLayout>
    );
  }

  if (dashboardError || statisticsError) {
    return (
      <DashboardLayout>
        <Alert severity="error" sx={{ m: 2 }}>
          Failed to load reports data. Please try again.
        </Alert>
      </DashboardLayout>
    );
  }

  return (
    <DashboardLayout>
      <PageHeader
        title="Reports Management"
        subtitle="Generate, manage, and analyze comprehensive reports"
        actions={[
          {
            label: 'Refresh Data',
            onClick: handleRefresh,
            variant: 'outlined',
            startIcon: <RefreshIcon />
          },
          {
            label: 'Export Report',
            onClick: () => setExportDialogOpen(true),
            variant: 'contained',
            startIcon: <FileDownloadIcon />,
            disabled: !selectedTemplate
          }
        ]}
      />

      {/* Statistics Cards */}
      <Grid container spacing={3} sx={{ mb: 4 }}>
        <Grid item xs={12} sm={6} md={3}>
          <InfoCard
            title="Total Reports"
            value={statistics?.total_reports || 0}
            icon={<AssessmentIcon />}
            trend={{ value: 12, isPositive: true }}
            sx={cardSx}
          />
        </Grid>
        <Grid item xs={12} sm={6} md={3}>
          <InfoCard
            title="This Month"
            value={statistics?.reports_this_month || 0}
            icon={<TrendingUpIcon />}
            trend={{ value: 8, isPositive: true }}
            sx={cardSx}
          />
        </Grid>
        <Grid item xs={12} sm={6} md={3}>
          <InfoCard
            title="Most Popular"
            value={statistics?.most_popular_report || 'Financial Report'}
            icon={<BarChartIcon />}
            sx={cardSx}
          />
        </Grid>
        <Grid item xs={12} sm={6} md={3}>
          <InfoCard
            title="Last Generated"
            value="2 hours ago"
            icon={<DateRangeIcon />}
            sx={cardSx}
          />
        </Grid>
      </Grid>

      {/* Main Content */}
      <Card sx={cardSx}>
        <CardContent>
          <Box sx={{ borderBottom: 1, borderColor: 'divider', mb: 3 }}>
            <Tabs value={tabValue} onChange={handleTabChange}>
              <Tab label="Generate Reports" />
              <Tab label="Report History" />
              <Tab label="Analytics" />
            </Tabs>
          </Box>

          <TabPanel value={tabValue} index={0}>
            {/* Generate Reports Tab */}
            <Stack spacing={3}>
              {/* Date Range Filters */}
              <Card variant="outlined">
                <CardContent>
                  <Typography variant="h6" gutterBottom>
                    Report Parameters
                  </Typography>
                  <Grid container spacing={2}>
                    <Grid item xs={12} md={4}>
                      <TextField
                        fullWidth
                        label="From Date"
                        type="date"
                        value={fromDate}
                        onChange={(e) => setFromDate(e.target.value)}
                        InputLabelProps={{ shrink: true }}
                      />
                    </Grid>
                    <Grid item xs={12} md={4}>
                      <TextField
                        fullWidth
                        label="To Date"
                        type="date"
                        value={toDate}
                        onChange={(e) => setToDate(e.target.value)}
                        InputLabelProps={{ shrink: true }}
                      />
                    </Grid>
                    <Grid item xs={12} md={4}>
                      <FormControl fullWidth>
                        <InputLabel>Report Category</InputLabel>
                        <Select
                          value={reportType}
                          label="Report Category"
                          onChange={(e) => setReportType(e.target.value as any)}
                        >
                          <MenuItem value="financial">Financial</MenuItem>
                          <MenuItem value="occupancy">Occupancy</MenuItem>
                          <MenuItem value="tenant">Tenant</MenuItem>
                          <MenuItem value="comprehensive">Comprehensive</MenuItem>
                        </Select>
                      </FormControl>
                    </Grid>
                  </Grid>
                </CardContent>
              </Card>

              {/* Report Templates */}
              <Typography variant="h6" gutterBottom>
                Available Report Templates
              </Typography>
              <Grid container spacing={2}>
                {reportTemplates
                  .filter(template => reportType === 'comprehensive' || template.category === reportType)
                  .map((template) => (
                    <Grid item xs={12} md={6} lg={4} key={template.id}>
                      <Card 
                        variant="outlined" 
                        sx={{ 
                          cursor: 'pointer',
                          transition: 'all 0.2s',
                          '&:hover': {
                            boxShadow: 2,
                            transform: 'translateY(-2px)'
                          },
                          ...(selectedTemplate?.id === template.id && {
                            borderColor: 'primary.main',
                            boxShadow: 2
                          })
                        }}
                        onClick={() => setSelectedTemplate(template)}
                      >
                        <CardContent>
                          <Stack direction="row" spacing={2} alignItems="center" sx={{ mb: 2 }}>
                            <Box sx={{ color: 'primary.main' }}>
                              {template.icon}
                            </Box>
                            <Box>
                              <Typography variant="h6" component="div">
                                {template.name}
                              </Typography>
                              <Chip 
                                label={template.category} 
                                size="small" 
                                color="primary" 
                                variant="outlined" 
                              />
                            </Box>
                          </Stack>
                          <Typography variant="body2" color="text.secondary" sx={{ mb: 2 }}>
                            {template.description}
                          </Typography>
                          <Typography variant="caption" color="text.secondary">
                            Estimated time: {template.estimatedTime}
                          </Typography>
                          <Box sx={{ mt: 2 }}>
                            <Button
                              variant="contained"
                              size="small"
                              onClick={(e) => {
                                e.stopPropagation();
                                handleGenerateReport(template);
                              }}
                              disabled={!fromDate || !toDate}
                              fullWidth
                            >
                              Generate Report
                            </Button>
                          </Box>
                        </CardContent>
                      </Card>
                    </Grid>
                  ))}
              </Grid>
            </Stack>
          </TabPanel>

          <TabPanel value={tabValue} index={1}>
            {/* Report History Tab */}
            <Typography variant="h6" gutterBottom>
              Recent Reports
            </Typography>
            <TableContainer component={Paper} elevation={0}>
              <Table>
                <TableHead>
                  <TableRow>
                    <TableCell>Report Name</TableCell>
                    <TableCell>Type</TableCell>
                    <TableCell>Generated</TableCell>
                    <TableCell>Status</TableCell>
                    <TableCell>Actions</TableCell>
                  </TableRow>
                </TableHead>
                <TableBody>
                  {[
                    { id: 1, name: 'Monthly Financial Report', type: 'Financial', generated: '2 hours ago', status: 'Completed' },
                    { id: 2, name: 'Occupancy Analysis', type: 'Occupancy', generated: '1 day ago', status: 'Completed' },
                    { id: 3, name: 'Tenant Report Q4', type: 'Tenant', generated: '3 days ago', status: 'Completed' },
                    { id: 4, name: 'Comprehensive Report', type: 'Comprehensive', generated: '1 week ago', status: 'Completed' },
                  ].map((report) => (
                    <TableRow key={report.id} hover>
                      <TableCell>
                        <Typography variant="body1" fontWeight="medium">
                          {report.name}
                        </Typography>
                      </TableCell>
                      <TableCell>
                        <Chip
                          label={report.type}
                          size="small"
                          color="primary"
                          variant="outlined"
                        />
                      </TableCell>
                      <TableCell>{report.generated}</TableCell>
                      <TableCell>
                        <Chip
                          label={report.status}
                          size="small"
                          color="success"
                        />
                      </TableCell>
                      <TableCell>
                        <Stack direction="row" spacing={1}>
                          <IconButton size="small" color="primary">
                            <FileDownloadIcon />
                          </IconButton>
                          <IconButton size="small" color="secondary">
                            <RefreshIcon />
                          </IconButton>
                        </Stack>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </TableContainer>
          </TabPanel>

          <TabPanel value={tabValue} index={2}>
            {/* Analytics Tab */}
            <Typography variant="h6" gutterBottom>
              Report Analytics
            </Typography>
            <Grid container spacing={3}>
              <Grid item xs={12} md={6}>
                <Card>
                  <CardContent>
                    <Typography variant="h6" gutterBottom>
                      Report Generation Trends
                    </Typography>
                    <Box sx={{ height: 200, display: 'flex', alignItems: 'center', justifyContent: 'center' }}>
                      <Typography variant="body2" color="text.secondary">
                        Chart showing report generation trends over time
                      </Typography>
                    </Box>
                  </CardContent>
                </Card>
              </Grid>
              <Grid item xs={12} md={6}>
                <Card>
                  <CardContent>
                    <Typography variant="h6" gutterBottom>
                      Popular Report Types
                    </Typography>
                    <Stack spacing={2}>
                      {[
                        { type: 'Financial Reports', count: 45, percentage: 40 },
                        { type: 'Occupancy Reports', count: 35, percentage: 31 },
                        { type: 'Tenant Reports', count: 20, percentage: 18 },
                        { type: 'Comprehensive Reports', count: 12, percentage: 11 },
                      ].map((item) => (
                        <Box key={item.type}>
                          <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 1 }}>
                            <Typography variant="body2">{item.type}</Typography>
                            <Typography variant="body2" color="text.secondary">
                              {item.count} ({item.percentage}%)
                            </Typography>
                          </Box>
                          <LinearProgress
                            variant="determinate"
                            value={item.percentage}
                            sx={{ height: 6, borderRadius: 3 }}
                          />
                        </Box>
                      ))}
                    </Stack>
                  </CardContent>
                </Card>
              </Grid>
              <Grid item xs={12}>
                <Card>
                  <CardContent>
                    <Typography variant="h6" gutterBottom>
                      Report Performance Metrics
                    </Typography>
                    <Grid container spacing={3}>
                      <Grid item xs={12} sm={6} md={3}>
                        <Box sx={{ textAlign: 'center' }}>
                          <Typography variant="h4" color="primary">
                            2.3s
                          </Typography>
                          <Typography variant="body2" color="text.secondary">
                            Avg Generation Time
                          </Typography>
                        </Box>
                      </Grid>
                      <Grid item xs={12} sm={6} md={3}>
                        <Box sx={{ textAlign: 'center' }}>
                          <Typography variant="h4" color="success.main">
                            99.2%
                          </Typography>
                          <Typography variant="body2" color="text.secondary">
                            Success Rate
                          </Typography>
                        </Box>
                      </Grid>
                      <Grid item xs={12} sm={6} md={3}>
                        <Box sx={{ textAlign: 'center' }}>
                          <Typography variant="h4" color="info.main">
                            1.2MB
                          </Typography>
                          <Typography variant="body2" color="text.secondary">
                            Avg File Size
                          </Typography>
                        </Box>
                      </Grid>
                      <Grid item xs={12} sm={6} md={3}>
                        <Box sx={{ textAlign: 'center' }}>
                          <Typography variant="h4" color="warning.main">
                            85%
                          </Typography>
                          <Typography variant="body2" color="text.secondary">
                            Download Rate
                          </Typography>
                        </Box>
                      </Grid>
                    </Grid>
                  </CardContent>
                </Card>
              </Grid>
            </Grid>
          </TabPanel>
        </CardContent>
      </Card>

      {/* Export Dialog */}
      <Dialog open={exportDialogOpen} onClose={() => setExportDialogOpen(false)}>
        <DialogTitle>Export Report</DialogTitle>
        <DialogContent>
          <FormControl fullWidth sx={{ mt: 2 }}>
            <InputLabel>Export Format</InputLabel>
            <Select
              value={exportFormat}
              label="Export Format"
              onChange={(e) => setExportFormat(e.target.value as any)}
            >
              <MenuItem value="pdf">PDF</MenuItem>
              <MenuItem value="excel">Excel</MenuItem>
              <MenuItem value="csv">CSV</MenuItem>
            </Select>
          </FormControl>
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setExportDialogOpen(false)}>Cancel</Button>
          <Button 
            onClick={handleExportReport} 
            variant="contained"
            disabled={exportLoading}
            startIcon={exportLoading ? <CircularProgress size={16} /> : <FileDownloadIcon />}
          >
            {exportLoading ? 'Exporting...' : 'Export'}
          </Button>
        </DialogActions>
      </Dialog>
    </DashboardLayout>
  );
}
