'use client';

import React, { useState } from 'react';
import { ProtectedRoute } from '@/components/auth/ProtectedRoute';
import { DashboardLayout } from '@/components/layout/DashboardLayout';
import {
  Box,
  Typography,
  Tabs,
  Tab,
  IconButton,
  Tooltip,
  useTheme,
  alpha,
  Fab,
  Zoom,
} from '@mui/material';
import {
  Analytics as AnalyticsIcon,
  Home as PropertyIcon,
  People as TenantIcon,
  Assessment as ExecutiveIcon,
  Build as CustomIcon,
  Refresh as RefreshIcon,
  Settings as SettingsIcon,
} from '@mui/icons-material';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';

// Import dashboard components
import ExecutiveDashboard from '@/components/dashboard/ExecutiveDashboard';
import FinancialAnalyticsDashboard from '@/components/dashboard/FinancialAnalyticsDashboard';
import PropertyAnalyticsDashboard from '@/components/dashboard/PropertyAnalyticsDashboard';
import TenantAnalyticsDashboard from '@/components/dashboard/TenantAnalyticsDashboard';
import CustomReportBuilder from '@/components/analytics/CustomReportBuilder';

// Create a query client for React Query
const queryClient = new QueryClient({
  defaultOptions: {
    queries: {
      staleTime: 5 * 60 * 1000, // 5 minutes
      refetchOnWindowFocus: false,
      retry: 2,
    },
  },
});

interface TabPanelProps {
  children?: React.ReactNode;
  index: number;
  value: number;
}

function TabPanel(props: TabPanelProps) {
  const { children, value, index, ...other } = props;

  return (
    <div
      role="tabpanel"
      hidden={value !== index}
      id={`dashboard-tabpanel-${index}`}
      aria-labelledby={`dashboard-tab-${index}`}
      {...other}
    >
      {value === index && <Box sx={{ py: 3 }}>{children}</Box>}
    </div>
  );
}

export default function DashboardPage() {
  const theme = useTheme();
  const [activeTab, setActiveTab] = useState(0);
  const [refreshing, setRefreshing] = useState(false);

  const handleTabChange = (event: React.SyntheticEvent, newValue: number) => {
    setActiveTab(newValue);
  };

  const handleRefresh = async () => {
    setRefreshing(true);
    // Refresh will be handled by individual components
    setTimeout(() => setRefreshing(false), 1000);
  };

  const tabs = [
    { label: 'Executive', icon: ExecutiveIcon, component: ExecutiveDashboard },
    { label: 'Financial', icon: AnalyticsIcon, component: FinancialAnalyticsDashboard },
    { label: 'Property', icon: PropertyIcon, component: PropertyAnalyticsDashboard },
    { label: 'Tenant', icon: TenantIcon, component: TenantAnalyticsDashboard },
    { label: 'Custom Reports', icon: CustomIcon, component: CustomReportBuilder },
  ];

  return (
    <QueryClientProvider client={queryClient}>
      <ProtectedRoute>
        <DashboardLayout
          title="Dynamic Dashboard"
          breadcrumbs={[
            { label: 'Home', href: '/' },
            { label: 'Dashboard' }
          ]}
        >
          {/* Header Section */}
          <Box sx={{
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'space-between',
            mb: 3,
            background: `linear-gradient(135deg, ${alpha(theme.palette.background.paper, 0.8)} 0%, ${alpha(theme.palette.background.paper, 0.95)} 100%)`,
            backdropFilter: 'blur(20px)',
            border: `1px solid ${alpha(theme.palette.divider, 0.1)}`,
            borderRadius: 3,
            p: 3,
            boxShadow: `0 8px 32px ${alpha(theme.palette.common.black, 0.1)}`,
          }}>
            <Box>
              <Typography
                variant="h4"
                component="h1"
                gutterBottom
                sx={{
                  fontWeight: 700,
                  background: `linear-gradient(45deg, ${theme.palette.primary.main}, ${theme.palette.secondary.main})`,
                  backgroundClip: 'text',
                  WebkitBackgroundClip: 'text',
                  WebkitTextFillColor: 'transparent',
                  mb: 1
                }}
              >
                Dynamic Dashboard
              </Typography>
              <Typography
                variant="body1"
                color="text.secondary"
                sx={{ mb: 0 }}
              >
                Real-time insights and analytics for your tenant management system
              </Typography>
            </Box>

            <Box sx={{ display: 'flex', gap: 1 }}>
              <Tooltip title="Refresh All Data">
                <IconButton
                  onClick={handleRefresh}
                  disabled={refreshing}
                  sx={{
                    background: alpha(theme.palette.primary.main, 0.1),
                    '&:hover': {
                      background: alpha(theme.palette.primary.main, 0.2),
                    }
                  }}
                >
                  <RefreshIcon />
                </IconButton>
              </Tooltip>
              <Tooltip title="Dashboard Settings">
                <IconButton
                  sx={{
                    background: alpha(theme.palette.secondary.main, 0.1),
                    '&:hover': {
                      background: alpha(theme.palette.secondary.main, 0.2),
                    }
                  }}
                >
                  <SettingsIcon />
                </IconButton>
              </Tooltip>
            </Box>
          </Box>

          {/* Dashboard Navigation Tabs */}
          <Box sx={{
            background: `linear-gradient(135deg, ${alpha(theme.palette.background.paper, 0.8)} 0%, ${alpha(theme.palette.background.paper, 0.95)} 100%)`,
            backdropFilter: 'blur(20px)',
            border: `1px solid ${alpha(theme.palette.divider, 0.1)}`,
            borderRadius: 3,
            mb: 3,
            overflow: 'hidden',
          }}>
            <Tabs
              value={activeTab}
              onChange={handleTabChange}
              variant="fullWidth"
              sx={{
                '& .MuiTab-root': {
                  minHeight: 64,
                  textTransform: 'none',
                  fontWeight: 600,
                  fontSize: '0.95rem',
                  '&.Mui-selected': {
                    background: alpha(theme.palette.primary.main, 0.1),
                    color: 'primary.main',
                  },
                },
                '& .MuiTabs-indicator': {
                  height: 3,
                  borderRadius: '3px 3px 0 0',
                },
              }}
            >
              {tabs.map((tab, index) => (
                <Tab
                  key={index}
                  label={tab.label}
                  icon={<tab.icon />}
                  iconPosition="start"
                  sx={{
                    '& .MuiTab-iconWrapper': {
                      mr: 1,
                    },
                  }}
                />
              ))}
            </Tabs>
          </Box>
          {/* Dashboard Content Panels */}
          {tabs.map((tab, index) => (
            <TabPanel key={index} value={activeTab} index={index}>
              <tab.component onRefresh={handleRefresh} />
            </TabPanel>
          ))}

          {/* Floating Action Button for Quick Actions */}
          <Zoom in={true}>
            <Fab
              color="primary"
              sx={{
                position: 'fixed',
                bottom: 24,
                right: 24,
                background: `linear-gradient(45deg, ${theme.palette.primary.main}, ${theme.palette.secondary.main})`,
                '&:hover': {
                  background: `linear-gradient(45deg, ${theme.palette.primary.dark}, ${theme.palette.secondary.dark})`,
                },
              }}
              onClick={handleRefresh}
            >
              <RefreshIcon />
            </Fab>
          </Zoom>
        </DashboardLayout>
      </ProtectedRoute>
    </QueryClientProvider>
  );
}