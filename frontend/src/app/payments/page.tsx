'use client';

import React, { useState } from 'react';
import {
  Box,
  Card,
  CardContent,
  Typography,
  Button,
  Chip,
  IconButton,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Avatar,
  Grid,
  TextField,
  InputAdornment,
  Menu,
  MenuItem,
  Alert,
  Divider,
  ListItemText,
  ListItemIcon,
  FormControl,
  InputLabel,
  Select,
  Pagination,
} from '@mui/material';
import {
  Add as AddIcon,
  Search as SearchIcon,
  FilterList as FilterIcon,
  MoreVert as MoreIcon,
  Payment as PaymentIcon,
  CheckCircle as CheckCircleIcon,
  Edit as EditIcon,
  Visibility as ViewIcon,
  Delete as DeleteIcon,
  Download as DownloadIcon,
  AccountBalance as AccountBalanceIcon,
  AttachMoney as MoneyIcon,
  Schedule as ScheduleIcon,
  CreditCard as CreditCardIcon,
  AccountBalanceWallet as WalletIcon,
  LocalAtm as CashIcon,
  Receipt as ReceiptIcon,
} from '@mui/icons-material';
import { DashboardLayout } from '@/components/layout/DashboardLayout';
import { PageHeader } from '@/components/layout/PageHeader';
import { InfoCard } from '@/components/data-display/InfoCard';
import { useRouter } from 'next/navigation';
import { LoadingSpinner } from '@/components/feedback/LoadingSpinner';
import { ConfirmDialog } from '@/components/feedback/ConfirmDialog';
import { useAuth } from '@/hooks/useAuth';
import { usePayments, usePaymentStatistics } from '@/hooks/usePayments';
import { Payment } from '@/services/payment.service';

// Types are now imported from the service
export default function PaymentsPage() {
  useAuth();
  const router = useRouter();

  // State for filters and pagination
  const [searchTerm, setSearchTerm] = useState('');
  const [statusFilter, setStatusFilter] = useState('');
  const [methodFilter, setMethodFilter] = useState('');
  const [page, setPage] = useState(1);
  const [perPage] = useState(15);

  // Payment filters
  const paymentFilters = {
    search: searchTerm,
    status: statusFilter || undefined,
    payment_method: methodFilter || undefined,
    page,
    per_page: perPage,
  };

  // Fetch data using hooks
  const {
    data: paymentsData,
    loading: paymentsLoading,
    error: paymentsError,
    deletePayment,
  } = usePayments(paymentFilters);

  const {
    data: statsData,
    loading: statsLoading,
    error: statsError,
  } = usePaymentStatistics();

  // Dialog states
  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false);
  const [paymentToDelete, setPaymentToDelete] = useState<Payment | null>(null);

  // Menu states
  const [anchorEl, setAnchorEl] = useState<null | HTMLElement>(null);
  const [selectedPaymentForMenu, setSelectedPaymentForMenu] = useState<Payment | null>(null);

  // Handle search
  const handleSearch = (value: string) => {
    setSearchTerm(value);
    setPage(1); // Reset to first page when searching
  };

  // Handle filter changes
  const handleStatusFilterChange = (value: string) => {
    setStatusFilter(value);
    setPage(1);
  };

  const handleMethodFilterChange = (value: string) => {
    setMethodFilter(value);
    setPage(1);
  };

  // Handle pagination
  const handlePageChange = (_: React.ChangeEvent<unknown>, value: number) => {
    setPage(value);
  };

  // Handle menu actions
  const handleMenuOpen = (event: React.MouseEvent<HTMLElement>, payment: Payment) => {
    setAnchorEl(event.currentTarget);
    setSelectedPaymentForMenu(payment);
  };

  const handleMenuClose = () => {
    setAnchorEl(null);
    setSelectedPaymentForMenu(null);
  };

  const handleDeletePayment = () => {
    setPaymentToDelete(selectedPaymentForMenu);
    setDeleteDialogOpen(true);
    handleMenuClose();
  };

  const confirmDeletePayment = async () => {
    if (paymentToDelete) {
      try {
        await deletePayment(paymentToDelete.id);
        setDeleteDialogOpen(false);
        setPaymentToDelete(null);
      } catch (error) {
        console.error('Failed to delete payment:', error);
      }
    }
  };

  // Get status color
  const getStatusColor = (status: string) => {
    switch (status) {
      case 'completed': return 'success';
      case 'pending': return 'warning';
      case 'failed': return 'error';
      case 'cancelled': return 'default';
      default: return 'default';
    }
  };

  // Get payment method icon
  const getPaymentMethodIcon = (method: string) => {
    switch (method) {
      case 'online': return <CreditCardIcon />;
      case 'bank_transfer': return <AccountBalanceIcon />;
      case 'cash': return <CashIcon />;
      case 'cheque': return <ReceiptIcon />;
      case 'upi': return <WalletIcon />;
      default: return <PaymentIcon />;
    }
  };

  // Format currency
  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-IN', {
      style: 'currency',
      currency: 'INR',
      minimumFractionDigits: 0,
      maximumFractionDigits: 0,
    }).format(amount);
  };



  // Format date and time (SSR-safe)
  const formatDateTime = (dateString: string) => {
    if (typeof window === 'undefined') {
      // Server-side: use simple format
      const d = new Date(dateString);
      return d.toISOString().replace('T', ' ').split('.')[0];
    }
    // Client-side: use locale formatting
    return new Date(dateString).toLocaleString('en-IN', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit',
    });
  };

  // Loading state
  if (paymentsLoading && statsLoading) {
    return (
      <DashboardLayout>
        <Box display="flex" justifyContent="center" alignItems="center" minHeight="400px">
          <LoadingSpinner />
        </Box>
      </DashboardLayout>
    );
  }

  // Error state
  if (paymentsError || statsError) {
    return (
      <DashboardLayout>
        <Box p={3}>
          <Alert severity="error">
            {paymentsError || statsError}
          </Alert>
        </Box>
      </DashboardLayout>
    );
  }

  return (
    <DashboardLayout>
      <PageHeader
        title="Payment Management"
        subtitle="Track and manage all payment transactions"
        actions={[
          {
            label: 'Record Payment',
            onClick: () => router.push('/payments/add'),
            variant: 'contained',
            startIcon: <AddIcon />
          }
        ]}
      />

      <Box sx={{ p: 3 }}>
        {/* Statistics Cards */}
        <Grid container spacing={3} sx={{ mb: 3 }}>
          <Grid item xs={12} sm={6} md={3}>
            <InfoCard
              title="Total Payments"
              value={statsData?.total_payments?.toString() || '0'}
              icon={<PaymentIcon />}
              color="primary"
            />
          </Grid>
          <Grid item xs={12} sm={6} md={3}>
            <InfoCard
              title="Total Amount"
              value={formatCurrency(statsData?.total_amount || 0)}
              icon={<MoneyIcon />}
              color="success"
            />
          </Grid>
          <Grid item xs={12} sm={6} md={3}>
            <InfoCard
              title="Completed"
              value={statsData?.completed_payments?.toString() || '0'}
              icon={<CheckCircleIcon />}
              color="success"
            />
          </Grid>
          <Grid item xs={12} sm={6} md={3}>
            <InfoCard
              title="Pending"
              value={statsData?.pending_payments?.toString() || '0'}
              icon={<ScheduleIcon />}
              color="warning"
            />
          </Grid>
        </Grid>

        {/* Filters */}
        <Card sx={{ mb: 3 }}>
          <CardContent>
            <Grid container spacing={2} alignItems="center">
              <Grid item xs={12} md={4}>
                <TextField
                  fullWidth
                  placeholder="Search payments..."
                  value={searchTerm}
                  onChange={(e) => handleSearch(e.target.value)}
                  InputProps={{
                    startAdornment: (
                      <InputAdornment position="start">
                        <SearchIcon />
                      </InputAdornment>
                    ),
                  }}
                />
              </Grid>
              <Grid item xs={12} md={3}>
                <FormControl fullWidth>
                  <InputLabel>Status</InputLabel>
                  <Select
                    value={statusFilter}
                    label="Status"
                    onChange={(e) => handleStatusFilterChange(e.target.value)}
                  >
                    <MenuItem value="">All Status</MenuItem>
                    <MenuItem value="pending">Pending</MenuItem>
                    <MenuItem value="completed">Completed</MenuItem>
                    <MenuItem value="failed">Failed</MenuItem>
                    <MenuItem value="cancelled">Cancelled</MenuItem>
                  </Select>
                </FormControl>
              </Grid>
              <Grid item xs={12} md={3}>
                <FormControl fullWidth>
                  <InputLabel>Payment Method</InputLabel>
                  <Select
                    value={methodFilter}
                    label="Payment Method"
                    onChange={(e) => handleMethodFilterChange(e.target.value)}
                  >
                    <MenuItem value="">All Methods</MenuItem>
                    <MenuItem value="online">Online</MenuItem>
                    <MenuItem value="bank_transfer">Bank Transfer</MenuItem>
                    <MenuItem value="cash">Cash</MenuItem>
                    <MenuItem value="cheque">Cheque</MenuItem>
                    <MenuItem value="upi">UPI</MenuItem>
                  </Select>
                </FormControl>
              </Grid>
              <Grid item xs={12} md={2}>
                <Button
                  fullWidth
                  variant="outlined"
                  startIcon={<FilterIcon />}
                  onClick={() => {
                    setSearchTerm('');
                    setStatusFilter('');
                    setMethodFilter('');
                    setPage(1);
                  }}
                >
                  Clear
                </Button>
              </Grid>
            </Grid>
          </CardContent>
        </Card>

        {/* Payments Table */}
        <Card>
          <CardContent>
            {paymentsLoading ? (
              <Box display="flex" justifyContent="center" p={3}>
                <LoadingSpinner />
              </Box>
            ) : paymentsData?.data?.length === 0 ? (
              <Box textAlign="center" p={3}>
                <Typography variant="h6" color="textSecondary">
                  No payments found
                </Typography>
                <Typography variant="body2" color="textSecondary" sx={{ mt: 1 }}>
                  {searchTerm || statusFilter || methodFilter
                    ? 'Try adjusting your filters'
                    : 'Record your first payment to get started'
                  }
                </Typography>
              </Box>
            ) : (
              <>
                <TableContainer>
                  <Table>
                    <TableHead>
                      <TableRow>
                        <TableCell>Tenant</TableCell>
                        <TableCell>Unit</TableCell>
                        <TableCell>Amount</TableCell>
                        <TableCell>Payment Date</TableCell>
                        <TableCell>Method</TableCell>
                        <TableCell>Status</TableCell>
                        <TableCell>Actions</TableCell>
                      </TableRow>
                    </TableHead>
                    <TableBody>
                      {paymentsData?.data?.map((payment: Payment) => (
                        <TableRow key={payment.id}>
                          <TableCell>
                            <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
                              <Avatar>
                                {payment.tenant?.name?.charAt(0) || 'T'}
                              </Avatar>
                              <Box>
                                <Typography variant="subtitle2">
                                  {payment.tenant?.name || 'Unknown Tenant'}
                                </Typography>
                                <Typography variant="body2" color="textSecondary">
                                  {payment.tenant?.email || ''}
                                </Typography>
                              </Box>
                            </Box>
                          </TableCell>
                          <TableCell>
                            <Typography variant="subtitle2">
                              {payment.unit?.unit_number || 'N/A'}
                            </Typography>
                          </TableCell>
                          <TableCell>
                            <Typography variant="subtitle2">
                              {formatCurrency(payment.amount)}
                            </Typography>
                          </TableCell>
                          <TableCell>
                            <Typography variant="body2">
                              {formatDateTime(payment.payment_date)}
                            </Typography>
                          </TableCell>
                          <TableCell>
                            <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                              {getPaymentMethodIcon(payment.payment_method)}
                              <Typography variant="body2">
                                {payment.payment_method.replace('_', ' ')}
                              </Typography>
                            </Box>
                          </TableCell>
                          <TableCell>
                            <Chip
                              label={payment.status}
                              color={getStatusColor(payment.status) as any}
                              size="small"
                            />
                          </TableCell>
                          <TableCell>
                            <IconButton
                              size="small"
                              onClick={(e) => handleMenuOpen(e, payment)}
                            >
                              <MoreIcon />
                            </IconButton>
                          </TableCell>
                        </TableRow>
                      ))}
                    </TableBody>
                  </Table>
                </TableContainer>

                {/* Pagination */}
                {paymentsData?.total > perPage && (
                  <Box display="flex" justifyContent="center" mt={2}>
                    <Pagination
                      count={Math.ceil(paymentsData.total / perPage)}
                      page={page}
                      onChange={handlePageChange}
                      color="primary"
                    />
                  </Box>
                )}
              </>
            )}
          </CardContent>
        </Card>
      </Box>

      {/* Action Menu */}
      <Menu
        anchorEl={anchorEl}
        open={Boolean(anchorEl)}
        onClose={handleMenuClose}
      >
        <MenuItem onClick={handleMenuClose}>
          <ListItemIcon>
            <EditIcon fontSize="small" />
          </ListItemIcon>
          <ListItemText>Edit Payment</ListItemText>
        </MenuItem>
        <MenuItem onClick={() => handleMenuClose()}>
          <ListItemIcon>
            <ViewIcon fontSize="small" />
          </ListItemIcon>
          <ListItemText>View Details</ListItemText>
        </MenuItem>
        <MenuItem onClick={() => handleMenuClose()}>
          <ListItemIcon>
            <DownloadIcon fontSize="small" />
          </ListItemIcon>
          <ListItemText>Download Receipt</ListItemText>
        </MenuItem>
        <Divider />
        <MenuItem onClick={handleDeletePayment}>
          <ListItemIcon>
            <DeleteIcon fontSize="small" color="error" />
          </ListItemIcon>
          <ListItemText>Delete Payment</ListItemText>
        </MenuItem>
      </Menu>

      {/* Delete Confirmation Dialog */}
      <ConfirmDialog
        open={deleteDialogOpen}
        title="Delete Payment"
        message={`Are you sure you want to delete this payment? This action cannot be undone.`}
        onConfirm={confirmDeletePayment}
        onCancel={() => {
          setDeleteDialogOpen(false);
          setPaymentToDelete(null);
        }}
      />
    </DashboardLayout>
  );
}