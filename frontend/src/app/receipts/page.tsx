'use client';

import React, { useState, useEffect } from 'react';
import {
  Box,
  Card,
  CardContent,
  <PERSON><PERSON><PERSON>,
  Button,
  Chip,
  IconButton,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Paper,
  Avatar,
  Stack,
  Grid,
  TextField,
  InputAdornment,
  Menu,
  MenuItem,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Alert,
  LinearProgress,
  Tooltip,
  Badge,
  Tabs,
  Tab,
  Divider,
  List,
  ListItem,
  ListItemText,
  ListItemIcon,
  FormControl,
  InputLabel,
  Select,
  Switch,
  FormControlLabel,
  Stepper,
  Step,
  StepLabel,
  StepContent,
} from '@mui/material';
import {
  Add as AddIcon,
  Search as SearchIcon,
  FilterList as FilterIcon,
  MoreVert as MoreIcon,
  Receipt as ReceiptIcon,
  ReceiptLong as ReceiptLongIcon,
  Assignment as AssignmentIcon,
  TrendingUp as TrendingUpIcon,
  Warning as WarningIcon,
  CheckCircle as CheckCircleIcon,
  Error as ErrorIcon,
  Edit as EditIcon,
  Visibility as ViewIcon,
  Delete as DeleteIcon,
  Download as DownloadIcon,
  Settings as SettingsIcon,
  Assessment as AssessmentIcon,
  Email as EmailIcon,
  Print as PrintIcon,
  Share as ShareIcon,
  Schedule as ScheduleIcon,
  CloudDownload as CloudDownloadIcon,
  CloudUpload as CloudUploadIcon,
  Description as TemplateIcon,
} from '@mui/icons-material';
import { DashboardLayout } from '@/components/layout/DashboardLayout';
import { PageHeader } from '@/components/layout/PageHeader';
import { DataTable } from '@/components/data-display/DataTable';
import { InfoCard } from '@/components/data-display/InfoCard';
import { LoadingSpinner } from '@/components/feedback/LoadingSpinner';
import { ConfirmDialog } from '@/components/feedback/ConfirmDialog';
import { useAuth } from '@/hooks/useAuth';
import { useReceipts, useReceiptStats } from '@/hooks/useReceipts';
import { formatCurrency, formatDate, formatTime } from '@/utils/format';

interface Receipt {
  id: string;
  paymentId: string;
  receiptNumber: string;
  tenantId: string;
  tenantName: string;
  tenantEmail: string;
  unitId: string;
  unitNumber: string;
  amount: number;
  paymentMethod: string;
  paymentDate: string;
  generatedAt: string;
  deliveredAt?: string;
  status: 'generated' | 'delivered' | 'failed' | 'pending';
  templateId: string;
  templateName: string;
  downloadUrl?: string;
  emailSent: boolean;
  smsSent: boolean;
  notes?: string;
  metadata?: Record<string, any>;
}

interface ReceiptTemplate {
  id: string;
  name: string;
  description: string;
  isActive: boolean;
  isDefault: boolean;
  headerLogo?: string;
  footerText?: string;
  customFields: string[];
  createdAt: string;
  updatedAt: string;
}

interface ReceiptStats {
  totalReceipts: number;
  generatedReceipts: number;
  deliveredReceipts: number;
  failedReceipts: number;
  pendingReceipts: number;
  averageDeliveryTime: number;
  deliverySuccessRate: number;
  totalTemplates: number;
  activeTemplates: number;
}

const mockReceipts: Receipt[] = [
  {
    id: '1',
    paymentId: 'payment-1',
    receiptNumber: 'RCP-2024-001',
    tenantId: 'tenant-1',
    tenantName: 'John Doe',
    tenantEmail: '<EMAIL>',
    unitId: 'unit-1',
    unitNumber: 'A-101',
    amount: 15000,
    paymentMethod: 'online',
    paymentDate: '2024-01-15T10:30:00Z',
    generatedAt: '2024-01-15T10:35:00Z',
    deliveredAt: '2024-01-15T10:40:00Z',
    status: 'delivered',
    templateId: 'template-1',
    templateName: 'Standard Receipt',
    downloadUrl: '/receipts/1/download',
    emailSent: true,
    smsSent: false,
    notes: 'Rent payment receipt for January 2024',
    metadata: {
      paymentReference: 'PAY-001',
      transactionId: 'TXN-123456'
    }
  },
  {
    id: '2',
    paymentId: 'payment-2',
    receiptNumber: 'RCP-2024-002',
    tenantId: 'tenant-2',
    tenantName: 'Sarah Wilson',
    tenantEmail: '<EMAIL>',
    unitId: 'unit-2',
    unitNumber: 'B-201',
    amount: 18000,
    paymentMethod: 'bank_transfer',
    paymentDate: '2024-01-16T14:20:00Z',
    generatedAt: '2024-01-16T14:25:00Z',
    deliveredAt: '2024-01-16T14:30:00Z',
    status: 'delivered',
    templateId: 'template-1',
    templateName: 'Standard Receipt',
    downloadUrl: '/receipts/2/download',
    emailSent: true,
    smsSent: true,
    notes: 'Rent payment receipt for January 2024',
    metadata: {
      paymentReference: 'PAY-002',
      transactionId: 'TXN-123457'
    }
  },
  {
    id: '3',
    paymentId: 'payment-3',
    receiptNumber: 'RCP-2024-003',
    tenantId: 'tenant-3',
    tenantName: 'David Brown',
    tenantEmail: '<EMAIL>',
    unitId: 'unit-3',
    unitNumber: 'C-301',
    amount: 12000,
    paymentMethod: 'cash',
    paymentDate: '2024-01-17T09:15:00Z',
    generatedAt: '2024-01-17T09:20:00Z',
    status: 'generated',
    templateId: 'template-2',
    templateName: 'Premium Receipt',
    downloadUrl: '/receipts/3/download',
    emailSent: false,
    smsSent: false,
    notes: 'Rent payment receipt for January 2024',
    metadata: {
      paymentReference: 'PAY-003',
      transactionId: 'TXN-123458'
    }
  },
  {
    id: '4',
    paymentId: 'payment-4',
    receiptNumber: 'RCP-2024-004',
    tenantId: 'tenant-4',
    tenantName: 'Emily Johnson',
    tenantEmail: '<EMAIL>',
    unitId: 'unit-4',
    unitNumber: 'D-401',
    amount: 20000,
    paymentMethod: 'upi',
    paymentDate: '2024-01-18T11:45:00Z',
    generatedAt: '2024-01-18T11:50:00Z',
    status: 'failed',
    templateId: 'template-1',
    templateName: 'Standard Receipt',
    emailSent: false,
    smsSent: false,
    notes: 'Rent payment receipt for January 2024 - Failed to deliver',
    metadata: {
      paymentReference: 'PAY-004',
      transactionId: 'TXN-123459'
    }
  }
];

const mockTemplates: ReceiptTemplate[] = [
  {
    id: 'template-1',
    name: 'Standard Receipt',
    description: 'Default receipt template with basic information',
    isActive: true,
    isDefault: true,
    headerLogo: '/logo.png',
    footerText: 'Thank you for your payment',
    customFields: ['payment_reference', 'transaction_id', 'unit_details'],
    createdAt: '2024-01-01T00:00:00Z',
    updatedAt: '2024-01-15T10:00:00Z'
  },
  {
    id: 'template-2',
    name: 'Premium Receipt',
    description: 'Premium receipt template with enhanced design',
    isActive: true,
    isDefault: false,
    headerLogo: '/logo-premium.png',
    footerText: 'Thank you for choosing our services',
    customFields: ['payment_reference', 'transaction_id', 'unit_details', 'payment_breakdown'],
    createdAt: '2024-01-10T00:00:00Z',
    updatedAt: '2024-01-15T10:00:00Z'
  },
  {
    id: 'template-3',
    name: 'Simple Receipt',
    description: 'Minimal receipt template for basic needs',
    isActive: false,
    isDefault: false,
    footerText: 'Payment received',
    customFields: ['payment_reference'],
    createdAt: '2024-01-05T00:00:00Z',
    updatedAt: '2024-01-15T10:00:00Z'
  }
];

const mockStats: ReceiptStats = {
  totalReceipts: 45,
  generatedReceipts: 45,
  deliveredReceipts: 40,
  failedReceipts: 3,
  pendingReceipts: 2,
  averageDeliveryTime: 2.5,
  deliverySuccessRate: 88.9,
  totalTemplates: 3,
  activeTemplates: 2
};

export default function ReceiptsPage() {
  const { user } = useAuth();
  const [searchTerm, setSearchTerm] = useState('');
  const [statusFilter, setStatusFilter] = useState<string>('all');
  const [templateFilter, setTemplateFilter] = useState<string>('all');

  // Use real hooks for data fetching
  const { data: receiptsData, isLoading: receiptsLoading, error: receiptsError } = useReceipts({
    status: statusFilter !== 'all' && ['delivered', 'failed', 'pending'].includes(statusFilter)
      ? statusFilter as 'delivered' | 'failed' | 'pending'
      : undefined,
  });

  const { data: statsData, isLoading: statsLoading } = useReceiptStats();

  const receipts = receiptsData?.data || mockReceipts;
  const stats = statsData || mockStats;
  const loading = receiptsLoading || statsLoading;
  const error = receiptsError ? 'Failed to fetch receipts' : null;
  const templates = mockTemplates; // Keep templates as mock for now
  const [tabValue, setTabValue] = useState(0);
  const [selectedReceipt, setSelectedReceipt] = useState<Receipt | null>(null);
  const [selectedTemplate, setSelectedTemplate] = useState<ReceiptTemplate | null>(null);
  const [showReceiptDialog, setShowReceiptDialog] = useState(false);
  const [showTemplateDialog, setShowTemplateDialog] = useState(false);
  const [showDeleteDialog, setShowDeleteDialog] = useState(false);
  const [anchorEl, setAnchorEl] = useState<null | HTMLElement>(null);
  const [selectedItem, setSelectedItem] = useState<any>(null);

  // No need for useEffect and fetchReceipts since we're using hooks

  const handleSearch = (event: React.ChangeEvent<HTMLInputElement>) => {
    setSearchTerm(event.target.value);
  };

  const handleStatusFilter = (status: string) => {
    setStatusFilter(status);
  };

  const handleTemplateFilter = (template: string) => {
    setTemplateFilter(template);
  };

  const handleTabChange = (event: React.SyntheticEvent, newValue: number) => {
    setTabValue(newValue);
  };

  const handleAddTemplate = () => {
    setSelectedTemplate(null);
    setShowTemplateDialog(true);
  };

  const handleEditTemplate = (template: ReceiptTemplate) => {
    setSelectedTemplate(template);
    setShowTemplateDialog(true);
  };

  const handleDeleteTemplate = (template: ReceiptTemplate) => {
    setSelectedItem(template);
    setShowDeleteDialog(true);
  };

  const handleMenuClick = (event: React.MouseEvent<HTMLElement>, item: any) => {
    setAnchorEl(event.currentTarget);
    setSelectedItem(item);
  };

  const handleMenuClose = () => {
    setAnchorEl(null);
    setSelectedItem(null);
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'delivered': return 'success';
      case 'generated': return 'info';
      case 'pending': return 'warning';
      case 'failed': return 'error';
      default: return 'default';
    }
  };

  const getDeliveryIcon = (receipt: Receipt) => {
    if (receipt.emailSent && receipt.smsSent) {
      return <EmailIcon color="success" />;
    } else if (receipt.emailSent) {
      return <EmailIcon color="primary" />;
    } else if (receipt.smsSent) {
      return <ShareIcon color="secondary" />;
    } else {
      return <WarningIcon color="warning" />;
    }
  };

  const filteredReceipts = receipts.filter(receipt => {
    // Handle both mock data (camelCase) and real API data (snake_case with nested objects)
    const tenantName = 'tenantName' in receipt ? receipt.tenantName : receipt.payment?.tenant?.name || '';
    const unitNumber = 'unitNumber' in receipt ? receipt.unitNumber : receipt.payment?.unit?.unit_number || '';
    const receiptNumber = 'receiptNumber' in receipt ? receipt.receiptNumber : receipt.receipt_number || '';
    const templateId = 'templateId' in receipt ? receipt.templateId : '';

    const matchesSearch =
      tenantName.toLowerCase().includes(searchTerm.toLowerCase()) ||
      unitNumber.toLowerCase().includes(searchTerm.toLowerCase()) ||
      receiptNumber.toLowerCase().includes(searchTerm.toLowerCase());
    const matchesStatus = statusFilter === 'all' || receipt.status === statusFilter;
    const matchesTemplate = templateFilter === 'all' || templateId === templateFilter;

    return matchesSearch && matchesStatus && matchesTemplate;
  });

  // Removed unused mock user and handlers

  if (loading && receipts.length === 0) {
    return (
      <DashboardLayout>
        <LoadingSpinner />
      </DashboardLayout>
    );
  }

  return (
    <DashboardLayout>
      <PageHeader
        title="Receipt Management"
        subtitle="Generate, manage, and deliver receipts with automated templates and tracking"
        actions={[
          {
            label: 'Add Template',
            onClick: handleAddTemplate,
            variant: 'contained',
            startIcon: <AddIcon />
          },
          {
            label: 'Bulk Generate',
            onClick: () => console.log('Bulk generate receipts'),
            variant: 'outlined'
          }
        ]}
        breadcrumbs={[
          { label: 'Dashboard', href: '/' },
          { label: 'Financial Management', href: '/billing' },
          { label: 'Receipts' }
        ]}
      />

      {error && (
        <Alert severity="error" sx={{ mb: 3 }}>
          {error}
        </Alert>
      )}

      {/* Statistics Cards */}
      <Grid container spacing={3} sx={{ mb: 3 }}>
        <Grid item xs={12} sm={6} md={3}>
          <InfoCard
            title="Total Receipts"
            value={('totalReceipts' in stats ? stats.totalReceipts : stats.total_receipts).toString()}
            icon={<ReceiptIcon />}
            color="primary"
          />
        </Grid>
        <Grid item xs={12} sm={6} md={3}>
          <InfoCard
            title="Delivered"
            value={('deliveredReceipts' in stats ? stats.deliveredReceipts : stats.delivered_receipts).toString()}
            icon={<CheckCircleIcon />}
            color="success"
          />
        </Grid>
        <Grid item xs={12} sm={6} md={3}>
          <InfoCard
            title="Success Rate"
            value={`${('deliverySuccessRate' in stats ? stats.deliverySuccessRate : stats.delivery_rate)}%`}
            icon={<TrendingUpIcon />}
            color="info"
          />
        </Grid>
        <Grid item xs={12} sm={6} md={3}>
          <InfoCard
            title="Templates"
            value={('activeTemplates' in stats ? stats.activeTemplates : 3).toString()}
            icon={<TemplateIcon />}
            color="secondary"
          />
        </Grid>
      </Grid>

      {/* Search and Filters */}
      <Card sx={{ mb: 3 }}>
        <CardContent>
          <Grid container spacing={2} alignItems="center">
            <Grid item xs={12} md={4}>
              <TextField
                fullWidth
                placeholder="Search by tenant, unit, or receipt number..."
                value={searchTerm}
                onChange={handleSearch}
                InputProps={{
                  startAdornment: (
                    <InputAdornment position="start">
                      <SearchIcon />
                    </InputAdornment>
                  ),
                }}
              />
            </Grid>
            <Grid item xs={12} md={3}>
              <FormControl fullWidth>
                <InputLabel>Status Filter</InputLabel>
                <Select
                  value={statusFilter}
                  onChange={(e) => handleStatusFilter(e.target.value)}
                  label="Status Filter"
                >
                  <MenuItem value="all">All Status</MenuItem>
                  <MenuItem value="delivered">Delivered</MenuItem>
                  <MenuItem value="generated">Generated</MenuItem>
                  <MenuItem value="pending">Pending</MenuItem>
                  <MenuItem value="failed">Failed</MenuItem>
                </Select>
              </FormControl>
            </Grid>
            <Grid item xs={12} md={3}>
              <FormControl fullWidth>
                <InputLabel>Template Filter</InputLabel>
                <Select
                  value={templateFilter}
                  onChange={(e) => handleTemplateFilter(e.target.value)}
                  label="Template Filter"
                >
                  <MenuItem value="all">All Templates</MenuItem>
                  {templates.map(template => (
                    <MenuItem key={template.id} value={template.id}>
                      {template.name}
                    </MenuItem>
                  ))}
                </Select>
              </FormControl>
            </Grid>
            <Grid item xs={12} md={2}>
              <Button
                fullWidth
                variant="outlined"
                startIcon={<FilterIcon />}
                onClick={() => console.log('Advanced filters')}
              >
                Filters
              </Button>
            </Grid>
          </Grid>
        </CardContent>
      </Card>

      {/* Tabs */}
      <Card>
        <Box sx={{ borderBottom: 1, borderColor: 'divider' }}>
          <Tabs value={tabValue} onChange={handleTabChange}>
            <Tab label="All Receipts" />
            <Tab label="Templates" />
            <Tab label="Analytics" />
          </Tabs>
        </Box>

        {/* All Receipts Tab */}
        {tabValue === 0 && (
          <Box sx={{ p: 3 }}>
            <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>
              <Typography variant="h6">
                All Receipts ({filteredReceipts.length})
              </Typography>
            </Box>

            <TableContainer component={Paper} elevation={0}>
              <Table>
                <TableHead>
                  <TableRow>
                    <TableCell>Receipt Number</TableCell>
                    <TableCell>Tenant</TableCell>
                    <TableCell>Unit</TableCell>
                    <TableCell>Amount</TableCell>
                    <TableCell>Generated</TableCell>
                    <TableCell>Delivered</TableCell>
                    <TableCell>Status</TableCell>
                    <TableCell>Delivery</TableCell>
                    <TableCell>Actions</TableCell>
                  </TableRow>
                </TableHead>
                <TableBody>
                  {filteredReceipts.map((receipt) => (
                    <TableRow key={receipt.id} hover>
                      <TableCell>
                        <Typography variant="body1" fontWeight="medium">
                          {'receiptNumber' in receipt ? receipt.receiptNumber : receipt.receipt_number}
                        </Typography>
                        <Typography variant="body2" color="text.secondary">
                          {'templateName' in receipt ? receipt.templateName : 'Standard Template'}
                        </Typography>
                      </TableCell>
                      <TableCell>
                        <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
                          <Avatar>
                            {('tenantName' in receipt ? receipt.tenantName : receipt.payment?.tenant?.name || 'T').charAt(0)}
                          </Avatar>
                          <Box>
                            <Typography variant="body1" fontWeight="medium">
                              {'tenantName' in receipt ? receipt.tenantName : receipt.payment?.tenant?.name || 'Unknown'}
                            </Typography>
                            <Typography variant="body2" color="text.secondary">
                              {'tenantEmail' in receipt ? receipt.tenantEmail : receipt.payment?.tenant?.email || receipt.recipient_email}
                            </Typography>
                          </Box>
                        </Box>
                      </TableCell>
                      <TableCell>
                        <Typography variant="body1">
                          {'unitNumber' in receipt ? receipt.unitNumber : receipt.payment?.unit?.unit_number || 'N/A'}
                        </Typography>
                      </TableCell>
                      <TableCell>
                        <Typography variant="body1" fontWeight="medium">
                          {formatCurrency((receipt as any).amount || (receipt as any).payment?.amount || 0)}
                        </Typography>
                        <Typography variant="body2" color="text.secondary">
                          {((receipt as any).paymentMethod || (receipt as any).payment_method || 'Unknown').replace('_', ' ')}
                        </Typography>
                      </TableCell>
                      <TableCell>
                        <Typography variant="body2">
                          {formatDate((receipt as any).generatedAt || (receipt as any).generated_at || (receipt as any).created_at)}
                        </Typography>
                        <Typography variant="body2" color="text.secondary">
                          {formatTime((receipt as any).generatedAt || (receipt as any).generated_at || (receipt as any).created_at)}
                        </Typography>
                      </TableCell>
                      <TableCell>
                        {(receipt as any).deliveredAt || (receipt as any).delivered_at ? (
                          <>
                            <Typography variant="body2">
                              {formatDate((receipt as any).deliveredAt || (receipt as any).delivered_at)}
                            </Typography>
                            <Typography variant="body2" color="text.secondary">
                              {formatTime((receipt as any).deliveredAt || (receipt as any).delivered_at)}
                            </Typography>
                          </>
                        ) : (
                          <Typography variant="body2" color="text.secondary">
                            Not delivered
                          </Typography>
                        )}
                      </TableCell>
                      <TableCell>
                        <Chip
                          label={receipt.status}
                          color={getStatusColor(receipt.status) as any}
                          size="small"
                        />
                      </TableCell>
                      <TableCell>
                        <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                          {getDeliveryIcon(receipt as any)}
                          <Box>
                            <Typography variant="body2" fontSize="0.75rem">
                              {(receipt as any).emailSent || (receipt as any).email_sent ? 'Email ✓' : 'Email ✗'}
                            </Typography>
                            <Typography variant="body2" fontSize="0.75rem">
                              {(receipt as any).smsSent || (receipt as any).sms_sent ? 'SMS ✓' : 'SMS ✗'}
                            </Typography>
                          </Box>
                        </Box>
                      </TableCell>
                      <TableCell>
                        <Box sx={{ display: 'flex', gap: 1 }}>
                          <Tooltip title="View Receipt">
                            <IconButton size="small">
                              <ViewIcon />
                            </IconButton>
                          </Tooltip>
                          <Tooltip title="Download Receipt">
                            <IconButton size="small">
                              <DownloadIcon />
                            </IconButton>
                          </Tooltip>
                          <Tooltip title="Print Receipt">
                            <IconButton size="small">
                              <PrintIcon />
                            </IconButton>
                          </Tooltip>
                          <Tooltip title="More Actions">
                            <IconButton
                              size="small"
                              onClick={(e) => handleMenuClick(e, receipt)}
                            >
                              <MoreIcon />
                            </IconButton>
                          </Tooltip>
                        </Box>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </TableContainer>
          </Box>
        )}

        {/* Templates Tab */}
        {tabValue === 1 && (
          <Box sx={{ p: 3 }}>
            <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>
              <Typography variant="h6">
                Receipt Templates ({templates.length})
              </Typography>
              <Button
                variant="contained"
                startIcon={<AddIcon />}
                onClick={handleAddTemplate}
              >
                Add Template
              </Button>
            </Box>

            <Grid container spacing={3}>
              {templates.map((template) => (
                <Grid item xs={12} sm={6} md={4} key={template.id}>
                  <Card>
                    <CardContent>
                      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'flex-start', mb: 2 }}>
                        <Box>
                          <Typography variant="h6">
                            {template.name}
                            {template.isDefault && (
                              <Chip label="Default" size="small" color="primary" sx={{ ml: 1 }} />
                            )}
                          </Typography>
                          <Typography variant="body2" color="text.secondary">
                            {template.description}
                          </Typography>
                        </Box>
                        <FormControlLabel
                          control={
                            <Switch
                              checked={template.isActive}
                              onChange={() => console.log('Toggle template')}
                            />
                          }
                          label=""
                        />
                      </Box>
                      
                      <Box sx={{ mb: 2 }}>
                        <Typography variant="body2" color="text.secondary" gutterBottom>
                          Custom Fields:
                        </Typography>
                        <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 0.5 }}>
                          {template.customFields.map((field) => (
                            <Chip
                              key={field}
                              label={field.replace('_', ' ')}
                              size="small"
                              variant="outlined"
                            />
                          ))}
                        </Box>
                      </Box>

                      <Box sx={{ display: 'flex', gap: 1 }}>
                        <Button size="small" variant="outlined" startIcon={<EditIcon />}>
                          Edit
                        </Button>
                        <Button size="small" variant="outlined" startIcon={<ViewIcon />}>
                          Preview
                        </Button>
                        {!template.isDefault && (
                          <Button size="small" variant="outlined" startIcon={<DeleteIcon />}>
                            Delete
                          </Button>
                        )}
                      </Box>
                    </CardContent>
                  </Card>
                </Grid>
              ))}
            </Grid>
          </Box>
        )}

        {/* Analytics Tab */}
        {tabValue === 2 && (
          <Box sx={{ p: 3 }}>
            <Typography variant="h6" gutterBottom>
              Receipt Analytics
            </Typography>
            <Typography variant="body2" color="text.secondary" sx={{ mb: 3 }}>
              Comprehensive receipt analytics and delivery performance
            </Typography>
            
            <Grid container spacing={3}>
              <Grid item xs={12} md={6}>
                <Card>
                  <CardContent>
                    <Typography variant="h6" gutterBottom>
                      Receipt Generation Trends
                    </Typography>
                    <Typography variant="body2" color="text.secondary">
                      Receipt generation trends chart will be displayed here
                    </Typography>
                  </CardContent>
                </Card>
              </Grid>
              <Grid item xs={12} md={6}>
                <Card>
                  <CardContent>
                    <Typography variant="h6" gutterBottom>
                      Delivery Success Rate
                    </Typography>
                    <Typography variant="body2" color="text.secondary">
                      Delivery success rate chart will be displayed here
                    </Typography>
                  </CardContent>
                </Card>
              </Grid>
              <Grid item xs={12} md={6}>
                <Card>
                  <CardContent>
                    <Typography variant="h6" gutterBottom>
                      Template Usage
                    </Typography>
                    <Typography variant="body2" color="text.secondary">
                      Template usage distribution will be displayed here
                    </Typography>
                  </CardContent>
                </Card>
              </Grid>
              <Grid item xs={12} md={6}>
                <Card>
                  <CardContent>
                    <Typography variant="h6" gutterBottom>
                      Delivery Methods
                    </Typography>
                    <Typography variant="body2" color="text.secondary">
                      Email vs SMS delivery statistics will be displayed here
                    </Typography>
                  </CardContent>
                </Card>
              </Grid>
            </Grid>
          </Box>
        )}
      </Card>

      {/* Action Menu */}
      <Menu
        anchorEl={anchorEl}
        open={Boolean(anchorEl)}
        onClose={handleMenuClose}
      >
        <MenuItem onClick={handleMenuClose}>
          <ListItemIcon>
            <ViewIcon fontSize="small" />
          </ListItemIcon>
          View Receipt
        </MenuItem>
        <MenuItem onClick={handleMenuClose}>
          <ListItemIcon>
            <DownloadIcon fontSize="small" />
          </ListItemIcon>
          Download Receipt
        </MenuItem>
        <MenuItem onClick={handleMenuClose}>
          <ListItemIcon>
            <PrintIcon fontSize="small" />
          </ListItemIcon>
          Print Receipt
        </MenuItem>
        <MenuItem onClick={handleMenuClose}>
          <ListItemIcon>
            <EmailIcon fontSize="small" />
          </ListItemIcon>
          Resend Email
        </MenuItem>
        <Divider />
        <MenuItem onClick={handleMenuClose} sx={{ color: 'error.main' }}>
          <ListItemIcon>
            <DeleteIcon fontSize="small" color="error" />
          </ListItemIcon>
          Delete Receipt
        </MenuItem>
      </Menu>

      {/* Template Dialog */}
      <Dialog
        open={showTemplateDialog}
        onClose={() => setShowTemplateDialog(false)}
        maxWidth="md"
        fullWidth
      >
        <DialogTitle>
          {selectedTemplate ? 'Edit Template' : 'Add Template'}
        </DialogTitle>
        <DialogContent>
          <Typography variant="body2" color="text.secondary" gutterBottom>
            Receipt template configuration form will be displayed here
          </Typography>
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setShowTemplateDialog(false)}>
            Cancel
          </Button>
          <Button variant="contained">
            {selectedTemplate ? 'Update Template' : 'Create Template'}
          </Button>
        </DialogActions>
      </Dialog>

      {/* Delete Confirmation Dialog */}
      <ConfirmDialog
        open={showDeleteDialog}
        title="Delete Template"
        message="Are you sure you want to delete this template? This action cannot be undone."
        onConfirm={() => {
          setShowDeleteDialog(false);
          // Handle delete logic here
        }}
        onCancel={() => setShowDeleteDialog(false)}
      />
    </DashboardLayout>
  );
} 