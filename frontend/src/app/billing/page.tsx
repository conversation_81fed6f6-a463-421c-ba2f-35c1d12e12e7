'use client';

import React, { useState, useEffect } from 'react';
import {
  Box,
  Card,
  CardContent,
  <PERSON>po<PERSON>,
  Button,
  Chip,
  IconButton,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Paper,
  Avatar,
  Stack,
  Grid,
  TextField,
  InputAdornment,
  Menu,
  MenuItem,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Alert,
  LinearProgress,
  Tooltip,
  Badge,
  Tabs,
  Tab,
  Divider,
  List,
  ListItem,
  ListItemText,
  ListItemIcon,
  FormControl,
  InputLabel,
  Select,
  Switch,
  FormControlLabel,
  Pagination,
} from '@mui/material';
import {
  Add as AddIcon,
  Search as SearchIcon,
  FilterList as FilterIcon,
  MoreVert as MoreIcon,
  Payment as PaymentIcon,
  Receipt as ReceiptIcon,
  Assignment as AssignmentIcon,
  TrendingUp as TrendingUpIcon,
  Warning as WarningIcon,
  CheckCircle as CheckCircleIcon,
  Error as ErrorIcon,
  Edit as EditIcon,
  Visibility as ViewIcon,
  Delete as DeleteIcon,
  Download as DownloadIcon,
  Settings as SettingsIcon,
  Assessment as AssessmentIcon,
  AccountBalance as AccountBalanceIcon,
  AttachMoney as MoneyIcon,
  Schedule as ScheduleIcon,
} from '@mui/icons-material';
import { DashboardLayout } from '@/components/layout/DashboardLayout';
import { PageHeader } from '@/components/layout/PageHeader';
import { DataTable } from '@/components/data-display/DataTable';
import { InfoCard } from '@/components/data-display/InfoCard';
import { LoadingSpinner } from '@/components/feedback/LoadingSpinner';
import { ConfirmDialog } from '@/components/feedback/ConfirmDialog';
import { useAuth } from '@/hooks/useAuth';
import { useBillingRules, useBillingRecords } from '@/hooks/useBilling';
import { BillingRule, BillingRecord } from '@/services/billing.service';

// Types are now imported from the service

interface BillingStats {
  totalRevenue: number;
  monthlyRevenue: number;
  outstandingDues: number;
  pendingPayments: number;
  completedPayments: number;
  failedPayments: number;
  averagePaymentTime: number;
  collectionRate: number;
}

export default function BillingPage() {
  const { user } = useAuth();

  // State for filters and pagination
  const [activeTab, setActiveTab] = useState(0);
  const [searchTerm, setSearchTerm] = useState('');
  const [typeFilter, setTypeFilter] = useState('');
  const [statusFilter, setStatusFilter] = useState('');
  const [page, setPage] = useState(1);
  const [perPage] = useState(15);

  // Billing Rules filters
  const billingRulesFilters = {
    search: searchTerm,
    type: typeFilter || undefined,
    is_active: statusFilter === 'active' ? true : statusFilter === 'inactive' ? false : undefined,
    page,
    per_page: perPage,
  };

  // Billing Records filters
  const billingRecordsFilters = {
    search: searchTerm,
    charge_type: typeFilter || undefined,
    status: statusFilter || undefined,
    page,
    per_page: perPage,
  };

  // Fetch data using hooks
  const {
    data: billingRulesData,
    loading: billingRulesLoading,
    error: billingRulesError,
    createBillingRule,
    updateBillingRule,
    deleteBillingRule,
  } = useBillingRules(billingRulesFilters);

  const {
    data: billingRecordsData,
    loading: billingRecordsLoading,
    error: billingRecordsError,
  } = useBillingRecords(billingRecordsFilters);

  // Dialog states
  const [ruleDialogOpen, setRuleDialogOpen] = useState(false);
  const [selectedRule, setSelectedRule] = useState<BillingRule | null>(null);
  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false);
  const [ruleToDelete, setRuleToDelete] = useState<BillingRule | null>(null);

  // Menu states
  const [anchorEl, setAnchorEl] = useState<null | HTMLElement>(null);
  const [selectedRuleForMenu, setSelectedRuleForMenu] = useState<BillingRule | null>(null);

  // Calculate stats from real data
  const stats: BillingStats = {
    totalRevenue: billingRecordsData?.data?.reduce((sum: number, record: BillingRecord) =>
      record.status === 'paid' ? sum + parseFloat(record.amount.toString()) : sum, 0) || 0,
    monthlyRevenue: billingRecordsData?.data?.filter((record: BillingRecord) => {
      const recordDate = new Date(record.period_start);
      const currentMonth = new Date();
      return recordDate.getMonth() === currentMonth.getMonth() &&
             recordDate.getFullYear() === currentMonth.getFullYear() &&
             record.status === 'paid';
    }).reduce((sum: number, record: BillingRecord) => sum + parseFloat(record.amount.toString()), 0) || 0,
    outstandingDues: billingRecordsData?.data?.reduce((sum: number, record: BillingRecord) =>
      record.status === 'pending' || record.status === 'overdue' ? sum + parseFloat(record.amount.toString()) : sum, 0) || 0,
    pendingPayments: billingRecordsData?.data?.filter((record: BillingRecord) => record.status === 'pending').length || 0,
    completedPayments: billingRecordsData?.data?.filter((record: BillingRecord) => record.status === 'paid').length || 0,
    failedPayments: billingRecordsData?.data?.filter((record: BillingRecord) => record.status === 'cancelled').length || 0,
    averagePaymentTime: 3.5, // This would need more complex calculation
    collectionRate: billingRecordsData?.data?.length > 0 ?
      (billingRecordsData.data.filter((record: BillingRecord) => record.status === 'paid').length / billingRecordsData.data.length) * 100 : 0,
  };

  // Handle search
  const handleSearch = (value: string) => {
    setSearchTerm(value);
    setPage(1); // Reset to first page when searching
  };

  // Handle filter changes
  const handleTypeFilterChange = (value: string) => {
    setTypeFilter(value);
    setPage(1);
  };

  const handleStatusFilterChange = (value: string) => {
    setStatusFilter(value);
    setPage(1);
  };

  // Handle pagination
  const handlePageChange = (event: React.ChangeEvent<unknown>, value: number) => {
    setPage(value);
  };

  // Handle menu actions
  const handleMenuOpen = (event: React.MouseEvent<HTMLElement>, rule: BillingRule) => {
    setAnchorEl(event.currentTarget);
    setSelectedRuleForMenu(rule);
  };

  const handleMenuClose = () => {
    setAnchorEl(null);
    setSelectedRuleForMenu(null);
  };

  const handleEditRule = () => {
    setSelectedRule(selectedRuleForMenu);
    setRuleDialogOpen(true);
    handleMenuClose();
  };

  const handleDeleteRule = () => {
    setRuleToDelete(selectedRuleForMenu);
    setDeleteDialogOpen(true);
    handleMenuClose();
  };

  const confirmDeleteRule = async () => {
    if (ruleToDelete) {
      try {
        await deleteBillingRule(ruleToDelete.id);
        setDeleteDialogOpen(false);
        setRuleToDelete(null);
      } catch (error) {
        console.error('Failed to delete billing rule:', error);
      }
    }
  };

  // Get type label
  const getTypeLabel = (type: string) => {
    const labels: Record<string, string> = {
      maintenance: 'Maintenance',
      utilities: 'Utilities',
      parking: 'Parking',
      non_occupancy: 'Non-Occupancy',
      noc_charges: 'NOC Charges',
    };
    return labels[type] || type;
  };

  // Get status color
  const getStatusColor = (status: string) => {
    switch (status) {
      case 'paid': return 'success';
      case 'pending': return 'warning';
      case 'overdue': return 'error';
      case 'cancelled': return 'default';
      default: return 'default';
    }
  };

  // Format currency (SSR-safe)
  const formatCurrency = (amount: number) => {
    if (typeof window === 'undefined') {
      return `₹${amount.toString().replace(/\B(?=(\d{3})+(?!\d))/g, ',')}`;
    }
    return new Intl.NumberFormat('en-IN', {
      style: 'currency',
      currency: 'INR',
      minimumFractionDigits: 0,
      maximumFractionDigits: 0,
    }).format(amount);
  };

  // Format date (SSR-safe)
  const formatDate = (dateString: string) => {
    if (typeof window === 'undefined') {
      const d = new Date(dateString);
      return d.toISOString().split('T')[0];
    }
    return new Date(dateString).toLocaleDateString('en-IN', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
    });
  };

  // Loading state
  if (billingRulesLoading && billingRecordsLoading) {
    return (
      <DashboardLayout>
        <Box display="flex" justifyContent="center" alignItems="center" minHeight="400px">
          <LoadingSpinner />
        </Box>
      </DashboardLayout>
    );
  }

  // Error state
  if (billingRulesError || billingRecordsError) {
    return (
      <DashboardLayout>
        <Box p={3}>
          <Alert severity="error">
            {billingRulesError || billingRecordsError}
          </Alert>
        </Box>
      </DashboardLayout>
    );
  }

  return (
    <DashboardLayout>
      <PageHeader
        title="Billing Management"
        subtitle="Manage billing rules, records, and payment tracking"
        action={
          <Button
            variant="contained"
            startIcon={<AddIcon />}
            onClick={() => {
              setSelectedRule(null);
              setRuleDialogOpen(true);
            }}
          >
            Add Billing Rule
          </Button>
        }
      />

      <Box sx={{ p: 3 }}>
        {/* Statistics Cards */}
        <Grid container spacing={3} sx={{ mb: 3 }}>
          <Grid item xs={12} sm={6} md={3}>
            <InfoCard
              title="Total Revenue"
              value={formatCurrency(stats.totalRevenue)}
              icon={<MoneyIcon />}
              color="primary"
            />
          </Grid>
          <Grid item xs={12} sm={6} md={3}>
            <InfoCard
              title="Monthly Revenue"
              value={formatCurrency(stats.monthlyRevenue)}
              icon={<TrendingUpIcon />}
              color="success"
            />
          </Grid>
          <Grid item xs={12} sm={6} md={3}>
            <InfoCard
              title="Outstanding Dues"
              value={formatCurrency(stats.outstandingDues)}
              icon={<WarningIcon />}
              color="warning"
            />
          </Grid>
          <Grid item xs={12} sm={6} md={3}>
            <InfoCard
              title="Collection Rate"
              value={`${stats.collectionRate.toFixed(1)}%`}
              icon={<AssessmentIcon />}
              color="info"
            />
          </Grid>
        </Grid>

        {/* Tabs */}
        <Card sx={{ mb: 3 }}>
          <Tabs
            value={activeTab}
            onChange={(_, newValue) => setActiveTab(newValue)}
            sx={{ borderBottom: 1, borderColor: 'divider' }}
          >
            <Tab label="Billing Rules" />
            <Tab label="Billing Records" />
          </Tabs>
        </Card>

        {/* Filters */}
        <Card sx={{ mb: 3 }}>
          <CardContent>
            <Grid container spacing={2} alignItems="center">
              <Grid item xs={12} md={4}>
                <TextField
                  fullWidth
                  placeholder="Search..."
                  value={searchTerm}
                  onChange={(e) => handleSearch(e.target.value)}
                  InputProps={{
                    startAdornment: (
                      <InputAdornment position="start">
                        <SearchIcon />
                      </InputAdornment>
                    ),
                  }}
                />
              </Grid>
              <Grid item xs={12} md={3}>
                <FormControl fullWidth>
                  <InputLabel>Type</InputLabel>
                  <Select
                    value={typeFilter}
                    label="Type"
                    onChange={(e) => handleTypeFilterChange(e.target.value)}
                  >
                    <MenuItem value="">All Types</MenuItem>
                    <MenuItem value="maintenance">Maintenance</MenuItem>
                    <MenuItem value="utilities">Utilities</MenuItem>
                    <MenuItem value="parking">Parking</MenuItem>
                    <MenuItem value="non_occupancy">Non-Occupancy</MenuItem>
                    <MenuItem value="noc_charges">NOC Charges</MenuItem>
                  </Select>
                </FormControl>
              </Grid>
              <Grid item xs={12} md={3}>
                <FormControl fullWidth>
                  <InputLabel>Status</InputLabel>
                  <Select
                    value={statusFilter}
                    label="Status"
                    onChange={(e) => handleStatusFilterChange(e.target.value)}
                  >
                    <MenuItem value="">All Status</MenuItem>
                    {activeTab === 0 ? (
                      <>
                        <MenuItem value="active">Active</MenuItem>
                        <MenuItem value="inactive">Inactive</MenuItem>
                      </>
                    ) : (
                      <>
                        <MenuItem value="pending">Pending</MenuItem>
                        <MenuItem value="paid">Paid</MenuItem>
                        <MenuItem value="overdue">Overdue</MenuItem>
                        <MenuItem value="cancelled">Cancelled</MenuItem>
                      </>
                    )}
                  </Select>
                </FormControl>
              </Grid>
              <Grid item xs={12} md={2}>
                <Button
                  fullWidth
                  variant="outlined"
                  startIcon={<FilterIcon />}
                  onClick={() => {
                    setSearchTerm('');
                    setTypeFilter('');
                    setStatusFilter('');
                    setPage(1);
                  }}
                >
                  Clear
                </Button>
              </Grid>
            </Grid>
          </CardContent>
        </Card>

        {/* Content based on active tab */}
        {activeTab === 0 ? (
          /* Billing Rules Tab */
          <Card>
            <CardContent>
              {billingRulesLoading ? (
                <Box display="flex" justifyContent="center" p={3}>
                  <LoadingSpinner />
                </Box>
              ) : billingRulesData?.data?.length === 0 ? (
                <Box textAlign="center" p={3}>
                  <Typography variant="h6" color="textSecondary">
                    No billing rules found
                  </Typography>
                  <Typography variant="body2" color="textSecondary" sx={{ mt: 1 }}>
                    Create your first billing rule to get started
                  </Typography>
                </Box>
              ) : (
                <>
                  <TableContainer>
                    <Table>
                      <TableHead>
                        <TableRow>
                          <TableCell>Name</TableCell>
                          <TableCell>Type</TableCell>
                          <TableCell>Amount</TableCell>
                          <TableCell>Calculation</TableCell>
                          <TableCell>Frequency</TableCell>
                          <TableCell>Status</TableCell>
                          <TableCell>Actions</TableCell>
                        </TableRow>
                      </TableHead>
                      <TableBody>
                        {billingRulesData?.data?.map((rule: BillingRule) => (
                          <TableRow key={rule.id}>
                            <TableCell>
                              <Box>
                                <Typography variant="subtitle2">
                                  {rule.name}
                                </Typography>
                                <Typography variant="body2" color="textSecondary">
                                  {rule.description}
                                </Typography>
                              </Box>
                            </TableCell>
                            <TableCell>
                              <Chip
                                label={getTypeLabel(rule.type)}
                                size="small"
                                variant="outlined"
                              />
                            </TableCell>
                            <TableCell>
                              {rule.calculation_method === 'percentage'
                                ? `${rule.amount}%`
                                : formatCurrency(rule.amount)
                              }
                            </TableCell>
                            <TableCell>
                              <Typography variant="body2">
                                {rule.calculation_method.replace('_', ' ')}
                              </Typography>
                            </TableCell>
                            <TableCell>
                              <Typography variant="body2">
                                {rule.frequency}
                              </Typography>
                            </TableCell>
                            <TableCell>
                              <Chip
                                label={rule.is_active ? 'Active' : 'Inactive'}
                                color={rule.is_active ? 'success' : 'default'}
                                size="small"
                              />
                            </TableCell>
                            <TableCell>
                              <IconButton
                                size="small"
                                onClick={(e) => handleMenuOpen(e, rule)}
                              >
                                <MoreIcon />
                              </IconButton>
                            </TableCell>
                          </TableRow>
                        ))}
                      </TableBody>
                    </Table>
                  </TableContainer>

                  {/* Pagination */}
                  {billingRulesData?.total > perPage && (
                    <Box display="flex" justifyContent="center" mt={2}>
                      <Pagination
                        count={Math.ceil(billingRulesData.total / perPage)}
                        page={page}
                        onChange={handlePageChange}
                        color="primary"
                      />
                    </Box>
                  )}
                </>
              )}
            </CardContent>
          </Card>
        ) : (
          /* Billing Records Tab */
          <Card>
            <CardContent>
              {billingRecordsLoading ? (
                <Box display="flex" justifyContent="center" p={3}>
                  <LoadingSpinner />
                </Box>
              ) : billingRecordsData?.data?.length === 0 ? (
                <Box textAlign="center" p={3}>
                  <Typography variant="h6" color="textSecondary">
                    No billing records found
                  </Typography>
                  <Typography variant="body2" color="textSecondary" sx={{ mt: 1 }}>
                    Billing records will appear here once generated
                  </Typography>
                </Box>
              ) : (
                <>
                  <TableContainer>
                    <Table>
                      <TableHead>
                        <TableRow>
                          <TableCell>Unit</TableCell>
                          <TableCell>Tenant</TableCell>
                          <TableCell>Type</TableCell>
                          <TableCell>Amount</TableCell>
                          <TableCell>Period</TableCell>
                          <TableCell>Due Date</TableCell>
                          <TableCell>Status</TableCell>
                          <TableCell>Actions</TableCell>
                        </TableRow>
                      </TableHead>
                      <TableBody>
                        {billingRecordsData?.data?.map((record: BillingRecord) => (
                          <TableRow key={record.id}>
                            <TableCell>
                              <Typography variant="subtitle2">
                                {record.unit?.unit_number || 'N/A'}
                              </Typography>
                            </TableCell>
                            <TableCell>
                              <Typography variant="body2">
                                {record.tenant?.name || 'N/A'}
                              </Typography>
                            </TableCell>
                            <TableCell>
                              <Chip
                                label={getTypeLabel(record.charge_type)}
                                size="small"
                                variant="outlined"
                              />
                            </TableCell>
                            <TableCell>
                              <Typography variant="subtitle2">
                                {formatCurrency(parseFloat(record.amount.toString()))}
                              </Typography>
                            </TableCell>
                            <TableCell>
                              <Typography variant="body2">
                                {formatDate(record.period_start)} - {formatDate(record.period_end)}
                              </Typography>
                            </TableCell>
                            <TableCell>
                              <Typography variant="body2">
                                {formatDate(record.due_date)}
                              </Typography>
                            </TableCell>
                            <TableCell>
                              <Chip
                                label={record.status}
                                color={getStatusColor(record.status) as any}
                                size="small"
                              />
                            </TableCell>
                            <TableCell>
                              <IconButton size="small">
                                <ViewIcon />
                              </IconButton>
                            </TableCell>
                          </TableRow>
                        ))}
                      </TableBody>
                    </Table>
                  </TableContainer>

                  {/* Pagination */}
                  {billingRecordsData?.total > perPage && (
                    <Box display="flex" justifyContent="center" mt={2}>
                      <Pagination
                        count={Math.ceil(billingRecordsData.total / perPage)}
                        page={page}
                        onChange={handlePageChange}
                        color="primary"
                      />
                    </Box>
                  )}
                </>
              )}
            </CardContent>
          </Card>
        )}
      </Box>

      {/* Action Menu */}
      <Menu
        anchorEl={anchorEl}
        open={Boolean(anchorEl)}
        onClose={handleMenuClose}
      >
        <MenuItem onClick={handleEditRule}>
          <ListItemIcon>
            <EditIcon fontSize="small" />
          </ListItemIcon>
          <ListItemText>Edit</ListItemText>
        </MenuItem>
        <MenuItem onClick={handleDeleteRule}>
          <ListItemIcon>
            <DeleteIcon fontSize="small" />
          </ListItemIcon>
          <ListItemText>Delete</ListItemText>
        </MenuItem>
      </Menu>

      {/* Delete Confirmation Dialog */}
      <ConfirmDialog
        open={deleteDialogOpen}
        title="Delete Billing Rule"
        message={`Are you sure you want to delete "${ruleToDelete?.name}"? This action cannot be undone.`}
        onConfirm={confirmDeleteRule}
        onCancel={() => {
          setDeleteDialogOpen(false);
          setRuleToDelete(null);
        }}
      />
    </DashboardLayout>
  );
}