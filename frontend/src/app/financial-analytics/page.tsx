'use client';

import React, { useState, useEffect } from 'react';
import {
  Box,
  Card,
  CardContent,
  Typography,
  Button,
  Chip,
  IconButton,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Paper,
  Grid,
  Menu,
  MenuItem,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Alert,
  Tooltip,
  Tabs,
  Tab,
  Divider,
  FormControl,
  InputLabel,
  Select,
  ListItemIcon,
} from '@mui/material';
import {
  MoreVert as MoreIcon,
  TrendingUp as TrendingUpIcon,
  TrendingDown as TrendingDownIcon,
  Assessment as AssessmentIcon,
  AccountBalance as AccountBalanceIcon,
  Schedule as ScheduleIcon,
  Edit as EditIcon,
  Visibility as ViewIcon,
  Delete as DeleteIcon,
  Download as DownloadIcon,
  Analytics as AnalyticsIcon,
  Payment as PaymentIcon,
  AccountBalanceWallet as WalletIcon,
  CreditCard as CreditCardIcon,
  LocalAtm as CashIcon,
  ReceiptLong as ReceiptLongIcon,
} from '@mui/icons-material';
import { DashboardLayout } from '@/components/layout/DashboardLayout';
import { PageHeader } from '@/components/layout/PageHeader';
import { InfoCard } from '@/components/data-display/InfoCard';
import { LoadingSpinner } from '@/components/feedback/LoadingSpinner';
import { ConfirmDialog } from '@/components/feedback/ConfirmDialog';
import { useAuth } from '@/hooks/useAuth';
import apiClient from '@/lib/api-client';

interface FinancialMetric {
  id: string;
  name: string;
  value: number;
  previousValue: number;
  change: number;
  changePercentage: number;
  trend: 'up' | 'down' | 'stable';
  currency: string;
  period: string;
}

interface RevenueData {
  month: string;
  revenue: number;
  expenses: number;
  profit: number;
  units: number;
  occupancy: number;
}

interface PaymentMethodData {
  method: string;
  count: number;
  amount: number;
  percentage: number;
}

interface OutstandingDue {
  id: string;
  tenantName: string;
  unitNumber: string;
  amount: number;
  dueDate: string;
  daysOverdue: number;
  status: 'overdue' | 'due_soon' | 'paid';
}

interface FinancialReport {
  id: string;
  name: string;
  type: 'monthly' | 'quarterly' | 'yearly' | 'custom';
  period: string;
  generatedAt: string;
  status: 'generated' | 'processing' | 'failed';
  downloadUrl?: string;
}

const mockMetrics: FinancialMetric[] = [
  {
    id: '1',
    name: 'Total Revenue',
    value: 450000,
    previousValue: 420000,
    change: 30000,
    changePercentage: 7.14,
    trend: 'up',
    currency: 'INR',
    period: 'This Month'
  },
  {
    id: '2',
    name: 'Monthly Revenue',
    value: 45000,
    previousValue: 42000,
    change: 3000,
    changePercentage: 7.14,
    trend: 'up',
    currency: 'INR',
    period: 'This Month'
  },
  {
    id: '3',
    name: 'Outstanding Dues',
    value: 25000,
    previousValue: 30000,
    change: -5000,
    changePercentage: -16.67,
    trend: 'down',
    currency: 'INR',
    period: 'This Month'
  },
  {
    id: '4',
    name: 'Collection Rate',
    value: 92.5,
    previousValue: 90.0,
    change: 2.5,
    changePercentage: 2.78,
    trend: 'up',
    currency: '%',
    period: 'This Month'
  },
  {
    id: '5',
    name: 'Average Payment Time',
    value: 3.5,
    previousValue: 4.2,
    change: -0.7,
    changePercentage: -16.67,
    trend: 'down',
    currency: 'days',
    period: 'This Month'
  },
  {
    id: '6',
    name: 'Occupancy Rate',
    value: 85.0,
    previousValue: 82.0,
    change: 3.0,
    changePercentage: 3.66,
    trend: 'up',
    currency: '%',
    period: 'This Month'
  }
];

const mockRevenueData: RevenueData[] = [
  { month: 'Jan', revenue: 45000, expenses: 15000, profit: 30000, units: 45, occupancy: 85 },
  { month: 'Feb', revenue: 48000, expenses: 16000, profit: 32000, units: 48, occupancy: 88 },
  { month: 'Mar', revenue: 52000, expenses: 17000, profit: 35000, units: 50, occupancy: 90 },
  { month: 'Apr', revenue: 49000, expenses: 16500, profit: 32500, units: 47, occupancy: 87 },
  { month: 'May', revenue: 51000, expenses: 17200, profit: 33800, units: 49, occupancy: 89 },
  { month: 'Jun', revenue: 54000, expenses: 18000, profit: 36000, units: 52, occupancy: 92 }
];

const mockPaymentMethods: PaymentMethodData[] = [
  { method: 'Online Payment', count: 25, amount: 375000, percentage: 45 },
  { method: 'Bank Transfer', count: 15, amount: 225000, percentage: 27 },
  { method: 'Cash', count: 8, amount: 120000, percentage: 15 },
  { method: 'UPI', count: 12, amount: 180000, percentage: 11 },
  { method: 'Cheque', count: 5, amount: 75000, percentage: 2 }
];

const mockOutstandingDues: OutstandingDue[] = [
  {
    id: '1',
    tenantName: 'John Doe',
    unitNumber: 'A-101',
    amount: 15000,
    dueDate: '2024-01-05',
    daysOverdue: 15,
    status: 'overdue'
  },
  {
    id: '2',
    tenantName: 'Sarah Wilson',
    unitNumber: 'B-201',
    amount: 18000,
    dueDate: '2024-01-05',
    daysOverdue: 10,
    status: 'overdue'
  },
  {
    id: '3',
    tenantName: 'David Brown',
    unitNumber: 'C-301',
    amount: 12000,
    dueDate: '2024-02-05',
    daysOverdue: -5,
    status: 'due_soon'
  }
];

const mockReports: FinancialReport[] = [
  {
    id: '1',
    name: 'Monthly Financial Report - January 2024',
    type: 'monthly',
    period: 'January 2024',
    generatedAt: '2024-01-31T23:59:59Z',
    status: 'generated',
    downloadUrl: '/reports/monthly-jan-2024.pdf'
  },
  {
    id: '2',
    name: 'Quarterly Financial Report - Q4 2023',
    type: 'quarterly',
    period: 'Q4 2023',
    generatedAt: '2024-01-15T10:30:00Z',
    status: 'generated',
    downloadUrl: '/reports/quarterly-q4-2023.pdf'
  },
  {
    id: '3',
    name: 'Annual Financial Report - 2023',
    type: 'yearly',
    period: '2023',
    generatedAt: '2024-01-01T00:00:00Z',
    status: 'generated',
    downloadUrl: '/reports/annual-2023.pdf'
  }
];

export default function FinancialAnalyticsPage() {
  useAuth(); // Keep for authentication
  const [metrics, setMetrics] = useState<FinancialMetric[]>(mockMetrics);
  const [revenueData, setRevenueData] = useState<RevenueData[]>(mockRevenueData);
  const [paymentMethods, setPaymentMethods] = useState<PaymentMethodData[]>(mockPaymentMethods);
  const [outstandingDues, setOutstandingDues] = useState<OutstandingDue[]>(mockOutstandingDues);
  const [reports] = useState<FinancialReport[]>(mockReports);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [periodFilter, setPeriodFilter] = useState<string>('month');
  const [tabValue, setTabValue] = useState(0);
  const [_selectedReport, setSelectedReport] = useState<FinancialReport | null>(null);
  const [_showReportDialog, setShowReportDialog] = useState(false);
  const [showDeleteDialog, setShowDeleteDialog] = useState(false);
  const [anchorEl, setAnchorEl] = useState<null | HTMLElement>(null);
  const [_selectedItem, setSelectedItem] = useState<any>(null);

  useEffect(() => {
    fetchAnalyticsData();
  }, []);

  const fetchAnalyticsData = async () => {
    setLoading(true);
    try {
      const [metricsResponse, revenueResponse, methodsResponse, duesResponse] = await Promise.all([
        apiClient.get('/analytics/metrics'),
        apiClient.get('/analytics/revenue'),
        apiClient.get('/analytics/payment-methods'),
        apiClient.get('/analytics/outstanding-dues')
      ]);

      if (metricsResponse.data.success) {
        setMetrics(metricsResponse.data.data);
      }
      if (revenueResponse.data.success) {
        setRevenueData(revenueResponse.data.data);
      }
      if (methodsResponse.data.success) {
        setPaymentMethods(methodsResponse.data.data);
      }
      if (duesResponse.data.success) {
        setOutstandingDues(duesResponse.data.data);
      }

      setLoading(false);
    } catch (err) {
      console.error('Failed to fetch analytics data:', err);
      setError('Failed to fetch analytics data');
      setLoading(false);
    }
  };

  const handlePeriodFilter = (period: string) => {
    setPeriodFilter(period);
  };

  const handleTabChange = (_event: React.SyntheticEvent, newValue: number) => {
    setTabValue(newValue);
  };

  const handleGenerateReport = () => {
    setSelectedReport(null);
    setShowReportDialog(true);
  };

  const handleMenuClick = (event: React.MouseEvent<HTMLElement>, item: any) => {
    setAnchorEl(event.currentTarget);
    setSelectedItem(item);
  };

  const handleMenuClose = () => {
    setAnchorEl(null);
    setSelectedItem(null);
  };

  const getTrendIcon = (trend: string) => {
    switch (trend) {
      case 'up': return <TrendingUpIcon color="success" />;
      case 'down': return <TrendingDownIcon color="error" />;
      default: return <TrendingUpIcon color="disabled" />;
    }
  };

  const getTrendColor = (trend: string) => {
    switch (trend) {
      case 'up': return 'success';
      case 'down': return 'error';
      default: return 'default';
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'overdue': return 'error';
      case 'due_soon': return 'warning';
      case 'paid': return 'success';
      default: return 'default';
    }
  };

  const getMethodIcon = (method: string) => {
    switch (method) {
      case 'Online Payment': return <CreditCardIcon />;
      case 'Bank Transfer': return <AccountBalanceIcon />;
      case 'Cash': return <CashIcon />;
      case 'UPI': return <WalletIcon />;
      case 'Cheque': return <ReceiptLongIcon />;
      default: return <PaymentIcon />;
    }
  };

  const mockUser = {
    name: 'Admin User',
    email: '<EMAIL>',
    role: 'admin',
    avatar: '/api/placeholder/32/32'
  };

  const handleLogout = () => console.log('Logout');
  const handleProfileClick = () => console.log('Profile click');
  const handleNotificationsClick = () => console.log('Notifications click');

  if (loading && metrics.length === 0) {
    return (
      <DashboardLayout>
        <LoadingSpinner />
      </DashboardLayout>
    );
  }

  return (
    <DashboardLayout>
      <PageHeader
        title="Financial Analytics"
        subtitle="Comprehensive financial analytics, revenue tracking, and performance metrics"
        actions={[
          {
            label: 'Generate Report',
            onClick: handleGenerateReport,
            variant: 'contained',
            startIcon: <AssessmentIcon />
          },
          {
            label: 'Export Data',
            onClick: () => console.log('Export data'),
            variant: 'outlined'
          }
        ]}
        breadcrumbs={[
          { label: 'Dashboard', href: '/' },
          { label: 'Financial Management', href: '/billing' },
          { label: 'Analytics' }
        ]}
      />

      {error && (
        <Alert severity="error" sx={{ mb: 3 }}>
          {error}
        </Alert>
      )}

      {/* Period Filter */}
      <Card sx={{ mb: 3 }}>
        <CardContent>
          <Grid container spacing={2} alignItems="center">
            <Grid item xs={12} md={4}>
              <Typography variant="h6">Analytics Period</Typography>
            </Grid>
            <Grid item xs={12} md={4}>
              <FormControl fullWidth>
                <InputLabel>Select Period</InputLabel>
                <Select
                  value={periodFilter}
                  onChange={(e) => handlePeriodFilter(e.target.value)}
                  label="Select Period"
                >
                  <MenuItem value="week">This Week</MenuItem>
                  <MenuItem value="month">This Month</MenuItem>
                  <MenuItem value="quarter">This Quarter</MenuItem>
                  <MenuItem value="year">This Year</MenuItem>
                  <MenuItem value="custom">Custom Period</MenuItem>
                </Select>
              </FormControl>
            </Grid>
            <Grid item xs={12} md={4}>
              <Button
                fullWidth
                variant="outlined"
                startIcon={<AnalyticsIcon />}
                onClick={() => console.log('Refresh analytics')}
              >
                Refresh Data
              </Button>
            </Grid>
          </Grid>
        </CardContent>
      </Card>

      {/* Key Metrics */}
      <Grid container spacing={3} sx={{ mb: 3 }}>
        {metrics.map((metric) => (
          <Grid item xs={12} sm={6} md={4} key={metric.id}>
            <InfoCard
              title={metric.name}
              value={`${metric.currency === 'INR' ? '₹' : ''}${metric.value.toLocaleString()}${metric.currency === '%' ? '%' : ''}${metric.currency === 'days' ? ' days' : ''}`}
              icon={getTrendIcon(metric.trend)}
              description={`${metric.change > 0 ? '+' : ''}${metric.changePercentage}% from last month`}
            />
          </Grid>
        ))}
      </Grid>

      {/* Tabs */}
      <Card>
        <Box sx={{ borderBottom: 1, borderColor: 'divider' }}>
          <Tabs value={tabValue} onChange={handleTabChange}>
            <Tab label="Revenue Analytics" />
            <Tab label="Payment Methods" />
            <Tab label="Outstanding Dues" />
            <Tab label="Reports" />
          </Tabs>
        </Box>

        {/* Revenue Analytics Tab */}
        {tabValue === 0 && (
          <Box sx={{ p: 3 }}>
            <Typography variant="h6" gutterBottom>
              Revenue Analytics
            </Typography>
            <Typography variant="body2" color="text.secondary" sx={{ mb: 3 }}>
              Revenue trends and performance analysis
            </Typography>
            
            <Grid container spacing={3}>
              <Grid item xs={12} md={8}>
                <Card>
                  <CardContent>
                    <Typography variant="h6" gutterBottom>
                      Revenue Trends (Last 6 Months)
                    </Typography>
                    <Typography variant="body2" color="text.secondary">
                      Revenue trends chart will be displayed here
                    </Typography>
                  </CardContent>
                </Card>
              </Grid>
              <Grid item xs={12} md={4}>
                <Card>
                  <CardContent>
                    <Typography variant="h6" gutterBottom>
                      Revenue Summary
                    </Typography>
                    <TableContainer>
                      <Table size="small">
                        <TableBody>
                          {revenueData.slice(-3).map((data, index) => (
                            <TableRow key={index}>
                              <TableCell>{data.month}</TableCell>
                              <TableCell align="right">₹{data.revenue.toLocaleString()}</TableCell>
                              <TableCell align="right">{data.occupancy}%</TableCell>
                            </TableRow>
                          ))}
                        </TableBody>
                      </Table>
                    </TableContainer>
                  </CardContent>
                </Card>
              </Grid>
              <Grid item xs={12} md={6}>
                <Card>
                  <CardContent>
                    <Typography variant="h6" gutterBottom>
                      Profit vs Expenses
                    </Typography>
                    <Typography variant="body2" color="text.secondary">
                      Profit vs expenses chart will be displayed here
                    </Typography>
                  </CardContent>
                </Card>
              </Grid>
              <Grid item xs={12} md={6}>
                <Card>
                  <CardContent>
                    <Typography variant="h6" gutterBottom>
                      Occupancy Trends
                    </Typography>
                    <Typography variant="body2" color="text.secondary">
                      Occupancy trends chart will be displayed here
                    </Typography>
                  </CardContent>
                </Card>
              </Grid>
            </Grid>
          </Box>
        )}

        {/* Payment Methods Tab */}
        {tabValue === 1 && (
          <Box sx={{ p: 3 }}>
            <Typography variant="h6" gutterBottom>
              Payment Method Analytics
            </Typography>
            <Typography variant="body2" color="text.secondary" sx={{ mb: 3 }}>
              Payment method distribution and performance
            </Typography>
            
            <Grid container spacing={3}>
              <Grid item xs={12} md={6}>
                <Card>
                  <CardContent>
                    <Typography variant="h6" gutterBottom>
                      Payment Method Distribution
                    </Typography>
                    <Typography variant="body2" color="text.secondary">
                      Payment method pie chart will be displayed here
                    </Typography>
                  </CardContent>
                </Card>
              </Grid>
              <Grid item xs={12} md={6}>
                <Card>
                  <CardContent>
                    <Typography variant="h6" gutterBottom>
                      Payment Method Performance
                    </Typography>
                    <TableContainer>
                      <Table>
                        <TableHead>
                          <TableRow>
                            <TableCell>Method</TableCell>
                            <TableCell align="right">Count</TableCell>
                            <TableCell align="right">Amount</TableCell>
                            <TableCell align="right">%</TableCell>
                          </TableRow>
                        </TableHead>
                        <TableBody>
                          {paymentMethods.map((method) => (
                            <TableRow key={method.method}>
                              <TableCell>
                                <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                                  {getMethodIcon(method.method)}
                                  {method.method}
                                </Box>
                              </TableCell>
                              <TableCell align="right">{method.count}</TableCell>
                              <TableCell align="right">₹{method.amount.toLocaleString()}</TableCell>
                              <TableCell align="right">{method.percentage}%</TableCell>
                            </TableRow>
                          ))}
                        </TableBody>
                      </Table>
                    </TableContainer>
                  </CardContent>
                </Card>
              </Grid>
            </Grid>
          </Box>
        )}

        {/* Outstanding Dues Tab */}
        {tabValue === 2 && (
          <Box sx={{ p: 3 }}>
            <Typography variant="h6" gutterBottom>
              Outstanding Dues
            </Typography>
            <Typography variant="body2" color="text.secondary" sx={{ mb: 3 }}>
              Track outstanding payments and overdue amounts
            </Typography>
            
            <TableContainer component={Paper} elevation={0}>
              <Table>
                <TableHead>
                  <TableRow>
                    <TableCell>Tenant</TableCell>
                    <TableCell>Unit</TableCell>
                    <TableCell>Amount</TableCell>
                    <TableCell>Due Date</TableCell>
                    <TableCell>Days Overdue</TableCell>
                    <TableCell>Status</TableCell>
                    <TableCell>Actions</TableCell>
                  </TableRow>
                </TableHead>
                <TableBody>
                  {outstandingDues.map((due) => (
                    <TableRow key={due.id} hover>
                      <TableCell>
                        <Typography variant="body1" fontWeight="medium">
                          {due.tenantName}
                        </Typography>
                      </TableCell>
                      <TableCell>
                        <Typography variant="body1">
                          {due.unitNumber}
                        </Typography>
                      </TableCell>
                      <TableCell>
                        <Typography variant="body1" fontWeight="medium">
                          ₹{due.amount.toLocaleString()}
                        </Typography>
                      </TableCell>
                      <TableCell>
                        <Typography variant="body2">
                          {new Date(due.dueDate).toLocaleDateString()}
                        </Typography>
                      </TableCell>
                      <TableCell>
                        <Chip
                          label={due.daysOverdue > 0 ? `${due.daysOverdue} days` : `${Math.abs(due.daysOverdue)} days left`}
                          color={getStatusColor(due.status) as any}
                          size="small"
                        />
                      </TableCell>
                      <TableCell>
                        <Chip
                          label={due.status.replace('_', ' ')}
                          color={getStatusColor(due.status) as any}
                          size="small"
                        />
                      </TableCell>
                      <TableCell>
                        <Box sx={{ display: 'flex', gap: 1 }}>
                          <Tooltip title="Send Reminder">
                            <IconButton size="small">
                              <ScheduleIcon />
                            </IconButton>
                          </Tooltip>
                          <Tooltip title="View Details">
                            <IconButton size="small">
                              <ViewIcon />
                            </IconButton>
                          </Tooltip>
                          <Tooltip title="More Actions">
                            <IconButton
                              size="small"
                              onClick={(e) => handleMenuClick(e, due)}
                            >
                              <MoreIcon />
                            </IconButton>
                          </Tooltip>
                        </Box>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </TableContainer>
          </Box>
        )}

        {/* Reports Tab */}
        {tabValue === 3 && (
          <Box sx={{ p: 3 }}>
            <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>
              <Typography variant="h6">
                Financial Reports ({reports.length})
              </Typography>
              <Button
                variant="contained"
                startIcon={<AssessmentIcon />}
                onClick={handleGenerateReport}
              >
                Generate Report
              </Button>
            </Box>

            <TableContainer component={Paper} elevation={0}>
              <Table>
                <TableHead>
                  <TableRow>
                    <TableCell>Report Name</TableCell>
                    <TableCell>Type</TableCell>
                    <TableCell>Period</TableCell>
                    <TableCell>Generated</TableCell>
                    <TableCell>Status</TableCell>
                    <TableCell>Actions</TableCell>
                  </TableRow>
                </TableHead>
                <TableBody>
                  {reports.map((report) => (
                    <TableRow key={report.id} hover>
                      <TableCell>
                        <Typography variant="body1" fontWeight="medium">
                          {report.name}
                        </Typography>
                      </TableCell>
                      <TableCell>
                        <Chip
                          label={report.type}
                          size="small"
                          variant="outlined"
                        />
                      </TableCell>
                      <TableCell>
                        <Typography variant="body2">
                          {report.period}
                        </Typography>
                      </TableCell>
                      <TableCell>
                        <Typography variant="body2">
                          {new Date(report.generatedAt).toLocaleDateString()}
                        </Typography>
                        <Typography variant="body2" color="text.secondary">
                          {new Date(report.generatedAt).toLocaleTimeString()}
                        </Typography>
                      </TableCell>
                      <TableCell>
                        <Chip
                          label={report.status}
                          color={getStatusColor(report.status) as any}
                          size="small"
                        />
                      </TableCell>
                      <TableCell>
                        <Box sx={{ display: 'flex', gap: 1 }}>
                          <Tooltip title="Download Report">
                            <IconButton size="small">
                              <DownloadIcon />
                            </IconButton>
                          </Tooltip>
                          <Tooltip title="View Report">
                            <IconButton size="small">
                              <ViewIcon />
                            </IconButton>
                          </Tooltip>
                          <Tooltip title="More Actions">
                            <IconButton
                              size="small"
                              onClick={(e) => handleMenuClick(e, report)}
                            >
                              <MoreIcon />
                            </IconButton>
                          </Tooltip>
                        </Box>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </TableContainer>
          </Box>
        )}
      </Card>

      {/* Action Menu */}
      <Menu
        anchorEl={anchorEl}
        open={Boolean(anchorEl)}
        onClose={handleMenuClose}
      >
        <MenuItem onClick={handleMenuClose}>
          <ListItemIcon>
            <ViewIcon fontSize="small" />
          </ListItemIcon>
          View Details
        </MenuItem>
        <MenuItem onClick={handleMenuClose}>
          <ListItemIcon>
            <DownloadIcon fontSize="small" />
          </ListItemIcon>
          Download
        </MenuItem>
        <MenuItem onClick={handleMenuClose}>
          <ListItemIcon>
            <EditIcon fontSize="small" />
          </ListItemIcon>
          Edit
        </MenuItem>
        <Divider />
        <MenuItem onClick={handleMenuClose} sx={{ color: 'error.main' }}>
          <ListItemIcon>
            <DeleteIcon fontSize="small" color="error" />
          </ListItemIcon>
          Delete
        </MenuItem>
      </Menu>

      {/* Report Generation Dialog */}
      <Dialog
        open={_showReportDialog}
        onClose={() => setShowReportDialog(false)}
        maxWidth="md"
        fullWidth
      >
        <DialogTitle>
          Generate Financial Report
        </DialogTitle>
        <DialogContent>
          <Typography variant="body2" color="text.secondary" gutterBottom>
            Report generation form will be displayed here
          </Typography>
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setShowReportDialog(false)}>
            Cancel
          </Button>
          <Button variant="contained">
            Generate Report
          </Button>
        </DialogActions>
      </Dialog>

      {/* Delete Confirmation Dialog */}
      <ConfirmDialog
        open={showDeleteDialog}
        title="Delete Item"
        message="Are you sure you want to delete this item? This action cannot be undone."
        onConfirm={() => {
          setShowDeleteDialog(false);
          // Handle delete logic here
        }}
        onCancel={() => setShowDeleteDialog(false)}
      />
    </DashboardLayout>
  );
} 