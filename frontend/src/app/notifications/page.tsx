'use client';

import React, { useState } from 'react';
import {
  Box,
  Container,
  Typography,
  Paper,
  Grid,
  Button,
  TextField,
  InputAdornment,
  Chip,
  IconButton,
  Menu,
  MenuItem,
  Card,
  CardContent,
  Avatar,
  Tabs,
  Tab,
  List,
  ListItem,
  ListItemIcon,
  ListItemText,
  ListItemSecondaryAction,
  Divider,
  FormControl,
  InputLabel,
  Select,
  SelectChangeEvent,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Switch,
  FormControlLabel,
  Alert,
  Pagination,
} from '@mui/material';
import {
  Search,
  Settings as SettingsIcon,
  Notifications as NotificationsIcon,
  NotificationImportant,
  Warning,
  Error as ErrorIcon,
  CheckCircle,
  Info,
  Payment,
  Home,
  Build,
  Person,
  MoreVert,
  MarkEmailRead,
  Delete,
  Circle,
} from '@mui/icons-material';
import { ProtectedRoute } from '@/components/auth/ProtectedRoute';
import { DashboardLayout } from '@/components/layout/DashboardLayout';

interface MockNotification {
  id: string;
  title: string;
  message: string;
  type: 'info' | 'warning' | 'error' | 'success';
  priority: 'low' | 'medium' | 'high' | 'urgent';
  channel: 'email' | 'sms' | 'push' | 'in_app';
  isRead: boolean;
  timestamp: string;
  category: string;
}

// Mock notification data
const mockNotifications: MockNotification[] = [
  {
    id: '1',
    title: 'Rent Payment Reminder',
    message: 'Your monthly rent payment is due in 3 days. Please make the payment to avoid late fees.',
    type: 'warning',
    priority: 'high',
    channel: 'email',
    isRead: false,
    timestamp: '2024-01-15T10:30:00Z',
    category: 'payment'
  },
  {
    id: '2',
    title: 'Maintenance Request Update',
    message: 'Your maintenance request for Unit 301 has been assigned to our technician.',
    type: 'info',
    priority: 'medium',
    channel: 'in_app',
    isRead: false,
    timestamp: '2024-01-14T15:45:00Z',
    category: 'maintenance'
  },
  {
    id: '3',
    title: 'Lease Agreement Renewal',
    message: 'Your lease agreement expires in 30 days. Please contact us to discuss renewal options.',
    type: 'warning',
    priority: 'high',
    channel: 'email',
    isRead: true,
    timestamp: '2024-01-13T09:15:00Z',
    category: 'agreement'
  },
  {
    id: '4',
    title: 'Payment Confirmation',
    message: 'Thank you! Your payment of ₹25,000 has been received and processed successfully.',
    type: 'success',
    priority: 'low',
    channel: 'sms',
    isRead: true,
    timestamp: '2024-01-12T14:20:00Z',
    category: 'payment'
  },
  {
    id: '5',
    title: 'System Maintenance Notice',
    message: 'The tenant portal will be under maintenance tonight from 2:00 AM to 6:00 AM.',
    type: 'info',
    priority: 'medium',
    channel: 'in_app',
    isRead: false,
    timestamp: '2024-01-11T16:00:00Z',
    category: 'system'
  }
];

const NotificationsPage: React.FC = () => {
  const [notifications, setNotifications] = useState<MockNotification[]>(mockNotifications);
  const [selectedTab, setSelectedTab] = useState(0);
  const [searchQuery, setSearchQuery] = useState('');
  const [filterType, setFilterType] = useState<string>('all');
  const [currentPage, setCurrentPage] = useState(1);
  const [settingsOpen, setSettingsOpen] = useState(false);

  // Filter notifications based on tab and search
  const filteredNotifications = notifications.filter(notification => {
    const matchesTab = selectedTab === 0 || (selectedTab === 1 && !notification.isRead);
    const matchesSearch = notification.title.toLowerCase().includes(searchQuery.toLowerCase()) ||
                         notification.message.toLowerCase().includes(searchQuery.toLowerCase());
    const matchesFilter = filterType === 'all' || notification.type === filterType;
    
    return matchesTab && matchesSearch && matchesFilter;
  });

  // Calculate statistics
  const totalNotifications = notifications.length;
  const unreadNotifications = notifications.filter(n => !n.isRead).length;
  const highPriorityNotifications = notifications.filter(n => n.priority === 'high' || n.priority === 'urgent').length;
  const todayNotifications = notifications.filter(n => {
    const today = new Date().toDateString();
    const notificationDate = new Date(n.timestamp).toDateString();
    return today === notificationDate;
  }).length;

  const handleTabChange = (event: React.SyntheticEvent, newValue: number) => {
    setSelectedTab(newValue);
    setCurrentPage(1);
  };

  const handleMarkAsRead = (notificationId: string) => {
    setNotifications(prev => 
      prev.map(notification => 
        notification.id === notificationId 
          ? { ...notification, isRead: true }
          : notification
      )
    );
  };

  const handleMarkAllAsRead = () => {
    setNotifications(prev => 
      prev.map(notification => ({ ...notification, isRead: true }))
    );
  };

  const getNotificationIcon = (type: string, priority: string) => {
    if (priority === 'urgent' || priority === 'high') {
      return <NotificationImportant color="error" />;
    }
    
    switch (type) {
      case 'warning':
        return <Warning color="warning" />;
      case 'error':
        return <ErrorIcon color="error" />;
      case 'success':
        return <CheckCircle color="success" />;
      default:
        return <Info color="info" />;
    }
  };

  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case 'urgent':
        return 'error';
      case 'high':
        return 'warning';
      case 'medium':
        return 'info';
      default:
        return 'default';
    }
  };

  const formatTimestamp = (timestamp: string) => {
    const date = new Date(timestamp);
    const now = new Date();
    const diffInHours = Math.floor((now.getTime() - date.getTime()) / (1000 * 60 * 60));
    
    if (diffInHours < 1) {
      return 'Just now';
    } else if (diffInHours < 24) {
      return `${diffInHours} hours ago`;
    } else {
      const diffInDays = Math.floor(diffInHours / 24);
      return `${diffInDays} days ago`;
    }
  };

  return (
    <ProtectedRoute>
      <DashboardLayout>
        <Container maxWidth="lg" sx={{ mt: 4, mb: 4 }}>
          {/* Header */}
          <Box sx={{ mb: 4 }}>
            <Typography variant="h4" component="h1" gutterBottom>
              Notifications
            </Typography>
            <Typography variant="body1" color="text.secondary">
              Stay updated with important messages and alerts
            </Typography>
          </Box>

          {/* Statistics Cards */}
          <Grid container spacing={3} sx={{ mb: 4 }}>
            <Grid item xs={12} sm={6} md={3}>
              <Card>
                <CardContent>
                  <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
                    <Box>
                      <Typography color="text.secondary" gutterBottom>
                        Total Notifications
                      </Typography>
                      <Typography variant="h4">
                        {totalNotifications}
                      </Typography>
                    </Box>
                    <NotificationsIcon color="primary" sx={{ fontSize: 40 }} />
                  </Box>
                </CardContent>
              </Card>
            </Grid>
            
            <Grid item xs={12} sm={6} md={3}>
              <Card>
                <CardContent>
                  <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
                    <Box>
                      <Typography color="text.secondary" gutterBottom>
                        Unread
                      </Typography>
                      <Typography variant="h4">
                        {unreadNotifications}
                      </Typography>
                    </Box>
                    <Circle color="warning" sx={{ fontSize: 40 }} />
                  </Box>
                </CardContent>
              </Card>
            </Grid>
            
            <Grid item xs={12} sm={6} md={3}>
              <Card>
                <CardContent>
                  <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
                    <Box>
                      <Typography color="text.secondary" gutterBottom>
                        High Priority
                      </Typography>
                      <Typography variant="h4">
                        {highPriorityNotifications}
                      </Typography>
                    </Box>
                    <NotificationImportant color="error" sx={{ fontSize: 40 }} />
                  </Box>
                </CardContent>
              </Card>
            </Grid>
            
            <Grid item xs={12} sm={6} md={3}>
              <Card>
                <CardContent>
                  <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
                    <Box>
                      <Typography color="text.secondary" gutterBottom>
                        Today
                      </Typography>
                      <Typography variant="h4">
                        {todayNotifications}
                      </Typography>
                    </Box>
                    <CheckCircle color="success" sx={{ fontSize: 40 }} />
                  </Box>
                </CardContent>
              </Card>
            </Grid>
          </Grid>

          {/* Main Content */}
          <Paper sx={{ p: 3 }}>
            {/* Controls */}
            <Box sx={{ mb: 3 }}>
              <Grid container spacing={2} alignItems="center">
                <Grid item xs={12} md={6}>
                  <TextField
                    fullWidth
                    placeholder="Search notifications..."
                    value={searchQuery}
                    onChange={(e) => setSearchQuery(e.target.value)}
                    InputProps={{
                      startAdornment: (
                        <InputAdornment position="start">
                          <Search />
                        </InputAdornment>
                      ),
                    }}
                  />
                </Grid>
                <Grid item xs={12} md={3}>
                  <FormControl fullWidth>
                    <InputLabel>Filter by Type</InputLabel>
                    <Select
                      value={filterType}
                      label="Filter by Type"
                      onChange={(e) => setFilterType(e.target.value)}
                    >
                      <MenuItem value="all">All Types</MenuItem>
                      <MenuItem value="info">Info</MenuItem>
                      <MenuItem value="warning">Warning</MenuItem>
                      <MenuItem value="error">Error</MenuItem>
                      <MenuItem value="success">Success</MenuItem>
                    </Select>
                  </FormControl>
                </Grid>
                <Grid item xs={12} md={3}>
                  <Box sx={{ display: 'flex', gap: 1 }}>
                    <Button
                      variant="outlined"
                      onClick={handleMarkAllAsRead}
                      startIcon={<MarkEmailRead />}
                      disabled={unreadNotifications === 0}
                    >
                      Mark All Read
                    </Button>
                    <IconButton onClick={() => setSettingsOpen(true)}>
                      <SettingsIcon />
                    </IconButton>
                  </Box>
                </Grid>
              </Grid>
            </Box>

            {/* Tabs */}
            <Tabs value={selectedTab} onChange={handleTabChange} sx={{ mb: 3 }}>
              <Tab label={`All (${totalNotifications})`} />
              <Tab label={`Unread (${unreadNotifications})`} />
            </Tabs>

            {/* Notifications List */}
            {filteredNotifications.length === 0 ? (
              <Box sx={{ textAlign: 'center', py: 8 }}>
                <NotificationsIcon sx={{ fontSize: 64, color: 'text.secondary', mb: 2 }} />
                <Typography variant="h6" color="text.secondary" gutterBottom>
                  {searchQuery || filterType !== 'all' ? 'No notifications match your filters' : 'No notifications found'}
                </Typography>
                <Typography variant="body2" color="text.secondary">
                  {searchQuery || filterType !== 'all' ? 'Try adjusting your search or filter criteria' : 'You\'re all caught up!'}
                </Typography>
              </Box>
            ) : (
              <List>
                {filteredNotifications.map((notification, index) => (
                  <React.Fragment key={notification.id}>
                    <ListItem
                      sx={{
                        bgcolor: notification.isRead ? 'transparent' : 'action.hover',
                        borderRadius: 1,
                        mb: 1,
                      }}
                    >
                      <ListItemIcon>
                        {getNotificationIcon(notification.type, notification.priority)}
                      </ListItemIcon>
                      <ListItemText
                        primary={
                          <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                            <Typography variant="subtitle1" sx={{ fontWeight: notification.isRead ? 'normal' : 'bold' }}>
                              {notification.title}
                            </Typography>
                            <Chip
                              label={notification.priority}
                              size="small"
                              color={getPriorityColor(notification.priority) as any}
                              variant="outlined"
                            />
                            {!notification.isRead && (
                              <Circle color="primary" sx={{ fontSize: 8 }} />
                            )}
                          </Box>
                        }
                        secondary={
                          <Box>
                            <Typography variant="body2" color="text.secondary" sx={{ mb: 0.5 }}>
                              {notification.message}
                            </Typography>
                            <Typography variant="caption" color="text.secondary">
                              {formatTimestamp(notification.timestamp)} • {notification.channel}
                            </Typography>
                          </Box>
                        }
                      />
                      <ListItemSecondaryAction>
                        <Box sx={{ display: 'flex', gap: 1 }}>
                          {!notification.isRead && (
                            <IconButton
                              size="small"
                              onClick={() => handleMarkAsRead(notification.id)}
                              title="Mark as read"
                            >
                              <MarkEmailRead />
                            </IconButton>
                          )}
                          <IconButton size="small">
                            <MoreVert />
                          </IconButton>
                        </Box>
                      </ListItemSecondaryAction>
                    </ListItem>
                    {index < filteredNotifications.length - 1 && <Divider />}
                  </React.Fragment>
                ))}
              </List>
            )}

            {/* Pagination */}
            {filteredNotifications.length > 10 && (
              <Box sx={{ display: 'flex', justifyContent: 'center', mt: 3 }}>
                <Pagination
                  count={Math.ceil(filteredNotifications.length / 10)}
                  page={currentPage}
                  onChange={(event, page) => setCurrentPage(page)}
                  color="primary"
                />
              </Box>
            )}
          </Paper>

          {/* Settings Dialog */}
          <Dialog open={settingsOpen} onClose={() => setSettingsOpen(false)} maxWidth="sm" fullWidth>
            <DialogTitle>Notification Settings</DialogTitle>
            <DialogContent>
              <Box sx={{ py: 2 }}>
                <Typography variant="h6" gutterBottom>
                  Notification Preferences
                </Typography>
                <FormControlLabel
                  control={<Switch defaultChecked />}
                  label="Email notifications"
                />
                <FormControlLabel
                  control={<Switch defaultChecked />}
                  label="SMS notifications"
                />
                <FormControlLabel
                  control={<Switch defaultChecked />}
                  label="Push notifications"
                />
                <FormControlLabel
                  control={<Switch defaultChecked />}
                  label="In-app notifications"
                />
              </Box>
            </DialogContent>
            <DialogActions>
              <Button onClick={() => setSettingsOpen(false)}>Cancel</Button>
              <Button variant="contained" onClick={() => setSettingsOpen(false)}>
                Save Settings
              </Button>
            </DialogActions>
          </Dialog>
        </Container>
      </DashboardLayout>
    </ProtectedRoute>
  );
};

export default NotificationsPage;
