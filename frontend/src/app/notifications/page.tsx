'use client';

import React, { useState } from 'react';
import {
  Box,
  Container,
  Typography,
  Paper,
  Grid,
  Button,
  TextField,
  InputAdornment,
  Chip,
  IconButton,
  Menu,
  MenuItem,
  Card,
  CardContent,
  Avatar,
  Tabs,
  Tab,
  List,
  ListItem,
  ListItemIcon,
  ListItemText,
  ListItemSecondaryAction,
  Divider,
  FormControl,
  InputLabel,
  Select,
  SelectChangeEvent,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Switch,
  FormControlLabel,
  Alert,
  Skeleton,
  Pagination,
  CircularProgress,
} from '@mui/material';
import {
  Search,
  Settings as SettingsIcon,
  Notifications as NotificationsIcon,
  NotificationImportant,
  Warning,
  Error as ErrorIcon,
  CheckCircle,
  Info,
  Email,
  Sms,
  PushPin,
  MarkEmailRead,
  Delete,
  MoreVert,
  Refresh,
  Add,
  Circle,
} from '@mui/icons-material';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import { Toaster } from 'react-hot-toast';
import { ProtectedRoute } from '@/components/auth/ProtectedRoute';
import { DashboardLayout } from '@/components/layout/DashboardLayout';
import {
  useFormattedNotifications,
  useNotificationCounts,
  useMarkNotificationAsRead,
  useMarkAllNotificationsAsRead
} from '@/hooks/useNotifications';

interface Notification {
  id: string;
  title: string;
  message: string;
  type: 'info' | 'warning' | 'error' | 'success';
  priority: 'low' | 'medium' | 'high' | 'urgent';
  channel: 'email' | 'sms' | 'push' | 'in_app';
  isRead: boolean;
  createdAt: string;
  sender: string;
  category: string;
}

// Create a query client for React Query
const queryClient = new QueryClient({
  defaultOptions: {
    queries: {
      staleTime: 30 * 1000, // 30 seconds
      refetchOnWindowFocus: true,
      retry: 2,
    },
  },
});

function NotificationsPageContent() {
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedTab, setSelectedTab] = useState(0);
  const [filterType, setFilterType] = useState<string>('all');
  const [filterPriority, setFilterPriority] = useState<string>('all');
  const [anchorEl, setAnchorEl] = useState<null | HTMLElement>(null);
  const [selectedNotification, setSelectedNotification] = useState<Notification | null>(null);
  const [settingsOpen, setSettingsOpen] = useState(false);
  const [currentPage, setCurrentPage] = useState(1);

  // API hooks
  const {
    notifications,
    pagination,
    isLoading,
    error,
    refetch
  } = useFormattedNotifications({
    unread_only: selectedTab === 1,
    per_page: 10,
    page: currentPage,
  });

  const {
    totalNotifications,
    unreadNotifications,
    isLoading: statsLoading
  } = useNotificationCounts();

  const markAsReadMutation = useMarkNotificationAsRead();
  const markAllAsReadMutation = useMarkAllNotificationsAsRead();

  const handleSearchChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    setSearchQuery(event.target.value);
  };

  const handleTabChange = (event: React.SyntheticEvent, newValue: number) => {
    setSelectedTab(newValue);
  };

  const handleFilterTypeChange = (event: SelectChangeEvent) => {
    setFilterType(event.target.value);
  };

  const handleFilterPriorityChange = (event: SelectChangeEvent) => {
    setFilterPriority(event.target.value);
  };

  const handleMenuClick = (event: React.MouseEvent<HTMLElement>, notification: Notification) => {
    setAnchorEl(event.currentTarget);
    setSelectedNotification(notification);
  };

  const handleMenuClose = () => {
    setAnchorEl(null);
    setSelectedNotification(null);
  };

  const handleMarkAsRead = (notificationId: string) => {
    markAsReadMutation.mutate(notificationId);
    handleMenuClose();
  };

  const handleMarkAllAsRead = () => {
    markAllAsReadMutation.mutate();
  };

  const handleDeleteNotification = (notificationId: string) => {
    // Note: Delete functionality would need to be implemented in the backend
    console.log('Delete notification:', notificationId);
    handleMenuClose();
  };

  const handleRefresh = () => {
    refetch();
  };

  const getNotificationIcon = (type: string) => {
    switch (type) {
      case 'warning': return <Warning color="warning" />;
      case 'error': return <ErrorIcon color="error" />;
      case 'success': return <CheckCircle color="success" />;
      case 'info': return <Info color="info" />;
      default: return <NotificationsIcon />;
    }
  };

  const getChannelIcon = (channel: string) => {
    switch (channel) {
      case 'email': return <Email />;
      case 'sms': return <Sms />;
      case 'push': return <PushPin />;
      case 'in_app': return <NotificationsIcon />;
      default: return <NotificationsIcon />;
    }
  };

  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case 'high': return 'error';
      case 'medium': return 'warning';
      case 'low': return 'success';
      default: return 'default';
    }
  };

  const filteredNotifications = notifications.filter(notification => {
    const matchesSearch =
      notification.title.toLowerCase().includes(searchQuery.toLowerCase()) ||
      notification.message.toLowerCase().includes(searchQuery.toLowerCase()) ||
      (notification.sender || '').toLowerCase().includes(searchQuery.toLowerCase());

    const matchesTab =
      selectedTab === 0 || // All
      (selectedTab === 1 && !notification.isRead) || // Unread
      (selectedTab === 2 && (notification.priority === 'high' || notification.priority === 'urgent')); // High Priority

    const matchesType = filterType === 'all' || notification.type === filterType;
    const matchesPriority = filterPriority === 'all' || notification.priority === filterPriority;

    return matchesSearch && matchesTab && matchesType && matchesPriority;
  });

  const unreadCount = unreadNotifications;
  const highPriorityCount = notifications.filter(n => n.priority === 'high' || n.priority === 'urgent').length;
  const todayCount = notifications.filter(n => {
    const today = new Date().toDateString();
    const notificationDate = new Date(n.createdAt).toDateString();
    return today === notificationDate;
  }).length;

  return (
    <ProtectedRoute>
      <DashboardLayout
        title="Notifications Management"
        breadcrumbs={[
          { label: 'Home', href: '/dashboard' },
          { label: 'Notifications' }
        ]}
        headerActions={
          <Box sx={{ display: 'flex', gap: 1 }}>
            <Button
              variant="outlined"
              startIcon={<SettingsIcon />}
              onClick={() => setSettingsOpen(true)}
            >
              Settings
            </Button>
            <Button
              variant="contained"
              startIcon={<Add />}
            >
              New Notification
            </Button>
          </Box>
        }
      >
        <Container maxWidth="xl" sx={{ py: 4 }}>
          {/* Statistics Cards */}
          <Grid container spacing={3} sx={{ mb: 4 }}>
            <Grid item xs={12} sm={6} md={3}>
              <Card>
                <CardContent>
                  <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
                    <Avatar sx={{ bgcolor: 'primary.main' }}>
                      <NotificationsIcon />
                    </Avatar>
                    <Box>
                      <Typography variant="h4" fontWeight="bold">
                        {isLoading ? (
                          <CircularProgress size={24} />
                        ) : (
                          pagination?.total || notifications.length
                        )}
                      </Typography>
                      <Typography variant="body2" color="text.secondary">
                        Total Notifications
                      </Typography>
                    </Box>
                  </Box>
                </CardContent>
              </Card>
            </Grid>
            <Grid item xs={12} sm={6} md={3}>
              <Card>
                <CardContent>
                  <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
                    <Avatar sx={{ bgcolor: 'error.main' }}>
                      <NotificationImportant />
                    </Avatar>
                    <Box>
                      <Typography variant="h4" fontWeight="bold">
                        {isLoading ? (
                          <CircularProgress size={24} />
                        ) : (
                          unreadCount
                        )}
                      </Typography>
                      <Typography variant="body2" color="text.secondary">
                        Unread
                      </Typography>
                    </Box>
                  </Box>
                </CardContent>
              </Card>
            </Grid>
            <Grid item xs={12} sm={6} md={3}>
              <Card>
                <CardContent>
                  <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
                    <Avatar sx={{ bgcolor: 'warning.main' }}>
                      <Warning />
                    </Avatar>
                    <Box>
                      <Typography variant="h4" fontWeight="bold">
                        {isLoading ? (
                          <CircularProgress size={24} />
                        ) : (
                          highPriorityCount
                        )}
                      </Typography>
                      <Typography variant="body2" color="text.secondary">
                        High Priority
                      </Typography>
                    </Box>
                  </Box>
                </CardContent>
              </Card>
            </Grid>
            <Grid item xs={12} sm={6} md={3}>
              <Card>
                <CardContent>
                  <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
                    <Avatar sx={{ bgcolor: 'success.main' }}>
                      <CheckCircle />
                    </Avatar>
                    <Box>
                      <Typography variant="h4" fontWeight="bold">
                        {isLoading ? (
                          <CircularProgress size={24} />
                        ) : (
                          todayCount
                        )}
                      </Typography>
                      <Typography variant="body2" color="text.secondary">
                        Today
                      </Typography>
                    </Box>
                  </Box>
                </CardContent>
              </Card>
            </Grid>
          </Grid>

          {/* Search and Filters */}
          <Paper sx={{ p: 3, mb: 3 }}>
            <Grid container spacing={2} alignItems="center">
              <Grid item xs={12} md={4}>
                <TextField
                  fullWidth
                  placeholder="Search notifications..."
                  value={searchQuery}
                  onChange={handleSearchChange}
                  InputProps={{
                    startAdornment: (
                      <InputAdornment position="start">
                        <Search />
                      </InputAdornment>
                    ),
                  }}
                />
              </Grid>
              <Grid item xs={12} md={3}>
                <FormControl fullWidth>
                  <InputLabel>Type</InputLabel>
                  <Select
                    value={filterType}
                    label="Type"
                    onChange={handleFilterTypeChange}
                  >
                    <MenuItem value="all">All Types</MenuItem>
                    <MenuItem value="info">Info</MenuItem>
                    <MenuItem value="warning">Warning</MenuItem>
                    <MenuItem value="error">Error</MenuItem>
                    <MenuItem value="success">Success</MenuItem>
                  </Select>
                </FormControl>
              </Grid>
              <Grid item xs={12} md={3}>
                <FormControl fullWidth>
                  <InputLabel>Priority</InputLabel>
                  <Select
                    value={filterPriority}
                    label="Priority"
                    onChange={handleFilterPriorityChange}
                  >
                    <MenuItem value="all">All Priorities</MenuItem>
                    <MenuItem value="high">High</MenuItem>
                    <MenuItem value="medium">Medium</MenuItem>
                    <MenuItem value="low">Low</MenuItem>
                  </Select>
                </FormControl>
              </Grid>
              <Grid item xs={12} md={2}>
                <Button
                  fullWidth
                  variant="outlined"
                  onClick={handleMarkAllAsRead}
                  startIcon={<MarkEmailRead />}
                >
                  Mark All as Read
                </Button>
              </Grid>
            </Grid>
          </Paper>

          {/* Recent Notifications */}
          <Paper>
            <Box sx={{ borderBottom: 1, borderColor: 'divider' }}>
              <Tabs value={selectedTab} onChange={handleTabChange}>
                <Tab label={`All (${notifications.length})`} />
                <Tab label={`Unread (${unreadCount})`} />
                <Tab label={`High Priority (${highPriorityCount})`} />
                <Tab label="Settings" />
              </Tabs>
            </Box>

            <Box sx={{ p: 3 }}>
              {error && (
                <Alert severity="error" sx={{ mb: 2 }}>
                  Failed to load notifications: {error.message}
                  <Button onClick={() => refetch()} sx={{ ml: 2 }}>
                    Retry
                  </Button>
                </Alert>
              )}

              {isLoading ? (
                <Box>
                  {Array.from({ length: 5 }).map((_, index) => (
                    <Box key={index} sx={{ mb: 2 }}>
                      <Skeleton variant="rectangular" height={80} />
                    </Box>
                  ))}
                </Box>
              ) : filteredNotifications.length === 0 ? (
                <Box sx={{ textAlign: 'center', py: 4 }}>
                  <NotificationsIcon sx={{ fontSize: 64, color: 'text.secondary', mb: 2 }} />
                  <Typography variant="h6" color="text.secondary">
                    No notifications found
                  </Typography>
                  <Typography variant="body2" color="text.secondary">
                    {searchQuery ? 'Try adjusting your search or filters' : 'You\'re all caught up!'}
                  </Typography>
                </Box>
              ) : (
                <List>
                  {filteredNotifications.map((notification, index) => (
                  <React.Fragment key={notification.id}>
                    <ListItem
                      sx={{
                        bgcolor: notification.isRead ? 'transparent' : 'action.hover',
                        borderRadius: 1,
                        mb: 1,
                      }}
                    >
                      <ListItemIcon>
                        {getNotificationIcon(notification.type)}
                      </ListItemIcon>
                      <ListItemText
                        primary={
                          <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                            <Typography variant="subtitle1" fontWeight={notification.isRead ? 'normal' : 'medium'}>
                              {notification.title}
                            </Typography>
                            {!notification.isRead && (
                              <Circle sx={{ fontSize: 8, color: 'primary.main' }} />
                            )}
                            <Chip
                              label={notification.priority || 'low'}
                              size="small"
                              color={getPriorityColor(notification.priority || 'low') as any}
                            />
                            <Chip
                              icon={getChannelIcon(notification.channel || 'in_app')}
                              label={notification.channel || 'in_app'}
                              size="small"
                              variant="outlined"
                            />
                          </Box>
                        }
                        secondary={
                          <Box>
                            <Typography variant="body2" color="text.secondary">
                              {notification.message}
                            </Typography>
                            <Typography variant="caption" color="text.secondary">
                              {notification.sender} • {new Date(notification.createdAt).toLocaleString()}
                            </Typography>
                          </Box>
                        }
                      />
                      <ListItemSecondaryAction>
                        <Box sx={{ display: 'flex', gap: 1 }}>
                          {!notification.isRead && (
                            <IconButton
                              size="small"
                              onClick={() => handleMarkAsRead(notification.id)}
                            >
                              <MarkEmailRead />
                            </IconButton>
                          )}
                          <IconButton
                            size="small"
                            onClick={(e) => handleMenuClick(e, notification)}
                          >
                            <MoreVert />
                          </IconButton>
                        </Box>
                      </ListItemSecondaryAction>
                    </ListItem>
                    {index < filteredNotifications.length - 1 && <Divider />}
                    </React.Fragment>
                  ))}
                </List>
              )}

              {/* Pagination */}
              {pagination && pagination.lastPage > 1 && (
                <Box sx={{ display: 'flex', justifyContent: 'center', mt: 3 }}>
                  <Pagination
                    count={pagination.lastPage}
                    page={currentPage}
                    onChange={(_, page) => setCurrentPage(page)}
                    color="primary"
                  />
                </Box>
              )}
            </Box>
          </Paper>

          {/* Action Menu */}
          <Menu
            anchorEl={anchorEl}
            open={Boolean(anchorEl)}
            onClose={handleMenuClose}
          >
            <MenuItem onClick={() => selectedNotification && handleMarkAsRead(selectedNotification.id)}>
              <ListItemIcon>
                <MarkEmailRead fontSize="small" />
              </ListItemIcon>
              Mark as Read
            </MenuItem>
            <MenuItem onClick={handleMenuClose}>
              <ListItemIcon>
                <Info fontSize="small" />
              </ListItemIcon>
              View Details
            </MenuItem>
            <Divider />
            <MenuItem 
              onClick={() => selectedNotification && handleDeleteNotification(selectedNotification.id)}
              sx={{ color: 'error.main' }}
            >
              <ListItemIcon>
                <Delete fontSize="small" color="error" />
              </ListItemIcon>
              Delete
            </MenuItem>
          </Menu>

          {/* Settings Dialog */}
          <Dialog
            open={settingsOpen}
            onClose={() => setSettingsOpen(false)}
            maxWidth="sm"
            fullWidth
          >
            <DialogTitle>Notification Settings</DialogTitle>
            <DialogContent>
              <Box sx={{ mt: 2 }}>
                <Typography variant="h6" gutterBottom>
                  Notification Preferences
                </Typography>
                <FormControlLabel
                  control={<Switch defaultChecked />}
                  label="Email Notifications"
                />
                <FormControlLabel
                  control={<Switch defaultChecked />}
                  label="SMS Notifications"
                />
                <FormControlLabel
                  control={<Switch defaultChecked />}
                  label="Push Notifications"
                />
                <FormControlLabel
                  control={<Switch defaultChecked />}
                  label="In-App Notifications"
                />
              </Box>
            </DialogContent>
            <DialogActions>
              <Button onClick={() => setSettingsOpen(false)}>Cancel</Button>
              <Button variant="contained">Save Settings</Button>
            </DialogActions>
          </Dialog>
        </Container>
      </DashboardLayout>
    </ProtectedRoute>
  );
}

export default function NotificationsPage() {
  return (
    <QueryClientProvider client={queryClient}>
      <NotificationsPageContent />
      <Toaster position="top-right" />
    </QueryClientProvider>
  );
}
