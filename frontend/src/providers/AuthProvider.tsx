'use client';

import React, { createContext, useContext, useEffect, useState } from 'react';
import { authService, type User } from '@/services/auth.service';

interface AuthContextType {
  isAuthenticated: boolean;
  isLoading: boolean;
  user: User | null;
  login: (email?: string, password?: string) => Promise<void>;
  logout: () => Promise<void>;
  error: string | null;
}

const AuthContext = createContext<AuthContextType | undefined>(undefined);

export const useAuth = () => {
  const context = useContext(AuthContext);
  if (!context) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
};

export const AuthProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const [isLoading, setIsLoading] = useState(true);
  const [user, setUser] = useState<User | null>(null);
  const [isAuthenticated, setIsAuthenticated] = useState(false);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    initializeAuth();
  }, []);

  const initializeAuth = async () => {
    try {
      setIsLoading(true);
      setError(null);

      console.log('🔄 Initializing authentication...');

      // Try to restore authentication or perform demo login
      const authenticatedUser = await authService.initializeAuth();

      if (authenticatedUser) {
        setUser(authenticatedUser);
        setIsAuthenticated(true);
        console.log('✅ Authentication initialized for:', authenticatedUser.email);
      } else {
        console.log('❌ Authentication initialization failed');
        setError('Failed to authenticate. Please try refreshing the page.');
      }
    } catch (error: any) {
      console.error('❌ Auth initialization error:', error);
      setError(error.message || 'Authentication failed');
    } finally {
      setIsLoading(false);
    }
  };

  const login = async (email?: string, password?: string): Promise<void> => {
    try {
      setIsLoading(true);
      setError(null);

      const credentials = {
        email: email || '<EMAIL>',
        password: password || 'password',
      };

      console.log('🔐 Logging in with:', credentials.email);

      const response = await authService.login(credentials);

      setUser(response.data.user);
      setIsAuthenticated(true);

      console.log('✅ Login successful for:', response.data.user.email);
    } catch (error: any) {
      console.error('❌ Login failed:', error);
      setError(error.message || 'Login failed');
      setIsAuthenticated(false);
      setUser(null);
      throw error;
    } finally {
      setIsLoading(false);
    }
  };

  const logout = async (): Promise<void> => {
    try {
      setIsLoading(true);
      await authService.logout();
    } catch (error) {
      console.error('Logout error:', error);
    } finally {
      setUser(null);
      setIsAuthenticated(false);
      setError(null);
      setIsLoading(false);
      console.log('🚪 User logged out');
    }
  };

  const value: AuthContextType = {
    isAuthenticated,
    isLoading,
    user,
    login,
    logout,
    error,
  };

  return <AuthContext.Provider value={value}>{children}</AuthContext.Provider>;
};
