'use client';

import React, { createContext, useContext, useEffect, useState } from 'react';

interface User {
  id: string;
  email: string;
  name: string;
  role: string;
  permissions: string[];
}

interface AuthContextType {
  isAuthenticated: boolean;
  isLoading: boolean;
  user: User | null;
  login: (email?: string, password?: string) => void;
  logout: () => void;
}

const AuthContext = createContext<AuthContextType | undefined>(undefined);

export const useAuth = () => {
  const context = useContext(AuthContext);
  if (!context) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
};

// Mock users for demonstration
const mockUsers: User[] = [
  {
    id: '1',
    email: '<EMAIL>',
    name: 'System Administrator',
    role: 'admin',
    permissions: ['read', 'write', 'delete', 'manage_users', 'manage_properties', 'manage_billing']
  },
  {
    id: '2',
    email: '<EMAIL>',
    name: 'Property Owner',
    role: 'owner',
    permissions: ['read', 'write', 'manage_properties', 'view_billing']
  },
  {
    id: '3',
    email: '<EMAIL>',
    name: 'Tenant User',
    role: 'tenant',
    permissions: ['read', 'view_own_data']
  },
  {
    id: '4',
    email: '<EMAIL>',
    name: 'Staff Member',
    role: 'staff',
    permissions: ['read', 'write', 'manage_properties']
  }
];

export const AuthProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const [isLoading, setIsLoading] = useState(true);
  const [user, setUser] = useState<User | null>(null);
  const [isAuthenticated, setIsAuthenticated] = useState(false);

  useEffect(() => {
    // Check for existing session in localStorage
    const savedUser = localStorage.getItem('mock_user');
    if (savedUser) {
      try {
        const parsedUser = JSON.parse(savedUser);
        setUser(parsedUser);
        setIsAuthenticated(true);
      } catch (error) {
        console.error('Error parsing saved user:', error);
        localStorage.removeItem('mock_user');
      }
    }
    setIsLoading(false);
  }, []);

  const login = (email?: string, password?: string) => {
    // For demo purposes, accept any credentials or auto-login with admin
    let selectedUser = mockUsers[0]; // Default to admin

    if (email) {
      const foundUser = mockUsers.find(u => u.email.toLowerCase() === email.toLowerCase());
      if (foundUser) {
        selectedUser = foundUser;
      }
    }

    setUser(selectedUser);
    setIsAuthenticated(true);
    localStorage.setItem('mock_user', JSON.stringify(selectedUser));
  };

  const logout = () => {
    setUser(null);
    setIsAuthenticated(false);
    localStorage.removeItem('mock_user');
    // Clean up any API tokens that might have been stored
    localStorage.removeItem('access_token');
    localStorage.removeItem('auth_user');
  };

  const value: AuthContextType = {
    isAuthenticated,
    isLoading,
    user,
    login,
    logout,
  };

  return <AuthContext.Provider value={value}>{children}</AuthContext.Provider>;
};
