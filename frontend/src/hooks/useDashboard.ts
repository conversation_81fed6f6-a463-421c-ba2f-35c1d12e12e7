import { useQuery, useQueries } from '@tanstack/react-query';
import { dashboardService } from '@/services/dashboard.service';
import type { 
  UseDashboardReturn, 
  UseFinancialAnalyticsReturn,
  UsePropertyAnalyticsReturn,
  UseTenantAnalyticsReturn,
  MetricCard,
  Activity,
  Task,
  Alert
} from '@/types/dashboard';

// Query keys
export const dashboardKeys = {
  all: ['dashboard'] as const,
  metrics: () => [...dashboardKeys.all, 'metrics'] as const,
  financial: () => [...dashboardKeys.all, 'financial'] as const,
  property: () => [...dashboardKeys.all, 'property'] as const,
  tenant: () => [...dashboardKeys.all, 'tenant'] as const,
  reports: () => [...dashboardKeys.all, 'reports'] as const,
  receipts: () => [...dashboardKeys.all, 'receipts'] as const,
  activities: () => [...dashboardKeys.all, 'activities'] as const,
  tasks: () => [...dashboardKeys.all, 'tasks'] as const,
};

// Main dashboard hook
export function useDashboard(): UseDashboardReturn {
  const queries = useQueries({
    queries: [
      {
        queryKey: dashboardKeys.metrics(),
        queryFn: dashboardService.getDashboardMetrics,
        staleTime: 5 * 60 * 1000, // 5 minutes
        refetchInterval: 5 * 60 * 1000, // Auto-refresh every 5 minutes
      },
      {
        queryKey: dashboardKeys.reports(),
        queryFn: dashboardService.getReportsDashboard,
        staleTime: 5 * 60 * 1000,
      },
    ],
  });

  const [metricsQuery, reportsQuery] = queries;

  // Transform data into MetricCard format
  const metrics: MetricCard[] = metricsQuery.data ? [
    {
      id: 'total-units',
      title: 'Total Units',
      value: metricsQuery.data.totalUnits,
      subtitle: `${metricsQuery.data.occupiedUnits} occupied, ${metricsQuery.data.vacantUnits} vacant`,
      icon: require('@mui/icons-material/Home').default,
      color: 'primary',
      onClick: () => window.location.href = '/properties',
    },
    {
      id: 'rented-units',
      title: 'Rented Units',
      value: metricsQuery.data.rentedUnits,
      subtitle: `${metricsQuery.data.vacantUnits} available to let`,
      icon: require('@mui/icons-material/TrendingUp').default,
      color: 'success',
      onClick: () => window.location.href = '/properties?status=rented',
    },
    {
      id: 'total-tenants',
      title: 'Total Tenants',
      value: metricsQuery.data.activeTenantsCount,
      subtitle: `${metricsQuery.data.activeTenantsCount} verified`,
      icon: require('@mui/icons-material/People').default,
      color: 'info',
      onClick: () => window.location.href = '/tenants',
    },
    {
      id: 'active-agreements',
      title: 'Active Agreements',
      value: metricsQuery.data.activeAgreements,
      subtitle: `${metricsQuery.data.expiringSoon} expiring soon`,
      icon: require('@mui/icons-material/Assignment').default,
      color: 'warning',
      onClick: () => window.location.href = '/agreements',
    },
    {
      id: 'monthly-revenue',
      title: 'Monthly Revenue',
      value: `₹${metricsQuery.data.monthlyRevenue?.toLocaleString() || '0'}`,
      subtitle: `${metricsQuery.data.collectionRate || 0}% collection rate`,
      icon: require('@mui/icons-material/AccountBalance').default,
      color: 'success',
      onClick: () => window.location.href = '/financial-analytics',
    },
    {
      id: 'collection-days',
      title: 'Avg Collection Days',
      value: metricsQuery.data.avgCollectionDays || 0,
      subtitle: 'Average payment collection time',
      icon: require('@mui/icons-material/Schedule').default,
      color: 'info',
      onClick: () => window.location.href = '/payments',
    },
  ] : [];

  // Transform activities from reports data
  const activities: Activity[] = reportsQuery.data?.recentActivities?.map((activity: any) => ({
    id: activity.id.toString(),
    type: activity.type,
    title: activity.message,
    description: activity.message,
    timestamp: new Date(activity.time),
    status: 'completed',
  })) || [];

  // Mock tasks for now (to be replaced with real API)
  const tasks: Task[] = [
    {
      id: '1',
      title: 'Agreement renewal for Unit A-101',
      description: 'Rental agreement expires in 15 days',
      dueDate: new Date(Date.now() + 15 * 24 * 60 * 60 * 1000),
      priority: 'high',
      status: 'pending',
      category: 'agreement',
    },
    {
      id: '2',
      title: 'Tenant verification pending',
      description: 'Complete KYC verification for new tenant',
      dueDate: new Date(Date.now() + 3 * 24 * 60 * 60 * 1000),
      priority: 'medium',
      status: 'pending',
      category: 'tenant',
    },
    {
      id: '3',
      title: 'NOC application review',
      description: 'Review and approve pending NOC request',
      dueDate: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000),
      priority: 'low',
      status: 'pending',
      category: 'other',
    },
  ];

  // Mock alerts for now (to be replaced with real API)
  const alerts: Alert[] = [
    {
      id: '1',
      type: 'warning',
      title: 'Agreements Expiring Soon',
      message: '1 agreement(s) expiring within 30 days',
      timestamp: new Date(),
      dismissed: false,
      actionable: true,
      action: {
        label: 'View Agreements',
        onClick: () => window.location.href = '/agreements',
      },
    },
    {
      id: '2',
      type: 'success',
      title: 'New Rental Enquiry',
      message: '1 new rental enquiry received',
      timestamp: new Date(),
      dismissed: false,
      actionable: true,
      action: {
        label: 'View Enquiries',
        onClick: () => window.location.href = '/enquiries',
      },
    },
  ];

  const loading = queries.some(query => query.isLoading);
  const error = queries.find(query => query.error)?.error?.message || null;
  const lastUpdated = queries.find(query => query.dataUpdatedAt)?.dataUpdatedAt 
    ? new Date(Math.max(...queries.map(q => q.dataUpdatedAt || 0)))
    : null;

  const refresh = async () => {
    await Promise.all(queries.map(query => query.refetch()));
  };

  return {
    metrics,
    activities,
    tasks,
    alerts,
    loading,
    error,
    refresh,
    lastUpdated,
  };
}

// Financial analytics hook
export function useFinancialAnalytics(): UseFinancialAnalyticsReturn {
  const queries = useQueries({
    queries: [
      {
        queryKey: [...dashboardKeys.financial(), 'metrics'],
        queryFn: dashboardService.getFinancialMetrics,
        staleTime: 5 * 60 * 1000,
      },
      {
        queryKey: [...dashboardKeys.financial(), 'revenue'],
        queryFn: dashboardService.getRevenueAnalytics,
        staleTime: 5 * 60 * 1000,
      },
      {
        queryKey: [...dashboardKeys.financial(), 'payment-methods'],
        queryFn: dashboardService.getPaymentMethods,
        staleTime: 5 * 60 * 1000,
      },
      {
        queryKey: [...dashboardKeys.financial(), 'outstanding-dues'],
        queryFn: dashboardService.getOutstandingDues,
        staleTime: 5 * 60 * 1000,
      },
    ],
  });

  const [metricsQuery, revenueQuery, paymentMethodsQuery, outstandingDuesQuery] = queries;

  const loading = queries.some(query => query.isLoading);
  const error = queries.find(query => query.error)?.error?.message || null;

  const refresh = async () => {
    await Promise.all(queries.map(query => query.refetch()));
  };

  return {
    metrics: metricsQuery.data || [],
    revenueData: revenueQuery.data || [],
    paymentMethods: paymentMethodsQuery.data || [],
    outstandingDues: outstandingDuesQuery.data || [],
    loading,
    error,
    refresh,
  };
}

// Property analytics hook
export function usePropertyAnalytics(): UsePropertyAnalyticsReturn {
  const { data, isLoading, error, refetch } = useQuery({
    queryKey: dashboardKeys.property(),
    queryFn: dashboardService.getPropertyAnalytics,
    staleTime: 10 * 60 * 1000, // 10 minutes
  });

  return {
    analytics: data || null,
    loading: isLoading,
    error: error?.message || null,
    refresh: refetch,
  };
}

// Tenant analytics hook
export function useTenantAnalytics(): UseTenantAnalyticsReturn {
  const { data, isLoading, error, refetch } = useQuery({
    queryKey: dashboardKeys.tenant(),
    queryFn: dashboardService.getTenantAnalytics,
    staleTime: 10 * 60 * 1000, // 10 minutes
  });

  return {
    analytics: data || null,
    loading: isLoading,
    error: error?.message || null,
    refresh: refetch,
  };
}
