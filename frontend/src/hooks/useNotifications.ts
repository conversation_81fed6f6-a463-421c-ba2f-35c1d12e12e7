import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { notificationsService, type NotificationFilters } from '@/services/notifications.service';
import { toast } from 'react-hot-toast';

// Query keys
export const notificationKeys = {
  all: ['notifications'] as const,
  lists: () => [...notificationKeys.all, 'list'] as const,
  list: (filters: NotificationFilters) => [...notificationKeys.lists(), filters] as const,
  statistics: () => [...notificationKeys.all, 'statistics'] as const,
};

/**
 * Hook to fetch notifications with pagination and filtering
 */
export function useNotifications(filters: NotificationFilters = {}) {
  return useQuery({
    queryKey: notificationKeys.list(filters),
    queryFn: () => notificationsService.getNotifications(filters),
    staleTime: 30 * 1000, // 30 seconds
    refetchInterval: 60 * 1000, // Refetch every minute for real-time updates
    refetchOnWindowFocus: true,
  });
}

/**
 * Hook to fetch notification statistics
 */
export function useNotificationStatistics() {
  return useQuery({
    queryKey: notificationKeys.statistics(),
    queryFn: () => notificationsService.getStatistics(),
    staleTime: 30 * 1000, // 30 seconds
    refetchInterval: 60 * 1000, // Refetch every minute
  });
}

/**
 * Hook to mark a notification as read
 */
export function useMarkNotificationAsRead() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (notificationId: string) => notificationsService.markAsRead(notificationId),
    onSuccess: (data, notificationId) => {
      // Update all notification queries
      queryClient.invalidateQueries({ queryKey: notificationKeys.all });
      
      // Optimistically update the specific notification in cache
      queryClient.setQueriesData(
        { queryKey: notificationKeys.lists() },
        (oldData: any) => {
          if (!oldData) return oldData;
          
          return {
            ...oldData,
            data: oldData.data.map((notification: any) =>
              notification.id === notificationId
                ? { ...notification, read_at: new Date().toISOString() }
                : notification
            ),
          };
        }
      );

      toast.success('Notification marked as read');
    },
    onError: (error: any) => {
      toast.error(error.response?.data?.message || 'Failed to mark notification as read');
    },
  });
}

/**
 * Hook to mark all notifications as read
 */
export function useMarkAllNotificationsAsRead() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: () => notificationsService.markAllAsRead(),
    onSuccess: () => {
      // Invalidate all notification queries to refetch fresh data
      queryClient.invalidateQueries({ queryKey: notificationKeys.all });
      
      // Optimistically update all notifications in cache
      queryClient.setQueriesData(
        { queryKey: notificationKeys.lists() },
        (oldData: any) => {
          if (!oldData) return oldData;
          
          return {
            ...oldData,
            data: oldData.data.map((notification: any) => ({
              ...notification,
              read_at: new Date().toISOString(),
            })),
          };
        }
      );

      toast.success('All notifications marked as read');
    },
    onError: (error: any) => {
      toast.error(error.response?.data?.message || 'Failed to mark all notifications as read');
    },
  });
}

/**
 * Hook to send a test notification
 */
export function useSendTestNotification() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (data: {
      unit_id: string;
      previous_status: string;
      new_status: string;
      reason?: string;
    }) => notificationsService.sendTestNotification(data),
    onSuccess: () => {
      // Invalidate notifications to show the new test notification
      queryClient.invalidateQueries({ queryKey: notificationKeys.all });
      toast.success('Test notification sent successfully');
    },
    onError: (error: any) => {
      toast.error(error.response?.data?.message || 'Failed to send test notification');
    },
  });
}

/**
 * Hook to get formatted notifications for display
 */
export function useFormattedNotifications(filters: NotificationFilters = {}) {
  const { data, isLoading, error, refetch } = useNotifications(filters);

  const formattedNotifications = data?.data?.map((notification) =>
    notificationsService.formatNotificationForDisplay(notification)
  ) || [];

  return {
    notifications: formattedNotifications,
    pagination: data ? {
      currentPage: data.current_page,
      lastPage: data.last_page,
      perPage: data.per_page,
      total: data.total,
      from: data.from,
      to: data.to,
    } : null,
    isLoading,
    error,
    refetch,
  };
}

/**
 * Hook to get notification counts and statistics
 */
export function useNotificationCounts() {
  const { data: stats, isLoading, error } = useNotificationStatistics();

  return {
    totalNotifications: stats?.total_notifications || 0,
    unreadNotifications: stats?.unread_notifications || 0,
    readNotifications: stats?.read_notifications || 0,
    recentNotifications: stats?.recent_notifications?.map((notification) =>
      notificationsService.formatNotificationForDisplay(notification)
    ) || [],
    isLoading,
    error,
  };
}

/**
 * Hook for real-time notification updates
 */
export function useNotificationUpdates() {
  const queryClient = useQueryClient();

  const refreshNotifications = () => {
    queryClient.invalidateQueries({ queryKey: notificationKeys.all });
  };

  const markAsReadOptimistic = (notificationId: string) => {
    queryClient.setQueriesData(
      { queryKey: notificationKeys.lists() },
      (oldData: any) => {
        if (!oldData) return oldData;
        
        return {
          ...oldData,
          data: oldData.data.map((notification: any) =>
            notification.id === notificationId
              ? { ...notification, read_at: new Date().toISOString() }
              : notification
          ),
        };
      }
    );
  };

  return {
    refreshNotifications,
    markAsReadOptimistic,
  };
}
