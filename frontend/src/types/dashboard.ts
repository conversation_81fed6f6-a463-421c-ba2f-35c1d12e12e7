export interface DashboardState {
  loading: boolean;
  error: string | null;
  lastUpdated: Date | null;
}

export interface MetricCard {
  id: string;
  title: string;
  value: number | string;
  subtitle?: string;
  icon: React.ComponentType<any>;
  color: 'primary' | 'secondary' | 'success' | 'warning' | 'error' | 'info';
  trend?: {
    value: number;
    direction: 'up' | 'down';
    period: string;
  };
  onClick?: () => void;
}

export interface ChartData {
  name: string;
  value: number;
  [key: string]: any;
}

export interface TimeSeriesData {
  period: string;
  [key: string]: any;
}

export interface Activity {
  id: string;
  type: 'agreement' | 'tenant' | 'payment' | 'maintenance' | 'noc' | 'enquiry';
  title: string;
  description: string;
  timestamp: Date;
  status: 'completed' | 'pending' | 'failed' | 'in_progress';
  priority?: 'low' | 'medium' | 'high';
  metadata?: Record<string, any>;
}

export interface Task {
  id: string;
  title: string;
  description: string;
  dueDate: Date;
  priority: 'low' | 'medium' | 'high';
  status: 'pending' | 'in_progress' | 'completed' | 'overdue';
  assignee?: string;
  category: 'agreement' | 'tenant' | 'maintenance' | 'payment' | 'other';
}

export interface Alert {
  id: string;
  type: 'info' | 'warning' | 'error' | 'success';
  title: string;
  message: string;
  timestamp: Date;
  dismissed: boolean;
  actionable?: boolean;
  action?: {
    label: string;
    onClick: () => void;
  };
}

export interface DashboardWidget {
  id: string;
  title: string;
  type: 'metric' | 'chart' | 'table' | 'list' | 'custom';
  size: 'small' | 'medium' | 'large' | 'full';
  position: {
    x: number;
    y: number;
    w: number;
    h: number;
  };
  config: Record<string, any>;
  visible: boolean;
  refreshInterval?: number;
}

export interface DashboardLayout {
  id: string;
  name: string;
  description?: string;
  widgets: DashboardWidget[];
  isDefault: boolean;
  createdAt: Date;
  updatedAt: Date;
}

export interface FilterOptions {
  dateRange?: {
    start: Date;
    end: Date;
  };
  properties?: string[];
  tenants?: string[];
  status?: string[];
  categories?: string[];
}

export interface DashboardPreferences {
  defaultLayout: string;
  refreshInterval: number;
  notifications: {
    enabled: boolean;
    types: string[];
  };
  theme: 'light' | 'dark' | 'auto';
  compactMode: boolean;
}

// API Response Types
export interface ApiResponse<T> {
  success: boolean;
  message: string;
  data: T;
  meta?: {
    total?: number;
    page?: number;
    limit?: number;
    hasMore?: boolean;
  };
}

export interface PaginatedResponse<T> extends ApiResponse<T[]> {
  meta: {
    total: number;
    page: number;
    limit: number;
    hasMore: boolean;
    totalPages: number;
  };
}

// Component Props Types
export interface DashboardComponentProps {
  loading?: boolean;
  error?: string | null;
  onRefresh?: () => void;
  className?: string;
  style?: React.CSSProperties;
}

export interface MetricCardProps extends DashboardComponentProps {
  metric: MetricCard;
  size?: 'small' | 'medium' | 'large';
  variant?: 'default' | 'compact' | 'detailed';
}

export interface ChartComponentProps extends DashboardComponentProps {
  data: ChartData[];
  title?: string;
  height?: number;
  type?: 'line' | 'bar' | 'pie' | 'area' | 'scatter';
  config?: Record<string, any>;
}

export interface ActivityListProps extends DashboardComponentProps {
  activities: Activity[];
  maxItems?: number;
  showFilters?: boolean;
  onActivityClick?: (activity: Activity) => void;
}

export interface TaskListProps extends DashboardComponentProps {
  tasks: Task[];
  maxItems?: number;
  showFilters?: boolean;
  onTaskClick?: (task: Task) => void;
  onTaskComplete?: (taskId: string) => void;
}

// Hook Return Types
export interface UseDashboardReturn {
  metrics: MetricCard[];
  activities: Activity[];
  tasks: Task[];
  alerts: Alert[];
  loading: boolean;
  error: string | null;
  refresh: () => Promise<void>;
  lastUpdated: Date | null;
}

export interface UseFinancialAnalyticsReturn {
  metrics: any[];
  revenueData: any[];
  paymentMethods: any[];
  outstandingDues: any[];
  loading: boolean;
  error: string | null;
  refresh: () => Promise<void>;
}

export interface UsePropertyAnalyticsReturn {
  analytics: any;
  loading: boolean;
  error: string | null;
  refresh: () => Promise<void>;
}

export interface UseTenantAnalyticsReturn {
  analytics: any;
  loading: boolean;
  error: string | null;
  refresh: () => Promise<void>;
}

// Utility Types
export type DashboardSection = 
  | 'overview' 
  | 'financial' 
  | 'property' 
  | 'tenant' 
  | 'reports' 
  | 'analytics';

export type RefreshStrategy = 'manual' | 'auto' | 'realtime';

export type DataSource = 
  | 'financial' 
  | 'property' 
  | 'tenant' 
  | 'maintenance' 
  | 'reports' 
  | 'receipts';

export interface DashboardConfig {
  sections: DashboardSection[];
  refreshStrategy: RefreshStrategy;
  refreshInterval: number;
  enableRealtime: boolean;
  compactMode: boolean;
  showAlerts: boolean;
  maxActivities: number;
  maxTasks: number;
}
