import { create } from 'zustand';
import { devtools } from 'zustand/middleware';

interface UIState {
  sidebarOpen: boolean;
  theme: 'light' | 'dark';
  notifications: Notification[];
  toggleSidebar: () => void;
  setTheme: (theme: 'light' | 'dark') => void;
  addNotification: (notification: Notification) => void;
  removeNotification: (id: string) => void;
}

export interface Notification {
  id: string;
  type: 'success' | 'error' | 'warning' | 'info';
  message: string;
  title?: string;
  duration?: number;
}

export const useUIStore = create<UIState>()(
  devtools(
    (set, get) => ({
      sidebarOpen: false,
      theme: 'light',
      notifications: [],
      
      toggleSidebar: () => {
        set((state) => ({ sidebarOpen: !state.sidebarOpen }));
      },
      
      setTheme: (theme) => {
        set({ theme });
        // TODO: Implement theme persistence
      },
      
      addNotification: (notification) => {
        // SSR-safe ID generation
        const id = typeof window !== 'undefined'
          ? Math.random().toString(36).substr(2, 9)
          : `ssr-${Date.now()}-${Math.floor(Math.random() * 1000)}`;
        const newNotification = { ...notification, id };
        set((state) => ({
          notifications: [...state.notifications, newNotification],
        }));

        // Auto-remove notification after duration (client-side only)
        if (notification.duration && typeof window !== 'undefined') {
          setTimeout(() => {
            get().removeNotification(id);
          }, notification.duration);
        }
      },
      
      removeNotification: (id) => {
        set((state) => ({
          notifications: state.notifications.filter(n => n.id !== id),
        }));
      },
    }),
    { name: 'ui-store' }
  )
);
