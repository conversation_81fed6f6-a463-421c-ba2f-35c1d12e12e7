{"name": "frontend", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack", "build": "next build", "start": "next start", "lint": "next lint", "lint:fix": "next lint --fix", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage --watchAll=false", "test:unit": "jest --testPathPattern='(unit|__tests__)' --testPathIgnorePatterns='integration|e2e'", "test:integration": "jest --testPathPattern='integration'", "test:component": "jest --testPathPattern='components.*test'", "test:hooks": "jest --testPathPattern='hooks.*test'", "test:services": "jest --testPathPattern='services.*test'", "test:e2e": "playwright test", "test:e2e:ui": "playwright test --ui", "test:e2e:headed": "playwright test --headed", "test:e2e:debug": "playwright test --debug", "test:all": "./scripts/test-runner.sh all", "test:ci": "./scripts/test-runner.sh ci", "type-check": "tsc --noEmit", "type-check:watch": "tsc --noEmit --watch", "prepare": "husky install", "pre-commit": "lint-staged"}, "dependencies": {"@emotion/react": "^11.11.0", "@emotion/styled": "^11.11.0", "@hookform/resolvers": "^3.3.0", "@mui/icons-material": "^5.18.0", "@mui/material": "^5.15.0", "@mui/system": "^7.2.0", "@mui/x-date-pickers": "^8.8.0", "@tanstack/react-query": "^5.17.0", "@tanstack/react-query-devtools": "^5.83.0", "@types/recharts": "^1.8.29", "@vercel/mcp-adapter": "^0.11.2", "axios": "^1.6.0", "date-fns": "^4.1.0", "dompurify": "^3.0.8", "next": "15.3.5", "react": "^19.0.0", "react-dom": "^19.0.0", "react-dropzone": "^14.3.8", "react-hook-form": "^7.48.0", "react-hot-toast": "^2.5.2", "recharts": "^2.15.4", "zod": "^3.22.0", "zustand": "^4.4.0"}, "devDependencies": {"@axe-core/react": "^4.8.0", "@eslint/eslintrc": "^3", "@playwright/test": "^1.40.0", "@tailwindcss/postcss": "^4", "@testing-library/jest-dom": "^6.6.3", "@testing-library/react": "^15.0.7", "@testing-library/user-event": "^14.6.1", "@types/dompurify": "^3.0.5", "@types/jest": "^29.5.14", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "@types/testing-library__jest-dom": "^6.0.0", "@typescript-eslint/eslint-plugin": "^8.36.0", "@typescript-eslint/parser": "^8.36.0", "eslint": "^9", "eslint-config-next": "15.3.5", "eslint-config-prettier": "^10.1.5", "eslint-plugin-prettier": "^5.5.1", "husky": "^8.0.3", "jest": "^29.7.0", "jest-axe": "^9.0.0", "jest-environment-jsdom": "^29.7.0", "lint-staged": "^15.2.0", "msw": "^2.10.4", "prettier": "^3.6.2", "tailwindcss": "^4", "ts-jest": "^29.1.0", "typescript": "^5", "webpack-bundle-analyzer": "^4.10.1"}}