/**
 * Test script to verify Custom Reports tab is working
 * Run this in the browser console after opening the dashboard
 */

function testCustomReportsTab() {
  console.log('🧪 Testing Custom Reports Tab...\n');

  // Check if we're on the dashboard page
  if (!window.location.pathname.includes('/dashboard')) {
    console.error('❌ Please navigate to /dashboard first');
    return;
  }

  // Look for the Custom Reports tab
  const customReportsTab = Array.from(document.querySelectorAll('[role="tab"]'))
    .find(tab => tab.textContent.includes('Custom Reports'));

  if (!customReportsTab) {
    console.error('❌ Custom Reports tab not found');
    return;
  }

  console.log('✅ Custom Reports tab found');

  // Check if AssessmentIcon is present (should not throw error)
  try {
    // Click the Custom Reports tab
    customReportsTab.click();
    console.log('✅ Custom Reports tab clicked successfully');

    // Wait a moment for the tab to load
    setTimeout(() => {
      // Check for the KPI Metric button that uses AssessmentIcon
      const kpiButton = Array.from(document.querySelectorAll('button'))
        .find(btn => btn.textContent.includes('KPI Metric'));

      if (kpiButton) {
        console.log('✅ KPI Metric button found (AssessmentIcon working)');
        
        // Check if the button has an icon
        const hasIcon = kpiButton.querySelector('svg') || kpiButton.querySelector('[data-testid*="Icon"]');
        if (hasIcon) {
          console.log('✅ AssessmentIcon is rendering correctly');
        } else {
          console.warn('⚠️  Icon not detected, but no error thrown');
        }
      } else {
        console.warn('⚠️  KPI Metric button not found');
      }

      // Check for any React errors in console
      const hasReactErrors = window.console.error.toString().includes('React') ||
                           document.querySelector('[data-reactroot] [style*="color: red"]');
      
      if (!hasReactErrors) {
        console.log('✅ No React errors detected');
      }

      // Check for the Custom Report Builder components
      const reportBuilderElements = [
        'Custom Report Builder',
        'Components',
        'Charts',
        'Data Sources'
      ];

      let foundElements = 0;
      reportBuilderElements.forEach(text => {
        if (document.body.textContent.includes(text)) {
          foundElements++;
          console.log(`✅ Found: ${text}`);
        }
      });

      console.log(`\n📊 Summary:`);
      console.log(`- Custom Reports tab: ✅ Working`);
      console.log(`- AssessmentIcon: ✅ No errors`);
      console.log(`- Report Builder elements: ${foundElements}/${reportBuilderElements.length} found`);
      
      if (foundElements === reportBuilderElements.length) {
        console.log('🎉 Custom Reports tab is fully functional!');
      } else {
        console.log('⚠️  Some elements may still be loading...');
      }

    }, 1000);

  } catch (error) {
    console.error('❌ Error testing Custom Reports tab:', error);
  }
}

// Test function for AssessmentIcon specifically
function testAssessmentIcon() {
  console.log('🔍 Testing AssessmentIcon specifically...\n');

  try {
    // Try to find any element that should contain AssessmentIcon
    const buttons = document.querySelectorAll('button');
    let iconFound = false;

    buttons.forEach(button => {
      if (button.textContent.includes('KPI Metric') || button.textContent.includes('Assessment')) {
        const icon = button.querySelector('svg');
        if (icon) {
          iconFound = true;
          console.log('✅ AssessmentIcon found and rendered');
          console.log(`   Button text: "${button.textContent.trim()}"`);
          console.log(`   Icon element:`, icon);
        }
      }
    });

    if (!iconFound) {
      console.log('ℹ️  AssessmentIcon not currently visible (may need to click Custom Reports tab first)');
    }

  } catch (error) {
    console.error('❌ Error checking AssessmentIcon:', error);
  }
}

// Auto-run basic checks
if (typeof window !== 'undefined') {
  console.log('🎯 Custom Reports Test Script Loaded');
  console.log('📝 Run testCustomReportsTab() to test the Custom Reports functionality');
  console.log('📝 Run testAssessmentIcon() to specifically test the AssessmentIcon');
  
  // Auto-check if we're already on dashboard
  if (window.location.pathname.includes('/dashboard')) {
    console.log('ℹ️  Dashboard detected. You can run the tests now.');
  }
}
