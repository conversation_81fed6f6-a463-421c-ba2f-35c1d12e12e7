/**
 * Test script to verify notifications integration
 * Run this in the browser console on the notifications page
 */

async function testNotificationsIntegration() {
  console.log('🧪 Testing Notifications Integration...\n');

  // Test 1: Check if we're on the notifications page
  if (!window.location.pathname.includes('/notifications')) {
    console.error('❌ Please navigate to /notifications first');
    return;
  }

  console.log('✅ On notifications page');

  // Test 2: Check for React Query
  const hasReactQuery = window.__REACT_QUERY_DEVTOOLS_GLOBAL_HOOK__ || 
                       document.querySelector('[data-rq]') ||
                       window.React;
  console.log(`${hasReactQuery ? '✅' : '⚠️'} React Query: ${hasReactQuery ? 'Detected' : 'Not detected'}`);

  // Test 3: Check for notification components
  const notificationElements = [
    'Total Notifications',
    'Unread',
    'High Priority',
    'Today'
  ];

  let foundElements = 0;
  notificationElements.forEach(text => {
    if (document.body.textContent.includes(text)) {
      foundElements++;
      console.log(`✅ Found: ${text}`);
    } else {
      console.log(`❌ Missing: ${text}`);
    }
  });

  // Test 4: Check for loading states
  const hasLoadingIndicators = document.querySelector('[role="progressbar"]') ||
                              document.querySelector('.MuiCircularProgress-root') ||
                              document.querySelector('.MuiSkeleton-root');
  console.log(`${hasLoadingIndicators ? '✅' : 'ℹ️'} Loading indicators: ${hasLoadingIndicators ? 'Present' : 'Not currently visible'}`);

  // Test 5: Check for notification list
  const hasNotificationList = document.querySelector('[role="list"]') ||
                              document.querySelector('.MuiList-root') ||
                              document.body.textContent.includes('No notifications found');
  console.log(`${hasNotificationList ? '✅' : '❌'} Notification list: ${hasNotificationList ? 'Present' : 'Missing'}`);

  // Test 6: Check for error messages
  const hasErrors = document.querySelector('[role="alert"]') ||
                   document.body.textContent.includes('Failed to load') ||
                   document.body.textContent.includes('Error');
  console.log(`${hasErrors ? '⚠️' : '✅'} Error states: ${hasErrors ? 'Error detected' : 'No errors'}`);

  // Test 7: Check for real data vs mock data
  const hasMockData = document.body.textContent.includes('Rent Payment Reminder') ||
                     document.body.textContent.includes('Unit 301') ||
                     document.body.textContent.includes('John Smith');
  
  const hasRealData = document.body.textContent.includes('Unit status changed') ||
                     document.body.textContent.includes('UnitStatusChangedNotification');

  if (hasRealData) {
    console.log('✅ Real API data detected');
  } else if (hasMockData) {
    console.log('⚠️ Mock data detected - API may not be connected');
  } else {
    console.log('ℹ️ No specific data patterns detected');
  }

  // Test 8: Check for interactive elements
  const hasInteractiveElements = document.querySelector('button[aria-label*="Mark"]') ||
                                document.querySelector('button[title*="Mark"]') ||
                                document.querySelector('.MuiIconButton-root');
  console.log(`${hasInteractiveElements ? '✅' : '❌'} Interactive elements: ${hasInteractiveElements ? 'Present' : 'Missing'}`);

  // Test 9: Check for tabs
  const hasTabs = document.querySelector('[role="tablist"]') ||
                 document.querySelector('.MuiTabs-root') ||
                 document.body.textContent.includes('All (') ||
                 document.body.textContent.includes('Unread (');
  console.log(`${hasTabs ? '✅' : '❌'} Navigation tabs: ${hasTabs ? 'Present' : 'Missing'}`);

  // Test 10: Check for search functionality
  const hasSearch = document.querySelector('input[placeholder*="Search"]') ||
                   document.querySelector('input[placeholder*="search"]');
  console.log(`${hasSearch ? '✅' : '❌'} Search functionality: ${hasSearch ? 'Present' : 'Missing'}`);

  // Summary
  console.log('\n📊 Integration Test Summary:');
  console.log(`- Component elements: ${foundElements}/${notificationElements.length} found`);
  console.log(`- Page functionality: ${hasNotificationList && hasTabs && hasSearch ? 'Working' : 'Issues detected'}`);
  console.log(`- Data source: ${hasRealData ? 'Real API' : hasMockData ? 'Mock data' : 'Unknown'}`);
  console.log(`- Error state: ${hasErrors ? 'Errors present' : 'Clean'}`);

  if (foundElements === notificationElements.length && hasNotificationList && !hasErrors) {
    console.log('🎉 Notifications integration appears to be working correctly!');
  } else {
    console.log('⚠️ Some issues detected. Check the details above.');
  }

  return {
    elementsFound: foundElements,
    totalElements: notificationElements.length,
    hasNotificationList,
    hasErrors,
    hasRealData,
    hasMockData
  };
}

// Test API endpoints directly
async function testNotificationsAPI() {
  console.log('\n🔌 Testing Notifications API...\n');

  const endpoints = [
    '/api/v1/notifications',
    '/api/v1/notifications/statistics'
  ];

  for (const endpoint of endpoints) {
    try {
      console.log(`📡 Testing ${endpoint}...`);
      
      const response = await fetch(endpoint, {
        headers: {
          'Accept': 'application/json',
          'Content-Type': 'application/json'
        }
      });

      console.log(`   Status: ${response.status} ${response.statusText}`);
      
      if (response.ok) {
        const data = await response.json();
        console.log(`   Success: ${data.success}`);
        console.log(`   Data type: ${Array.isArray(data.data) ? 'Array' : typeof data.data}`);
        if (Array.isArray(data.data)) {
          console.log(`   Items count: ${data.data.length}`);
        }
      } else {
        console.log(`   Error: ${response.status}`);
      }
    } catch (error) {
      console.log(`   Network error: ${error.message}`);
    }
    console.log('');
  }
}

// Auto-run if in browser console
if (typeof window !== 'undefined') {
  console.log('🎯 Notifications Test Script Loaded');
  console.log('📝 Run testNotificationsIntegration() to test the UI integration');
  console.log('📝 Run testNotificationsAPI() to test the API endpoints');
  
  // Auto-check if we're on notifications page
  if (window.location.pathname.includes('/notifications')) {
    console.log('ℹ️ Notifications page detected. You can run the tests now.');
  }
}
