/**
 * Test script to verify authentication fix for notifications
 * Run this in the browser console on the notifications page
 */

async function testAuthenticationFix() {
  console.log('🔐 Testing Authentication Fix...\n');

  // Test 1: Check if we're on the notifications page
  if (!window.location.pathname.includes('/notifications')) {
    console.error('❌ Please navigate to /notifications first');
    return;
  }

  console.log('✅ On notifications page');

  // Test 2: Check for authentication errors
  const hasAuthErrors = document.body.textContent.includes('401') ||
                       document.body.textContent.includes('Unauthorized') ||
                       document.body.textContent.includes('Failed to load notifications: Request failed with status code 401');

  if (hasAuthErrors) {
    console.error('❌ Authentication errors still present');
    console.log('   Check browser console for 401 errors');
  } else {
    console.log('✅ No authentication errors detected');
  }

  // Test 3: Check for loading states or data
  const hasLoadingStates = document.querySelector('[role="progressbar"]') ||
                          document.querySelector('.MuiCircularProgress-root') ||
                          document.querySelector('.MuiSkeleton-root');

  const hasNotificationData = document.body.textContent.includes('Unit A101 Status Changed') ||
                              document.body.textContent.includes('Status changed from') ||
                              document.body.textContent.includes('System Administrator');

  const hasEmptyState = document.body.textContent.includes('No notifications found') ||
                       document.body.textContent.includes('You\'re all caught up');

  if (hasNotificationData) {
    console.log('✅ Real notification data detected');
  } else if (hasEmptyState) {
    console.log('ℹ️ Empty state detected (no notifications)');
  } else if (hasLoadingStates) {
    console.log('⏳ Loading states detected - data may still be loading');
  } else {
    console.log('⚠️ No clear data state detected');
  }

  // Test 4: Check statistics cards
  const statisticsCards = document.querySelectorAll('.MuiCard-root');
  let statsWorking = false;

  statisticsCards.forEach(card => {
    const cardText = card.textContent;
    if (cardText.includes('Total Notifications') || 
        cardText.includes('Unread') || 
        cardText.includes('High Priority') || 
        cardText.includes('Today')) {
      
      // Check if the card shows numbers (not loading)
      const hasNumbers = /\d+/.test(cardText);
      if (hasNumbers && !cardText.includes('CircularProgress')) {
        statsWorking = true;
      }
    }
  });

  console.log(`${statsWorking ? '✅' : '⚠️'} Statistics cards: ${statsWorking ? 'Working with data' : 'Loading or no data'}`);

  // Test 5: Check browser console for errors
  console.log('\n🔍 Browser Console Check:');
  console.log('   Look for any red error messages in the browser console');
  console.log('   Common issues to check:');
  console.log('   - 401 Unauthorized errors');
  console.log('   - Network errors');
  console.log('   - React component errors');

  // Test 6: Test API endpoints directly
  await testAPIEndpoints();

  // Summary
  console.log('\n📋 Authentication Fix Summary:');
  console.log(`- Authentication errors: ${hasAuthErrors ? '❌ Still present' : '✅ Resolved'}`);
  console.log(`- Data loading: ${hasNotificationData ? '✅ Real data' : hasEmptyState ? 'ℹ️ Empty state' : '⚠️ Unknown'}`);
  console.log(`- Statistics: ${statsWorking ? '✅ Working' : '⚠️ Issues'}`);

  if (!hasAuthErrors && (hasNotificationData || hasEmptyState) && statsWorking) {
    console.log('🎉 Authentication fix appears to be working correctly!');
  } else {
    console.log('⚠️ Some issues may still exist. Check the details above.');
  }

  return {
    authErrors: hasAuthErrors,
    hasData: hasNotificationData,
    hasEmptyState: hasEmptyState,
    statsWorking: statsWorking
  };
}

async function testAPIEndpoints() {
  console.log('\n🔌 Testing API Endpoints...\n');

  const endpoints = [
    '/api/v1/notifications',
    '/api/v1/notifications/statistics'
  ];

  for (const endpoint of endpoints) {
    try {
      console.log(`📡 Testing ${endpoint}...`);
      
      const response = await fetch(endpoint, {
        headers: {
          'Accept': 'application/json',
          'Content-Type': 'application/json'
        },
        credentials: 'include' // Include cookies for session-based auth
      });

      console.log(`   Status: ${response.status} ${response.statusText}`);
      
      if (response.ok) {
        const data = await response.json();
        console.log(`   Success: ${data.success}`);
        
        if (endpoint.includes('/statistics')) {
          console.log(`   Total notifications: ${data.data?.total_notifications || 'N/A'}`);
          console.log(`   Unread notifications: ${data.data?.unread_notifications || 'N/A'}`);
        } else {
          console.log(`   Data type: ${Array.isArray(data.data?.data) ? 'Array' : typeof data.data}`);
          if (Array.isArray(data.data?.data)) {
            console.log(`   Notifications count: ${data.data.data.length}`);
          }
        }
      } else if (response.status === 401) {
        console.log(`   ❌ Authentication failed - 401 Unauthorized`);
      } else {
        console.log(`   ❌ Error: ${response.status}`);
      }
    } catch (error) {
      console.log(`   ❌ Network error: ${error.message}`);
    }
    console.log('');
  }
}

// Check authentication status
function checkAuthStatus() {
  console.log('\n👤 Authentication Status Check:');
  
  // Check localStorage for tokens
  const accessToken = localStorage.getItem('access_token');
  const mockUser = localStorage.getItem('mock_user');
  
  console.log(`- Access token: ${accessToken ? '✅ Present' : '❌ Missing'}`);
  console.log(`- Mock user: ${mockUser ? '✅ Present' : '❌ Missing'}`);
  
  if (mockUser) {
    try {
      const user = JSON.parse(mockUser);
      console.log(`- User email: ${user.email}`);
      console.log(`- User role: ${user.role}`);
    } catch (error) {
      console.log(`- Mock user parse error: ${error.message}`);
    }
  }
}

// Auto-run if in browser console
if (typeof window !== 'undefined') {
  console.log('🎯 Authentication Fix Test Script Loaded');
  console.log('📝 Run testAuthenticationFix() to test the authentication fix');
  console.log('📝 Run checkAuthStatus() to check authentication status');
  
  // Auto-check if we're on notifications page
  if (window.location.pathname.includes('/notifications')) {
    console.log('ℹ️ Notifications page detected. You can run the tests now.');
    
    // Auto-run basic auth status check
    setTimeout(() => {
      checkAuthStatus();
    }, 1000);
  }
}
