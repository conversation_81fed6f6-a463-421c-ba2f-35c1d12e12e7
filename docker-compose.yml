version: '3.8'

services:
  # MySQL Database
  mysql:
    image: mysql:8.0
    container_name: tms_mysql
    environment:
      MYSQL_ROOT_PASSWORD: root_password
      MYSQL_DATABASE: tms_db
      MYSQL_USER: tms_user
      MYSQL_PASSWORD: tms_password
    ports:
      - "3307:3306"
    volumes:
      - mysql_data:/var/lib/mysql
      - ./docker/mysql/init:/docker-entrypoint-initdb.d
    networks:
      - tms_network

  # Redis Cache
  redis:
    image: redis:7-alpine
    container_name: tms_redis
    ports:
      - "6380:6379"
    volumes:
      - redis_data:/data
    networks:
      - tms_network

  # Keycloak Authentication Server
  keycloak:
    image: quay.io/keycloak/keycloak:22.0
    container_name: tms_keycloak
    environment:
      KEYCLOAK_ADMIN: admin
      KEYCLOAK_ADMIN_PASSWORD: admin
      KC_HTTP_ENABLED: true
      KC_HOSTNAME_STRICT_HTTPS: false
    ports:
      - "8082:8080"
    command: start-dev
    networks:
      - tms_network

  # Kong API Gateway
  kong-database:
    image: postgres:13
    container_name: tms_kong_db
    environment:
      POSTGRES_DB: kong
      POSTGRES_USER: kong
      POSTGRES_PASSWORD: kong_password
    volumes:
      - kong_data:/var/lib/postgresql/data
    networks:
      - tms_network

  kong-migrations:
    image: kong:3.4
    container_name: tms_kong_migrations
    environment:
      KONG_DATABASE: postgres
      KONG_PG_HOST: kong-database
      KONG_PG_DATABASE: kong
      KONG_PG_USER: kong
      KONG_PG_PASSWORD: kong_password
    command: kong migrations bootstrap
    depends_on:
      - kong-database
    networks:
      - tms_network

  kong:
    image: kong:3.4
    container_name: tms_kong
    environment:
      KONG_DATABASE: postgres
      KONG_PG_HOST: kong-database
      KONG_PG_DATABASE: kong
      KONG_PG_USER: kong
      KONG_PG_PASSWORD: kong_password
      KONG_PROXY_ACCESS_LOG: /dev/stdout
      KONG_ADMIN_ACCESS_LOG: /dev/stdout
      KONG_PROXY_ERROR_LOG: /dev/stderr
      KONG_ADMIN_ERROR_LOG: /dev/stderr
      KONG_ADMIN_LISTEN: 0.0.0.0:8001
      KONG_ADMIN_GUI_URL: http://localhost:8002
    ports:
      - "8000:8000"  # Kong proxy
      - "8001:8001"  # Kong admin API
      - "8002:8002"  # Kong Manager (GUI)
    depends_on:
      - kong-database
      - kong-migrations
    networks:
      - tms_network

  # Laravel Backend (Development)
  backend:
    build:
      context: ./backend
      dockerfile: Dockerfile
    container_name: tms_backend
    environment:
      APP_ENV: local
      APP_DEBUG: true
      DB_CONNECTION: mysql
      DB_HOST: mysql
      DB_PORT: 3306
      DB_DATABASE: tms_db
      DB_USERNAME: tms_user
      DB_PASSWORD: tms_password
      REDIS_HOST: redis
      REDIS_PORT: 6379
    ports:
      - "8083:8080"
    volumes:
      - ./backend:/var/www/html
      - vendor_data:/var/www/html/vendor
    depends_on:
      - mysql
      - redis
    networks:
      - tms_network

  # Next.js Frontend (Development)
  frontend:
    build:
      context: ./frontend
      dockerfile: Dockerfile
    container_name: tms_frontend
    environment:
      NODE_ENV: development
      NEXT_PUBLIC_API_URL: http://localhost:8000
      NEXT_PUBLIC_KEYCLOAK_URL: http://localhost:8080
    ports:
      - "3000:3000"
    volumes:
      - ./frontend:/app
      - node_modules:/app/node_modules
    depends_on:
      - backend
      - keycloak
    networks:
      - tms_network

volumes:
  mysql_data:
  redis_data:
  kong_data:
  vendor_data:
  node_modules:

networks:
  tms_network:
    driver: bridge 